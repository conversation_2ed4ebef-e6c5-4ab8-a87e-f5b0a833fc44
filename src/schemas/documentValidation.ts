
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const documentSchema = z.object({
  title: z.string().min(3, "Document title must be at least 3 characters"),
  documentType: commonValidationPatterns.requiredString("Document type"),
  category: commonValidationPatterns.requiredString("Category"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  owner: commonValidationPatterns.requiredString("Owner"),
  department: commonValidationPatterns.requiredString("Department"),
  reviewDate: commonValidationPatterns.requiredDate,
  reviewCycle: z.enum(["Annual", "Biannual", "Quarterly", "Monthly"], {
    errorMap: () => ({ message: "Please select a valid review cycle" })
  }),
  confidentialityLevel: z.enum(["Public", "Internal", "Confidential", "Restricted"], {
    errorMap: () => ({ message: "Please select a confidentiality level" })
  }),
  tags: z.string().optional(),
  file: z.any().optional()
});

export type DocumentFormData = z.infer<typeof documentSchema>;

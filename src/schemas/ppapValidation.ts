
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const ppapSchema = z.object({
  partNumber: commonValidationPatterns.requiredString("Part Number"),
  supplier: commonValidationPatterns.requiredString("Supplier"),
  submissionLevel: commonValidationPatterns.requiredString("Submission Level"),
  description: commonValidationPatterns.optionalString,
  drawingRevision: commonValidationPatterns.optionalString,
  specRevision: commonValidationPatterns.optionalString
});

export type PPAPFormData = z.infer<typeof ppapSchema>;

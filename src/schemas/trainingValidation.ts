
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const trainingSchema = z.object({
  title: commonValidationPatterns.requiredString("Training Title"),
  description: commonValidationPatterns.requiredString("Description"),
  category: commonValidationPatterns.requiredString("Category"),
  department: commonValidationPatterns.requiredString("Department"),
  trainer: commonValidationPatterns.requiredString("Trainer"),
  startDate: commonValidationPatterns.requiredDate,
  endDate: commonValidationPatterns.requiredDate,
  duration: z.string()
    .min(1, "Duration is required")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: "Duration must be a positive number"
    }),
  maxParticipants: z.string()
    .min(1, "Max participants is required")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: "Max participants must be a positive number"
    }),
  location: commonValidationPatterns.requiredString("Location"),
  requirements: commonValidationPatterns.optionalString
});

export type TrainingFormData = z.infer<typeof trainingSchema>;


import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const profileSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: commonValidationPatterns.email,
  phone: commonValidationPatterns.phone.optional().or(z.literal("")),
  jobTitle: commonValidationPatterns.requiredString("Job title"),
  department: commonValidationPatterns.requiredString("Department"),
  location: commonValidationPatterns.requiredString("Location"),
  manager: z.string().optional(),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  linkedIn: z.string().url("Please enter a valid LinkedIn URL").optional().or(z.literal("")),
  skills: z.string().optional(),
  profilePicture: z.any().optional()
});

export type ProfileFormData = z.infer<typeof profileSchema>;


import { z } from "zod";

export const employeeSchema = z.object({
  id: z.string().min(1, "Employee ID is required"),
  name: z.string().min(2, "Employee name must be at least 2 characters"),
  jobTitle: z.string().min(2, "Job title must be at least 2 characters"),
  department: z.string().min(1, "Department is required"),
  processes: z.string().optional(),
  status: z.enum(["on-duty", "on-vacation", "suspended"], {
    errorMap: () => ({ message: "Please select a valid status" })
  }),
  email: z.string().email("Please enter a valid email address").optional().or(z.literal("")),
  joiningDate: z.string().min(1, "Date of joining is required"),
  leavingDate: z.string().optional()
});

export type EmployeeFormData = z.infer<typeof employeeSchema>;

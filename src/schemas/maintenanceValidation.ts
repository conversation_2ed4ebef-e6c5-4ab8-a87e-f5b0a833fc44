
import { z } from "zod";

export const maintenanceSchema = z.object({
  title: z.string().min(3, "Maintenance title must be at least 3 characters"),
  date: z.string().min(1, "Date is required"),
  time: z.string().min(1, "Time is required"),
  type: z.enum(["Preventive", "Corrective", "Inspection", "Emergency"], {
    errorMap: () => ({ message: "Please select a valid maintenance type" })
  }),
  technician: z.string().min(1, "Technician assignment is required"),
  asset: z.string().min(1, "Asset selection is required"),
  description: z.string().optional(),
  priority: z.enum(["Low", "Medium", "High", "Critical"], {
    errorMap: () => ({ message: "Please select a valid priority" })
  })
});

export type MaintenanceFormData = z.infer<typeof maintenanceSchema>;

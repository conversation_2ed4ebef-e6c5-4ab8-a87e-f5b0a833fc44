
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const materialSchema = z.object({
  materialId: commonValidationPatterns.requiredString("Material ID"),
  materialName: commonValidationPatterns.requiredString("Material Name"),
  type: commonValidationPatterns.requiredString("Type"),
  category: commonValidationPatterns.requiredString("Category"),
  uom: commonValidationPatterns.requiredString("UOM")
});

export type MaterialFormData = z.infer<typeof materialSchema>;

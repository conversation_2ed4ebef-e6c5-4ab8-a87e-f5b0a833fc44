
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const workOrderSchema = z.object({
  productId: commonValidationPatterns.requiredString("Product"),
  quantity: z.string()
    .min(1, "Quantity is required")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: "Quantity must be a positive number"
    }),
  assignedLine: commonValidationPatterns.requiredString("Production Line"),
  plannedDate: commonValidationPatterns.requiredDate,
  priority: z.enum(["High", "Medium", "Low"]),
  notes: commonValidationPatterns.optionalString
});

export type WorkOrderFormData = z.infer<typeof workOrderSchema>;

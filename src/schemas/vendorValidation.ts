
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const vendorSchema = z.object({
  name: commonValidationPatterns.requiredString("Vendor Name"),
  productService: commonValidationPatterns.requiredString("Product/Service"),
  contactName: commonValidationPatterns.optionalString,
  contactEmail: z.string().email("Please enter a valid email address").optional().or(z.literal("")),
  contactPhone: commonValidationPatterns.optionalString,
  address: commonValidationPatterns.optionalString,
  description: commonValidationPatterns.optionalString,
  assignee: commonValidationPatterns.optionalString,
  status: z.enum(["Under evaluation", "Approved", "Rejected", "Pending"]),
  reviewPeriod: z.enum(["Quarterly", "Bi-annually", "Annually"])
});

export type VendorFormData = z.infer<typeof vendorSchema>;

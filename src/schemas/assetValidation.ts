
import { z } from "zod";
import { commonValidationPatterns } from "@/hooks/useFormValidation";

export const assetSchema = z.object({
  id: commonValidationPatterns.requiredString("Asset ID"),
  name: commonValidationPatterns.requiredString("Asset name"),
  description: commonValidationPatterns.requiredString("Description"),
  owner: commonValidationPatterns.requiredString("Owner"),
  location: commonValidationPatterns.requiredString("Location"),
  purchaseDate: commonValidationPatterns.optionalString,
  status: commonValidationPatterns.requiredString("Status"),
  calibrationRequired: z.enum(["Yes", "No"], {
    errorMap: () => ({ message: "Please select if calibration is required" })
  }),
  calibrationDate: z.string().optional(),
  calibrationCertNo: z.string().optional(),
  calibrationPeriod: z.string().optional(),
}).refine((data) => {
  // Conditional validation: if calibration is required, calibration fields should be filled
  if (data.calibrationRequired === "Yes") {
    return data.calibrationDate && data.calibrationCertNo && data.calibrationPeriod;
  }
  return true;
}, {
  message: "Calibration details are required when calibration is enabled",
  path: ["calibrationDate"]
});

export type AssetFormData = z.infer<typeof assetSchema>;

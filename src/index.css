@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.75rem;

    /* Enhanced spacing system */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Animation timing */
    --animation-fast: 150ms;
    --animation-normal: 250ms;
    --animation-slow: 350ms;

    /* Enhanced shadows */
    --shadow-subtle: 0 1px 3px 0 rgb(0 0 0 / 0.04), 0 1px 2px -1px rgb(0 0 0 / 0.04);
    --shadow-soft: 0 4px 6px -1px rgb(0 0 0 / 0.04), 0 2px 4px -2px rgb(0 0 0 / 0.04);
    --shadow-medium: 0 10px 15px -3px rgb(0 0 0 / 0.06), 0 4px 6px -4px rgb(0 0 0 / 0.06);
    --shadow-large: 0 20px 25px -5px rgb(0 0 0 / 0.08), 0 8px 10px -6px rgb(0 0 0 / 0.08);

    /* Sidebar specific variables */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.5% 48%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.5% 48%;
  }
}

@layer base {
  * {
    @apply border-border transition-colors duration-200;
  }
  
  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    line-height: 1.6;
    letter-spacing: -0.011em;
  }

  /* Enhanced typography scale */
  h1 {
    @apply text-3xl font-bold tracking-tight leading-tight;
    letter-spacing: -0.025em;
  }

  h2 {
    @apply text-2xl font-semibold tracking-tight leading-tight;
    letter-spacing: -0.022em;
  }

  h3 {
    @apply text-xl font-semibold tracking-tight leading-snug;
    letter-spacing: -0.019em;
  }

  h4 {
    @apply text-lg font-medium tracking-tight leading-snug;
    letter-spacing: -0.016em;
  }

  p {
    @apply leading-relaxed;
    letter-spacing: -0.011em;
  }

  /* Smooth focus rings */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
    transition: box-shadow var(--animation-fast) ease-out;
  }
}

/* Enhanced micro-interactions */
@layer components {
  .modern-card {
    @apply bg-white rounded-xl border border-slate-200/60 shadow-[var(--shadow-subtle)] 
           hover:shadow-[var(--shadow-soft)] hover:border-slate-200 
           transition-all duration-300 ease-out;
  }

  .modern-button {
    @apply inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg 
           text-sm font-medium transition-all duration-200 ease-out 
           focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring 
           focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 
           transform-gpu hover:scale-[1.02] active:scale-[0.98];
  }

  .modern-input {
    @apply flex h-11 w-full rounded-lg border border-slate-200 bg-white px-4 py-2 
           text-sm transition-colors duration-200 ease-out
           file:border-0 file:bg-transparent file:text-sm file:font-medium 
           placeholder:text-muted-foreground 
           focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring 
           focus-visible:ring-offset-2 focus-visible:border-transparent
           disabled:cursor-not-allowed disabled:opacity-50;
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 ease-out
           hover:bg-sidebar-hover hover:scale-[1.02] hover:shadow-sm
           focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring;
  }

  .glass-effect {
    @apply backdrop-blur-sm border-white/20 shadow-[var(--shadow-medium)];
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gradient-to-r from-slate-200 via-slate-50 to-slate-200 bg-[length:200%_100%];
    animation: shimmer 2s infinite;
  }

  /* Enhanced hover effects */
  .hover-lift {
    @apply transition-all duration-300 ease-out hover:translate-y-[-2px] hover:shadow-[var(--shadow-medium)];
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out hover:shadow-lg hover:shadow-blue-500/10;
  }
}

/* Enhanced animations */
@layer utilities {
  .animate-in {
    animation: animate-in var(--animation-normal) ease-out;
  }

  .animate-out {
    animation: animate-out var(--animation-normal) ease-out;
  }

  .fade-in {
    animation: fade-in var(--animation-normal) ease-out;
  }

  .slide-in-from-left {
    animation: slide-in-from-left var(--animation-normal) ease-out;
  }

  .slide-in-from-right {
    animation: slide-in-from-right var(--animation-normal) ease-out;
  }

  .slide-in-from-bottom {
    animation: slide-in-from-bottom var(--animation-normal) ease-out;
  }

  .scale-in {
    animation: scale-in var(--animation-fast) ease-out;
  }

  .bounce-in {
    animation: bounce-in var(--animation-slow) ease-out;
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes animate-in {
  0% { opacity: 0; transform: translateY(10px) scale(0.98); }
  100% { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes animate-out {
  0% { opacity: 1; transform: translateY(0) scale(1); }
  100% { opacity: 0; transform: translateY(-10px) scale(0.98); }
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slide-in-from-left {
  0% { opacity: 0; transform: translateX(-20px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slide-in-from-right {
  0% { opacity: 0; transform: translateX(20px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slide-in-from-bottom {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes scale-in {
  0% { opacity: 0; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes bounce-in {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

/* Sidebar utility classes */
.bg-sidebar-bg {
  @apply bg-slate-900;
}

.text-sidebar-text {
  @apply text-slate-300;
}

.bg-sidebar-hover {
  @apply bg-slate-800/60;
}

/* Custom dropdown styling */
.dropdown:hover .dropdown-menu {
  display: block;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Industrial-themed card hover */
.hover-lift {
  @apply transition-all duration-300 ease-out hover:scale-105 hover:shadow-[0_6px_20px_0_rgba(37,99,235,0.12)];
}

.animate-fade-in {
  animation: fade-in 0.7s cubic-bezier(0.16, 1, 0.3, 1) both;
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(22px) scale(0.96);
  }
  60% {
    opacity: 1;
    transform: translateY(-2px) scale(1.03);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.will-change-transform {
  will-change: transform, box-shadow, opacity;
}

/* Delay utility classes for staggered animations (0–8) */
.delay-0 { animation-delay: 0ms; }
.delay-75 { animation-delay: 75ms; }
.delay-150 { animation-delay: 150ms; }
.delay-225 { animation-delay: 225ms; }
.delay-300 { animation-delay: 300ms; }
.delay-375 { animation-delay: 375ms; }
.delay-450 { animation-delay: 450ms; }
.delay-525 { animation-delay: 525ms; }
.delay-600 { animation-delay: 600ms; }

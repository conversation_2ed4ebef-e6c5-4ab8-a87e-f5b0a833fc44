
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { LoginPanel } from "@/components/auth/LoginPanel";
import { MarketingPanel } from "@/components/auth/MarketingPanel";
import { EmailStep } from "@/components/auth/EmailStep";
import { LoginMethodsStep } from "@/components/auth/LoginMethodsStep";
import { OTPStep } from "@/components/auth/OTPStep";
import { useFormValidation, loginValidationSchema } from "@/hooks/useFormValidation";
import { toast } from "sonner";

export default function Login() {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [step, setStep] = useState<"email" | "methods" | "otp">("email");
  const [darkMode, setDarkMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useFormValidation({
    schema: loginValidationSchema,
    defaultValues: { email: "", password: "" },
    mode: "onChange"
  });

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate email
    const emailValidation = loginValidationSchema.shape.email.safeParse(email);
    if (!emailValidation.success) {
      toast.error(emailValidation.error.errors[0].message);
      return;
    }

    if (email) {
      setStep("methods");
    }
  };

  const handleOTPLogin = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setStep("otp");
    }, 1500);
  };

  const handleSSOLogin = (provider: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      toast.success(`Successfully signed in with ${provider}`);
      navigate("/");
    }, 2000);
  };

  const handleVerifySuccess = () => {
    toast.success("Login successful!");
    setTimeout(() => {
      navigate("/");
    }, 1000);
  };

  const isEnterpriseEmail = email.includes("@") && !email.includes("@gmail.com") && !email.includes("@yahoo.com") && !email.includes("@hotmail.com");

  return (
    <div className={`flex w-screen h-screen ${darkMode ? 'dark' : ''}`}>
      {/* Left Panel - Login - Exactly half width */}
      <div className="w-full lg:w-1/2 flex">
        <LoginPanel step={step} darkMode={darkMode} setDarkMode={setDarkMode}>
          {step === "email" && (
            <EmailStep 
              email={email} 
              setEmail={setEmail} 
              onSubmit={handleEmailSubmit} 
            />
          )}
          
          {step === "methods" && (
            <LoginMethodsStep
              email={email}
              isEnterpriseEmail={isEnterpriseEmail}
              isLoading={isLoading}
              onOTPLogin={handleOTPLogin}
              onSSOLogin={handleSSOLogin}
              onChangeEmail={() => setStep("email")}
            />
          )}

          {step === "otp" && (
            <OTPStep
              email={email}
              onVerifySuccess={handleVerifySuccess}
              onChangeEmail={() => setStep("email")}
            />
          )}
        </LoginPanel>
      </div>
      
      {/* Right Panel - Marketing - Exactly half width on desktop, hidden on mobile */}
      <div className="w-full lg:w-1/2 hidden lg:flex">
        <MarketingPanel />
      </div>
    </div>
  );
}

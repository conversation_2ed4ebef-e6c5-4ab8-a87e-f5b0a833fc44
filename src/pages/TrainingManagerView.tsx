
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Users, Mail, Download, Clock, AlertTriangle, CheckCircle, User } from "lucide-react";

interface EmployeeTrainingData {
  id: string;
  name: string;
  email: string;
  jobTitle: string;
  assignedTrainings: number;
  completedTrainings: number;
  completionPercentage: number;
  overdueCount: number;
  lastLogin: string;
  status: "Active" | "Inactive";
  recentTrainings: {
    title: string;
    status: "Completed" | "In Progress" | "Not Started" | "Overdue";
    dueDate: string;
  }[];
}

const TrainingManagerViewContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [selectedTeam, setSelectedTeam] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock data for team members
  const teamMembers: EmployeeTrainingData[] = [
    {
      id: "EMP001",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      jobTitle: "Quality Analyst",
      assignedTrainings: 5,
      completedTrainings: 4,
      completionPercentage: 80,
      overdueCount: 0,
      lastLogin: "2024-12-20",
      status: "Active",
      recentTrainings: [
        { title: "ISO 9001 Refresher", status: "Completed", dueDate: "2024-12-15" },
        { title: "Fire Safety Training", status: "In Progress", dueDate: "2024-12-25" }
      ]
    },
    {
      id: "EMP002",
      name: "Michael Chen",
      email: "<EMAIL>",
      jobTitle: "Lab Technician",
      assignedTrainings: 6,
      completedTrainings: 3,
      completionPercentage: 50,
      overdueCount: 2,
      lastLogin: "2024-12-18",
      status: "Active",
      recentTrainings: [
        { title: "Chemical Handling Safety", status: "Overdue", dueDate: "2024-12-10" },
        { title: "GMP Basics", status: "Not Started", dueDate: "2024-12-30" }
      ]
    },
    {
      id: "EMP003",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      jobTitle: "Production Supervisor",
      assignedTrainings: 4,
      completedTrainings: 4,
      completionPercentage: 100,
      overdueCount: 0,
      lastLogin: "2024-12-21",
      status: "Active",
      recentTrainings: [
        { title: "Lean Manufacturing", status: "Completed", dueDate: "2024-12-20" },
        { title: "Data Privacy & GDPR", status: "Completed", dueDate: "2024-12-15" }
      ]
    },
    {
      id: "EMP004",
      name: "David Wilson",
      email: "<EMAIL>",
      jobTitle: "Quality Inspector",
      assignedTrainings: 3,
      completedTrainings: 1,
      completionPercentage: 33,
      overdueCount: 1,
      lastLogin: "2024-12-15",
      status: "Active",
      recentTrainings: [
        { title: "ISO 9001 Refresher", status: "Overdue", dueDate: "2024-12-12" },
        { title: "Fire Safety Training", status: "Not Started", dueDate: "2024-12-28" }
      ]
    },
    {
      id: "EMP005",
      name: "Lisa Thompson",
      email: "<EMAIL>",
      jobTitle: "Process Engineer",
      assignedTrainings: 7,
      completedTrainings: 5,
      completionPercentage: 71,
      overdueCount: 0,
      lastLogin: "2024-12-19",
      status: "Active",
      recentTrainings: [
        { title: "Lean Manufacturing", status: "In Progress", dueDate: "2024-12-30" },
        { title: "Chemical Handling Safety", status: "Completed", dueDate: "2024-12-18" }
      ]
    },
    {
      id: "EMP006",
      name: "James Martinez",
      email: "<EMAIL>",
      jobTitle: "Lab Assistant",
      assignedTrainings: 4,
      completedTrainings: 2,
      completionPercentage: 50,
      overdueCount: 1,
      lastLogin: "2024-12-16",
      status: "Inactive",
      recentTrainings: [
        { title: "GMP Basics", status: "Overdue", dueDate: "2024-12-14" },
        { title: "Fire Safety Training", status: "In Progress", dueDate: "2024-12-26" }
      ]
    },
    {
      id: "EMP007",
      name: "Anna Garcia",
      email: "<EMAIL>",
      jobTitle: "Quality Coordinator",
      assignedTrainings: 5,
      completedTrainings: 5,
      completionPercentage: 100,
      overdueCount: 0,
      lastLogin: "2024-12-21",
      status: "Active",
      recentTrainings: [
        { title: "Data Privacy & GDPR", status: "Completed", dueDate: "2024-12-20" },
        { title: "ISO 9001 Refresher", status: "Completed", dueDate: "2024-12-18" }
      ]
    },
    {
      id: "EMP008",
      name: "Robert Kim",
      email: "<EMAIL>",
      jobTitle: "Senior Technician",
      assignedTrainings: 6,
      completedTrainings: 3,
      completionPercentage: 50,
      overdueCount: 2,
      lastLogin: "2024-12-17",
      status: "Active",
      recentTrainings: [
        { title: "Chemical Handling Safety", status: "Overdue", dueDate: "2024-12-11" },
        { title: "Lean Manufacturing", status: "Not Started", dueDate: "2024-12-29" }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Completed": { variant: "default" as const, className: "bg-green-100 text-green-800" },
      "In Progress": { variant: "default" as const, className: "bg-blue-100 text-blue-800" },
      "Not Started": { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" },
      "Overdue": { variant: "destructive" as const, className: "bg-red-100 text-red-800" }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Not Started"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  const handleSendReminder = (employeeId: string) => {
    console.log("Sending reminder to employee:", employeeId);
    // Implementation for sending reminder
  };

  const handleExportReport = () => {
    console.log("Exporting team training report");
    // Implementation for exporting report
  };

  const filteredMembers = teamMembers.filter(member => {
    if (statusFilter === "overdue" && member.overdueCount === 0) return false;
    if (statusFilter === "completed" && member.completionPercentage < 100) return false;
    if (statusFilter === "in-progress" && (member.completionPercentage === 0 || member.completionPercentage === 100)) return false;
    return true;
  });

  // Calculate team metrics
  const teamMetrics = {
    totalEmployees: teamMembers.length,
    avgCompletion: Math.round(teamMembers.reduce((sum, emp) => sum + emp.completionPercentage, 0) / teamMembers.length),
    totalOverdue: teamMembers.reduce((sum, emp) => sum + emp.overdueCount, 0),
    activeEmployees: teamMembers.filter(emp => emp.status === "Active").length
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Users className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Team Training Progress</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          {/* Team Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Employees</p>
                    <p className="text-2xl font-bold">{teamMetrics.totalEmployees}</p>
                  </div>
                  <User className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Completion</p>
                    <p className="text-2xl font-bold text-green-600">{teamMetrics.avgCompletion}%</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Overdue</p>
                    <p className="text-2xl font-bold text-red-600">{teamMetrics.totalOverdue}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Active Employees</p>
                    <p className="text-2xl font-bold text-blue-600">{teamMetrics.activeEmployees}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Actions */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                <div className="flex flex-col md:flex-row gap-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Team</label>
                    <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Team Members</SelectItem>
                        <SelectItem value="quality">Quality Team</SelectItem>
                        <SelectItem value="lab">Lab Team</SelectItem>
                        <SelectItem value="production">Production Team</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Status Filter</label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <Button onClick={handleExportReport} className="gap-2">
                  <Download className="h-4 w-4" />
                  Export Report
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Employee Training Table */}
          <Card>
            <CardHeader>
              <CardTitle>Team Training Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Assigned Trainings</TableHead>
                      <TableHead>Completion %</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Overdue Count</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{member.name}</div>
                            <div className="text-sm text-gray-500">{member.jobTitle}</div>
                            <div className="text-xs text-gray-400">{member.email}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-center">
                            <span className="font-medium">{member.completedTrainings}</span>
                            <span className="text-gray-500">/{member.assignedTrainings}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={`font-medium ${
                            member.completionPercentage >= 90 ? 'text-green-600' :
                            member.completionPercentage >= 70 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {member.completionPercentage}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="w-24">
                            <Progress value={member.completionPercentage} className="h-2" />
                          </div>
                        </TableCell>
                        <TableCell>
                          {member.overdueCount > 0 ? (
                            <Badge variant="destructive">{member.overdueCount}</Badge>
                          ) : (
                            <span className="text-green-600">0</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(member.lastLogin).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={member.status === "Active" ? "default" : "secondary"}>
                            {member.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleSendReminder(member.id)}
                              className="gap-1"
                              disabled={member.overdueCount === 0}
                            >
                              <Mail className="h-3 w-3" />
                              Remind
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const TrainingManagerView = () => {
  return <TrainingManagerViewContent />;
};

export default TrainingManagerView;

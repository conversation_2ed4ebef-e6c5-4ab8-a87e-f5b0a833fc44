
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { TrainingProvider } from "@/contexts/TrainingContext";
import { TrainingDashboard } from "@/components/people/training-dashboard/TrainingDashboard";

export default function TrainingLibrary() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <TrainingProvider>
      <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
        <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
          <Sidebar />
        </div>
        
        <div className="flex-1 flex flex-col overflow-hidden w-full">
          <TopBar>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">Training Library</h1>
            </div>
          </TopBar>
          
          <div className="flex-1 overflow-y-auto p-4 md:p-6">
            <div className="max-w-7xl mx-auto">
              <TrainingDashboard />
            </div>
          </div>
        </div>
      </div>
    </TrainingProvider>
  );
}

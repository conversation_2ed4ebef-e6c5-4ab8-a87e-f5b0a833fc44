
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WifiOff, RefreshCw, Home } from 'lucide-react';
import { SupportShareButton } from '@/components/error-handling/SupportShareButton';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';

const NetworkError: React.FC = () => {
  const navigate = useNavigate();
  const { networkStatus, retry } = useNetworkStatus();

  const handleRetry = () => {
    retry();
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <WifiOff className="h-16 w-16 mx-auto text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Network Error</h1>
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Connection Failed</h2>
          <p className="text-gray-600 mb-6">
            {!networkStatus.isOnline 
              ? "You appear to be offline. Please check your internet connection and try again."
              : "We're having trouble connecting to our servers. This might be a temporary issue."
            }
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={handleRetry}
              className="w-full gap-2"
              disabled={!networkStatus.isOnline}
            >
              <RefreshCw className="h-4 w-4" />
              Retry Connection ({networkStatus.retryCount})
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard')}
              className="w-full gap-2"
              disabled={!networkStatus.isOnline}
            >
              <Home className="h-4 w-4" />
              Go to Dashboard
            </Button>

            <SupportShareButton
              errorDetails={{
                url: window.location.href,
                timestamp: new Date().toISOString(),
                additionalContext: `Network error - Online: ${networkStatus.isOnline}, Slow: ${networkStatus.isSlowConnection}`
              }}
              title="Report Network Issue"
              variant="outline"
              size="sm"
            />
          </div>

          <div className="mt-6 text-xs text-gray-500">
            Connection Status: {networkStatus.isOnline ? 'Online' : 'Offline'}
            {networkStatus.lastDisconnected && (
              <div>Last disconnected: {networkStatus.lastDisconnected.toLocaleTimeString()}</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NetworkError;

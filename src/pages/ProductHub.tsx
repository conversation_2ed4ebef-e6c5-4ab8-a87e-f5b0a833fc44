
import React, { useState } from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Package } from "lucide-react";
import { ProductAnalyticsDashboard } from "@/components/product-hub/ProductAnalyticsDashboard";
import { ProductDetails } from "@/components/product-hub/ProductDetails";
import { ProductMaster } from "@/components/product-hub/ProductMaster";
import { ProductCreationForm } from "@/components/product-hub/ProductCreationForm";
import { RecipeBuilder } from "@/components/product-hub/RecipeBuilder";
import { CreateBOMModal } from "@/components/product-hub/CreateBOMModal";
import { SpecificationsManagement } from "@/components/product-hub/SpecificationsManagement";
import { QualityManagement } from "@/components/product-hub/QualityManagement";
import { BOMTraceabilityDashboard } from "@/components/product-hub/bom-traceability/BOMTraceabilityDashboard";

const ProductHub = () => {
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'dashboard' | 'products' | 'specifications' | 'quality' | 'bom-traceability' | 'details' | 'create' | 'recipe' | 'bom'>('dashboard');
  const [isBOMModalOpen, setIsBOMModalOpen] = useState(false);

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <Package className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Product Hub</h1>
        <p className="text-sm text-gray-600">Manage products, specifications, and analytics</p>
      </div>
    </div>
  );

  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
    setActiveView('details');
  };

  const handleBackToProducts = () => {
    setSelectedProduct(null);
    setActiveView('products');
  };

  const handleCreateProduct = () => {
    setActiveView('create');
  };

  const handleCreateRecipe = () => {
    setActiveView('recipe');
  };

  const handleCreateBOM = () => {
    setIsBOMModalOpen(true);
  };

  const handleSaveProduct = (productData: any) => {
    console.log('Saving product:', productData);
    setActiveView('products');
  };

  // Render specific views
  if (activeView === 'details' && selectedProduct) {
    return (
      <StandardPageLayout headerContent={headerContent}>
        <ProductDetails productId={selectedProduct} onBack={handleBackToProducts} />
      </StandardPageLayout>
    );
  }

  if (activeView === 'create') {
    return (
      <StandardPageLayout headerContent={headerContent}>
        <ProductCreationForm 
          onBack={handleBackToProducts} 
          onSave={handleSaveProduct}
          onCreateBOM={handleCreateBOM}
          onCreateRecipe={handleCreateRecipe}
        />
      </StandardPageLayout>
    );
  }

  if (activeView === 'recipe') {
    return (
      <StandardPageLayout headerContent={headerContent}>
        <RecipeBuilder onBack={() => setActiveView('create')} />
      </StandardPageLayout>
    );
  }

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <Tabs value={activeView} onValueChange={(value) => setActiveView(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard">Analytics</TabsTrigger>
            <TabsTrigger value="products">Products</TabsTrigger>
            <TabsTrigger value="specifications">Specifications</TabsTrigger>
            <TabsTrigger value="quality">Quality</TabsTrigger>
            <TabsTrigger value="bom-traceability">BOM Traceability</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-6">
            <ProductAnalyticsDashboard />
          </TabsContent>

          <TabsContent value="products" className="space-y-6">
            <ProductMaster 
              onSelectProduct={handleProductSelect}
              onCreateProduct={handleCreateProduct}
            />
          </TabsContent>

          <TabsContent value="specifications" className="space-y-6">
            <SpecificationsManagement />
          </TabsContent>

          <TabsContent value="quality" className="space-y-6">
            <QualityManagement />
          </TabsContent>

          <TabsContent value="bom-traceability" className="space-y-6">
            <BOMTraceabilityDashboard productId={selectedProduct || 'default-product'} />
          </TabsContent>
        </Tabs>

        <CreateBOMModal 
          open={isBOMModalOpen}
          onOpenChange={setIsBOMModalOpen}
        />
      </div>
    </StandardPageLayout>
  );
};

export default ProductHub;


import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ProfileForm } from '@/components/profile/ProfileForm';

const Profile = () => {
  const navigate = useNavigate();
  // Useful debug log to ensure this page mounts
  React.useEffect(() => {
    console.log('Profile page mounted');
  }, []);
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => navigate(-1)}
          className="mr-4"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl font-bold">Profile Settings</h1>
      </div>
      <ProfileForm />
    </div>
  );
};

export default Profile;

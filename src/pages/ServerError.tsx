
import React from 'react';
import { useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ServerCrash, Home, ArrowLeft } from 'lucide-react';
import { SupportShareButton } from '@/components/error-handling/SupportShareButton';

const ServerError: React.FC = () => {
  const location = useLocation();
  const errorCode = location.state?.statusCode || 500;
  const errorMessage = location.state?.message || 'Internal Server Error';

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <ServerCrash className="h-16 w-16 mx-auto text-red-500 mb-4" />
          <h1 className="text-4xl font-bold text-gray-800 mb-2">{errorCode}</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Server Error</h2>
          <p className="text-gray-600 mb-6">
            {errorMessage === 'Internal Server Error' 
              ? "We're experiencing technical difficulties. Our team has been notified and is working on a fix."
              : errorMessage
            }
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={() => window.location.reload()}
              className="w-full"
            >
              Try Again
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.location.href = '/'}
              className="w-full gap-2"
            >
              <Home className="h-4 w-4" />
              Go to Dashboard
            </Button>

            <SupportShareButton
              errorDetails={{
                statusCode: errorCode,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                additionalContext: `Server error ${errorCode}: ${errorMessage}`
              }}
              title="Report Issue"
              variant="outline"
              size="sm"
            />
          </div>

          <div className="mt-6 text-xs text-gray-500">
            Error Code: {errorCode} | Time: {new Date().toLocaleString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ServerError;

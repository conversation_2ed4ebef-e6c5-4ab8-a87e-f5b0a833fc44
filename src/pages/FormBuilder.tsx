
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, FormInput, HelpCircle } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { FormBuilderInterface } from "@/components/supplier-quality/form-builder/FormBuilderInterface";
import { FormBuilderTutorial } from "@/components/supplier-quality/form-builder/FormBuilderTutorial";

export default function FormBuilder() {
  console.log("FormBuilder page rendering...");
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const [showTutorial, setShowTutorial] = useState(false);

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <FormInput className="h-6 w-6 text-green-600" />
              <h1 className="text-2xl font-bold">Form Builder</h1>
            </div>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-hidden w-full min-w-0 relative">
          {/* Navigation and Tutorial buttons moved to main body */}
          <div className="absolute top-4 left-4 right-4 z-10 flex justify-between items-center">
            <Link to="/supplier-forms">
              <Button variant="ghost" size="sm" className="gap-2 bg-white shadow-sm border">
                <ChevronLeft className="h-4 w-4" />
                Back to Forms
              </Button>
            </Link>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTutorial(true)}
              className="gap-2 bg-white shadow-sm"
            >
              <HelpCircle className="h-4 w-4" />
              Tutorial
            </Button>
          </div>
          
          <FormBuilderInterface />
        </div>
      </div>

      <FormBuilderTutorial open={showTutorial} onOpenChange={setShowTutorial} />
    </div>
  );
}

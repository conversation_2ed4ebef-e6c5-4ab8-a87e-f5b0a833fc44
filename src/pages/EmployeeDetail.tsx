
import React from "react";
import { useParams } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { User, Mail, Phone, MapPin, Calendar, Briefcase } from "lucide-react";
import { useAccount } from "@/contexts/AccountContext";
import { TrainingTab } from "@/components/people/TrainingTab";
import { EmployeeDocuments } from "@/components/people/EmployeeDocuments";

const EmployeeDetail = () => {
  const { employeeId } = useParams();
  const { currentAccount } = useAccount();
  
  const employee = currentAccount.people.find(p => p.id === employeeId);

  if (!employee) {
    return (
      <StandardPageLayout title="Employee Not Found">
        <div className="text-center py-8">
          <p>Employee not found.</p>
        </div>
      </StandardPageLayout>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      case "On Leave":
        return <Badge className="bg-yellow-100 text-yellow-800">On Leave</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <User className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">{employee.name}</h1>
        <p className="text-sm text-gray-600">{employee.jobTitle}</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6 space-y-6">
        {/* Employee Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Employee Overview</span>
              {getStatusBadge(employee.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Employee ID</p>
                    <p className="font-medium">{employee.id}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium">{employee.email || "Not provided"}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Briefcase className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Department</p>
                    <p className="font-medium">{employee.department || "Not assigned"}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-2">Assigned Processes</p>
                  <div className="flex flex-wrap gap-2">
                    {employee.processes.map((process) => (
                      <Badge key={process} variant="outline">
                        {process}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for different sections */}
        <Tabs defaultValue="training" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="training">Training</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="training" className="space-y-4">
            <TrainingTab employeeId={employee.id} employeeName={employee.name} />
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <EmployeeDocuments employeeId={employee.id} employeeName={employee.name} />
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Performance metrics coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </StandardPageLayout>
  );
};

export default EmployeeDetail;

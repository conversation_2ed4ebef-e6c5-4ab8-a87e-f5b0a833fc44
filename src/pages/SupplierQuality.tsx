
import React, { useState } from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp } from "lucide-react";
import { TabContent } from "@/components/supplier-quality/tab-components/TabContent";

const SupplierQuality = () => {
  const [activeTab, setActiveTab] = useState("management");

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <TrendingUp className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Supplier Quality</h1>
        <p className="text-sm text-gray-600">Comprehensive supplier quality management system</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 lg:grid-cols-9">
            <TabsTrigger value="management">Management</TabsTrigger>
            <TabsTrigger value="audits">Audits</TabsTrigger>
            <TabsTrigger value="traceability">Traceability</TabsTrigger>
            <TabsTrigger value="matrix">Matrix</TabsTrigger>
            <TabsTrigger value="forms">Forms</TabsTrigger>
            <TabsTrigger value="esourcing">E-Sourcing</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="feedback">Feedback</TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabContent activeTab={activeTab} />
          </div>
        </Tabs>
      </div>
    </StandardPageLayout>
  );
};

export default SupplierQuality;

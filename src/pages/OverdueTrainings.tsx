
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { OverdueTrainingsHeader } from "@/components/training-admin/OverdueTrainingsHeader";
import { OverdueTrainingsFilters } from "@/components/training-admin/OverdueTrainingsFilters";
import { OverdueTrainingsTable } from "@/components/training-admin/OverdueTrainingsTable";
import { ReminderWorkflowModal } from "@/components/training-admin/ReminderWorkflowModal";
import { mockOverdueTrainings } from "@/data/mockOverdueTrainings";
import { OverdueTraining, OverdueFilters } from "@/types/overdueTraining";

const OverdueTrainings = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [selectedTrainings, setSelectedTrainings] = useState<string[]>([]);
  const [filters, setFilters] = useState<OverdueFilters>({
    department: "all",
    role: "all",
    overdueDays: "all"
  });
  const [showReminderModal, setShowReminderModal] = useState(false);

  const filteredTrainings = mockOverdueTrainings.filter((training) => {
    if (filters.department !== "all" && training.department !== filters.department) return false;
    if (filters.role !== "all" && !training.assignedRoles.includes(filters.role)) return false;
    
    if (filters.overdueDays !== "all") {
      const daysOverdue = training.daysOverdue;
      switch (filters.overdueDays) {
        case "1-7":
          return daysOverdue >= 1 && daysOverdue <= 7;
        case "8-30":
          return daysOverdue >= 8 && daysOverdue <= 30;
        case "30+":
          return daysOverdue > 30;
        default:
          return true;
      }
    }
    
    return true;
  });

  const handleSelectTraining = (trainingId: string) => {
    setSelectedTrainings(prev => 
      prev.includes(trainingId) 
        ? prev.filter(id => id !== trainingId)
        : [...prev, trainingId]
    );
  };

  const handleSelectAll = () => {
    setSelectedTrainings(
      selectedTrainings.length === filteredTrainings.length 
        ? [] 
        : filteredTrainings.map(t => t.id)
    );
  };

  const handleBulkAction = (action: "remind" | "escalate" | "extend") => {
    if (action === "remind") {
      setShowReminderModal(true);
    } else {
      console.log(`Bulk action: ${action} for trainings:`, selectedTrainings);
      // Implementation would go here
    }
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <OverdueTrainingsHeader />
        </TopBar>
        
        <div className="flex-1 overflow-auto bg-gray-50">
          <div className="max-w-[1600px] mx-auto p-6 space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <OverdueTrainingsFilters 
                filters={filters}
                onFiltersChange={setFilters}
                selectedCount={selectedTrainings.length}
                onBulkAction={handleBulkAction}
              />
              
              <OverdueTrainingsTable
                trainings={filteredTrainings}
                selectedTrainings={selectedTrainings}
                onSelectTraining={handleSelectTraining}
                onSelectAll={handleSelectAll}
              />
            </div>
          </div>
        </div>
      </div>

      <ReminderWorkflowModal
        open={showReminderModal}
        onOpenChange={setShowReminderModal}
        selectedTrainings={selectedTrainings.map(id => 
          filteredTrainings.find(t => t.id === id)!
        ).filter(Boolean)}
      />
    </div>
  );
};

export default OverdueTrainings;

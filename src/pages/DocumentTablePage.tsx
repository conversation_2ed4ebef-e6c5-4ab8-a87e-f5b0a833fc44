import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import DocumentTable from "@/components/DocumentTable";
import { FileText } from "lucide-react";

const DocumentTablePage = () => {
  const headerContent = (
    <div className="flex items-center gap-3">
      <FileText className="h-6 w-6 text-blue-600" />
      <div>
        <h1 className="text-2xl font-bold">Document Table</h1>
        <p className="text-gray-500 text-sm">
          Self-contained document table component
        </p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <DocumentTable
          title="Document Management Table"
          subtitle="View and manage all documents in a comprehensive table format"
          showSearch={true}
          showFilters={true}
        />
      </div>
    </StandardPageLayout>
  );
};

export default DocumentTablePage;

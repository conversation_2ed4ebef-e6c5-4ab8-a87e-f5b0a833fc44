
import React from "react";
import { CheckCircle } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";

export default function AuditTemplates() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <CheckCircle className="h-6 w-6 text-indigo-600" />
            <h1 className="text-2xl font-bold">Audit Templates</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full p-6">
          <div className="max-w-6xl mx-auto">
            <p className="text-gray-600">Audit templates functionality will be implemented here.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

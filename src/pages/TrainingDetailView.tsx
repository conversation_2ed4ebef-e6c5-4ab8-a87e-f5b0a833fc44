
import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Play, Pause, CheckCircle } from "lucide-react";
import { useTraining } from "@/contexts/TrainingContext";
import { TrainingHeader } from "@/components/training/training-detail/TrainingHeader";
import { TrainingModules } from "@/components/training/training-detail/TrainingModules";
import { TrainingSidePanel } from "@/components/training/training-detail/TrainingSidePanel";
import { formatDate } from "@/utils/dateUtils";

const TrainingDetailView = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get('mode') || 'start';
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const { employeeTrainings, startTraining, updateProgress, completeTraining } = useTraining();

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(0);
  const [timeSpent, setTimeSpent] = useState(0);

  const training = employeeTrainings.find(t => t.id === id);

  useEffect(() => {
    if (training) {
      setCurrentProgress(training.progress);
      // Parse the timeSpent string to extract the numeric value
      const timeSpentValue = parseInt(training.timeSpent.toString().split(' ')[0]) || 0;
      setTimeSpent(timeSpentValue);
    }
  }, [training]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPlaying && training) {
      interval = setInterval(() => {
        setTimeSpent(prev => {
          const newTime = prev + 1;
          const newProgress = Math.min(currentProgress + 1, 100);
          
          updateProgress(training.id, newProgress, `${newTime} minutes`);
          setCurrentProgress(newProgress);
          
          if (newProgress >= 100) {
            setIsPlaying(false);
            completeTraining(training.id, 95);
          }
          
          return newTime;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPlaying, training, currentProgress, updateProgress, completeTraining]);

  if (!training) {
    return (
      <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
        <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
          <Sidebar />
        </div>
        <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
          <TopBar>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">Training Not Found</h1>
            </div>
          </TopBar>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">Training Not Found</h2>
              <p className="text-gray-600 mb-4">The requested training could not be found.</p>
              <Button onClick={() => navigate('/people')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to People
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleStartStop = () => {
    if (!isPlaying) {
      startTraining(training.id);
    }
    setIsPlaying(!isPlaying);
  };

  const mockTrainingData = {
    ...training.training,
    objectives: [
      "Understand the key principles of quality management",
      "Learn to implement ISO 9001:2015 standards",
      "Apply quality control processes in your work",
      "Identify and resolve quality issues"
    ],
    instructor: {
      name: "Dr. Sarah Johnson",
      title: "Quality Management Expert",
      experience: "15+ years in quality systems"
    },
    history: [
      { action: "Training assigned", date: formatDate(training.assignedDate), time: "09:00 AM" },
      { action: "Training started", date: formatDate(training.lastAccessed || training.assignedDate), time: "10:30 AM" }
    ],
    modules: [
      { name: "Introduction to Quality Management", duration: "15 min", completed: currentProgress > 25 },
      { name: "ISO 9001:2015 Overview", duration: "20 min", completed: currentProgress > 50 },
      { name: "Implementation Strategies", duration: "25 min", completed: currentProgress > 75 },
      { name: "Assessment and Review", duration: "10 min", completed: currentProgress >= 100 }
    ]
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">Training Detail</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full min-w-0">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Back to People Button */}
            <div className="mb-4">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => navigate('/people')}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to People
              </Button>
            </div>

            {/* Training Header */}
            <TrainingHeader 
              training={mockTrainingData}
              currentProgress={currentProgress}
              completedModules={mockTrainingData.modules.filter(m => m.completed).length}
              totalModules={mockTrainingData.modules.length}
            />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Training Player */}
                <div className="bg-white rounded-lg border p-6">
                  <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center mb-4">
                    <div className="text-center text-white">
                      <div className="text-6xl mb-4">📹</div>
                      <h3 className="text-xl font-semibold mb-2">{training.training.title}</h3>
                      <p className="text-gray-300">Time spent: {timeSpent} minutes</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-center gap-4">
                    <Button 
                      onClick={handleStartStop}
                      className="gap-2"
                      disabled={currentProgress >= 100}
                    >
                      {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      {isPlaying ? 'Pause' : currentProgress >= 100 ? 'Completed' : 'Start'}
                    </Button>
                    
                    {currentProgress >= 100 && (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-5 w-5" />
                        <span className="font-medium">Training Completed!</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Training Modules */}
                <TrainingModules modules={mockTrainingData.modules} />
              </div>

              {/* Side Panel */}
              <div className="lg:col-span-1">
                <TrainingSidePanel training={mockTrainingData} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainingDetailView;

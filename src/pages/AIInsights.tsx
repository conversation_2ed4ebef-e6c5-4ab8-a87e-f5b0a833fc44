
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Info } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { InsightsCenter } from "@/components/supplier-quality/insights/InsightsCenter";
import { SidebarProvider } from "@/components/ui/sidebar";

export default function AIInsights() {
  return (
    <SidebarProvider>
      <div className="flex h-screen overflow-hidden bg-[#f5f6f8]">
        <Sidebar />
        
        <div className="flex-1 overflow-auto w-full min-w-0 bg-[#f5f6f8]">
          <div className="w-full min-w-0 px-4 lg:px-6 py-6 lg:py-8 max-w-7xl mx-auto">
            <div className="mb-6 lg:mb-8">
              <Link to="/supplier-quality">
                <Button variant="ghost" size="sm" className="mb-4 text-slate-600 hover:text-slate-900">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to Supplier Quality
                </Button>
              </Link>
              
              <div className="flex items-center gap-4 mb-2">
                <div className="p-3 bg-blue-100 rounded-xl">
                  <Info className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-semibold text-slate-900">AI Insights</h1>
                  <p className="text-slate-600 text-lg mt-1">
                    AI-powered insights and recommendations for supplier management
                  </p>
                </div>
              </div>
            </div>
            
            <InsightsCenter />
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}

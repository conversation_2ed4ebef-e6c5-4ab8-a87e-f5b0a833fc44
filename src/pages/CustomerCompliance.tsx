
import React, { useState } from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { WorkspaceDescription } from "@/components/customer-compliance/workspace/WorkspaceDescription";
import { WorkspaceHeader } from "@/components/customer-compliance/workspace/WorkspaceHeader";
import { WorkspaceCardView } from "@/components/customer-compliance/workspace/WorkspaceCardView";
import { CreateWorkspaceModal } from "@/components/customer-compliance/workspace-creation/CreateWorkspaceModal";
import { mockCustomers } from "@/data/mockCustomers";
import { Badge } from "@/components/ui/badge";
import { Users } from "lucide-react";

const CustomerCompliance: React.FC = () => {
  const [isCreateWorkspaceOpen, setIsCreateWorkspaceOpen] = useState(false);

  const handleCreateWorkspace = () => {
    setIsCreateWorkspaceOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      'Active': 'bg-green-500 text-white',
      'Read-only': 'bg-yellow-500 text-white',
      'Access Expired': 'bg-red-500 text-white'
    };
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-500 text-white'}>
        {status}
      </Badge>
    );
  };

  const getAuditStatusBadge = (auditStatus: string) => {
    const statusColors = {
      'Passed': 'bg-green-500 text-white',
      'Pending': 'bg-yellow-500 text-white',
      'Failed': 'bg-red-500 text-white',
      'Scheduled': 'bg-blue-500 text-white'
    };
    return (
      <Badge className={statusColors[auditStatus as keyof typeof statusColors] || 'bg-gray-500 text-white'}>
        {auditStatus}
      </Badge>
    );
  };

  const headerContent = (
    <div className="flex items-center gap-3">
      <Users className="h-6 w-6 text-blue-600" />
      <div>
        <h1 className="text-2xl font-bold">Customer Compliance</h1>
        <p className="text-gray-500 text-sm">Manage customer workspaces and compliance</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <div className="w-full space-y-6">
          <WorkspaceHeader onCreateWorkspace={handleCreateWorkspace} />
          <WorkspaceDescription />
          <WorkspaceCardView 
            customers={mockCustomers}
            formatDate={formatDate}
            getStatusBadge={getStatusBadge}
            getAuditStatusBadge={getAuditStatusBadge}
          />
        </div>
      </div>

      <CreateWorkspaceModal 
        isOpen={isCreateWorkspaceOpen}
        onClose={() => setIsCreateWorkspaceOpen(false)}
      />
    </StandardPageLayout>
  );
};

export default CustomerCompliance;

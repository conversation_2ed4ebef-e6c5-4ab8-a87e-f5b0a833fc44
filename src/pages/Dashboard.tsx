
import React, { useState } from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { BarChart3, Brain, Zap, Shield } from "lucide-react";
import { PredictiveAnalyticsDashboard } from "@/components/analytics/PredictiveAnalyticsDashboard";
import { WorkflowAutomationEngine } from "@/components/workflow/WorkflowAutomationEngine";
import { RegulatoryComplianceTracker } from "@/components/compliance/RegulatoryComplianceTracker";

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <BarChart3 className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Quality Management Dashboard</h1>
        <p className="text-sm text-gray-600">Comprehensive overview of your quality, compliance, and governance metrics</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              AI Analytics
            </TabsTrigger>
            <TabsTrigger value="workflows" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Workflows
            </TabsTrigger>
            <TabsTrigger value="compliance" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Compliance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-blue-900 mb-2">Quality Score</h3>
                    <p className="text-3xl font-bold text-blue-800">92%</p>
                    <p className="text-sm text-blue-600 mt-1">↑ 3% from last month</p>
                  </div>
                  <BarChart3 className="h-12 w-12 text-blue-600" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-green-900 mb-2">Active Workflows</h3>
                    <p className="text-3xl font-bold text-green-800">24</p>
                    <p className="text-sm text-green-600 mt-1">12 completed today</p>
                  </div>
                  <Zap className="h-12 w-12 text-green-600" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-purple-900 mb-2">Compliance Rate</h3>
                    <p className="text-3xl font-bold text-purple-800">89%</p>
                    <p className="text-sm text-purple-600 mt-1">7 actions pending</p>
                  </div>
                  <Shield className="h-12 w-12 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">Select a tab above to access detailed functionality:</p>
              <div className="flex flex-wrap justify-center gap-4">
                <button 
                  onClick={() => setActiveTab("analytics")}
                  className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                >
                  View AI Analytics
                </button>
                <button 
                  onClick={() => setActiveTab("workflows")}
                  className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                >
                  Manage Workflows
                </button>
                <button 
                  onClick={() => setActiveTab("compliance")}
                  className="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors"
                >
                  Check Compliance
                </button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <PredictiveAnalyticsDashboard />
          </TabsContent>

          <TabsContent value="workflows">
            <WorkflowAutomationEngine />
          </TabsContent>

          <TabsContent value="compliance">
            <RegulatoryComplianceTracker />
          </TabsContent>
        </Tabs>
      </div>
    </StandardPageLayout>
  );
};

export default Dashboard;

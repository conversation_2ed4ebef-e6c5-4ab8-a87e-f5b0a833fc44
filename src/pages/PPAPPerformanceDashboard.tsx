
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, BarChart3, TrendingUp, CheckCircle, Clock } from "lucide-react";
import { Link } from "react-router-dom";

export default function PPAPPerformanceDashboard() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <BarChart3 className="h-6 w-6 text-green-600" />
            <h1 className="text-2xl font-bold">PPAP Performance Dashboard</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="mb-4">
              <Link to="/ppap?tab=reports">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to PPAP Reports
                </Button>
              </Link>
            </div>

            {/* Performance KPIs */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Approval Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">94.2%</div>
                  <p className="text-sm text-gray-600">↑ 2.1% from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-blue-500" />
                    Avg Review Time
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">5.2</div>
                  <p className="text-sm text-gray-600">days (↓ 0.8 days)</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-purple-500" />
                    First Pass Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">87%</div>
                  <p className="text-sm text-gray-600">↑ 3% improvement</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-orange-500" />
                    Active PPAPs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-orange-600">127</div>
                  <p className="text-sm text-gray-600">8 pending review</p>
                </CardContent>
              </Card>
            </div>

            {/* Supplier Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Suppliers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">ABC Manufacturing</span>
                      <p className="text-sm text-gray-600">15 PPAPs submitted</p>
                    </div>
                    <div className="text-right">
                      <div className="text-green-600 font-bold">98%</div>
                      <p className="text-sm text-gray-600">Success rate</p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">XYZ Components</span>
                      <p className="text-sm text-gray-600">12 PPAPs submitted</p>
                    </div>
                    <div className="text-right">
                      <div className="text-green-600 font-bold">96%</div>
                      <p className="text-sm text-gray-600">Success rate</p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">Precision Parts Inc</span>
                      <p className="text-sm text-gray-600">9 PPAPs submitted</p>
                    </div>
                    <div className="text-right">
                      <div className="text-yellow-600 font-bold">89%</div>
                      <p className="text-sm text-gray-600">Success rate</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Monthly Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Monthly Performance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-6 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold">Jan</div>
                    <div className="text-green-600">92%</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Feb</div>
                    <div className="text-green-600">89%</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Mar</div>
                    <div className="text-green-600">91%</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Apr</div>
                    <div className="text-green-600">93%</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">May</div>
                    <div className="text-green-600">94%</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-blue-600">Jun</div>
                    <div className="text-green-600 font-bold">96%</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

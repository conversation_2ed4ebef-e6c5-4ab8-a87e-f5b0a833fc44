
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { TrainingAdminHeader } from "@/components/training-admin/TrainingAdminHeader";
import { TrainingAdminMetrics } from "@/components/training-admin/TrainingAdminMetrics";
import { TrainingAdminFilters } from "@/components/training-admin/TrainingAdminFilters";
import { TrainingAdminTable } from "@/components/training-admin/TrainingAdminTable";
import { TrainingActivityTable } from "@/components/training-admin/TrainingActivityTable";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { mockTrainingAssignments } from "@/data/mockTrainingAdminData";
import { TrainingAssignment, TrainingFilters } from "@/types/trainingAdmin";
import { ClipboardList, Activity } from "lucide-react";

const TrainingAdmin = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [selectedTrainings, setSelectedTrainings] = useState<string[]>([]);
  const [filters, setFilters] = useState<TrainingFilters>({
    role: "all",
    department: "all",
    status: "all",
    dueDateRange: "all"
  });

  const filteredTrainings = mockTrainingAssignments.filter((training) => {
    if (filters.role !== "all" && !training.assignedRoles.includes(filters.role)) return false;
    if (filters.department !== "all" && training.department !== filters.department) return false;
    if (filters.status !== "all" && training.status !== filters.status) return false;
    
    // Due date filtering logic would go here
    return true;
  });

  const handleSelectTraining = (trainingId: string) => {
    setSelectedTrainings(prev => 
      prev.includes(trainingId) 
        ? prev.filter(id => id !== trainingId)
        : [...prev, trainingId]
    );
  };

  const handleSelectAll = () => {
    setSelectedTrainings(
      selectedTrainings.length === filteredTrainings.length 
        ? [] 
        : filteredTrainings.map(t => t.id)
    );
  };

  const handleBulkAction = (action: "remind" | "reassign" | "complete") => {
    console.log(`Bulk action: ${action} for trainings:`, selectedTrainings);
    // Implementation would go here
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40 flex-shrink-0`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col w-full min-w-0">
        <TopBar>
          <TrainingAdminHeader />
        </TopBar>
        
        <main className="flex-1 w-full min-w-0 overflow-y-auto p-4 md:p-6">
          <div className="w-full space-y-6">
            <div className="w-full overflow-x-auto">
              <TrainingAdminMetrics trainings={mockTrainingAssignments} />
            </div>
            
            <Tabs defaultValue="assignments" className="w-full">
              <div className="overflow-x-auto w-full mb-6">
                <TabsList className="grid grid-cols-2 w-max min-w-full md:min-w-0 md:max-w-md">
                  <TabsTrigger value="assignments" className="flex items-center gap-2">
                    <ClipboardList className="h-4 w-4" />
                    Training Assignments
                  </TabsTrigger>
                  <TabsTrigger value="activity" className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Training Activity
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="assignments" className="mt-0 w-full">
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm w-full">
                  <TrainingAdminFilters 
                    filters={filters}
                    onFiltersChange={setFilters}
                    selectedCount={selectedTrainings.length}
                    onBulkAction={handleBulkAction}
                  />
                  
                  <div className="w-full overflow-x-auto">
                    <TrainingAdminTable
                      trainings={filteredTrainings}
                      selectedTrainings={selectedTrainings}
                      onSelectTraining={handleSelectTraining}
                      onSelectAll={handleSelectAll}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="activity" className="mt-0 w-full">
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm w-full">
                  <div className="w-full overflow-x-auto">
                    <TrainingActivityTable />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
};

export default TrainingAdmin;

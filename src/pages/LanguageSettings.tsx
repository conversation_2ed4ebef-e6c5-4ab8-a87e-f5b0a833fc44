
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Globe, Languages, Filter, CheckCircle } from "lucide-react";

const LanguageSettingsContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [uiLanguage, setUiLanguage] = useState("en");
  const [autoFilter, setAutoFilter] = useState(true);
  const [contentLanguages, setContentLanguages] = useState(["en", "es"]);

  const languages = [
    { code: "en", name: "English", flag: "🇺🇸" },
    { code: "es", name: "Spanish", flag: "🇪🇸" },
    { code: "de", name: "German", flag: "🇩🇪" }
  ];

  const handleSaveSettings = () => {
    console.log("Saving language settings:", {
      uiLanguage,
      autoFilter,
      contentLanguages
    });
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Globe className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Language Settings</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* UI Language Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Languages className="h-5 w-5 text-blue-600" />
                  Interface Language
                </CardTitle>
                <p className="text-sm text-gray-600">Choose your preferred language for the user interface</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="ui-language">UI Language</Label>
                  <Select value={uiLanguage} onValueChange={setUiLanguage}>
                    <SelectTrigger className="w-full md:w-64">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          <div className="flex items-center gap-2">
                            <span>{lang.flag}</span>
                            <span>{lang.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label>Auto-filter training content</Label>
                    <p className="text-sm text-gray-600">Show only trainings available in your preferred language</p>
                  </div>
                  <Switch checked={autoFilter} onCheckedChange={setAutoFilter} />
                </div>
              </CardContent>
            </Card>

            {/* Content Language Preferences */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5 text-blue-600" />
                  Content Language Preferences
                </CardTitle>
                <p className="text-sm text-gray-600">Select languages you're comfortable learning in</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {languages.map((lang) => (
                    <div key={lang.code} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <input
                        type="checkbox"
                        id={`lang-${lang.code}`}
                        checked={contentLanguages.includes(lang.code)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setContentLanguages([...contentLanguages, lang.code]);
                          } else {
                            setContentLanguages(contentLanguages.filter(l => l !== lang.code));
                          }
                        }}
                        className="rounded"
                      />
                      <label htmlFor={`lang-${lang.code}`} className="flex items-center gap-2 cursor-pointer">
                        <span className="text-lg">{lang.flag}</span>
                        <span className="font-medium">{lang.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Training Content Language Status */}
            <Card>
              <CardHeader>
                <CardTitle>Training Content by Language</CardTitle>
                <p className="text-sm text-gray-600">Overview of available training content in different languages</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {languages.map((lang) => (
                    <div key={lang.code} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{lang.flag}</span>
                        <span className="font-medium">{lang.name}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-600">
                          {lang.code === "en" ? "1,247" : lang.code === "es" ? "892" : "645"} trainings available
                        </span>
                        {contentLanguages.includes(lang.code) && (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Separator />

            {/* Save Button */}
            <div className="flex justify-end">
              <Button onClick={handleSaveSettings} className="gap-2">
                <CheckCircle className="h-4 w-4" />
                Save Language Settings
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const LanguageSettings = () => {
  return <LanguageSettingsContent />;
};

export default LanguageSettings;


import React from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Users } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";

export default function CustomerWorkspace() {
  const { id } = useParams();
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/customer-compliance">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Customer Compliance
              </Button>
            </Link>
            <Users className="h-6 w-6 text-purple-600" />
            <h1 className="text-2xl font-bold">Customer Workspace</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full p-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-xl font-semibold mb-4">Customer ID: {id}</h2>
            <p className="text-gray-600">Customer workspace functionality will be implemented here.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

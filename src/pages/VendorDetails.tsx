
import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building, ArrowLeft } from "lucide-react";
import { VendorInfoCards } from "@/components/vendors/VendorInfoCards";
import { VendorDetailsTabs } from "@/components/vendors/VendorDetailsTabs";

// Mock vendor data (in real app, fetch by ID)
const mockVendorDetails = {
  "v001": {
    id: "v001",
    name: "TechSupply Corp",
    category: "Electronics",
    status: "Active",
    rating: 4.8,
    contact: "<EMAIL>",
    phone: "******-0123",
    location: "San Jose, CA",
    contracts: 12,
    lastOrder: "2024-01-15",
    description: "Leading supplier of electronic components and testing equipment.",
    paymentTerms: "Net 30",
    deliveryTerms: "FOB Origin"
  }
};

const VendorDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const vendor = mockVendorDetails[id as keyof typeof mockVendorDetails];

  if (!vendor) {
    return (
      <StandardPageLayout>
        <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
          <div className="text-center py-8">
            <h2 className="text-xl font-semibold">Vendor Not Found</h2>
            <Button onClick={() => navigate('/vendors')} className="mt-4">
              Back to Vendors
            </Button>
          </div>
        </div>
      </StandardPageLayout>
    );
  }

  const getStatusBadge = (status: string) => {
    const variant = status === "Active" ? "default" : status === "Pending" ? "secondary" : "destructive";
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <StandardPageLayout>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate('/vendors')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Vendors
          </Button>
          <div className="flex items-center gap-3">
            <Building className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold">{vendor.name}</h1>
              <p className="text-gray-600">{vendor.category} Supplier</p>
            </div>
          </div>
          <div className="ml-auto">
            {getStatusBadge(vendor.status)}
          </div>
        </div>

        {/* Vendor Info Cards */}
        <VendorInfoCards vendor={vendor} />

        {/* Main Content */}
        <VendorDetailsTabs vendor={vendor} />
      </div>
    </StandardPageLayout>
  );
};

export default VendorDetails;

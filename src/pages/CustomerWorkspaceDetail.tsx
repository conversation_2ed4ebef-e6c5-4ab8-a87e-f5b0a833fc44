
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { CustomerWorkspaceLayout } from "@/components/customer-compliance/workspace-detail/CustomerWorkspaceLayout";
import { mockCustomers } from "@/data/mockCustomers";

// Mock data for the workspace detail
const mockRecentDocuments = [
  {
    id: "doc-1",
    name: "Quality Manual v2.1",
    type: "Quality Manual",
    uploadDate: "2024-01-15",
    status: "Active",
    version: "2.1"
  },
  {
    id: "doc-2",
    name: "AS9100 Certificate",
    type: "Certificate",
    uploadDate: "2024-01-10",
    status: "Active",
    version: "1.0"
  }
];

const mockAuditHistory = [
  {
    id: "audit-1",
    date: "2024-01-15",
    type: "Internal Audit",
    status: "Passed",
    auditor: "<PERSON>",
    findings: 2
  },
  {
    id: "audit-2",
    date: "2023-12-10",
    type: "Customer Audit",
    status: "Passed",
    auditor: "Boeing Team",
    findings: 0
  }
];

const mockCapas = [
  {
    id: "capa-1",
    title: "Calibration Records Issue",
    status: "Open",
    dueDate: "2024-02-15",
    priority: "High",
    assignee: "<PERSON> Doe"
  },
  {
    id: "capa-2",
    title: "Document Control Process",
    status: "Closed",
    dueDate: "2024-01-30",
    priority: "Medium",
    assignee: "Sarah <PERSON>"
  }
];

const mockOtifData = [
  { month: 'Jan', onTime: 95, quality: 98 },
  { month: 'Feb', onTime: 92, quality: 97 },
  { month: 'Mar', onTime: 96, quality: 99 },
  { month: 'Apr', onTime: 94, quality: 98 },
  { month: 'May', onTime: 97, quality: 99 },
  { month: 'Jun', onTime: 93, quality: 97 }
];

export default function CustomerWorkspaceDetail() {
  const { id } = useParams<{ id: string }>();
  const [activeTab, setActiveTab] = useState("overview");
  const [isManageAccessOpen, setIsManageAccessOpen] = useState(false);

  // Find the customer by ID
  const customer = mockCustomers.find(c => c.id === id) || {
    id: id || "unknown",
    name: "Unknown Customer",
    logoUrl: "/placeholder.svg",
    status: "Active" as const,
    lastAccessed: "2024-01-20",
    sharedDocuments: 0,
    complianceScore: 0,
    qaContact: "Unknown Contact",
    industry: "Unknown" as const,
    auditStatus: "Pending" as const
  };

  const headerContent = (
    <div className="flex items-center gap-3">
      <h1 className="text-2xl font-bold">{customer.name} Workspace</h1>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <CustomerWorkspaceLayout
          customerName={customer.name}
          customer={customer}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isManageAccessOpen={isManageAccessOpen}
          setIsManageAccessOpen={setIsManageAccessOpen}
          recentDocuments={mockRecentDocuments}
          auditHistory={mockAuditHistory}
          sharedDocuments={mockRecentDocuments}
          capas={mockCapas}
          otifData={mockOtifData}
        />
      </div>
    </StandardPageLayout>
  );
}

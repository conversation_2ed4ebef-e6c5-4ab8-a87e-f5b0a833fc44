
import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Home, Search } from "lucide-react";
import { SupportShareButton } from "@/components/error-handling/SupportShareButton";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            Sorry, the page you're looking for doesn't exist or has been moved.
          </p>
          
          <div className="space-y-3">
            <Link to="/">
              <Button className="w-full gap-2">
                <Home className="h-4 w-4" />
                Go to Dashboard
              </Button>
            </Link>
            
            <Link to="/people">
              <Button variant="outline" className="w-full gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to People
              </Button>
            </Link>

            <SupportShareButton
              errorDetails={{
                statusCode: 404,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                additionalContext: `User tried to access non-existent route: ${location.pathname}`
              }}
              title="Report Missing Page"
              variant="outline"
              size="sm"
            />
          </div>

          <div className="mt-6 text-xs text-gray-500">
            Route: {location.pathname}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotFound;

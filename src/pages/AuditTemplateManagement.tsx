
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, File, Plus } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { TemplateList } from "@/components/audit-templates/TemplateList";
import { CreateTemplateDialog } from "@/components/audit-templates/CreateTemplateDialog";
import { TemplateDetail } from "@/components/audit-templates/TemplateDetail";
import { AuditTemplate } from "@/types/auditTemplates";
import { mockAuditTemplates } from "@/data/mockAuditTemplates";

export default function AuditTemplateManagement() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [templates, setTemplates] = useState<AuditTemplate[]>(mockAuditTemplates);
  const [selectedTemplate, setSelectedTemplate] = useState<AuditTemplate | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list');

  const handleCreateTemplate = (templateData: Omit<AuditTemplate, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTemplate: AuditTemplate = {
      ...templateData,
      id: `template-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setTemplates(prev => [...prev, newTemplate]);
    setIsCreateDialogOpen(false);
  };

  const handleSelectTemplate = (template: AuditTemplate) => {
    setSelectedTemplate(template);
    setViewMode('detail');
  };

  const handleBackToList = () => {
    setSelectedTemplate(null);
    setViewMode('list');
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Supplier Quality
              </Button>
            </Link>
            <File className="h-6 w-6 text-purple-600" />
            <h1 className="text-2xl font-bold">Audit Template Management</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full">
          <div className="w-full max-w-none mx-auto px-4 md:px-6 py-6">
            {viewMode === 'list' ? (
              <div className="w-full">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">Audit Templates</h2>
                    <p className="text-gray-600">
                      Create, manage, and apply standardized audit templates
                    </p>
                  </div>
                  <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Create Template
                  </Button>
                </div>
                
                <TemplateList 
                  templates={templates}
                  onSelectTemplate={handleSelectTemplate}
                />
              </div>
            ) : (
              selectedTemplate && (
                <TemplateDetail 
                  template={selectedTemplate}
                  onBack={handleBackToList}
                  onUpdate={(updatedTemplate) => {
                    setTemplates(prev => prev.map(t => 
                      t.id === updatedTemplate.id ? updatedTemplate : t
                    ));
                    setSelectedTemplate(updatedTemplate);
                  }}
                />
              )
            )}
            
            <CreateTemplateDialog 
              isOpen={isCreateDialogOpen}
              onClose={() => setIsCreateDialogOpen(false)}
              onCreateTemplate={handleCreateTemplate}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

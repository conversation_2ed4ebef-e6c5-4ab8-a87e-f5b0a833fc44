
import React from "react";
import { useNavigate } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { FolderTreeSidebar } from "@/components/document-hub/folder/FolderTreeSidebar";
import { FolderContentView } from "@/components/document-hub/folder/FolderContentView";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Folder } from "lucide-react";

const DocumentHubWithFolders = () => {
  const navigate = useNavigate();

  const headerContent = (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-3">
        <Folder className="h-6 w-6 text-blue-600" />
        <div>
          <h1 className="text-2xl font-bold">Document Hub - Folder View</h1>
          <p className="text-gray-500 text-sm">Browse documents organized in folders</p>
        </div>
      </div>
      <Button 
        variant="outline" 
        onClick={() => navigate('/document-hub')}
        className="flex items-center gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Document Hub
      </Button>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="flex h-full">
        <FolderTreeSidebar />
        <FolderContentView />
      </div>
    </StandardPageLayout>
  );
};

export default DocumentHubWithFolders;

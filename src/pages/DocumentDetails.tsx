
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { documentData } from "@/data/documents/index";
import { toast } from "sonner";
import { DocumentHeader } from "@/components/document-details/DocumentHeader";
import { DocumentTabs } from "@/components/document-details/DocumentTabs";

interface ApprovalStatus {
  approved: number;
  pending: number;
  total: number;
}

interface Approver {
  id: number;
  name: string;
  status: "pending" | "approved" | "rejected";
}

const DocumentDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("details");
  
  // Find the document by ID and log the search process
  console.log("Looking for document with ID:", id);
  console.log("Available document IDs:", documentData.map(doc => doc.id));
  const document = documentData.find(doc => doc.id === id);
  
  useEffect(() => {
    if (!document) {
      console.error(`Document not found with ID: ${id}`);
      toast.error("Document not found");
    } else {
      console.log("Document found:", document);
    }
  }, [document, id]);
  
  // Mock approval data
  const approvalStatus: ApprovalStatus = {
    approved: 1,
    pending: 2,
    total: 3
  };
  
  const approvers: Approver[] = [
    { id: 1, name: "John Doe", status: "approved" },
    { id: 2, name: "Jane Smith", status: "pending" },
    { id: 3, name: "Mike Johnson", status: "pending" }
  ];
  
  const handleStartReview = () => {
    console.log("Starting document review process");
    toast.success("Review process started");
  };
  
  const handleEditDetails = () => {
    console.log("Edit document details");
    toast.success("Edit mode activated");
  };
  
  const handleApprove = (approverId: number) => {
    console.log(`Approving document for approver ${approverId}`);
    toast.success(`Document approved by approver ${approverId}`);
  };
  
  const handleReject = (approverId: number) => {
    console.log(`Rejecting document for approver ${approverId}`);
    toast.error(`Document rejected by approver ${approverId}`);
  };
  
  const handleResend = (approverId: number) => {
    console.log(`Resending approval request to approver ${approverId}`);
    toast.info(`Approval request resent to approver ${approverId}`);
  };
  
  if (!document) {
    return (
      <StandardPageLayout title="Document Not Found">
        <div className="flex flex-col h-full w-full items-center justify-center">
          <h1 className="text-2xl font-bold mb-4">Document Not Found</h1>
          <p className="text-gray-500 mb-4">The document you are looking for does not exist or has been moved.</p>
          <button 
            onClick={() => navigate("/")}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors"
          >
            Return to Document Hub
          </button>
        </div>
      </StandardPageLayout>
    );
  }

  return (
    <StandardPageLayout title="Document Details">
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <DocumentHeader document={document} />
        
        <div className="flex flex-col space-y-6 mb-6">
          <DocumentTabs 
            document={document}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            approvalStatus={approvalStatus}
            approvers={approvers}
            onApprove={handleApprove}
            onReject={handleReject}
            onResend={handleResend}
            onStartReview={handleStartReview}
            onEditDetails={handleEditDetails}
          />
        </div>
      </div>
    </StandardPageLayout>
  );
};

export default DocumentDetails;


import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Download, Copy, Send, Globe } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { SupportedLanguage } from "@/types/formBuilder";
import { SUPPORTED_LANGUAGES } from "@/utils/translationUtils";

export default function FormPreview() {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const lang = (params.get('lang') as SupportedLanguage) || 'en';
  const viewMode = params.get('view') || 'supplier';
  const { toast } = useToast();
  
  const [currentLang, setCurrentLang] = useState<SupportedLanguage>(lang);
  
  // Example mock translations
  const translations: Record<string, Record<SupportedLanguage, string>> = {
    "title": {
      "en": "Supplier Questionnaire – v1.0",
      "de": "Lieferantenfragebogen – v1.0",
      "es-mx": "Cuestionario de Proveedores – v1.0",
      "fr": "Questionnaire Fournisseur – v1.0"
    },
    "preview_banner": {
      "en": "You are previewing the published supplier questionnaire.",
      "de": "Sie sehen eine Vorschau des veröffentlichten Lieferantenfragebogens.",
      "es-mx": "Está viendo una vista previa del cuestionario de proveedores publicado.",
      "fr": "Vous prévisualisez le questionnaire fournisseur publié."
    },
    "submit_button": {
      "en": "Submit Response",
      "de": "Antwort einreichen",
      "es-mx": "Enviar Respuesta",
      "fr": "Soumettre la Réponse"
    },
    "company_information": {
      "en": "Company Information",
      "de": "Unternehmensinformationen",
      "es-mx": "Información de la Empresa",
      "fr": "Informations sur l'Entreprise"
    },
    "quality_certifications": {
      "en": "Quality Certifications",
      "de": "Qualitätszertifizierungen",
      "es-mx": "Certificaciones de Calidad",
      "fr": "Certifications Qualité"
    },
    "performance_metrics": {
      "en": "Performance Metrics",
      "de": "Leistungskennzahlen",
      "es-mx": "Métricas de Desempeño",
      "fr": "Indicateurs de Performance"
    },
    "regulatory_compliance": {
      "en": "Regulatory Compliance",
      "de": "Einhaltung von Vorschriften",
      "es-mx": "Cumplimiento Regulatorio",
      "fr": "Conformité Réglementaire"
    }
  };
  
  const getTranslation = (key: string): string => {
    if (translations[key] && translations[key][currentLang]) {
      return translations[key][currentLang];
    }
    return translations[key]?.en || key;
  };
  
  const handleCopyLink = () => {
    navigator.clipboard.writeText(window.location.href);
    toast({
      title: "Link Copied",
      description: "Form link has been copied to clipboard"
    });
  };
  
  const handleDownloadPDF = () => {
    toast({
      title: "Download Started",
      description: "Form PDF is being generated and will download shortly"
    });
  };
  
  const handleSendToSupplier = () => {
    toast({
      title: "Send Form",
      description: "Send form dialog would open here"
    });
  };
  
  const handleLanguageChange = (lang: SupportedLanguage) => {
    setCurrentLang(lang);
  };
  
  useEffect(() => {
    // Update URL when language changes without reloading the page
    const newParams = new URLSearchParams(location.search);
    newParams.set('lang', currentLang);
    window.history.replaceState(
      {},
      '',
      `${location.pathname}?${newParams.toString()}`
    );
  }, [currentLang, location.pathname, location.search]);
  
  const renderLanguageSelector = () => {
    return (
      <div className="flex flex-wrap items-center gap-1 mb-4">
        {Object.entries(SUPPORTED_LANGUAGES).map(([code, { name, flag }]) => (
          <Button
            key={code}
            variant={currentLang === code ? "default" : "outline"}
            size="sm"
            onClick={() => handleLanguageChange(code as SupportedLanguage)}
            className="flex items-center gap-1"
          >
            <span>{flag}</span>
            <span className="hidden sm:inline">{name}</span>
          </Button>
        ))}
      </div>
    );
  };
  
  return (
    <div className="container max-w-4xl mx-auto px-4 py-8">
      <Alert className="bg-blue-50 border-blue-200 mb-6">
        <Globe className="h-4 w-4 text-blue-600" />
        <AlertTitle>Preview Mode</AlertTitle>
        <AlertDescription>{getTranslation("preview_banner")}</AlertDescription>
      </Alert>
      
      <div className="flex items-center justify-between mb-6">
        <div>
          <Link to="/supplier-forms">
            <Button variant="ghost" size="sm" className="mb-2">
              <ChevronLeft className="mr-1 h-4 w-4" />
              Back to Forms
            </Button>
          </Link>
          
          <h1 className="text-2xl font-bold">{getTranslation("title")}</h1>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleCopyLink} className="flex items-center gap-1">
            <Copy className="h-4 w-4" />
            <span className="hidden md:inline">Copy Link</span>
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownloadPDF} className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            <span className="hidden md:inline">Download PDF</span>
          </Button>
          <Button variant="default" size="sm" onClick={handleSendToSupplier} className="flex items-center gap-1">
            <Send className="h-4 w-4" />
            <span className="hidden md:inline">Send to Supplier</span>
          </Button>
        </div>
      </div>
      
      {/* Language selector */}
      {renderLanguageSelector()}
      
      <div className="space-y-6 mb-8">
        {/* Company Information Section */}
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation("company_information")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Company Legal Name</label>
              <div className="p-2 border rounded bg-gray-50">
                Delta Components Inc.
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Tax Identification Number</label>
              <div className="p-2 border rounded bg-gray-50">
                US-*********
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Quality Certifications Section */}
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation("quality_certifications")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Do you hold an ISO 9001 certification?</label>
              <div className="flex gap-4">
                <div className="flex items-center">
                  <input type="radio" id="iso-yes" checked disabled className="mr-2" />
                  <label htmlFor="iso-yes">Yes</label>
                </div>
                <div className="flex items-center">
                  <input type="radio" id="iso-no" disabled className="mr-2" />
                  <label htmlFor="iso-no">No</label>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Upload ISO 9001 Certificate</label>
              <div className="p-2 border rounded">
                <div className="flex items-center justify-between bg-blue-50 p-2 rounded">
                  <div className="flex items-center">
                    <svg className="h-6 w-6 text-blue-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <span>ISO9001_Delta_2023.pdf</span>
                  </div>
                  <button className="text-blue-600 hover:text-blue-800">View</button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Performance Metrics Section */}
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation("performance_metrics")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">What is your OTIF score for past 12 months? (%)</label>
              <div className="p-2 border rounded bg-gray-50">
                94%
              </div>
              <p className="text-xs text-gray-500">On-Time In-Full delivery performance</p>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">List your top 5 customers by volume</label>
              <div className="p-2 border rounded bg-gray-50">
                GE, Raytheon, Honeywell, Boeing, Lockheed Martin
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Regulatory Compliance Section */}
        <Card>
          <CardHeader>
            <CardTitle>{getTranslation("regulatory_compliance")}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Have you received any FDA warning letters in last 2 years?</label>
              <div className="flex gap-4">
                <div className="flex items-center">
                  <input type="radio" id="fda-yes" disabled className="mr-2" />
                  <label htmlFor="fda-yes">Yes</label>
                </div>
                <div className="flex items-center">
                  <input type="radio" id="fda-no" checked disabled className="mr-2" />
                  <label htmlFor="fda-no">No</label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-center mt-8">
        <Button size="lg" className="px-10">
          {getTranslation("submit_button")}
        </Button>
      </div>
    </div>
  );
}


import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ShoppingCart } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { LoadingProvider } from "@/hooks/use-loading";
import { MainLayout } from "@/components/layout/MainLayout";
import { ESourcingDashboard } from "@/components/supplier-quality/esourcing/ESourcingDashboard";

const ESourcingContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden">
        <TopBar>
          <div className="flex items-center gap-3">
            <ShoppingCart className="h-6 w-6 text-purple-600" />
            <h1 className="text-2xl font-bold">E-Sourcing Platform</h1>
          </div>
        </TopBar>
        
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            <div className="mb-6">
              <Link to="/supplier-quality">
                <Button variant="outline" size="sm" className="gap-2">
                  <ChevronLeft className="h-4 w-4" />
                  Back to Supplier Quality Center
                </Button>
              </Link>
            </div>
            
            <ESourcingDashboard />
          </div>
        </main>
      </div>
    </div>
  );
};

export default function ESourcing() {
  return (
    <SidebarProvider>
      <LoadingProvider>
        <MainLayout>
          <ESourcingContent />
        </MainLayout>
      </LoadingProvider>
    </SidebarProvider>
  );
}

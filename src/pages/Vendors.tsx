
import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Building } from "lucide-react";
import { VendorTable } from "@/components/vendors/VendorTable";
import { useVendorsData } from "@/hooks/useVendorsData";

const Vendors = () => {
  const { vendors } = useVendorsData();

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <Building className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Vendor Management</h1>
        <p className="text-sm text-gray-600">Manage supplier relationships and vendor information</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6 space-y-6">
        <VendorTable vendors={vendors} />
      </div>
    </StandardPageLayout>
  );
};

export default Vendors;

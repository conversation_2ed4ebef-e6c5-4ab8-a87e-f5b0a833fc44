
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { MaintenanceCalendar } from "@/components/cmms/MaintenanceCalendar";
import { ScheduleMaintenanceModal } from "@/components/cmms/ScheduleMaintenanceModal";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";

interface MaintenanceEvent {
  id: string;
  title: string;
  date: string;
  time: string;
  type: 'Preventive' | 'Corrective' | 'Inspection' | 'Emergency';
  technician: string;
  asset: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
}

const MaintenanceScheduling = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [maintenanceEvents, setMaintenanceEvents] = useState<MaintenanceEvent[]>([]);

  const handleScheduleMaintenance = (event: MaintenanceEvent) => {
    setMaintenanceEvents(prev => [...prev, event]);
    console.log('Scheduled maintenance:', event);
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center justify-between w-full">
            <h1 className="text-2xl font-bold">Maintenance Scheduling</h1>
            <Button onClick={() => setShowScheduleModal(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Schedule Maintenance
            </Button>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full">
          <MaintenanceCalendar />
        </div>
      </div>

      <ScheduleMaintenanceModal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        onSchedule={handleScheduleMaintenance}
      />
    </div>
  );
};

export default MaintenanceScheduling;

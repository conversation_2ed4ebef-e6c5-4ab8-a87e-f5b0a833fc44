
import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { PPAPTabs } from "@/components/ppap/PPAPTabs";
import { FileCheck } from "lucide-react";

const PPAP = () => {
  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <FileCheck className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">PPAP Management</h1>
        <p className="text-sm text-gray-600">Production Part Approval Process</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <PPAPTabs />
      </div>
    </StandardPageLayout>
  );
};

export default PPAP;

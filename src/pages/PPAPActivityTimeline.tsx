
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, ActivitySquare, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Link } from "react-router-dom";
import { formatDate } from "@/utils/dateUtils";

export default function PPAPActivityTimeline() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  const activities = [
    {
      id: "1",
      type: "Approved",
      description: "PPAP-002 (Wing Attachment Bracket) approved by <PERSON>",
      timestamp: "2024-01-18T14:30:00",
      icon: CheckCircle,
      color: "text-green-500"
    },
    {
      id: "2",
      type: "Submitted",
      description: "PPAP-001 (Main Landing Gear Bearing) submitted for review",
      timestamp: "2024-01-15T09:15:00",
      icon: Clock,
      color: "text-blue-500"
    },
    {
      id: "3",
      type: "Rejected",
      description: "PPAP-003 (Turbine Blade Assembly) rejected - material test issues",
      timestamp: "2024-01-12T16:45:00",
      icon: XCircle,
      color: "text-red-500"
    },
    {
      id: "4",
      type: "Under Review",
      description: "PPAP-004 (Hydraulic Valve) moved to review phase",
      timestamp: "2024-01-10T11:20:00",
      icon: AlertCircle,
      color: "text-orange-500"
    },
    {
      id: "5",
      type: "Approved",
      description: "PPAP-005 (Control Panel Display) approved with conditions",
      timestamp: "2024-01-08T13:10:00",
      icon: CheckCircle,
      color: "text-green-500"
    }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <ActivitySquare className="h-6 w-6 text-purple-600" />
            <h1 className="text-2xl font-bold">PPAP Activity Timeline</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="mb-4">
              <Link to="/ppap?tab=reports">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to PPAP Reports
                </Button>
              </Link>
            </div>

            {/* Activity Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">12</div>
                  <div className="text-sm text-gray-600">Approved Today</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">8</div>
                  <div className="text-sm text-gray-600">Under Review</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">5</div>
                  <div className="text-sm text-gray-600">Submitted</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">2</div>
                  <div className="text-sm text-gray-600">Rejected</div>
                </CardContent>
              </Card>
            </div>

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {activities.map((activity, index) => {
                    const IconComponent = activity.icon;
                    return (
                      <div key={activity.id} className="flex gap-4">
                        <div className="flex flex-col items-center">
                          <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                            <IconComponent className="h-4 w-4" />
                          </div>
                          {index < activities.length - 1 && (
                            <div className="w-px h-12 bg-gray-200 mt-2"></div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-medium text-sm">{activity.type}</h4>
                              <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                            </div>
                            <div className="text-xs text-gray-500 ml-4 shrink-0">
                              {formatDate(activity.timestamp)}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Activity Filter */}
            <Card>
              <CardHeader>
                <CardTitle>Activity Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm">All Activities</Button>
                  <Button variant="outline" size="sm">Approvals</Button>
                  <Button variant="outline" size="sm">Submissions</Button>
                  <Button variant="outline" size="sm">Rejections</Button>
                  <Button variant="outline" size="sm">Reviews</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

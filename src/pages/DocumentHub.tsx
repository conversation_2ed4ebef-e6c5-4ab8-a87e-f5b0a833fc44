
import React, { useState } from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { SearchAndFilter } from "@/components/document-hub/SearchAndFilter";
import { DocumentTableContainer } from "@/components/document-hub/DocumentTableContainer";
import { ViewToggle } from "@/components/document-hub/ViewToggle";
import { FileText } from "lucide-react";

const DocumentHub = () => {
  const [appliedFilters, setAppliedFilters] = useState({
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: []
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"table" | "folders">("table");
  const [isFilterSidePanelOpen, setIsFilterSidePanelOpen] = useState(false);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleApplyFilters = (filters: any) => {
    setAppliedFilters(filters);
  };

  const toggleFilterSidePanel = () => {
    setIsFilterSidePanelOpen(!isFilterSidePanelOpen);
  };

  const headerContent = (
    <div className="flex items-center gap-3">
      <FileText className="h-6 w-6 text-blue-600" />
      <div>
        <h1 className="text-2xl font-bold">Document Hub</h1>
        <p className="text-gray-500 text-sm">Manage, review, and approve documents</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <SearchAndFilter 
            isFilterSidePanelOpen={isFilterSidePanelOpen}
            toggleFilterSidePanel={toggleFilterSidePanel}
            appliedFilters={appliedFilters}
            onApplyFilters={handleApplyFilters}
            onSearch={handleSearch}
            searchTerm={searchTerm}
          />
          <ViewToggle viewMode={viewMode} setViewMode={setViewMode} />
        </div>
        
        <div className="flex-1 min-h-0">
          <DocumentTableContainer 
            appliedFilters={appliedFilters}
            searchTerm={searchTerm}
          />
        </div>
      </div>
    </StandardPageLayout>
  );
};

export default DocumentHub;

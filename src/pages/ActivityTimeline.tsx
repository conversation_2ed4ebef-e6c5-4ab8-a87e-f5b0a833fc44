
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ActivitySquare } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SidebarProvider } from "@/components/ui/sidebar";

export default function ActivityTimeline() {
  const activities = [
    {
      id: 1,
      date: "2024-12-15",
      time: "14:30",
      type: "Audit",
      supplier: "Advanced Manufacturing Corp",
      description: "Process audit completed",
      status: "completed"
    },
    {
      id: 2,
      date: "2024-12-14",
      time: "09:15",
      type: "Certificate",
      supplier: "Precision Aerospace Ltd",
      description: "ISO 9001 certificate uploaded",
      status: "pending"
    },
    {
      id: 3,
      date: "2024-12-13",
      time: "16:45",
      type: "CAPA",
      supplier: "Green Tech Solutions",
      description: "CAPA-2024-032 created for calibration issue",
      status: "open"
    },
    {
      id: 4,
      date: "2024-12-12",
      time: "11:20",
      type: "Scorecard",
      supplier: "Safe Work Industries",
      description: "Q4 2024 scorecard sent to supplier",
      status: "completed"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-emerald-100 text-emerald-800 border-emerald-200">Completed</Badge>;
      case "pending":
        return <Badge className="bg-amber-100 text-amber-800 border-amber-200">Pending</Badge>;
      case "open":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Open</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <SidebarProvider>
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        
        <div className="flex-1 overflow-auto w-full min-w-0 bg-slate-50">
          <div className="w-full min-w-0 px-4 lg:px-6 py-6 lg:py-8 max-w-none mx-auto">
            <div className="mb-6 lg:mb-8">
              <Link to="/supplier-quality">
                <Button variant="ghost" size="sm" className="mb-4 text-slate-600 hover:text-slate-900">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to Supplier Quality
                </Button>
              </Link>
              
              <div className="flex items-center gap-4 mb-2">
                <div className="p-3 bg-amber-100 rounded-xl">
                  <ActivitySquare className="h-8 w-8 text-amber-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-semibold text-slate-900">Activity Timeline</h1>
                  <p className="text-slate-600 text-lg mt-1">
                    Track all supplier quality activities and events
                  </p>
                </div>
              </div>
            </div>
            
            <Card className="shadow-sm border-slate-200 w-full min-w-0">
              <CardHeader className="pb-4">
                <CardTitle className="text-slate-900">Recent Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {activities.map((activity, index) => (
                    <div key={activity.id} className="flex items-start space-x-4 p-6 border border-slate-200 rounded-xl bg-white hover:shadow-md transition-shadow">
                      <div className="flex-shrink-0 relative">
                        <div className="w-4 h-4 bg-blue-500 rounded-full mt-1"></div>
                        {index < activities.length - 1 && (
                          <div className="absolute top-6 left-1.5 w-0.5 h-16 bg-slate-200"></div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-slate-900 mb-1">{activity.description}</h3>
                            <p className="text-slate-700 font-medium">{activity.supplier}</p>
                          </div>
                          <div className="flex items-center space-x-3 ml-4 flex-shrink-0">
                            {getStatusBadge(activity.status)}
                            <Badge variant="outline" className="border-slate-300 text-slate-600">{activity.type}</Badge>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 mt-3 text-sm text-slate-500">
                          <span className="font-medium">{activity.date}</span>
                          <span>•</span>
                          <span>{activity.time}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}

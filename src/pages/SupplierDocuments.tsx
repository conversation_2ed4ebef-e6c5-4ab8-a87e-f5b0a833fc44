
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, FileText } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { DocumentsTable } from "@/components/supplier-quality/documents/DocumentsTable";

export default function SupplierDocuments() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  // Mock documents data matching SupplierDocument interface
  const documents = [
    {
      id: "1",
      name: "Quality Manual",
      fileName: "quality_manual_v1.pdf",
      uploadDate: "2024-01-15",
      expiryDate: "2025-01-15",
      status: "Valid" as const,
      version: 1,
      reviewer: "<PERSON>",
      supplierName: "Acme Corp"
    }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Supplier Quality
              </Button>
            </Link>
            <FileText className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Supplier Documents</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <DocumentsTable documents={documents} />
        </div>
      </div>
    </div>
  );
}

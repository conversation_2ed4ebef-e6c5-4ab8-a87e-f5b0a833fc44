
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Chevron<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SidebarProvider } from "@/components/ui/sidebar";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts";

export default function PerformanceAnalysis() {
  const performanceData = [
    { month: 'Jan', quality: 95, delivery: 92, cost: 88 },
    { month: 'Feb', quality: 93, delivery: 94, cost: 90 },
    { month: 'Mar', quality: 97, delivery: 89, cost: 85 },
    { month: 'Apr', quality: 96, delivery: 91, cost: 87 },
    { month: 'May', quality: 98, delivery: 93, cost: 89 },
    { month: 'Jun', quality: 94, delivery: 95, cost: 91 }
  ];

  return (
    <SidebarProvider>
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        
        <div className="flex-1 overflow-auto w-full min-w-0 bg-slate-50">
          <div className="w-full min-w-0 px-4 lg:px-6 py-6 lg:py-8 max-w-none mx-auto">
            <div className="mb-6 lg:mb-8">
              <Link to="/supplier-quality">
                <Button variant="ghost" size="sm" className="mb-4 text-slate-600 hover:text-slate-900">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to Supplier Quality
                </Button>
              </Link>
              
              <div className="flex items-center gap-4 mb-2">
                <div className="p-3 bg-purple-100 rounded-xl">
                  <PieChart className="h-8 w-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-semibold text-slate-900">Performance Analysis</h1>
                  <p className="text-slate-600 text-lg mt-1">
                    Comprehensive analysis of supplier performance metrics
                  </p>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 w-full min-w-0">
              <Card className="shadow-sm border-slate-200">
                <CardHeader className="pb-4">
                  <CardTitle className="text-slate-900">Performance Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="month" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'white', 
                          border: '1px solid #e2e8f0',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                        }} 
                      />
                      <Line type="monotone" dataKey="quality" stroke="#10b981" strokeWidth={3} name="Quality %" />
                      <Line type="monotone" dataKey="delivery" stroke="#3b82f6" strokeWidth={3} name="Delivery %" />
                      <Line type="monotone" dataKey="cost" stroke="#f59e0b" strokeWidth={3} name="Cost Efficiency %" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="shadow-sm border-slate-200">
                <CardHeader className="pb-4">
                  <CardTitle className="text-slate-900">Performance Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis dataKey="month" stroke="#64748b" />
                      <YAxis stroke="#64748b" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'white', 
                          border: '1px solid #e2e8f0',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                        }} 
                      />
                      <Bar dataKey="quality" fill="#10b981" name="Quality %" radius={4} />
                      <Bar dataKey="delivery" fill="#3b82f6" name="Delivery %" radius={4} />
                      <Bar dataKey="cost" fill="#f59e0b" name="Cost Efficiency %" radius={4} />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}

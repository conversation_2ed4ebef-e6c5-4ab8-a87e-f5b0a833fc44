
import React from "react";
import { Link, useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Bar<PERSON><PERSON> } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SupplierScorecard } from "@/components/supplier-quality/scorecard/SupplierScorecard";
import { mockSupplierScorecards } from "@/data/mockSupplierScorecards";

export default function SupplierScorecardPage() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const { id } = useParams<{ id: string }>();

  // Find the scorecard by ID
  const scorecard = mockSupplierScorecards.find(sc => sc.id === id) || mockSupplierScorecards[0];

  const handleUpdateScorecard = (updatedScorecard: any) => {
    console.log("Scorecard updated:", updatedScorecard);
    // Handle scorecard update
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality/scorecards">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Scorecards
              </Button>
            </Link>
            <BarChart className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Supplier Scorecard - {scorecard.supplierName}</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <SupplierScorecard scorecard={scorecard} onUpdateScorecard={handleUpdateScorecard} />
        </div>
      </div>
    </div>
  );
}

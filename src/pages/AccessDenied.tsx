
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Home, ArrowLeft } from 'lucide-react';
import { SupportShareButton } from '@/components/error-handling/SupportShareButton';

const AccessDenied: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <Shield className="h-16 w-16 mx-auto text-yellow-500 mb-4" />
          <h1 className="text-4xl font-bold text-gray-800 mb-2">403</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-6">
            You don't have permission to access this resource. Please contact your administrator if you believe this is an error.
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={() => navigate(-1)}
              className="w-full gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Go Back
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard')}
              className="w-full gap-2"
            >
              <Home className="h-4 w-4" />
              Go to Dashboard
            </Button>

            <SupportShareButton
              errorDetails={{
                statusCode: 403,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                additionalContext: "User attempted to access restricted resource"
              }}
              title="Request Access"
              variant="outline"
              size="sm"
            />
          </div>

          <div className="mt-6 text-xs text-gray-500">
            If you need access to this resource, please contact your system administrator.
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessDenied;

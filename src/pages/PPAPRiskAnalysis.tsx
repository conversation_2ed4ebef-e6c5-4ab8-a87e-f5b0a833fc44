
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Shield, AlertTriangle, TrendingUp } from "lucide-react";
import { Link } from "react-router-dom";

export default function PPAPRiskAnalysis() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <Shield className="h-6 w-6 text-red-600" />
            <h1 className="text-2xl font-bold">PPAP Risk Analysis</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="mb-4">
              <Link to="/ppap?tab=reports">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to PPAP Reports
                </Button>
              </Link>
            </div>

            {/* Risk Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    High Risk Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-red-600">8</div>
                  <p className="text-sm text-gray-600">Require immediate attention</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    Medium Risk Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-yellow-600">15</div>
                  <p className="text-sm text-gray-600">Monitor closely</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    Risk Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">↓ 12%</div>
                  <p className="text-sm text-gray-600">Decrease from last month</p>
                </CardContent>
              </Card>
            </div>

            {/* Risk Matrix */}
            <Card>
              <CardHeader>
                <CardTitle>Risk Assessment Matrix</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-5 gap-2 text-center text-sm">
                  <div className="font-semibold">Impact →</div>
                  <div className="font-semibold">Very Low</div>
                  <div className="font-semibold">Low</div>
                  <div className="font-semibold">Medium</div>
                  <div className="font-semibold">High</div>
                  
                  <div className="font-semibold">Very High</div>
                  <div className="bg-yellow-200 p-2 rounded">2</div>
                  <div className="bg-orange-200 p-2 rounded">3</div>
                  <div className="bg-red-200 p-2 rounded">1</div>
                  <div className="bg-red-300 p-2 rounded">2</div>
                  
                  <div className="font-semibold">High</div>
                  <div className="bg-green-200 p-2 rounded">5</div>
                  <div className="bg-yellow-200 p-2 rounded">4</div>
                  <div className="bg-orange-200 p-2 rounded">2</div>
                  <div className="bg-red-200 p-2 rounded">3</div>
                  
                  <div className="font-semibold">Medium</div>
                  <div className="bg-green-200 p-2 rounded">8</div>
                  <div className="bg-green-200 p-2 rounded">6</div>
                  <div className="bg-yellow-200 p-2 rounded">3</div>
                  <div className="bg-orange-200 p-2 rounded">1</div>
                  
                  <div className="font-semibold">Low</div>
                  <div className="bg-green-100 p-2 rounded">12</div>
                  <div className="bg-green-200 p-2 rounded">9</div>
                  <div className="bg-green-200 p-2 rounded">4</div>
                  <div className="bg-yellow-200 p-2 rounded">2</div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Risk by Category</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Quality Control</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                      </div>
                      <span className="text-sm font-medium">High</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Supplier Performance</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '40%' }}></div>
                      </div>
                      <span className="text-sm font-medium">Medium</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Documentation</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '20%' }}></div>
                      </div>
                      <span className="text-sm font-medium">Low</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Process Control</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: '50%' }}></div>
                      </div>
                      <span className="text-sm font-medium">Medium-High</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

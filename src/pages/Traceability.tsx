
import React from "react";
import { useNavigate } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { GitBranch } from "lucide-react";
import { TraceabilityDashboard } from "@/components/supplier-quality/traceability/TraceabilityDashboard";

const Traceability = () => {
  const navigate = useNavigate();

  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <GitBranch className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Traceability</h1>
        <p className="text-sm text-gray-600">Track product lifecycle and supply chain</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <TraceabilityDashboard />
      </div>
    </StandardPageLayout>
  );
};

export default Traceability;


import React, { useState } from 'react';
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { WorkOrderDashboard } from '@/components/production-hub/WorkOrderDashboard';
import { CreateWorkOrder } from '@/components/production-hub/CreateWorkOrder';
import { ExecuteWorkOrder } from '@/components/production-hub/ExecuteWorkOrder';
import { BatchRecordReview } from '@/components/production-hub/BatchRecordReview';
import { BatchRecordDetail } from '@/components/production-hub/BatchRecordDetail';
import { OperatorConsole } from '@/components/production-hub/OperatorConsole';
import { CMMSDashboard } from '@/components/cmms/CMMSDashboard';
import { WorkOrderDetail } from '@/components/cmms/WorkOrderDetail';
import { AssetRegister } from '@/components/cmms/AssetRegister';
import { GembaWalkManagement } from '@/components/production-hub/gemba/GembaWalkManagement';

export default function ProductionHub() {
  const [selectedWorkOrder, setSelectedWorkOrder] = useState<string | null>(null);
  const [selectedBatch, setSelectedBatch] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'dashboard' | 'create' | 'execute' | 'review' | 'batch-detail' | 'console' | 'cmms' | 'cmms-wo-detail' | 'cmms-assets' | 'gemba'>('dashboard');

  const renderContent = () => {
    switch (activeView) {
      case 'create':
        return <CreateWorkOrder onBack={() => setActiveView('dashboard')} onComplete={() => setActiveView('dashboard')} />;
      case 'execute':
        return <ExecuteWorkOrder workOrderId={selectedWorkOrder || ''} onBack={() => setActiveView('dashboard')} />;
      case 'review':
        return <BatchRecordReview workOrderId={selectedWorkOrder || ''} onBack={() => setActiveView('dashboard')} />;
      case 'batch-detail':
        return <BatchRecordDetail batchId={selectedBatch || ''} onBack={() => setActiveView('dashboard')} />;
      case 'console':
        return <OperatorConsole onBack={() => setActiveView('dashboard')} />;
      case 'cmms':
        return <CMMSDashboard onViewWorkOrder={(id) => { setSelectedWorkOrder(id); setActiveView('cmms-wo-detail'); }} onManageAssets={() => setActiveView('cmms-assets')} />;
      case 'cmms-wo-detail':
        return <WorkOrderDetail workOrderId={selectedWorkOrder || ''} onBack={() => setActiveView('cmms')} />;
      case 'cmms-assets':
        return <AssetRegister onBack={() => setActiveView('cmms')} />;
      case 'gemba':
        return <GembaWalkManagement onBack={() => setActiveView('dashboard')} />;
      default:
        return (
          <WorkOrderDashboard 
            onCreateWorkOrder={() => setActiveView('create')}
            onExecuteWorkOrder={(id) => { setSelectedWorkOrder(id); setActiveView('execute'); }}
            onReviewBatch={(id) => { 
              setSelectedBatch(id); 
              setActiveView('batch-detail'); 
            }}
            onOpenConsole={() => setActiveView('console')}
            onOpenCMMS={() => setActiveView('cmms')}
            onOpenGemba={() => setActiveView('gemba')}
          />
        );
    }
  };

  return (
    <StandardPageLayout title="Production Hub">
      <div className="h-full w-full overflow-hidden">
        {renderContent()}
      </div>
    </StandardPageLayout>
  );
}

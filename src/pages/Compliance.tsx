
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { LoadingProvider } from "@/hooks/use-loading";
import { MainLayout } from "@/components/layout/MainLayout";

const ComplianceContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <h1 className="text-2xl font-bold">Compliance</h1>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="text-center py-8">
            <h2 className="text-xl font-semibold">Compliance Management</h2>
            <p className="text-gray-600">Coming soon...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const Compliance = () => {
  return (
    <SidebarProvider>
      <LoadingProvider>
        <MainLayout>
          <ComplianceContent />
        </MainLayout>
      </LoadingProvider>
    </SidebarProvider>
  );
};

export default Compliance;

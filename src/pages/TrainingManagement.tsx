
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Users, Clock, Calendar, Award } from "lucide-react";
import { CreateTrainingDialog } from "@/components/people/CreateTrainingDialog";
import { TrainingSkillsProficiencyChart } from "@/components/training-management/SkillsProficiencyChart";

const TrainingManagement = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const upcomingTrainings = [
    { id: 1, title: "Safety Protocols", date: "28 Mar 2025", participants: 15, duration: "4 hours" },
    { id: 2, title: "Quality Standards", date: "02 Apr 2025", participants: 22, duration: "6 hours" },
    { id: 3, title: "Equipment Training", date: "10 Apr 2025", participants: 8, duration: "3 hours" }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center justify-between w-full">
            <h1 className="text-2xl font-bold">Training Management</h1>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Training
            </Button>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Trainings</p>
                      <p className="text-3xl font-bold">12</p>
                    </div>
                    <Calendar className="h-12 w-12 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Participants</p>
                      <p className="text-3xl font-bold">156</p>
                    </div>
                    <Users className="h-12 w-12 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                      <p className="text-3xl font-bold">94%</p>
                    </div>
                    <Award className="h-12 w-12 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Training Hours</p>
                      <p className="text-3xl font-bold">248</p>
                    </div>
                    <Clock className="h-12 w-12 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Skills Proficiency Chart */}
            <TrainingSkillsProficiencyChart />

            {/* Upcoming Trainings */}
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Trainings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingTrainings.map((training) => (
                    <div key={training.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{training.title}</h4>
                        <p className="text-sm text-gray-600">
                          {training.date} • {training.duration}
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          {training.participants} participants
                        </Badge>
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <CreateTrainingDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog} 
      />
    </div>
  );
};

export default TrainingManagement;


import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { FunctionalityAudit } from "@/components/shared/FunctionalityAudit";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Settings } from "lucide-react";

const FunctionalityAuditContent: React.FC = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <Settings className="h-6 w-6 text-purple-600" />
            <h1 className="text-2xl font-bold">Functionality Audit</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <FunctionalityAudit />
        </div>
      </div>
    </div>
  );
};

const FunctionalityAuditPage: React.FC = () => {
  return (
    <SidebarProvider>
      <FunctionalityAuditContent />
    </SidebarProvider>
  );
};

export default FunctionalityAuditPage;

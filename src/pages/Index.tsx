import React, { useState } from "react";
import { Link } from "react-router-dom";
import { FolderOpen, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { SearchAndFilter } from "@/components/document-hub/SearchAndFilter";
import { DocumentTableContainer } from "@/components/document-hub/DocumentTableContainer";
import { useFilterState } from "@/hooks/useFilterState";
const Index = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const {
    appliedFilters,
    setAppliedFilters,
    isFilterSidePanelOpen,
    toggleFilterSidePanel
  } = useFilterState();
  const handleApplyFilters = (filters: any) => {
    setAppliedFilters(filters);
  };
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term && term.length > 0) {
      console.log("Searching for:", term);
    } else {
      console.log("Search cleared");
    }
  };
  const headerContent = <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-3">
        <FileText className="h-6 w-6 text-blue-600" />
        <div>
          <h1 className="text-2xl font-bold">Document Hub</h1>
          <p className="text-gray-500 text-sm">Manage, review, and approve documents</p>
        </div>
      </div>
      <Link to="/documents-folders">
        
      </Link>
    </div>;
  return <StandardPageLayout headerContent={headerContent}>
      <div className="w-full px-4 lg:px-6 xl:px-8 py-2">
        <SearchAndFilter isFilterSidePanelOpen={isFilterSidePanelOpen} toggleFilterSidePanel={toggleFilterSidePanel} appliedFilters={appliedFilters} onApplyFilters={handleApplyFilters} onSearch={handleSearch} searchTerm={searchTerm} />
      </div>
      
      <div className="flex-1 overflow-auto w-full min-w-0 px-4 lg:px-6 xl:px-8 pb-4">
        <DocumentTableContainer appliedFilters={appliedFilters} searchTerm={searchTerm} />
      </div>
    </StandardPageLayout>;
};
export default Index;

import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Calendar } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { VendorTimelineContainer } from "@/components/supplier-quality/timeline/VendorTimelineContainer";
import { AuditSummaryCard } from "@/components/supplier-quality/timeline/AuditSummaryCard";

export default function VendorTimeline() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden w-full">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden w-full min-w-0">
        <TopBar />
        
        <main className="flex-1 overflow-y-auto w-full min-w-0 bg-gray-50">
          <div className="w-full max-w-[1440px] mx-auto px-4 md:px-6 lg:px-8 py-6 space-y-6">
            {/* Page Header */}
            <div className="w-full">
              <div className="flex items-center gap-3 mb-4">
                <Calendar className="h-6 w-6 text-gray-600" />
                <h1 className="text-2xl font-bold">Vendor Audit Timeline</h1>
              </div>
            </div>
            
            {/* Back Button Section */}
            <div className="w-full">
              <Link to="/supplier-quality">
                <Button variant="outline" size="sm" className="gap-2">
                  <ChevronLeft className="h-4 w-4" />
                  Back to Supplier Quality Center
                </Button>
              </Link>
            </div>
            
            {/* Audit Summary Card */}
            <div className="w-full">
              <AuditSummaryCard
                vendorName="PT Sat Nusapersada Tbk"
                totalAudits={7}
                averageScore={89}
                lastAuditScore={96}
                openCapas={0}
                trend="up"
              />
            </div>
            
            {/* Timeline Content - Full Width */}
            <div className="w-full">
              <VendorTimelineContainer />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrainingVideoPlayer } from "@/components/training/TrainingVideoPlayer";
import { TrainingQuiz } from "@/components/training/TrainingQuiz";
import { TrainingModules } from "@/components/training/training-detail/TrainingModules";
import { ArrowLeft, Play, FileText, Award, Clock, Calendar } from "lucide-react";
import { useTraining } from "@/contexts/TrainingContext";

export default function TrainingDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const mode = searchParams.get('mode') || 'start';
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const { employeeTrainings, completeTraining } = useTraining();
  const [activeTab, setActiveTab] = useState(mode === 'review' ? 'overview' : 'video');
  const [showQuiz, setShowQuiz] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [quizScore, setQuizScore] = useState<number | null>(null);

  const training = employeeTrainings.find(t => t.id === id);

  if (!training) {
    return (
      <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
        <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
          <Sidebar />
        </div>
        
        <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
          <TopBar>
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-bold">Training Not Found</h1>
            </div>
          </TopBar>
          
          <div className="flex-1 flex items-center justify-center">
            <Card className="w-full max-w-md">
              <CardContent className="p-6 text-center">
                <h2 className="text-xl font-semibold mb-4">Training Not Found</h2>
                <p className="text-gray-600 mb-4">The training you're looking for doesn't exist.</p>
                <Button onClick={() => navigate('/people')}>
                  Back to People
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const mockModules = [
    { name: "Introduction to Quality Management", duration: "15 min", completed: true },
    { name: "ISO 9001:2015 Standards", duration: "25 min", completed: mode === 'review' },
    { name: "Implementation Guidelines", duration: "20 min", completed: false },
    { name: "Assessment and Certification", duration: "10 min", completed: false }
  ];

  const handleVideoComplete = () => {
    setShowQuiz(true);
    setActiveTab('quiz');
  };

  const handleQuizComplete = (score: number) => {
    setQuizScore(score);
    setQuizCompleted(true);
    if (score >= 70) {
      completeTraining(training.id, score);
      setTimeout(() => {
        setActiveTab('completion');
      }, 1000);
    }
  };

  const handleSaveAndExit = () => {
    navigate('/people');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Not Started":
        return <Badge className="bg-gray-100 text-gray-800">Not Started</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/people')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to People
            </Button>
            <h1 className="text-2xl font-bold">Training: {training.training.title}</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full min-w-0">
          <div className="w-full max-w-none space-y-6">
            {/* Training Header */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{training.training.title}</CardTitle>
                    <p className="text-gray-600 mt-1">{training.training.category} • {training.training.type}</p>
                  </div>
                  {getStatusBadge(training.status)}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Duration: {training.training.estimatedDuration}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Due: {training.dueDate}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Play className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Progress: {training.progress}%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">Time Spent: {training.timeSpent}</span>
                  </div>
                </div>
              </CardHeader>
            </Card>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview" className="gap-2">
                  <FileText className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="modules" className="gap-2">
                  <FileText className="h-4 w-4" />
                  Modules
                </TabsTrigger>
                <TabsTrigger value="video" className="gap-2">
                  <Play className="h-4 w-4" />
                  Video
                </TabsTrigger>
                <TabsTrigger value="quiz" className="gap-2" disabled={!showQuiz && mode !== 'review'}>
                  <Award className="h-4 w-4" />
                  Quiz
                </TabsTrigger>
                <TabsTrigger value="completion" className="gap-2" disabled={!quizCompleted && training.status !== 'Completed'}>
                  <Award className="h-4 w-4" />
                  Certificate
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Training Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">
                      This comprehensive training covers the fundamentals of {training.training.title}. 
                      You'll learn key concepts, best practices, and practical applications.
                    </p>
                    <div className="space-y-2">
                      <h4 className="font-semibold">Learning Objectives:</h4>
                      <ul className="list-disc list-inside text-gray-600 space-y-1">
                        <li>Understand core principles and concepts</li>
                        <li>Apply knowledge in practical scenarios</li>
                        <li>Meet compliance and certification requirements</li>
                        <li>Demonstrate proficiency through assessment</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="modules" className="space-y-4">
                <TrainingModules modules={mockModules} />
              </TabsContent>

              <TabsContent value="video" className="space-y-4">
                <TrainingVideoPlayer
                  trainingId={training.id}
                  trainingTitle={training.training.title}
                  onSaveAndExit={handleSaveAndExit}
                  onComplete={handleVideoComplete}
                />
              </TabsContent>

              <TabsContent value="quiz" className="space-y-4">
                {showQuiz || mode === 'review' ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>Assessment Quiz</CardTitle>
                      <p className="text-gray-600">Complete this quiz to demonstrate your understanding. Passing score: 70%</p>
                    </CardHeader>
                    <CardContent>
                      <TrainingQuiz 
                        trainingId={training.id}
                        onComplete={handleQuizComplete}
                      />
                      {quizScore !== null && (
                        <div className={`mt-4 p-4 rounded-lg ${quizScore >= 70 ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                          <h4 className={`font-semibold ${quizScore >= 70 ? 'text-green-800' : 'text-red-800'}`}>
                            Quiz {quizScore >= 70 ? 'Passed' : 'Failed'}
                          </h4>
                          <p className={`${quizScore >= 70 ? 'text-green-700' : 'text-red-700'}`}>
                            Your score: {quizScore}%
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <p className="text-gray-600">Complete the video training first to unlock the quiz.</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="completion" className="space-y-4">
                <Card>
                  <CardContent className="p-6 text-center">
                    <Award className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
                    <h2 className="text-2xl font-bold mb-4">Congratulations!</h2>
                    <p className="text-gray-600 mb-6">
                      You have successfully completed the {training.training.title} training.
                    </p>
                    <div className="space-y-2">
                      <p><strong>Final Score:</strong> {quizScore || training.quizScore || 'N/A'}%</p>
                      <p><strong>Time Spent:</strong> {training.timeSpent}</p>
                      <p><strong>Completion Date:</strong> {new Date().toLocaleDateString()}</p>
                    </div>
                    <div className="mt-6 space-x-4">
                      <Button onClick={() => navigate('/people')}>
                        Back to People
                      </Button>
                      <Button variant="outline">
                        Download Certificate
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}


import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useIsMobile } from "@/hooks/use-mobile";
import { SidebarProvider, useSidebar } from "@/components/ui/sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { 
  ClipboardCheck, 
  TreePine,
  Lightbulb, 
  LayoutGrid,
  Beaker
} from "lucide-react";

const standardsData = [
  {
    id: "iso9001",
    code: "ISO 9001:2015",
    name: "Quality Management System (QMS)",
    compliance: 2.33,
    icon: <ClipboardCheck className="h-8 w-8 text-teal-600" />
  },
  {
    id: "iso14001",
    code: "ISO 14001:2015",
    name: "Environmental Management System (EMS)",
    compliance: 2.78,
    icon: <TreePine className="h-8 w-8 text-teal-600" />
  },
  {
    id: "iso50001",
    code: "ISO 50001:2018",
    name: "Energy Management System (EnMS)",
    compliance: 2.86,
    icon: <Lightbulb className="h-8 w-8 text-teal-600" />
  },
  {
    id: "iso45001",
    code: "ISO 45001:2018",
    name: "Occupational Health and Safety Management System (OHSMS)",
    compliance: 2.44,
    icon: <LayoutGrid className="h-8 w-8 text-teal-600" />
  },
  {
    id: "iso17025",
    code: "ISO 17025:2017",
    name: "Laboratory Management System",
    compliance: 1.96,
    icon: <Beaker className="h-8 w-8 text-teal-600" />
  },
];

const StandardsHubContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40 flex-shrink-0`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col w-full min-w-0">
        <TopBar>
          <div className="flex items-center">
            <div>
              <h1 className="text-2xl font-bold">Standards Hub</h1>
              {!isMobile && (
                <p className="text-sm text-gray-500">Monitor and improve standards compliance</p>
              )}
            </div>
          </div>
        </TopBar>
        
        <main className="flex-1 w-full min-w-0 overflow-y-auto p-4 md:p-6">
          <div className="w-full overflow-x-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-max min-w-full">
              {standardsData.map((standard) => (
                <Card key={standard.id} className="shadow-sm hover:shadow-md transition-shadow duration-200 w-full min-w-[280px]">
                  <CardContent className="p-5">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">{standard.code}</p>
                        <h3 className="text-lg font-semibold text-gray-800">{standard.name}</h3>
                      </div>
                      <div className="flex-shrink-0">
                        {standard.icon}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <p className="text-sm font-medium">Compliance score:</p>
                        <p className="text-sm font-medium text-teal-600">{standard.compliance.toFixed(2)}%</p>
                      </div>
                      <div className="relative">
                        <Progress 
                          value={standard.compliance} 
                          className="h-2 bg-[#E5F6F6]" 
                        />
                        <div 
                          className="absolute top-0 left-0 h-2 w-2 rounded-full bg-teal-600" 
                          style={{ left: `${Math.min(Math.max(standard.compliance, 0), 100)}%`, transform: 'translateX(-50%)' }}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

const StandardsHub = () => {
  return (
    <SidebarProvider>
      <StandardsHubContent />
    </SidebarProvider>
  );
};

export default StandardsHub;

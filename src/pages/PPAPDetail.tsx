import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Download, CheckCircle, XCircle, MessageSquare } from "lucide-react";
import { Link, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { HelpOverlay } from "@/components/ui/help-overlay";
import { PPAPOverviewTab } from "@/components/ppap/detail/PPAPOverviewTab";
import { PPAPDocumentsTab } from "@/components/ppap/detail/PPAPDocumentsTab";
import { PPAPApprovalsTab } from "@/components/ppap/detail/PPAPApprovalsTab";
import { PPAPActivityLogTab } from "@/components/ppap/detail/PPAPActivityLogTab";

const ppapDetailHelpContent = [
  {
    title: "PPAP Package Review",
    description: "This screen shows detailed information about a specific PPAP submission, including all required documentation and approval status.",
    tips: [
      "Review all tabs thoroughly before making approval decisions",
      "Check document completeness and quality",
      "Provide clear feedback for any requested changes"
    ]
  },
  {
    title: "Review Process",
    description: "Follow a systematic approach when reviewing PPAP submissions.",
    steps: [
      "Start with the Overview tab to understand the submission scope",
      "Review all documents in the Documents tab",
      "Check approval requirements in the Approvals tab", 
      "Monitor progress through the Activity Log"
    ]
  },
  {
    title: "Making Decisions",
    description: "Use the action buttons to approve, request rework, or add comments to the submission.",
    steps: [
      "Add comments to provide feedback to the supplier",
      "Request rework if documentation needs improvement",
      "Approve only when all requirements are met",
      "Download complete packages for record keeping"
    ]
  }
];

export default function PPAPDetail() {
  const { openMobile } = useSidebar();
  const isMobile = useIsMobile();
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data for the PPAP package
  const ppapData = {
    id: id || "PPAP-ELX-2024-0021",
    partNumber: "X200-PCBA",
    partName: "PCB Assembly - Model X200",
    customer: "Flexonics Ltd.",
    status: "Under Review",
    revision: "Rev B",
    submissionReason: "Engineering Change (new PCB layout)",
    submittedDate: "2024-12-15",
    dueDate: "2024-12-22",
    progress: 60
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Submitted": { variant: "outline" as const, className: "border-blue-200 text-blue-800 bg-blue-50" },
      "Under Review": { variant: "outline" as const, className: "border-purple-200 text-purple-800 bg-purple-50" },
      "Approved": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "Revision Required": { variant: "outline" as const, className: "border-amber-200 text-amber-800 bg-amber-50" },
      "Rejected": { variant: "outline" as const, className: "border-red-200 text-red-800 bg-red-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["Submitted"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="flex h-screen bg-[#f5f6f8] overflow-hidden w-full">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-slate-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden w-full min-w-0">
        <TopBar />
        
        <main className="flex-1 overflow-y-auto p-4 lg:p-6 w-full min-w-0 bg-[#f5f6f8]">
          <div className="w-full min-w-0 max-w-7xl mx-auto space-y-6">
            {/* Page Header - Moved from TopBar */}
            <div className="w-full">
              <h1 className="text-2xl font-semibold text-slate-900">{ppapData.partName}</h1>
              <p className="text-slate-600 text-sm">{ppapData.id} • {ppapData.customer}</p>
            </div>

            {/* Action Buttons Section */}
            <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
              <Link to="/ppap">
                <Button variant="ghost" size="sm" className="text-slate-600 hover:text-slate-900">
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Back to PPAP
                </Button>
              </Link>
              <div className="flex items-center gap-2 flex-wrap">
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Download All
                </Button>
                <Button variant="outline" size="sm">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Add Comment
                </Button>
                <Button variant="outline" size="sm" className="text-amber-600 border-amber-200 hover:bg-amber-50">
                  <XCircle className="mr-2 h-4 w-4" />
                  Request Rework
                </Button>
                <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
              </div>
            </div>

            {/* Status Progress Bar */}
            <Card className="shadow-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium">Status Progress</CardTitle>
                  {getStatusBadge(ppapData.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm text-slate-600">
                    <span>Submitted</span>
                    <span>In Review</span>
                    <span>Approved</span>
                  </div>
                  <Progress value={ppapData.progress} className="h-2" />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>Dec 15, 2024</span>
                    <span>Current</span>
                    <span>Due: Dec 22, 2024</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Main Content Tabs */}
            <Card className="shadow-sm">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <CardHeader className="pb-0">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="documents">Documents</TabsTrigger>
                    <TabsTrigger value="approvals">Approvals</TabsTrigger>
                    <TabsTrigger value="activity">Activity Log</TabsTrigger>
                  </TabsList>
                </CardHeader>
                <CardContent className="pt-6">
                  <TabsContent value="overview" className="mt-0">
                    <PPAPOverviewTab data={ppapData} />
                  </TabsContent>
                  <TabsContent value="documents" className="mt-0">
                    <PPAPDocumentsTab ppapId={ppapData.id} />
                  </TabsContent>
                  <TabsContent value="approvals" className="mt-0">
                    <PPAPApprovalsTab ppapId={ppapData.id} />
                  </TabsContent>
                  <TabsContent value="activity" className="mt-0">
                    <PPAPActivityLogTab ppapId={ppapData.id} />
                  </TabsContent>
                </CardContent>
              </Tabs>
            </Card>
          </div>
        </main>
      </div>

      <HelpOverlay helpContent={ppapDetailHelpContent} moduleName="PPAP Detail" />
    </div>
  );
}


import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ClipboardCheck } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { AuditsDashboard } from "@/components/supplier-quality/audits/AuditsDashboard";

export default function SupplierAudits() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Supplier Quality
              </Button>
            </Link>
            <ClipboardCheck className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Supplier Audits</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <AuditsDashboard />
        </div>
      </div>
    </div>
  );
}

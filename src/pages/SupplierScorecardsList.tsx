
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronLeft, BarChart3, Plus } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { ScorecardList } from "@/components/supplier-quality/scorecard/ScorecardList";
import { mockSupplierScorecards } from "@/data/mockSupplierScorecards";
import { SupplierScorecard } from "@/types/supplierScorecard";

export default function SupplierScorecardsList() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const navigate = useNavigate();
  const [scorecards] = useState<SupplierScorecard[]>(mockSupplierScorecards);

  const handleViewScorecard = (scorecard: SupplierScorecard) => {
    navigate(`/supplier-quality/scorecard/${scorecard.id}`);
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Supplier Quality
              </Button>
            </Link>
            <BarChart3 className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Supplier Scorecards</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <p className="text-gray-600">
                Manage and track supplier performance scorecards
              </p>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Create Scorecard
              </Button>
            </div>
            
            <ScorecardList 
              scorecards={scorecards}
              onViewScorecard={handleViewScorecard}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

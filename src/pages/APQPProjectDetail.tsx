import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { HelpOverlay } from "@/components/ui/help-overlay";
import { ArrowLeft, Calendar, User, Building } from "lucide-react";
import { APQPOverviewTab } from "@/components/apqp/detail/APQPOverviewTab";
import { APQPPhasesTab } from "@/components/apqp/detail/APQPPhasesTab";
import { APQPFMEATab } from "@/components/apqp/detail/APQPFMEATab";
import { APQPControlPlanTab } from "@/components/apqp/detail/APQPControlPlanTab";
import { APQPTimelineTab } from "@/components/apqp/detail/APQPTimelineTab";
import { APQPCommentsTab } from "@/components/apqp/detail/APQPCommentsTab";

const apqpProjectHelpContent = [
  {
    title: "APQP Project Management",
    description: "This screen provides comprehensive management of an individual APQP project, tracking progress through all phases from planning to launch.",
    tips: [
      "Monitor phase progress and deliverable completion regularly",
      "Ensure cross-functional team participation",
      "Update timelines based on actual progress"
    ]
  },
  {
    title: "Phase Management",
    description: "Track progress through the five APQP phases: Planning, Product Design, Process Design, Product & Process Validation, and Launch.",
    steps: [
      "Review phase requirements and deliverables",
      "Monitor completion status of each deliverable",
      "Conduct phase gate reviews before proceeding",
      "Update risk assessments regularly"
    ]
  },
  {
    title: "FMEA Integration",
    description: "Failure Mode and Effects Analysis (FMEA) is integrated into the APQP process to identify and mitigate potential risks.",
    steps: [
      "Conduct Design FMEA during product design phase",
      "Perform Process FMEA during process design phase",
      "Update FMEAs based on validation results",
      "Implement recommended actions"
    ]
  },
  {
    title: "Control Plan Development",
    description: "Develop and maintain control plans that define how the production process will be controlled to meet customer requirements.",
    steps: [
      "Identify critical characteristics",
      "Define control methods and frequency",
      "Specify reaction plans for out-of-control conditions",
      "Update based on process validation results"
    ]
  }
];

export default function APQPProjectDetail() {
  const { id } = useParams();
  const { openMobile } = useSidebar();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState("overview");

  // Mock project data - in real app this would come from API
  const project = {
    id: "APQP-Q5-2025",
    name: "IoT Sensor Hub – Project Q5",
    customer: "Infineon",
    startDate: "2025-05-01",
    endDate: "2025-09-30",
    phase: "Planning",
    status: "Active",
    progress: 15,
    productType: "RF-enabled PCB Assembly",
    teamLead: "Priya Nair",
    priority: "High"
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const getPhaseColor = (phase: string) => {
    const phaseMap = {
      "Planning": "border-blue-200 text-blue-800 bg-blue-50",
      "Design": "border-purple-200 text-purple-800 bg-purple-50",
      "Launch": "border-green-200 text-green-800 bg-green-50",
      "Validation": "border-amber-200 text-amber-800 bg-amber-50",
    };
    return phaseMap[phase as keyof typeof phaseMap] || phaseMap["Planning"];
  };

  const getPriorityColor = (priority: string) => {
    const priorityMap = {
      "High": "border-red-200 text-red-800 bg-red-50",
      "Medium": "border-amber-200 text-amber-800 bg-amber-50",
      "Low": "border-green-200 text-green-800 bg-green-50",
    };
    return priorityMap[priority as keyof typeof priorityMap] || priorityMap["Medium"];
  };

  return (
    <div className="flex h-screen bg-[#f5f6f8] overflow-hidden w-full">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-slate-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden w-full min-w-0">
        <TopBar />
        
        <main className="flex-1 overflow-y-auto p-4 lg:p-6 w-full min-w-0 bg-[#f5f6f8]">
          <div className="w-full min-w-0 max-w-7xl mx-auto space-y-6">
            {/* Page Header - Moved from TopBar */}
            <div className="w-full">
              <h1 className="text-2xl font-semibold text-slate-900">{project.name}</h1>
              <p className="text-slate-600 text-sm">{project.id}</p>
            </div>

            {/* Back Button Section */}
            <div className="flex justify-start">
              <Button variant="ghost" size="sm" onClick={() => window.history.back()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
            </div>

            {/* Project Header */}
            <div className="bg-white rounded-lg border border-slate-200 p-6">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-2">
                  <div className="flex gap-2 mb-3">
                    <Badge variant="outline" className={getPhaseColor(project.phase)}>
                      {project.phase}
                    </Badge>
                    <Badge variant="outline" className={getPriorityColor(project.priority)}>
                      {project.priority}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <Building className="h-4 w-4" />
                      <span>{project.customer}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <User className="h-4 w-4" />
                      <span>Lead: {project.teamLead}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-600">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(project.startDate)} - {formatDate(project.endDate)}</span>
                    </div>
                  </div>
                </div>
                <div className="lg:col-span-2">
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-700">Progress</span>
                        <span className="text-sm text-slate-600">{project.progress}%</span>
                      </div>
                      <Progress value={project.progress} className="h-2" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-slate-700">Product Type</span>
                      <p className="text-sm text-slate-600">{project.productType}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="phases">Phases</TabsTrigger>
                <TabsTrigger value="fmea">FMEA</TabsTrigger>
                <TabsTrigger value="control">Control Plan</TabsTrigger>
                <TabsTrigger value="timeline">Timeline</TabsTrigger>
                <TabsTrigger value="comments">Comments</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-6">
                <APQPOverviewTab project={project} />
              </TabsContent>

              <TabsContent value="phases" className="mt-6">
                <APQPPhasesTab project={project} />
              </TabsContent>

              <TabsContent value="fmea" className="mt-6">
                <APQPFMEATab project={project} />
              </TabsContent>

              <TabsContent value="control" className="mt-6">
                <APQPControlPlanTab project={project} />
              </TabsContent>

              <TabsContent value="timeline" className="mt-6">
                <APQPTimelineTab project={project} />
              </TabsContent>

              <TabsContent value="comments" className="mt-6">
                <APQPCommentsTab project={project} />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      <HelpOverlay helpContent={apqpProjectHelpContent} moduleName="APQP Project" />
    </div>
  );
}

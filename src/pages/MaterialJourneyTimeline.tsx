
import React from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Route } from "lucide-react";
import { MaterialJourneyTimeline } from "@/components/supplier-quality/traceability/MaterialJourneyTimeline";

export default function MaterialJourneyTimelinePage() {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const [searchParams] = useSearchParams();
  const materialId = searchParams.get('materialId');

  return (
    <div className="w-screen min-h-screen bg-gray-50 font-inter overflow-hidden flex">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <Route className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold">Material Journey Timeline</h1>
              <p className="text-gray-500 text-sm">Interactive supply chain tracking</p>
            </div>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full bg-gray-50">
          <div className="w-full max-w-none">
            <div className="max-w-7xl mx-auto px-4 md:px-6 py-4 lg:py-6">
              <div className="mb-4">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => navigate(-1)}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Back
                </Button>
              </div>
              
              <MaterialJourneyTimeline materialId={materialId || undefined} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

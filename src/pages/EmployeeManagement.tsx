
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";

const EmployeeManagement = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="w-screen min-h-screen bg-gray-50 font-inter overflow-hidden flex">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <h1 className="text-2xl font-bold">Employee Management</h1>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 md:px-6 py-4 lg:py-6">
            <div className="text-center py-8">
              <h2 className="text-xl font-semibold">Employee Management</h2>
              <p className="text-gray-600">Coming soon...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeManagement;

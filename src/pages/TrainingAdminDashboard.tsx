
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  BookOpen, 
  Users, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  TrendingUp,
  BarChart3,
  PieChart,
  Calendar,
  Filter,
  RefreshCw
} from "lucide-react";
import { Bar<PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car<PERSON>ianGrid, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Pie, ResponsiveContainer } from "recharts";
import { RecertificationManagement } from "@/components/training/RecertificationManagement";

interface TrainingData {
  name: string;
  value: number;
  color: string;
}

interface DepartmentData {
  name: string;
  assigned: number;
  completed: number;
  overdue: number;
  compliance: number;
}

interface ProgressData {
  month: string;
  completed: number;
  assigned: number;
}

const TrainingAdminDashboardContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [selectedRole, setSelectedRole] = useState<string>("all");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [dateRange, setDateRange] = useState<string>("last-30-days");

  const departmentData = [
    { name: "Quality Control", assigned: 45, completed: 38, overdue: 2, compliance: 85 },
    { name: "Production", assigned: 78, completed: 65, overdue: 8, compliance: 78 },
    { name: "Laboratory", assigned: 32, completed: 28, overdue: 1, compliance: 91 },
    { name: "Warehouse", assigned: 25, completed: 20, overdue: 3, compliance: 80 },
    { name: "Engineering", assigned: 18, completed: 16, overdue: 1, compliance: 89 }
  ];

  const trainingTypeData = [
    { name: "Safety", value: 35, color: "#ef4444" },
    { name: "Quality", value: 28, color: "#3b82f6" },
    { name: "Compliance", value: 22, color: "#f59e0b" },
    { name: "Technical", value: 15, color: "#10b981" }
  ];

  const progressData = [
    { month: "Jul", completed: 32, assigned: 45 },
    { month: "Aug", completed: 41, assigned: 52 },
    { month: "Sep", completed: 38, assigned: 48 },
    { month: "Oct", completed: 45, assigned: 58 },
    { month: "Nov", completed: 52, assigned: 65 },
    { month: "Dec", completed: 48, assigned: 60 }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <BookOpen className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Training Admin Dashboard</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview" className="gap-2">
                <BarChart3 className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="analytics" className="gap-2">
                <PieChart className="h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="progress" className="gap-2">
                <TrendingUp className="h-4 w-4" />
                Progress
              </TabsTrigger>
              <TabsTrigger value="recertification" className="gap-2">
                <RefreshCw className="h-4 w-4" />
                Recertification
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Filters */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                    <div className="flex items-center gap-2">
                      <Filter className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium">Filters:</span>
                    </div>
                    
                    <div className="flex flex-col md:flex-row gap-4 flex-1">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Role</label>
                        <Select value={selectedRole} onValueChange={setSelectedRole}>
                          <SelectTrigger className="w-48">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Roles</SelectItem>
                            <SelectItem value="quality-manager">Quality Manager</SelectItem>
                            <SelectItem value="production-staff">Production Staff</SelectItem>
                            <SelectItem value="lab-technician">Lab Technician</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-2 block">Department</label>
                        <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                          <SelectTrigger className="w-48">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Departments</SelectItem>
                            <SelectItem value="quality-control">Quality Control</SelectItem>
                            <SelectItem value="production">Production</SelectItem>
                            <SelectItem value="laboratory">Laboratory</SelectItem>
                            <SelectItem value="warehouse">Warehouse</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-2 block">Date Range</label>
                        <Select value={dateRange} onValueChange={setDateRange}>
                          <SelectTrigger className="w-48">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="last-7-days">Last 7 days</SelectItem>
                            <SelectItem value="last-30-days">Last 30 days</SelectItem>
                            <SelectItem value="last-90-days">Last 90 days</SelectItem>
                            <SelectItem value="last-year">Last year</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Total Assigned</p>
                        <p className="text-3xl font-bold text-blue-600">1,247</p>
                        <p className="text-xs text-green-600 mt-1">+12% vs last month</p>
                      </div>
                      <BookOpen className="h-10 w-10 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Completed</p>
                        <p className="text-3xl font-bold text-green-600">1,034</p>
                        <p className="text-xs text-green-600 mt-1">+8% vs last month</p>
                      </div>
                      <CheckCircle className="h-10 w-10 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Overdue</p>
                        <p className="text-3xl font-bold text-red-600">67</p>
                        <p className="text-xs text-red-600 mt-1">-15% vs last month</p>
                      </div>
                      <AlertTriangle className="h-10 w-10 text-red-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Avg Time Spent</p>
                        <p className="text-3xl font-bold text-purple-600">2.4h</p>
                        <p className="text-xs text-green-600 mt-1">-5% vs last month</p>
                      </div>
                      <Clock className="h-10 w-10 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Compliance Score</p>
                        <p className="text-3xl font-bold text-indigo-600">87%</p>
                        <p className="text-xs text-green-600 mt-1">+3% vs last month</p>
                      </div>
                      <TrendingUp className="h-10 w-10 text-indigo-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Department Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Department Compliance Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={departmentData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="assigned" fill="#3b82f6" name="Assigned" />
                      <Bar dataKey="completed" fill="#10b981" name="Completed" />
                      <Bar dataKey="overdue" fill="#ef4444" name="Overdue" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Training Types Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Training Types Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          data={trainingTypeData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, value }) => `${name}: ${value}%`}
                        >
                          {trainingTypeData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                {/* Compliance Scores by Department */}
                <Card>
                  <CardHeader>
                    <CardTitle>Compliance Scores by Department</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {departmentData.map((dept) => (
                        <div key={dept.name} className="flex items-center justify-between">
                          <span className="font-medium">{dept.name}</span>
                          <div className="flex items-center gap-3">
                            <div className="w-32 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  dept.compliance >= 90 ? 'bg-green-500' :
                                  dept.compliance >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${dept.compliance}%` }}
                              />
                            </div>
                            <Badge variant={dept.compliance >= 90 ? "default" : dept.compliance >= 80 ? "secondary" : "destructive"}>
                              {dept.compliance}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              {/* Monthly Progress */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Training Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={progressData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="assigned" fill="#94a3b8" name="Assigned" />
                      <Bar dataKey="completed" fill="#10b981" name="Completed" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recertification">
              <RecertificationManagement />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

const TrainingAdminDashboard = () => {
  return <TrainingAdminDashboardContent />;
};

export default TrainingAdminDashboard;

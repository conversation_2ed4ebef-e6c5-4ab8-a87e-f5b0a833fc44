import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  FileText, 
  Eye, 
  MessageSquare,
  HelpCircle,
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { CustomerPortalHelp } from "@/components/customer-portal/CustomerPortalHelp";
import { CustomerPortalTable } from "@/components/customer-portal/CustomerPortalTable";
import { CustomerPortalFilters } from "@/components/customer-portal/CustomerPortalFilters";
import { mockCustomers, Customer } from "@/data/mockCustomers";
import { toast } from "sonner";

export default function CustomerPortal() {
  const [showHelp, setShowHelp] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [industryFilter, setIndustryFilter] = useState("");
  const [auditStatusFilter, setAuditStatusFilter] = useState("");
  const navigate = useNavigate();

  // Filter customers based on search and filters
  const filteredCustomers = useMemo(() => {
    return mockCustomers.filter((customer) => {
      const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          customer.qaContact.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          customer.industry.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesStatus = !statusFilter || statusFilter === "all" || customer.status === statusFilter;
      const matchesIndustry = !industryFilter || industryFilter === "all" || customer.industry === industryFilter;
      const matchesAuditStatus = !auditStatusFilter || auditStatusFilter === "all" || customer.auditStatus === auditStatusFilter;

      return matchesSearch && matchesStatus && matchesIndustry && matchesAuditStatus;
    });
  }, [searchQuery, statusFilter, industryFilter, auditStatusFilter]);

  // Calculate stats
  const stats = useMemo(() => {
    const totalCustomers = mockCustomers.length;
    const activePortals = mockCustomers.filter(c => c.status === "Active").length;
    const totalDocuments = mockCustomers.reduce((sum, c) => sum + c.sharedDocuments, 0);
    const avgComplianceScore = Math.round(mockCustomers.reduce((sum, c) => sum + c.complianceScore, 0) / totalCustomers);
    const passedAudits = mockCustomers.filter(c => c.auditStatus === "Passed").length;
    const pendingAudits = mockCustomers.filter(c => c.auditStatus === "Pending" || c.auditStatus === "Scheduled").length;

    return {
      totalCustomers,
      activePortals,
      totalDocuments,
      avgComplianceScore,
      passedAudits,
      pendingAudits
    };
  }, []);

  const handleManageCustomer = (customerId: string) => {
    console.log("Managing customer:", customerId);
    const customer = mockCustomers.find(c => c.id === customerId);
    
    // Mock management data
    const mockManagementData = {
      customerInfo: customer,
      activeUsers: Math.floor(Math.random() * 15) + 5,
      documentsShared: customer?.sharedDocuments || 0,
      recentActivity: [
        "User access granted to John Doe",
        "Document QM-2024-001 shared",
        "Audit report reviewed",
        "Compliance score updated"
      ],
      permissions: {
        viewDocuments: true,
        downloadDocuments: true,
        commentOnDocuments: false,
        auditAccess: true
      }
    };

    console.log("Mock management data:", mockManagementData);
    toast.success(`Opening customer management for ${customer?.name || customerId}`);
    navigate(`/customer-compliance/${customerId}`);
  };

  const handleViewPortal = (customerId: string) => {
    console.log("Viewing portal for customer:", customerId);
    const customer = mockCustomers.find(c => c.id === customerId);
    
    // Mock portal view data
    const mockPortalData = {
      customerInfo: customer,
      availableDocuments: [
        { id: 1, name: "Quality Manual v2.1", type: "PDF", lastUpdated: "2024-01-15" },
        { id: 2, name: "Process Control Plan", type: "Excel", lastUpdated: "2024-01-10" },
        { id: 3, name: "AS9100 Certificate", type: "PDF", lastUpdated: "2024-01-05" },
        { id: 4, name: "Supplier Requirements", type: "Word", lastUpdated: "2023-12-20" }
      ],
      recentAudits: [
        { date: "2024-01-15", type: "Internal Audit", status: "Passed" },
        { date: "2023-12-10", type: "Customer Audit", status: "Minor Finding" }
      ],
      messages: [
        { from: "QA Team", subject: "New quality manual available", date: "2024-01-16" },
        { from: "Compliance", subject: "Audit findings response required", date: "2024-01-12" }
      ]
    };

    console.log("Mock portal data:", mockPortalData);
    toast.success(`Opening customer workspace for ${customer?.name || customerId}`);
    window.open(`/customer-workspace/${customerId}`, '_blank');
  };

  const handleAddCustomer = () => {
    console.log("Adding new customer");
    toast.success("Add customer functionality coming soon!");
  };

  const handleExportData = () => {
    console.log("Exporting customer data");
    toast.success("Export functionality coming soon!");
  };

  return (
    <StandardPageLayout title="Customer Portal">
      <div className="space-y-6 p-6">
        {/* Header with Help */}
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-4">
            <div>
              <p className="text-gray-600">Manage customer access to documents and compliance information</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(true)}
              className="gap-2"
            >
              <HelpCircle className="h-4 w-4" />
              How it works
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Customers</p>
                  <p className="text-3xl font-bold text-blue-600">{stats.totalCustomers}</p>
                </div>
                <Users className="h-10 w-10 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Portals</p>
                  <p className="text-3xl font-bold text-green-600">{stats.activePortals}</p>
                </div>
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Shared Documents</p>
                  <p className="text-3xl font-bold text-purple-600">{stats.totalDocuments}</p>
                </div>
                <FileText className="h-10 w-10 text-purple-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Avg Compliance</p>
                  <p className="text-3xl font-bold text-orange-600">{stats.avgComplianceScore}%</p>
                </div>
                <TrendingUp className="h-10 w-10 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <CustomerPortalFilters
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              statusFilter={statusFilter}
              setStatusFilter={setStatusFilter}
              industryFilter={industryFilter}
              setIndustryFilter={setIndustryFilter}
              auditStatusFilter={auditStatusFilter}
              setAuditStatusFilter={setAuditStatusFilter}
              onAddCustomer={handleAddCustomer}
              onExportData={handleExportData}
            />
          </CardContent>
        </Card>

        {/* Customer Table */}
        <CustomerPortalTable
          customers={filteredCustomers}
          onManageCustomer={handleManageCustomer}
          onViewPortal={handleViewPortal}
        />

        {/* Results Summary */}
        {filteredCustomers.length !== mockCustomers.length && (
          <Card>
            <CardContent className="p-4">
              <p className="text-sm text-gray-600">
                Showing {filteredCustomers.length} of {mockCustomers.length} customers
                {(searchQuery || statusFilter || industryFilter || auditStatusFilter) && (
                  <span className="ml-2 text-blue-600">
                    (filtered results)
                  </span>
                )}
              </p>
            </CardContent>
          </Card>
        )}

        <CustomerPortalHelp isOpen={showHelp} onClose={() => setShowHelp(false)} />
      </div>
    </StandardPageLayout>
  );
}


import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Bell, Mail, MessageSquare, Save, Eye, AlertTriangle } from "lucide-react";

interface NotificationSettings {
  sevenDaysBefore: {
    enabled: boolean;
    email: boolean;
    inApp: boolean;
  };
  onDueDate: {
    enabled: boolean;
    email: boolean;
    inApp: boolean;
  };
  afterOverdue: {
    enabled: boolean;
    email: boolean;
    inApp: boolean;
    frequency: string;
  };
  escalation: {
    enabled: boolean;
    days: string;
    recipients: string[];
  };
}

const TrainingNotificationsContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  
  const [settings, setSettings] = useState<NotificationSettings>({
    sevenDaysBefore: { enabled: true, email: true, inApp: true },
    onDueDate: { enabled: true, email: true, inApp: true },
    afterOverdue: { enabled: true, email: true, inApp: false, frequency: "daily" },
    escalation: { enabled: false, days: "7", recipients: [] }
  });

  const [previewType, setPreviewType] = useState<"reminder" | "overdue" | "escalation">("reminder");

  const updateSetting = (section: keyof NotificationSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const emailTemplates = {
    reminder: {
      subject: "Training Reminder: ISO 9001 Refresher Due Soon",
      body: `Dear John Smith,

This is a friendly reminder that your assigned training "ISO 9001 Refresher" is due in 7 days.

Training Details:
- Title: ISO 9001 Refresher
- Type: Video
- Estimated Duration: 120 minutes
- Due Date: December 15, 2024

Please complete your training by the due date to maintain compliance.

Click here to access your training: [Training Portal Link]

If you have any questions, please contact your training administrator.

Best regards,
Training Team`
    },
    overdue: {
      subject: "OVERDUE: Training Assignment Requires Immediate Attention",
      body: `Dear John Smith,

Your assigned training "ISO 9001 Refresher" is now OVERDUE.

Training Details:
- Title: ISO 9001 Refresher
- Due Date: December 15, 2024 (3 days ago)
- Status: Overdue

Please complete this training immediately to maintain compliance. Failure to complete required training may result in escalation to your manager.

Click here to access your training: [Training Portal Link]

For assistance, please contact your training administrator immediately.

Urgent regards,
Training Team`
    },
    escalation: {
      subject: "Training Compliance Escalation: Employee Overdue",
      body: `Dear Manager,

The following employee has overdue training assignments that require your attention:

Employee: John Smith (<EMAIL>)
Department: Quality Control

Overdue Training:
- ISO 9001 Refresher (Due: Dec 15, 2024 - 7 days overdue)

This training is critical for compliance and requires immediate completion. Please follow up with the employee to ensure completion.

Training Portal: [Manager Dashboard Link]

Please contact the training team if you need assistance.

Best regards,
Training Administration Team`
    }
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Bell className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">Training Notifications</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Settings Panel */}
            <div className="space-y-6">
              
              {/* 7 Days Before Reminder */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-blue-600" />
                    7 Days Before Due Date
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Enable reminder</span>
                    <Switch 
                      checked={settings.sevenDaysBefore.enabled}
                      onCheckedChange={(checked) => updateSetting('sevenDaysBefore', 'enabled', checked)}
                    />
                  </div>
                  
                  {settings.sevenDaysBefore.enabled && (
                    <div className="space-y-3 pl-4 border-l-2 border-blue-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <span>Email notification</span>
                        </div>
                        <Switch 
                          checked={settings.sevenDaysBefore.email}
                          onCheckedChange={(checked) => updateSetting('sevenDaysBefore', 'email', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          <span>In-app notification</span>
                        </div>
                        <Switch 
                          checked={settings.sevenDaysBefore.inApp}
                          onCheckedChange={(checked) => updateSetting('sevenDaysBefore', 'inApp', checked)}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Due Date Reminder */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    On Due Date
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Enable reminder</span>
                    <Switch 
                      checked={settings.onDueDate.enabled}
                      onCheckedChange={(checked) => updateSetting('onDueDate', 'enabled', checked)}
                    />
                  </div>
                  
                  {settings.onDueDate.enabled && (
                    <div className="space-y-3 pl-4 border-l-2 border-orange-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <span>Email notification</span>
                        </div>
                        <Switch 
                          checked={settings.onDueDate.email}
                          onCheckedChange={(checked) => updateSetting('onDueDate', 'email', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          <span>In-app notification</span>
                        </div>
                        <Switch 
                          checked={settings.onDueDate.inApp}
                          onCheckedChange={(checked) => updateSetting('onDueDate', 'inApp', checked)}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Overdue Reminders */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    After Overdue
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Enable overdue reminders</span>
                    <Switch 
                      checked={settings.afterOverdue.enabled}
                      onCheckedChange={(checked) => updateSetting('afterOverdue', 'enabled', checked)}
                    />
                  </div>
                  
                  {settings.afterOverdue.enabled && (
                    <div className="space-y-3 pl-4 border-l-2 border-red-200">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Reminder frequency</label>
                        <Select 
                          value={settings.afterOverdue.frequency} 
                          onValueChange={(value) => updateSetting('afterOverdue', 'frequency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="every-2-days">Every 2 days</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          <span>Email notification</span>
                        </div>
                        <Switch 
                          checked={settings.afterOverdue.email}
                          onCheckedChange={(checked) => updateSetting('afterOverdue', 'email', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          <span>In-app notification</span>
                        </div>
                        <Switch 
                          checked={settings.afterOverdue.inApp}
                          onCheckedChange={(checked) => updateSetting('afterOverdue', 'inApp', checked)}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Escalation Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-purple-600" />
                    Manager Escalation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Enable escalation to managers</span>
                    <Switch 
                      checked={settings.escalation.enabled}
                      onCheckedChange={(checked) => updateSetting('escalation', 'enabled', checked)}
                    />
                  </div>
                  
                  {settings.escalation.enabled && (
                    <div className="space-y-3 pl-4 border-l-2 border-purple-200">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Escalate after (days overdue)</label>
                        <Select 
                          value={settings.escalation.days} 
                          onValueChange={(value) => updateSetting('escalation', 'days', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="3">3 days</SelectItem>
                            <SelectItem value="7">7 days</SelectItem>
                            <SelectItem value="14">14 days</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex gap-3">
                <Button className="gap-2">
                  <Save className="h-4 w-4" />
                  Save Settings
                </Button>
                <Button variant="outline">
                  Reset to Default
                </Button>
              </div>
            </div>

            {/* Email Preview Panel */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Email Template Preview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Preview type</label>
                    <Select value={previewType} onValueChange={(value: any) => setPreviewType(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="reminder">7-Day Reminder</SelectItem>
                        <SelectItem value="overdue">Overdue Notice</SelectItem>
                        <SelectItem value="escalation">Manager Escalation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="mb-3">
                      <Badge variant="outline" className="mb-2">Subject Line</Badge>
                      <p className="font-medium">{emailTemplates[previewType].subject}</p>
                    </div>
                    
                    <div>
                      <Badge variant="outline" className="mb-2">Email Body</Badge>
                      <Textarea 
                        value={emailTemplates[previewType].body}
                        readOnly
                        className="min-h-[300px] text-sm bg-white"
                      />
                    </div>
                  </div>

                  <div className="text-xs text-gray-500">
                    <p>Variables available: {`{employee_name}, {training_title}, {due_date}, {days_overdue}, {manager_name}`}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const TrainingNotifications = () => {
  return <TrainingNotificationsContent />;
};

export default TrainingNotifications;

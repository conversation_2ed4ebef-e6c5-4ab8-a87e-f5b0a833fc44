
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, PieChart, Shield, FileCheck, AlertCircle } from "lucide-react";
import { Link } from "react-router-dom";

export default function PPAPComplianceAnalytics() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <PieChart className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold">PPAP Compliance Analytics</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="mb-4">
              <Link to="/ppap?tab=reports">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to PPAP Reports
                </Button>
              </Link>
            </div>

            {/* Compliance Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-500" />
                    Overall Compliance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">94.2%</div>
                  <p className="text-sm text-gray-600">Above target (90%)</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileCheck className="h-5 w-5 text-blue-500" />
                    Complete Submissions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">119</div>
                  <p className="text-sm text-gray-600">out of 127 total</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-orange-500" />
                    Non-Conformances
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-orange-600">8</div>
                  <p className="text-sm text-gray-600">Require action</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5 text-purple-500" />
                    On-Time Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">91%</div>
                  <p className="text-sm text-gray-600">Within due dates</p>
                </CardContent>
              </Card>
            </div>

            {/* Compliance by Level */}
            <Card>
              <CardHeader>
                <CardTitle>Compliance Rate by PPAP Level</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Level 1</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-3">
                        <div className="bg-green-500 h-3 rounded-full" style={{ width: '100%' }}></div>
                      </div>
                      <span className="text-sm font-medium w-12">100%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Level 2</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-3">
                        <div className="bg-green-500 h-3 rounded-full" style={{ width: '98%' }}></div>
                      </div>
                      <span className="text-sm font-medium w-12">98%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Level 3</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-3">
                        <div className="bg-green-500 h-3 rounded-full" style={{ width: '95%' }}></div>
                      </div>
                      <span className="text-sm font-medium w-12">95%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Level 4</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-3">
                        <div className="bg-yellow-500 h-3 rounded-full" style={{ width: '87%' }}></div>
                      </div>
                      <span className="text-sm font-medium w-12">87%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Level 5</span>
                    <div className="flex items-center gap-2">
                      <div className="w-32 bg-gray-200 rounded-full h-3">
                        <div className="bg-orange-500 h-3 rounded-full" style={{ width: '82%' }}></div>
                      </div>
                      <span className="text-sm font-medium w-12">82%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Document Compliance */}
            <Card>
              <CardHeader>
                <CardTitle>Document Compliance Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-medium">Required Documents</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Design Records</span>
                        <span className="text-green-600">100%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Process Flow Diagram</span>
                        <span className="text-green-600">98%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Control Plan</span>
                        <span className="text-green-600">96%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dimensional Results</span>
                        <span className="text-yellow-600">89%</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <h4 className="font-medium">Additional Documents</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Design FMEA</span>
                        <span className="text-green-600">92%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Process FMEA</span>
                        <span className="text-yellow-600">87%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>MSA Studies</span>
                        <span className="text-orange-600">78%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Customer Approval</span>
                        <span className="text-green-600">94%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

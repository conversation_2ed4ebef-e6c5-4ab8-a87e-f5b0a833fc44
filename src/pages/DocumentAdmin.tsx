
import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const DocumentAdmin: React.FC = () => {
  const navigate = useNavigate();

  return (
    <StandardPageLayout title="Document Administration">
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <div className="mb-6">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => navigate("/")}
          >
            <ChevronLeft className="h-4 w-4" />
            Back to Documents
          </Button>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 w-full">
          <h2 className="text-2xl font-semibold mb-6">Document Administration Settings</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
            <div className="border rounded-lg p-4 hover:border-primary transition-colors w-full">
              <h3 className="text-lg font-medium mb-2">User Permissions</h3>
              <p className="text-gray-500 mb-4">Manage who can view, edit and approve documents</p>
              <Button>Configure</Button>
            </div>
            
            <div className="border rounded-lg p-4 hover:border-primary transition-colors w-full">
              <h3 className="text-lg font-medium mb-2">Document Templates</h3>
              <p className="text-gray-500 mb-4">Create and manage document templates</p>
              <Button>Configure</Button>
            </div>
            
            <div className="border rounded-lg p-4 hover:border-primary transition-colors w-full">
              <h3 className="text-lg font-medium mb-2">Workflow Settings</h3>
              <p className="text-gray-500 mb-4">Set up approval workflows and notifications</p>
              <Button>Configure</Button>
            </div>
            
            <div className="border rounded-lg p-4 hover:border-primary transition-colors w-full">
              <h3 className="text-lg font-medium mb-2">System Logs</h3>
              <p className="text-gray-500 mb-4">View document access and change history</p>
              <Button>Configure</Button>
            </div>
          </div>
        </div>
      </div>
    </StandardPageLayout>
  );
};

export default DocumentAdmin;

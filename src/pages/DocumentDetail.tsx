
import React from "react";
import { useParams } from "react-router-dom";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { DocumentViewer } from "@/components/document-hub/document-detail/DocumentViewer";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";

const DocumentDetailContent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <h1 className="text-2xl font-bold">Document Details</h1>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <DocumentViewer documentId={id || "1"} />
        </div>
      </div>
    </div>
  );
};

const DocumentDetail: React.FC = () => {
  return (
    <SidebarProvider>
      <DocumentDetailContent />
    </SidebarProvider>
  );
};

export default DocumentDetail;

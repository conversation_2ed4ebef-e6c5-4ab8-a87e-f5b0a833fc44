
import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, TrendingUp, TrendingDown, Minus } from "lucide-react";
import { Link } from "react-router-dom";

export default function PPAPTrendAnalysis() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center gap-3">
            <TrendingUp className="h-6 w-6 text-orange-600" />
            <h1 className="text-2xl font-bold">PPAP Trend Analysis</h1>
          </div>
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="mb-4">
              <Link to="/ppap?tab=reports">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to PPAP Reports
                </Button>
              </Link>
            </div>

            {/* Trend Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    Approval Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">↑ 8%</div>
                  <p className="text-sm text-gray-600">Last 3 months improvement</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingDown className="h-5 w-5 text-red-500" />
                    Review Time
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">↓ 15%</div>
                  <p className="text-sm text-gray-600">Faster processing times</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Minus className="h-5 w-5 text-blue-500" />
                    Quality Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">→ 0%</div>
                  <p className="text-sm text-gray-600">Stable performance</p>
                </CardContent>
              </Card>
            </div>

            {/* Monthly Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Monthly PPAP Submission Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-6 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold">Jan</div>
                    <div className="text-blue-600">45</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Feb</div>
                    <div className="text-blue-600">52</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Mar</div>
                    <div className="text-blue-600">48</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">Apr</div>
                    <div className="text-blue-600">61</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold">May</div>
                    <div className="text-blue-600">58</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-blue-600">Jun</div>
                    <div className="text-blue-600 font-bold">67</div>
                    <div className="text-xs text-gray-500">Submissions</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Supplier Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Supplier Performance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">ABC Manufacturing</span>
                      <p className="text-sm text-gray-600">Monthly trend</p>
                    </div>
                    <div className="flex items-center gap-2 text-green-600">
                      <TrendingUp className="h-4 w-4" />
                      <span className="text-sm font-medium">+12% improvement</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">XYZ Components</span>
                      <p className="text-sm text-gray-600">Monthly trend</p>
                    </div>
                    <div className="flex items-center gap-2 text-green-600">
                      <TrendingUp className="h-4 w-4" />
                      <span className="text-sm font-medium">+8% improvement</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">Precision Parts Inc</span>
                      <p className="text-sm text-gray-600">Monthly trend</p>
                    </div>
                    <div className="flex items-center gap-2 text-red-600">
                      <TrendingDown className="h-4 w-4" />
                      <span className="text-sm font-medium">-5% decline</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">FluidTech Systems</span>
                      <p className="text-sm text-gray-600">Monthly trend</p>
                    </div>
                    <div className="flex items-center gap-2 text-blue-600">
                      <Minus className="h-4 w-4" />
                      <span className="text-sm font-medium">Stable performance</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

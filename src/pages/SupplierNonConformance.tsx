
import React from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, AlertTriangle } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { NCTable } from "@/components/supplier-quality/non-conformance/NCTable";

export default function SupplierNonConformance() {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  // Mock non-conformance data matching SupplierNonConformance interface
  const nonConformances = [
    {
      id: "NC-001",
      ncNumber: "001",
      supplierName: "Acme Corp",
      issueDescription: "Material Defect",
      status: "Open" as const,
      severity: "Major" as const,
      capaLinked: true,
      dateReported: "2024-01-15",
      dueDate: "2024-02-15"
    }
  ];

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Link to="/supplier-quality">
              <Button variant="ghost" size="sm" className="gap-2">
                <ChevronLeft className="h-4 w-4" />
                Back to Supplier Quality
              </Button>
            </Link>
            <AlertTriangle className="h-6 w-6 text-red-600" />
            <h1 className="text-2xl font-bold">Non-Conformance</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-y-auto p-4 md:p-6">
          <NCTable nonConformances={nonConformances} />
        </div>
      </div>
    </div>
  );
}

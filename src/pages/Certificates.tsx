
import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FileCheck, Upload, Download, Eye, AlertCircle, CheckCircle, Clock } from "lucide-react";
import { Menu } from "lucide-react";

const CertificatesHeader = () => {
  const isMobile = useIsMobile();
  const { toggleSidebar } = useSidebar();
  
  return (
    <div className="flex flex-col w-full">
      <div className="flex justify-between items-center w-full">
        <div className="flex items-center">
          {isMobile && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleSidebar}
              className="mr-2"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          )}
          <div className="flex items-center gap-3">
            <FileCheck className="h-6 w-6 text-cyan-600" />
            <div>
              <h1 className="text-2xl font-bold">Certificates Management</h1>
              {!isMobile && (
                <p className="text-gray-500 text-sm">Track and manage supplier certifications</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CertificatesContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();
  const [activeTab, setActiveTab] = useState("all");

  const certificates = [
    {
      id: 1,
      name: "ISO 9001:2015 Quality Management",
      supplier: "Advanced Manufacturing Corp",
      issueDate: "2024-01-15",
      expiryDate: "2027-01-15",
      status: "active",
      type: "Quality"
    },
    {
      id: 2,
      name: "AS9100D Aerospace Quality",
      supplier: "Precision Aerospace Ltd",
      issueDate: "2023-06-10",
      expiryDate: "2025-06-10",
      status: "expiring",
      type: "Aerospace"
    },
    {
      id: 3,
      name: "ISO 14001:2015 Environmental",
      supplier: "Green Tech Solutions",
      issueDate: "2023-03-20",
      expiryDate: "2024-12-20",
      status: "expired",
      type: "Environmental"
    },
    {
      id: 4,
      name: "OHSAS 18001 Safety Management",
      supplier: "Safe Work Industries",
      issueDate: "2024-02-28",
      expiryDate: "2027-02-28",
      status: "active",
      type: "Safety"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case "expiring":
        return <Badge className="bg-amber-100 text-amber-800"><Clock className="h-3 w-3 mr-1" />Expiring Soon</Badge>;
      case "expired":
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <CertificatesHeader />
        </TopBar>
        
        <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
          <div className="max-w-7xl mx-auto space-y-6 w-full">
            {/* Upload Certificate Button moved to body */}
            <div className="flex justify-end mb-4">
              <Button className="gap-2">
                <Upload className="h-4 w-4" />
                Upload Certificate
              </Button>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Certificates</p>
                      <p className="text-2xl font-bold">{certificates.length}</p>
                    </div>
                    <FileCheck className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Active</p>
                      <p className="text-2xl font-bold text-green-600">
                        {certificates.filter(c => c.status === "active").length}
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Expiring Soon</p>
                      <p className="text-2xl font-bold text-amber-600">
                        {certificates.filter(c => c.status === "expiring").length}
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-amber-600" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Expired</p>
                      <p className="text-2xl font-bold text-red-600">
                        {certificates.filter(c => c.status === "expired").length}
                      </p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Certificates Table */}
            <Card>
              <CardHeader>
                <CardTitle>Supplier Certificates</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Certificate Name</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Issue Date</TableHead>
                      <TableHead>Expiry Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {certificates.map((cert) => (
                      <TableRow key={cert.id}>
                        <TableCell className="font-medium">{cert.name}</TableCell>
                        <TableCell>{cert.supplier}</TableCell>
                        <TableCell>{cert.type}</TableCell>
                        <TableCell>{cert.issueDate}</TableCell>
                        <TableCell>{cert.expiryDate}</TableCell>
                        <TableCell>{getStatusBadge(cert.status)}</TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

const Certificates = () => {
  return (
    <SidebarProvider>
      <CertificatesContent />
    </SidebarProvider>
  );
};

export default Certificates;

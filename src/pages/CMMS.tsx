
import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { Wrench } from "lucide-react";

const CMMS = () => {
  const headerContent = (
    <div className="flex items-center gap-3">
      <div className="p-2 bg-blue-100 rounded-lg">
        <Wrench className="h-6 w-6 text-blue-600" />
      </div>
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">CMMS</h1>
        <p className="text-sm text-gray-600">Computerized Maintenance Management System</p>
      </div>
    </div>
  );

  return (
    <StandardPageLayout headerContent={headerContent}>
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <div className="text-center py-8 text-gray-500">
          <Wrench className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>CMMS features coming soon</p>
        </div>
      </div>
    </StandardPageLayout>
  );
};

export default CMMS;


import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Settings, RefreshCw, Clock } from 'lucide-react';
import { SupportShareButton } from '@/components/error-handling/SupportShareButton';

const MaintenanceMode: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <Settings className="h-16 w-16 mx-auto text-blue-500 mb-4 animate-spin" />
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Under Maintenance</h1>
          <h2 className="text-lg font-semibold text-gray-700 mb-4">System Update in Progress</h2>
          <p className="text-gray-600 mb-6">
            We're currently performing scheduled maintenance to improve your experience. 
            The system will be back online shortly.
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={() => window.location.reload()}
              className="w-full gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Check Again
            </Button>

            <SupportShareButton
              errorDetails={{
                url: window.location.href,
                timestamp: new Date().toISOString(),
                additionalContext: "User accessed system during maintenance mode"
              }}
              title="Contact Support"
              variant="outline"
              size="sm"
            />
          </div>

          <div className="mt-6 flex items-center justify-center gap-2 text-sm text-gray-500">
            <Clock className="h-4 w-4" />
            Estimated completion: 30 minutes
          </div>

          <div className="mt-4 text-xs text-gray-400">
            For urgent issues, please contact your system administrator
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MaintenanceMode;

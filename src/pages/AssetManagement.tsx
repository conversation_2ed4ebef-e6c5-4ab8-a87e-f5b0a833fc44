
import React from "react";
import { Package } from "lucide-react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { LoadingProvider } from "@/hooks/use-loading";
import { MainLayout } from "@/components/layout/MainLayout";

const AssetManagementContent = () => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full">
        <TopBar>
          <div className="flex items-center gap-3">
            <Package className="h-6 w-6 text-green-600" />
            <h1 className="text-2xl font-bold">Asset Management</h1>
          </div>
        </TopBar>
        
        <div className="flex-1 overflow-auto w-full p-6">
          <div className="max-w-6xl mx-auto">
            <p className="text-gray-600">Asset management functionality will be implemented here.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AssetManagement() {
  return (
    <SidebarProvider>
      <LoadingProvider>
        <MainLayout>
          <AssetManagementContent />
        </MainLayout>
      </LoadingProvider>
    </SidebarProvider>
  );
}


import React from "react";
import { StandardPageLayout } from "@/components/layout/StandardPageLayout";
import { AssetsHeader } from "@/components/assets/AssetsHeader";
import { SearchAndFilter } from "@/components/assets/SearchAndFilter";
import { AssetTable } from "@/components/assets/AssetTable";
import { useAssetsData } from "@/hooks/useAssetsData";
import { FadeInStagger } from "@/components/ui/FadeInStagger";

const Assets = () => {
  const { searchQuery, setSearchQuery, assets, filteredAssets } = useAssetsData();
  const [statusFilter, setStatusFilter] = React.useState("all");

  // Filter assets based on search and status
  const finalFilteredAssets = filteredAssets.filter(asset => {
    const matchesStatus = statusFilter === "all" || asset.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesStatus;
  });

  return (
    <StandardPageLayout title="Asset Management">
      <div className="h-full w-full px-4 lg:px-6 xl:px-8 py-4 lg:py-6 space-y-6">
        <AssetsHeader />
        <SearchAndFilter 
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
        <FadeInStagger>
          <AssetTable 
            assets={assets}
            filteredAssets={finalFilteredAssets}
            searchQuery={searchQuery}
            statusFilter={statusFilter}
          />
        </FadeInStagger>
      </div>
    </StandardPageLayout>
  );
};

export default Assets;


import React, { useState } from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Filter, Search } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { APQPProjectCard } from "@/components/apqp/APQPProjectCard";
import { CreateAPQPProjectDialog } from "@/components/apqp/CreateAPQPProjectDialog";

export default function APQPDashboard() {
  const { openMobile } = useSidebar();
  const isMobile = useIsMobile();
  const [searchTerm, setSearchTerm] = useState("");
  const [customerFilter, setCustomerFilter] = useState("all");
  const [phaseFilter, setPhaseFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const mockProjects = [
    {
      id: "APQP-Q5-2025",
      name: "IoT Sensor Hub – Project Q5",
      customer: "Infineon",
      startDate: "2025-05-01",
      endDate: "2025-09-30",
      phase: "Planning",
      status: "Active",
      progress: 15,
      productType: "RF-enabled PCB Assembly",
      teamLead: "Priya Nair",
      priority: "High"
    },
    {
      id: "APQP-PM150-2025",
      name: "Power Module PM150",
      customer: "Jabil",
      startDate: "2025-03-15",
      endDate: "2025-08-15",
      phase: "Design",
      status: "Active",
      progress: 35,
      productType: "Power Electronics Module",
      teamLead: "Jason Liu",
      priority: "Medium"
    },
    {
      id: "APQP-CTRL-2024",
      name: "Smart Controller Unit",
      customer: "Infineon",
      startDate: "2024-11-01",
      endDate: "2025-03-30",
      phase: "Launch",
      status: "Active",
      progress: 85,
      productType: "Embedded Controller",
      teamLead: "Sanjay Patel",
      priority: "High"
    }
  ];

  const filteredProjects = mockProjects.filter(project => {
    const matchesSearch = (project.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (project.customer || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCustomer = customerFilter === "all" || project.customer === customerFilter;
    const matchesPhase = phaseFilter === "all" || project.phase === phaseFilter;
    const matchesStatus = statusFilter === "all" || project.status === statusFilter;
    
    return matchesSearch && matchesCustomer && matchesPhase && matchesStatus;
  });

  const customers = [...new Set(mockProjects.map(p => p.customer).filter(Boolean))];
  const phases = [...new Set(mockProjects.map(p => p.phase).filter(Boolean))];
  const statuses = [...new Set(mockProjects.map(p => p.status).filter(Boolean))];

  return (
    <div className="w-screen min-h-screen bg-[#f5f6f8] overflow-hidden flex">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-slate-200 flex flex-col z-40`}>
        <Sidebar />
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden w-full min-w-0">
        <TopBar>
          <div className="flex items-center justify-between w-full">
            <div>
              <h1 className="text-2xl font-semibold text-slate-900">APQP Projects</h1>
              <p className="text-slate-600 text-sm">Advanced Product Quality Planning</p>
            </div>
          </div>
        </TopBar>
        
        <main className="flex-1 overflow-y-auto w-full min-w-0 bg-[#f5f6f8]">
          <div className="max-w-7xl mx-auto px-4 lg:px-6 py-4 lg:py-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">Total Projects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-slate-900">{mockProjects.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">Active Projects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-slate-900">
                    {mockProjects.filter(p => p.status === "Active").length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">In Launch Phase</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-slate-900">
                    {mockProjects.filter(p => p.phase === "Launch").length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-slate-600">Avg Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-slate-900">
                    {Math.round(mockProjects.reduce((sum, p) => sum + (p.progress || 0), 0) / mockProjects.length)}%
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Create Button Section */}
            <div className="flex justify-end mb-6">
              <Button 
                className="bg-teal-600 hover:bg-teal-700 text-white"
                onClick={() => setShowCreateDialog(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create New APQP Project
              </Button>
            </div>

            {/* Filters */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg font-medium">Filter Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                    <Input
                      placeholder="Search projects..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={customerFilter} onValueChange={setCustomerFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Customers" />
                    </SelectTrigger>
                    <SelectContent className="bg-white z-50">
                      <SelectItem value="all">All Customers</SelectItem>
                      {customers.map(customer => (
                        <SelectItem key={customer} value={customer}>{customer}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={phaseFilter} onValueChange={setPhaseFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Phases" />
                    </SelectTrigger>
                    <SelectContent className="bg-white z-50">
                      <SelectItem value="all">All Phases</SelectItem>
                      {phases.map(phase => (
                        <SelectItem key={phase} value={phase}>{phase}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All Statuses" />
                    </SelectTrigger>
                    <SelectContent className="bg-white z-50">
                      <SelectItem value="all">All Statuses</SelectItem>
                      {statuses.map(status => (
                        <SelectItem key={status} value={status}>{status}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSearchTerm("");
                      setCustomerFilter("all");
                      setPhaseFilter("all");
                      setStatusFilter("all");
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Project Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredProjects.map(project => (
                <APQPProjectCard key={project.id} project={project} />
              ))}
            </div>

            {filteredProjects.length === 0 && (
              <Card>
                <CardContent className="py-12 text-center">
                  <p className="text-slate-500">No APQP projects found matching your filters.</p>
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>

      <CreateAPQPProjectDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog} 
      />
    </div>
  );
}

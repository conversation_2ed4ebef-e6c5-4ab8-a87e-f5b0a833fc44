
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  FileText, 
  Upload, 
  Download, 
  Package, 
  Users, 
  BarChart3, 
  CheckCircle, 
  AlertTriangle,
  ExternalLink
} from "lucide-react";

interface FunctionalityItem {
  name: string;
  description: string;
  status: 'implemented' | 'missing' | 'partial';
  route?: string;
  component?: string;
  icon: any;
}

interface HubAudit {
  hubName: string;
  route: string;
  functionalities: FunctionalityItem[];
}

export const FunctionalityAudit: React.FC = () => {
  const navigate = useNavigate();

  const hubAudits: HubAudit[] = [
    {
      hubName: "Customer Compliance",
      route: "/customer-compliance",
      functionalities: [
        {
          name: "Generate Package",
          description: "Create compliance packages for customers",
          status: 'implemented',
          icon: Package
        },
        {
          name: "Share New File",
          description: "Upload and share files with customers",
          status: 'implemented',
          icon: Upload
        },
        {
          name: "Manage Access",
          description: "Control customer access permissions",
          status: 'implemented',
          icon: Users
        },
        {
          name: "Workspace Creation",
          description: "Create new customer workspaces",
          status: 'implemented',
          icon: FileText
        }
      ]
    },
    {
      hubName: "PPAP Management",
      route: "/ppap",
      functionalities: [
        {
          name: "PPAP Submission",
          description: "Submit new PPAP packages",
          status: 'implemented',
          icon: Upload
        },
        {
          name: "Download Template",
          description: "Download PPAP submission templates",
          status: 'implemented',
          icon: Download
        },
        {
          name: "Review & Approval",
          description: "Review and approve PPAP submissions",
          status: 'implemented',
          icon: CheckCircle
        },
        {
          name: "Reports & Analytics",
          description: "Generate PPAP compliance reports",
          status: 'implemented',
          icon: BarChart3
        },
        {
          name: "Recall Management",
          description: "Manage product recalls",
          status: 'implemented',
          icon: AlertTriangle
        }
      ]
    },
    {
      hubName: "APQP Management",
      route: "/apqp",
      functionalities: [
        {
          name: "Project Creation",
          description: "Create new APQP projects",
          status: 'implemented',
          icon: FileText
        },
        {
          name: "Export Data",
          description: "Export APQP project data",
          status: 'implemented',
          icon: Download
        },
        {
          name: "Gate Reviews",
          description: "Conduct phase gate reviews",
          status: 'implemented',
          icon: CheckCircle
        },
        {
          name: "Deliverables Tracking",
          description: "Track project deliverables",
          status: 'implemented',
          icon: Package
        }
      ]
    },
    {
      hubName: "Supplier Quality",
      route: "/supplier-quality",
      functionalities: [
        {
          name: "Supplier Audits",
          description: "Manage supplier audit processes",
          status: 'implemented',
          route: "/supplier-audits",
          icon: CheckCircle
        },
        {
          name: "Document Management",
          description: "Manage supplier documents",
          status: 'implemented',
          route: "/supplier-documents",
          icon: FileText
        },
        {
          name: "Performance Scorecard",
          description: "Track supplier performance",
          status: 'implemented',
          route: "/supplier-scorecard",
          icon: BarChart3
        },
        {
          name: "Risk Assessment",
          description: "Assess supplier risks",
          status: 'implemented',
          route: "/supplier-risk-heatmap",
          icon: AlertTriangle
        }
      ]
    },
    {
      hubName: "Document Hub",
      route: "/document-hub",
      functionalities: [
        {
          name: "Document Upload",
          description: "Upload new documents",
          status: 'implemented',
          icon: Upload
        },
        {
          name: "Document Review",
          description: "Review and approve documents",
          status: 'implemented',
          icon: CheckCircle
        },
        {
          name: "Version Control",
          description: "Manage document versions",
          status: 'implemented',
          icon: FileText
        },
        {
          name: "Export Documents",
          description: "Export document packages",
          status: 'partial',
          icon: Download
        }
      ]
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'implemented':
        return <Badge className="bg-green-500 text-white">Implemented</Badge>;
      case 'partial':
        return <Badge className="bg-yellow-500 text-white">Partial</Badge>;
      case 'missing':
        return <Badge className="bg-red-500 text-white">Missing</Badge>;
      default:
        return <Badge className="bg-gray-500 text-white">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Functionality Audit</h1>
        <p className="text-gray-600">Comprehensive review of all hub functionalities</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {hubAudits.map((hub) => (
          <Card key={hub.hubName}>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">{hub.hubName}</CardTitle>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate(hub.route)}
                  className="gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  View Hub
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {hub.functionalities.map((functionality) => {
                  const IconComponent = functionality.icon;
                  return (
                    <div key={functionality.name} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <IconComponent className="h-5 w-5 text-gray-600" />
                        <div>
                          <p className="font-medium">{functionality.name}</p>
                          <p className="text-sm text-gray-600">{functionality.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(functionality.status)}
                        {functionality.route && (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => navigate(functionality.route!)}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {hubAudits.reduce((total, hub) => 
                  total + hub.functionalities.filter(f => f.status === 'implemented').length, 0
                )}
              </div>
              <p className="text-sm text-gray-600">Implemented</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {hubAudits.reduce((total, hub) => 
                  total + hub.functionalities.filter(f => f.status === 'partial').length, 0
                )}
              </div>
              <p className="text-sm text-gray-600">Partial</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {hubAudits.reduce((total, hub) => 
                  total + hub.functionalities.filter(f => f.status === 'missing').length, 0
                )}
              </div>
              <p className="text-sm text-gray-600">Missing</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

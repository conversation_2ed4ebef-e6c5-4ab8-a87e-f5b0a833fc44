
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TableSortableHeader } from "./TableSortableHeader";
import { SortField, SortDirection, TableColumn } from "./DataTable";

interface DataTableHeaderProps {
  columns: TableColumn[];
  sortField: SortField;
  sortDirection: SortDirection;
  onSort?: (field: SortField) => void;
  isMobile: boolean;
  showExpand?: boolean;
}

export const DataTableHeader: React.FC<DataTableHeaderProps> = ({
  columns,
  sortField,
  sortDirection,
  onSort,
  isMobile,
  showExpand = false
}) => {
  // For mobile view, we'll show a simplified header with fewer columns
  if (isMobile) {
    return (
      <TableHeader className="sticky top-0 z-20 bg-gray-50">
        <TableRow>
          {showExpand && (
            <TableHead className="w-10 p-2 bg-gray-50">
              <span className="sr-only">Expand</span>
            </TableHead>
          )}
          
          {columns.slice(0, 2).map((column, index) => (
            <TableSortableHeader
              key={column.field}
              field={column.field}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={onSort}
              className={`${column.width || ""} bg-gray-50 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100`}
              isFirstColumn={index === 0}
            >
              {column.header}
            </TableSortableHeader>
          ))}
          
          <TableHead className="w-12 p-2 bg-gray-50 text-xs font-semibold text-gray-600 uppercase tracking-wider">
            <span className="sr-only">Actions</span>
          </TableHead>
        </TableRow>
      </TableHeader>
    );
  }

  return (
    <TableHeader className="sticky top-0 z-20 bg-gray-50">
      <TableRow>
        {showExpand && (
          <TableHead className="w-12 pl-4 py-3 bg-gray-50">
            <span className="sr-only">Expand</span>
          </TableHead>
        )}
        
        {columns.map((column, index) => (
          <TableSortableHeader
            key={column.field}
            field={column.field}
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={column.sortable ? onSort : undefined}
            className={`${column.width || ""} bg-gray-50 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100`}
            isFirstColumn={index === 0}
          >
            {column.header}
          </TableSortableHeader>
        ))}
        
        {/* Actions column */}
        <TableHead className="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-24 bg-gray-50">
          Actions
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

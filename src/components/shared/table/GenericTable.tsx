import React, { useState } from "react";
import { Table, TableBody, TableHeader, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { VendorTablePagination } from "@/components/vendors/VendorTablePagination";

/**
 * The GenericTable component: Accepts columns, data and provides advanced features
 * like mobile view, pagination, sorting, expandable rows, and action buttons. Generic
 * for any use-case (vendors, documents, inventory, etc).
 */

type SortDirection = "asc" | "desc" | null;

export interface GenericTableColumn<T> {
  field: keyof T | string;
  header: string;
  sortable?: boolean;
  cellRenderer?: (item: T, index: number) => React.ReactNode;
  width?: string;
}

export interface GenericTableProps<T> {
  columns: GenericTableColumn<T>[];
  data: T[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  sortField?: string | null;
  sortDirection?: SortDirection;
  onSort?: (field: string) => void;
  isMobile?: boolean;
  renderActions?: (item: T, index: number) => React.ReactNode;
  showExpandColumn?: boolean;
  emptyMessage?: string;
}

export function GenericTable<T>({
  columns,
  data,
  totalCount,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  sortField = null,
  sortDirection = null,
  onSort,
  isMobile = false,
  renderActions,
  showExpandColumn = false,
  emptyMessage = "No data found",
}: GenericTableProps<T>) {
  // Simple sort handler for demonstration—delegated to parent if provided
  const handleSort = (field: string) => {
    if (onSort) onSort(field);
  };

  // (Optional) totalPages for pagination
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden w-full">
      <div className="p-6 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Data</h2>
      </div>
      <div className="overflow-auto">
        <ResizableTable className="w-full min-w-[400px]">
          <TableHeader>
            <TableRow>
              {/* Expand */}
              {showExpandColumn && <TableHead className="w-12"><span className="sr-only">Expand</span></TableHead>}
              {/* Headers */}
              {columns.map((col) => (
                <TableHead
                  key={String(col.field)}
                  sortable={col.sortable}
                  sortDirection={sortField === col.field ? sortDirection : null}
                  onSort={col.sortable ? () => handleSort(col.field as string) : undefined}
                  className={col.width || ""}
                >
                  {col.header}
                </TableHead>
              ))}
              {/* Actions */}
              {renderActions && (
                <TableHead className="w-[80px] text-center">
                  <span className="sr-only">Actions</span>
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (showExpandColumn ? 1 : 0) + (renderActions ? 1 : 0)}>
                  <div className="text-center text-gray-400 py-8">{emptyMessage}</div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((item, index) => (
                <TableRow key={(item as any).id || index}>
                  {/* Expand cell placeholder (optional future feature) */}
                  {showExpandColumn && (
                    <TableCell className="w-12">{/* expandable logic here in next phase if needed */}</TableCell>
                  )}
                  {columns.map((col, cidx) => (
                    <TableCell key={`${String(col.field)}-${cidx}`}>
                      {col.cellRenderer
                        ? col.cellRenderer(item, index)
                        : (item as any)[col.field] ?? "--"}
                    </TableCell>
                  ))}
                  {/* Actions */}
                  {renderActions && <TableCell className="text-right">{renderActions(item, index)}</TableCell>}
                </TableRow>
              ))
            )}
          </TableBody>
        </ResizableTable>
      </div>
      {/* Pagination */}
      <VendorTablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        total={totalCount}
        className="bg-white border-t border-gray-200"
      />
    </div>
  );
}

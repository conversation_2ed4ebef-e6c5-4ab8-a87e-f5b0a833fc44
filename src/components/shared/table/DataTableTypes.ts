
export type SortField = string | null;
export type SortDirection = 'asc' | 'desc' | null;

export interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  width?: string;
  cellRenderer?: (item: any) => React.ReactNode;
}

export interface DataTableProps {
  data: any[];
  filteredData: any[];
  columns: TableColumn[];
  currentPage: number;
  pageSize: number;
  totalCount: number;
  searchQuery?: string;
  statusFilter?: string;
  showExpand?: boolean;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  renderActions?: (item: any, index: number) => React.ReactNode;
  emptyMessage?: string;
  sortField?: SortField;
  sortDirection?: SortDirection;
  onSort?: (field: string) => void;
  className?: string;
  showFooter?: boolean;
  footerContent?: React.ReactNode;
  onRowClick?: (item: any) => void;
}

export interface DataTableCoreProps extends DataTableProps {
  isMobile: boolean;
}


import React from "react";
import { TableBody } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { DataTableHeader } from "./DataTableHeader";
import { DataTableRow } from "./DataTableRow";
import { EmptyTableRow } from "./EmptyTableRow";
import { DataTableCoreProps } from "./DataTableTypes";

export const DataTableCore: React.FC<DataTableCoreProps> = ({
  data,
  columns,
  filteredData,
  currentPage,
  pageSize,
  totalCount,
  searchQuery = "",
  statusFilter,
  showExpand = false,
  onPageChange,
  onPageSizeChange,
  renderActions,
  emptyMessage = "No items found",
  sortField = null,
  sortDirection = null,
  onSort,
  className = "",
  showFooter = false,
  footerContent,
  isMobile,
  onRowClick
}) => {
  const handleRowClick = (item: any) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  const handleColumnResize = (columnIndex: number, newWidth: number) => {
    // Optional: Store column widths in localStorage or state
    console.log(`Column ${columnIndex} resized to ${newWidth}px`);
  };

  return (
    <div className={`bg-white rounded-xl border border-slate-200 shadow-sm w-full min-w-0 ${className}`}>
      <div className="p-6 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Data</h2>
      </div>
      <div className="w-full overflow-x-auto overflow-y-auto max-h-[calc(100vh-280px)]">
        <div className="w-full min-w-max">
          <ResizableTable 
            className="w-full"
            minColumnWidth={120}
            onColumnResize={handleColumnResize}
          >
            <DataTableHeader 
              columns={columns}
              sortField={sortField}
              sortDirection={sortDirection}
              onSort={onSort}
              isMobile={isMobile}
              showExpand={showExpand}
            />
            <TableBody className="divide-y divide-slate-100">
              {filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <DataTableRow 
                    key={item.id || index}
                    item={item}
                    columns={columns}
                    index={index}
                    isMobile={isMobile}
                    showExpand={showExpand}
                    renderActions={renderActions}
                    onRowClick={() => handleRowClick(item)}
                  />
                ))
              ) : (
                <EmptyTableRow 
                  colSpan={isMobile ? 4 : columns.length + (renderActions ? 1 : 0) + (showExpand ? 1 : 0)}
                  message={emptyMessage}
                  searchQuery={searchQuery}
                  statusFilter={statusFilter}
                />
              )}
            </TableBody>
          </ResizableTable>
        </div>
      </div>
      
      {showFooter && footerContent && (
        <div className="sticky bottom-0 bg-slate-100 border-t border-slate-200 px-6 py-3 text-sm text-slate-600 font-medium">
          {footerContent}
        </div>
      )}
    </div>
  );
};

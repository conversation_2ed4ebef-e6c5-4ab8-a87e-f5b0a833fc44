
import React from "react";
import { TableHead } from "@/components/ui/table";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";
import { SortField, SortDirection } from "./DataTable";

interface TableSortableHeaderProps {
  field: string;
  children: React.ReactNode;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort?: (field: SortField) => void;
  className?: string;
  isFirstColumn?: boolean;
}

export const TableSortableHeader: React.FC<TableSortableHeaderProps> = ({
  field,
  children,
  sortField,
  sortDirection,
  onSort,
  className = "",
  isFirstColumn = false
}) => {
  const isSortable = !!onSort;
  
  const renderSortIcon = () => {
    if (!isSortable) return null;
    
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-blue-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-blue-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-slate-400" />;
  };

  const baseWidth = isFirstColumn ? "w-[300px]" : "";

  return (
    <TableHead 
      onClick={isSortable ? () => onSort(field) : undefined}
      className={`py-3 px-4 text-xs font-semibold text-gray-600 uppercase tracking-wider ${isSortable ? 'hover:bg-gray-100 group cursor-pointer' : ''} ${baseWidth} ${className}`}
    >
      <div className="flex items-center">
        {children}
        {renderSortIcon()}
      </div>
    </TableHead>
  );
};

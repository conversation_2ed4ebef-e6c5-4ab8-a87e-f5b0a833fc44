
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";

interface EmptyTableRowProps {
  colSpan: number;
  message?: string;
  searchQuery?: string;
  statusFilter?: string;
}

export const EmptyTableRow: React.FC<EmptyTableRowProps> = ({
  colSpan,
  message = "No items found",
  searchQuery,
  statusFilter
}) => {
  let displayMessage = message;

  if (searchQuery && statusFilter) {
    displayMessage = `No items found matching "${searchQuery}" with status "${statusFilter}"`;
  } else if (searchQuery) {
    displayMessage = `No items found matching "${searchQuery}"`;
  } else if (statusFilter) {
    displayMessage = `No items found with status "${statusFilter}"`;
  }

  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="py-6 text-center text-gray-500">
        {displayMessage}
      </TableCell>
    </TableRow>
  );
};

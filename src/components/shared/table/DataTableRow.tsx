
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { MobileDataTableRow } from "./MobileDataTableRow";
import { ChevronDown, ChevronRight } from "lucide-react";
import { TableColumn } from "./DataTable";

interface DataTableRowProps {
  item: any;
  columns: TableColumn[];
  index: number;
  isMobile: boolean;
  showExpand?: boolean;
  renderActions?: (item: any, index: number) => React.ReactNode;
  onRowClick?: () => void;
}

export const DataTableRow: React.FC<DataTableRowProps> = ({
  item,
  columns,
  index,
  isMobile,
  showExpand = false,
  renderActions,
  onRowClick
}) => {
  const [expanded, setExpanded] = useState(false);
  
  if (isMobile) {
    return (
      <MobileDataTableRow 
        item={item}
        columns={columns}
        index={index}
        showExpand={showExpand}
        renderActions={renderActions}
        onRowClick={onRowClick}
      />
    );
  }

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (showExpand) {
      setExpanded(prev => !prev);
    }
  };

  const handleRowClick = () => {
    if (onRowClick) {
      onRowClick();
    }
  };

  return (
    <TableRow 
      className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors cursor-pointer`}
      onClick={handleRowClick}
    >
      {showExpand && (
        <TableCell className="pl-4 pr-2 py-4 w-12" onClick={handleExpandClick}>
          <button type="button" className="p-1 rounded-full hover:bg-slate-200">
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </button>
        </TableCell>
      )}
      
      {columns.map((column, cellIndex) => (
        <TableCell key={`${item.id || index}-${column.field}-${cellIndex}`} className="py-4 px-4">
          {column.cellRenderer ? column.cellRenderer(item) : item[column.field] || "--"}
        </TableCell>
      ))}
      
      {renderActions && (
        <TableCell className="py-4 px-4 text-right">
          {renderActions(item, index)}
        </TableCell>
      )}
    </TableRow>
  );
};

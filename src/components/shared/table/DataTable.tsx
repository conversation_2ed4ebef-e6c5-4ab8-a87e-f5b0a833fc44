
import React from "react";
import { DataTableCore } from "./DataTableCore";
import { useIsMobile } from "@/hooks/use-mobile";
import { DataTableProps } from "./DataTableTypes";

// Re-export types for backward compatibility
export type { SortField, SortDirection, TableColumn } from "./DataTableTypes";

export const DataTable: React.FC<DataTableProps> = (props) => {
  const isMobile = useIsMobile();
  
  return <DataTableCore {...props} isMobile={isMobile} />;
};

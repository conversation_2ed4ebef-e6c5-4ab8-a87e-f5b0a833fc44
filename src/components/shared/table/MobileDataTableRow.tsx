
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { TableColumn } from "./DataTable";

interface MobileDataTableRowProps {
  item: any;
  columns: TableColumn[];
  index: number;
  showExpand?: boolean;
  renderActions?: (item: any, index: number) => React.ReactNode;
  onRowClick?: () => void;
}

export const MobileDataTableRow: React.FC<MobileDataTableRowProps> = ({
  item,
  columns,
  index,
  renderActions,
  onRowClick
}) => {
  const handleRowClick = () => {
    if (onRowClick) {
      onRowClick();
    }
  };

  return (
    <TableRow 
      className={`cursor-pointer transition-colors hover:bg-blue-50 ${
        index % 2 === 0 ? 'bg-white' : 'bg-slate-50'
      }`}
      onClick={handleRowClick}
    >
      <TableCell className="p-4" colSpan={3}>
        <div className="space-y-2">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              {columns[0]?.cellRenderer ? columns[0].cellRenderer(item) : (
                <div className="font-medium text-blue-600">
                  {item[columns[0]?.field] || "--"}
                </div>
              )}
            </div>
            {renderActions && (
              <div onClick={(e) => e.stopPropagation()}>
                {renderActions(item, index)}
              </div>
            )}
          </div>
          
          {columns.slice(1, 4).map((column, idx) => (
            <div key={idx} className="text-xs text-gray-600">
              <span className="font-medium">{column.header}:</span>{" "}
              {column.cellRenderer ? column.cellRenderer(item) : (item[column.field] || "--")}
            </div>
          ))}
        </div>
      </TableCell>
    </TableRow>
  );
};

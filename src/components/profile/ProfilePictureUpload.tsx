
import React, { useState, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Camera, UserRound } from "lucide-react";
import { toast } from "sonner";

interface ProfilePictureUploadProps {
  defaultImage?: string;
}

export const ProfilePictureUpload = ({ defaultImage }: ProfilePictureUploadProps) => {
  const [imageUrl, setImageUrl] = useState<string | null>(defaultImage || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    console.log("File selected:", file.name);
    
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image must be less than 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      console.log("File read successfully, updating image URL");
      setImageUrl(result);
      toast.success('Profile picture updated successfully');
    };
    
    reader.onerror = (e) => {
      console.error("Error reading file:", e);
      toast.error('Failed to read image file');
    };
    
    console.log("Starting to read file as data URL");
    reader.readAsDataURL(file);
  };

  const handleRemovePicture = () => {
    console.log("Removing profile picture");
    setImageUrl(null);
    toast.success('Profile picture removed');
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleChangePicture = () => {
    console.log("Change picture button clicked");
    // Programmatically click the hidden input
    fileInputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <Avatar className="w-24 h-24">
        <AvatarImage src={imageUrl || ''} />
        <AvatarFallback>
          <UserRound className="w-12 h-12" />
        </AvatarFallback>
      </Avatar>

      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="gap-2"
          onClick={handleChangePicture}
          type="button"
        >
          <Camera className="w-4 h-4" />
          Change Picture
        </Button>
        <input
          ref={fileInputRef}
          id="picture"
          type="file"
          className="hidden"
          accept="image/*"
          onChange={handleFileUpload}
        />
        
        {imageUrl && (
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleRemovePicture}
            type="button"
          >
            Remove
          </Button>
        )}
      </div>
    </div>
  );
};

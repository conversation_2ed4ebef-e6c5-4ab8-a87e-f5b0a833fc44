
import React from 'react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { profileSchema, ProfileFormData } from "@/schemas/profileValidation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { ProfilePictureUpload } from './ProfilePictureUpload';

export const ProfileForm = () => {
  // Example: these defaults would normally come from user context/api
  const DEFAULTS: ProfileFormData = {
    firstName: "John",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "",
    jobTitle: "Senior Quality Engineer",
    department: "Quality Assurance",
    location: "San Jose HQ",
    manager: "<PERSON> Lee",
    bio: "Experienced Senior Quality Engineer at Hub Electronics.",
    linkedIn: "https://linkedin.com/in/johnsmith",
    skills: "Quality Management, Audit, Six Sigma",
    profilePicture: undefined,
  };

  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: DEFAULTS,
  });

  const onSubmit = async (data: ProfileFormData) => {
    try {
      // For demonstration: normally, would send to backend
      console.log('Profile data to update:', data);
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error('Failed to update profile');
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white shadow rounded-lg p-6">
        <ProfilePictureUpload defaultImage="/lovable-uploads/c291cf2c-2b4e-42d6-a251-6223fd0e98af.png" />

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input id="firstName" {...register("firstName")} placeholder="First name" />
              {errors.firstName && (
                <span className="text-sm text-red-500">{errors.firstName.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input id="lastName" {...register("lastName")} placeholder="Last name" />
              {errors.lastName && (
                <span className="text-sm text-red-500">{errors.lastName.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" {...register("email")} placeholder="Email" disabled />
              {errors.email && (
                <span className="text-sm text-red-500">{errors.email.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input id="phone" {...register("phone")} placeholder="Phone number" />
              {errors.phone && (
                <span className="text-sm text-red-500">{errors.phone.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input id="jobTitle" {...register("jobTitle")} placeholder="Job title" />
              {errors.jobTitle && (
                <span className="text-sm text-red-500">{errors.jobTitle.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="department">Department</Label>
              <Input id="department" {...register("department")} placeholder="Department" />
              {errors.department && (
                <span className="text-sm text-red-500">{errors.department.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="location">Location</Label>
              <Input id="location" {...register("location")} placeholder="Location" />
              {errors.location && (
                <span className="text-sm text-red-500">{errors.location.message}</span>
              )}
            </div>

            <div>
              <Label htmlFor="manager">Manager</Label>
              <Input id="manager" {...register("manager")} placeholder="Manager (optional)" />
              {errors.manager && (
                <span className="text-sm text-red-500">{errors.manager.message}</span>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="linkedIn">LinkedIn</Label>
              <Input id="linkedIn" {...register("linkedIn")} placeholder="LinkedIn profile (optional)" />
              {errors.linkedIn && (
                <span className="text-sm text-red-500">{errors.linkedIn.message}</span>
              )}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="skills">Skills</Label>
              <Input id="skills" {...register("skills")} placeholder="Skills (comma separated)" />
              {errors.skills && (
                <span className="text-sm text-red-500">{errors.skills.message}</span>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea id="bio" {...register("bio")} placeholder="Write a short bio" />
            {errors.bio && (
              <span className="text-sm text-red-500">{errors.bio.message}</span>
            )}
          </div>

          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </form>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Shield, AlertTriangle, CheckCircle, Calendar, Globe, FileText, Bell } from 'lucide-react';

const regulations = [
  {
    id: '1',
    name: 'ISO 9001:2015',
    jurisdiction: 'Global',
    status: 'compliant',
    compliance: 94,
    nextAudit: '2024-08-15',
    lastUpdate: '2024-01-10',
    requirements: 23,
    completed: 22
  },
  {
    id: '2',
    name: 'FDA 21 CFR Part 820',
    jurisdiction: 'United States',
    status: 'action-required',
    compliance: 78,
    nextAudit: '2024-07-20',
    lastUpdate: '2024-02-15',
    requirements: 18,
    completed: 14
  },
  {
    id: '3',
    name: 'IATF 16949:2016',
    jurisdiction: 'Automotive Global',
    status: 'compliant',
    compliance: 98,
    nextAudit: '2024-09-30',
    lastUpdate: '2024-03-01',
    requirements: 31,
    completed: 30
  },
  {
    id: '4',
    name: 'RoHS Directive',
    jurisdiction: 'European Union',
    status: 'pending-review',
    compliance: 85,
    nextAudit: '2024-06-10',
    lastUpdate: '2024-01-25',
    requirements: 12,
    completed: 10
  }
];

const recentUpdates = [
  {
    id: '1',
    regulation: 'FDA 21 CFR Part 820',
    change: 'Updated quality system requirements for medical devices',
    impact: 'medium',
    effectiveDate: '2024-07-01',
    actionRequired: true
  },
  {
    id: '2',
    regulation: 'ISO 9001:2015',
    change: 'Clarification on risk-based thinking implementation',
    impact: 'low',
    effectiveDate: '2024-06-15',
    actionRequired: false
  },
  {
    id: '3',
    regulation: 'RoHS Directive',
    change: 'New substance restrictions added to Annex II',
    impact: 'high',
    effectiveDate: '2024-08-01',
    actionRequired: true
  }
];

const complianceMetrics = [
  { metric: 'Overall Compliance Score', value: 89, target: 95, trend: 'up' },
  { metric: 'Active Regulations', value: 24, target: 24, trend: 'stable' },
  { metric: 'Pending Actions', value: 7, target: 0, trend: 'down' },
  { metric: 'Due This Month', value: 3, target: 0, trend: 'down' }
];

export const RegulatoryComplianceTracker: React.FC = () => {
  const [selectedJurisdiction, setSelectedJurisdiction] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'action-required': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'pending-review': return <Calendar className="h-4 w-4 text-yellow-600" />;
      default: return <Shield className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      'compliant': 'bg-green-100 text-green-800',
      'action-required': 'bg-red-100 text-red-800',
      'pending-review': 'bg-yellow-100 text-yellow-800'
    };
    return <Badge className={variants[status]}>{status.replace('-', ' ')}</Badge>;
  };

  const getImpactBadge = (impact: string) => {
    const variants: Record<string, string> = {
      'high': 'bg-red-100 text-red-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'low': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[impact]}>{impact}</Badge>;
  };

  const filteredRegulations = regulations.filter(reg => {
    const jurisdictionMatch = selectedJurisdiction === 'all' || reg.jurisdiction.toLowerCase().includes(selectedJurisdiction.toLowerCase());
    const statusMatch = selectedStatus === 'all' || reg.status === selectedStatus;
    return jurisdictionMatch && statusMatch;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Regulatory Compliance Tracker
          </h1>
          <p className="text-gray-600">Monitor and manage regulatory compliance across all jurisdictions</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Subscribe to Updates
          </Button>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {complianceMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">{metric.metric}</p>
                <p className="text-3xl font-bold">{metric.value}{metric.metric.includes('Score') ? '%' : ''}</p>
                <div className="flex items-center gap-2">
                  <Progress value={(metric.value / metric.target) * 100} className="h-2 flex-1" />
                  <span className="text-xs text-gray-500">Target: {metric.target}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="regulations" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="regulations">Regulations</TabsTrigger>
          <TabsTrigger value="updates">Recent Updates</TabsTrigger>
          <TabsTrigger value="calendar">Compliance Calendar</TabsTrigger>
        </TabsList>

        <TabsContent value="regulations" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <Select value={selectedJurisdiction} onValueChange={setSelectedJurisdiction}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by jurisdiction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Jurisdictions</SelectItem>
                <SelectItem value="global">Global</SelectItem>
                <SelectItem value="united states">United States</SelectItem>
                <SelectItem value="european union">European Union</SelectItem>
                <SelectItem value="automotive">Automotive</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="compliant">Compliant</SelectItem>
                <SelectItem value="action-required">Action Required</SelectItem>
                <SelectItem value="pending-review">Pending Review</SelectItem>
              </SelectContent>
            </Select>

            <Input placeholder="Search regulations..." className="flex-1" />
          </div>

          {/* Regulations List */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredRegulations.map((regulation) => (
              <Card key={regulation.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(regulation.status)}
                      {regulation.name}
                    </CardTitle>
                    {getStatusBadge(regulation.status)}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Globe className="h-4 w-4" />
                    {regulation.jurisdiction}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Compliance Progress</span>
                      <span>{regulation.compliance}%</span>
                    </div>
                    <Progress value={regulation.compliance} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Requirements</p>
                      <p className="font-medium">{regulation.completed}/{regulation.requirements}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Next Audit</p>
                      <p className="font-medium">{new Date(regulation.nextAudit).toLocaleDateString()}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                    <Button variant="outline" size="sm">
                      Update Status
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="updates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Regulatory Updates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentUpdates.map((update) => (
                  <div key={update.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{update.regulation}</h4>
                        <p className="text-sm text-gray-600 mt-1">{update.change}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getImpactBadge(update.impact)}
                        {update.actionRequired && (
                          <Badge className="bg-red-100 text-red-800">Action Required</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>Effective: {new Date(update.effectiveDate).toLocaleDateString()}</span>
                      {update.actionRequired && (
                        <Button size="sm">Review & Act</Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Compliance Activities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border-l-4 border-l-red-500 bg-red-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-red-900">FDA 21 CFR Part 820 Audit</h4>
                      <p className="text-sm text-red-700">External audit scheduled</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-red-900">July 20, 2024</p>
                      <p className="text-xs text-red-600">In 25 days</p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-l-4 border-l-yellow-500 bg-yellow-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-yellow-900">RoHS Compliance Review</h4>
                      <p className="text-sm text-yellow-700">Internal assessment due</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-yellow-900">June 10, 2024</p>
                      <p className="text-xs text-yellow-600">In 5 days</p>
                    </div>
                  </div>
                </div>

                <div className="p-4 border-l-4 border-l-blue-500 bg-blue-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-blue-900">ISO 9001:2015 Surveillance</h4>
                      <p className="text-sm text-blue-700">Annual surveillance audit</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-blue-900">August 15, 2024</p>
                      <p className="text-xs text-blue-600">In 51 days</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ArrowLeft, Plus, X, ChefHat, Calculator, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
  cost: number;
  supplier: string;
  category: 'Active' | 'Excipient' | 'Flavoring' | 'Coating' | 'Other';
}

interface RecipeBuilderProps {
  onBack: () => void;
}

export const RecipeBuilder: React.FC<RecipeBuilderProps> = ({ onBack }) => {
  const { toast } = useToast();
  const [recipeName, setRecipeName] = useState('');
  const [batchSize, setBatchSize] = useState('');
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [newIngredient, setNewIngredient] = useState({
    name: '',
    quantity: '',
    unit: 'kg',
    cost: '',
    supplier: '',
    category: 'Active' as Ingredient['category']
  });

  const units = ['kg', 'g', 'mg', 'L', 'mL', 'pieces', '%'];
  const categories: Ingredient['category'][] = ['Active', 'Excipient', 'Flavoring', 'Coating', 'Other'];

  const addIngredient = () => {
    if (!newIngredient.name || !newIngredient.quantity) {
      toast({
        title: "Missing Information",
        description: "Please enter ingredient name and quantity",
        variant: "destructive"
      });
      return;
    }

    const ingredient: Ingredient = {
      id: `ing-${Date.now()}`,
      name: newIngredient.name,
      quantity: parseFloat(newIngredient.quantity),
      unit: newIngredient.unit,
      cost: parseFloat(newIngredient.cost) || 0,
      supplier: newIngredient.supplier,
      category: newIngredient.category
    };

    setIngredients([...ingredients, ingredient]);
    setNewIngredient({
      name: '',
      quantity: '',
      unit: 'kg',
      cost: '',
      supplier: '',
      category: 'Active'
    });

    toast({
      title: "Ingredient Added",
      description: `${ingredient.name} has been added to the recipe`
    });
  };

  const removeIngredient = (id: string) => {
    setIngredients(ingredients.filter(ing => ing.id !== id));
  };

  const getTotalCost = () => {
    return ingredients.reduce((total, ing) => total + (ing.cost * ing.quantity), 0);
  };

  const getTotalWeight = () => {
    return ingredients.reduce((total, ing) => {
      if (ing.unit === 'kg') return total + ing.quantity;
      if (ing.unit === 'g') return total + (ing.quantity / 1000);
      return total;
    }, 0);
  };

  const getCategoryBadge = (category: string) => {
    const colors = {
      'Active': 'bg-red-100 text-red-800',
      'Excipient': 'bg-blue-100 text-blue-800',
      'Flavoring': 'bg-green-100 text-green-800',
      'Coating': 'bg-purple-100 text-purple-800',
      'Other': 'bg-gray-100 text-gray-800'
    };
    return <Badge className={colors[category as keyof typeof colors]}>{category}</Badge>;
  };

  const saveRecipe = () => {
    if (!recipeName || ingredients.length === 0) {
      toast({
        title: "Incomplete Recipe",
        description: "Please enter recipe name and add at least one ingredient",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Recipe Saved",
      description: `Recipe "${recipeName}" has been saved successfully`
    });
    onBack();
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 w-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-3">
              <ChefHat className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Recipe Builder</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 md:p-6 w-full">
        <div className="w-full space-y-6">
          
          {/* Recipe Information */}
          <Card>
            <CardHeader>
              <CardTitle>Recipe Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="recipeName">Recipe Name *</Label>
                  <Input
                    id="recipeName"
                    value={recipeName}
                    onChange={(e) => setRecipeName(e.target.value)}
                    placeholder="e.g., Paracetamol 500mg Formula"
                  />
                </div>
                <div>
                  <Label htmlFor="batchSize">Batch Size</Label>
                  <Input
                    id="batchSize"
                    value={batchSize}
                    onChange={(e) => setBatchSize(e.target.value)}
                    placeholder="e.g., 1000 kg"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Add Ingredient */}
          <Card>
            <CardHeader>
              <CardTitle>Add Ingredient</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="ingredientName">Ingredient Name</Label>
                  <Input
                    id="ingredientName"
                    value={newIngredient.name}
                    onChange={(e) => setNewIngredient({...newIngredient, name: e.target.value})}
                    placeholder="e.g., Paracetamol API"
                  />
                </div>
                <div>
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={newIngredient.quantity}
                    onChange={(e) => setNewIngredient({...newIngredient, quantity: e.target.value})}
                    placeholder="Amount"
                  />
                </div>
                <div>
                  <Label htmlFor="unit">Unit</Label>
                  <Select value={newIngredient.unit} onValueChange={(value) => setNewIngredient({...newIngredient, unit: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {units.map(unit => (
                        <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={newIngredient.category} onValueChange={(value) => setNewIngredient({...newIngredient, category: value as Ingredient['category']})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="invisible">Action</Label>
                  <Button onClick={addIngredient} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cost">Cost per Unit</Label>
                  <Input
                    id="cost"
                    type="number"
                    value={newIngredient.cost}
                    onChange={(e) => setNewIngredient({...newIngredient, cost: e.target.value})}
                    placeholder="Cost"
                  />
                </div>
                <div>
                  <Label htmlFor="supplier">Supplier</Label>
                  <Input
                    id="supplier"
                    value={newIngredient.supplier}
                    onChange={(e) => setNewIngredient({...newIngredient, supplier: e.target.value})}
                    placeholder="Supplier name"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recipe Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Calculator className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <div className="text-2xl font-bold">{ingredients.length}</div>
                <div className="text-sm text-gray-600">Ingredients</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{getTotalWeight().toFixed(2)} kg</div>
                <div className="text-sm text-gray-600">Total Weight</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">${getTotalCost().toFixed(2)}</div>
                <div className="text-sm text-gray-600">Estimated Cost</div>
              </CardContent>
            </Card>
          </div>

          {/* Ingredients Table */}
          {ingredients.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recipe Ingredients</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Ingredient</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Cost</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ingredients.map((ingredient) => (
                      <TableRow key={ingredient.id}>
                        <TableCell className="font-medium">{ingredient.name}</TableCell>
                        <TableCell>{ingredient.quantity} {ingredient.unit}</TableCell>
                        <TableCell>{getCategoryBadge(ingredient.category)}</TableCell>
                        <TableCell>${(ingredient.cost * ingredient.quantity).toFixed(2)}</TableCell>
                        <TableCell>{ingredient.supplier || 'N/A'}</TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => removeIngredient(ingredient.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="bg-white border-t border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full flex flex-col sm:flex-row gap-3 justify-end">
          <Button variant="outline" onClick={onBack} className="sm:w-auto">
            Cancel
          </Button>
          <Button onClick={saveRecipe} className="sm:w-auto">
            <Save className="h-4 w-4 mr-2" />
            Save Recipe
          </Button>
        </div>
      </div>
    </div>
  );
};

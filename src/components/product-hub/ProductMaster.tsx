
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Plus, Filter, Grid, List, Package, Calendar, FileText } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  code: string;
  category: string;
  department: string;
  status: 'Active' | 'Development' | 'Discontinued';
  version: string;
  lastModified: string;
  complianceTags: string[];
  images: string[];
}

const mockProducts: Product[] = [
  {
    id: 'PRD-001',
    name: 'Paracetamol 500mg Tablets',
    code: 'PRD-PA-500',
    category: 'Tablet',
    department: 'Pharmaceuticals',
    status: 'Active',
    version: 'v2.1',
    lastModified: '2025-06-01',
    complianceTags: ['FDA 21 CFR Part 820', 'ISO 9001', 'GMP'],
    images: [
      'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1471864190281-a93a3070b6de?w=400&h=300&fit=crop'
    ]
  },
  {
    id: 'PRD-002',
    name: 'Vitamin C 1000mg Tablets',
    code: 'PRD-VC-1000',
    category: 'Tablet',
    department: 'Nutraceuticals',
    status: 'Active',
    version: 'v1.5',
    lastModified: '2025-05-28',
    complianceTags: ['ISO 9001', 'HACCP', 'GMP'],
    images: [
      'https://images.unsplash.com/photo-1550572017-edd951b55104?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1609840714821-c4effe79df10?w=400&h=300&fit=crop'
    ]
  },
  {
    id: 'PRD-003',
    name: 'Omega-3 Liquid Capsules',
    code: 'PRD-OM-LIQ',
    category: 'Capsule',
    department: 'Nutraceuticals',
    status: 'Development',
    version: 'v0.8',
    lastModified: '2025-06-03',
    complianceTags: ['ISO 9001', 'HACCP'],
    images: [
      'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=400&h=300&fit=crop'
    ]
  },
  {
    id: 'PRD-004',
    name: 'Probiotic Powder Supplement',
    code: 'PRD-PR-PWD',
    category: 'Powder',
    department: 'Nutraceuticals',
    status: 'Active',
    version: 'v1.2',
    lastModified: '2025-05-25',
    complianceTags: ['ISO 9001', 'HACCP', 'ISO 14001'],
    images: [
      'https://images.unsplash.com/photo-1505751172876-fa1923c5c528?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
    ]
  }
];

interface ProductMasterProps {
  onSelectProduct: (id: string) => void;
  onCreateProduct: () => void;
}

export const ProductMaster: React.FC<ProductMasterProps> = ({
  onSelectProduct,
  onCreateProduct
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [departmentFilter, setDepartmentFilter] = useState('All');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.code.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'All' || product.status === statusFilter;
    const matchesDepartment = departmentFilter === 'All' || product.department === departmentFilter;
    
    return matchesSearch && matchesStatus && matchesDepartment;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Development': return 'bg-yellow-100 text-yellow-800';
      case 'Discontinued': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 w-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Product Master</h1>
              <p className="text-gray-600">Manage your product catalog and specifications</p>
            </div>
            <Button onClick={onCreateProduct} className="gap-2">
              <Plus className="h-4 w-4" />
              Add New Product
            </Button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white border-b border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Status</SelectItem>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Development">Development</SelectItem>
                  <SelectItem value="Discontinued">Discontinued</SelectItem>
                </SelectContent>
              </Select>

              <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Departments</SelectItem>
                  <SelectItem value="Pharmaceuticals">Pharmaceuticals</SelectItem>
                  <SelectItem value="Nutraceuticals">Nutraceuticals</SelectItem>
                  <SelectItem value="Cosmetics">Cosmetics</SelectItem>
                  <SelectItem value="Medical Devices">Medical Devices</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-r-none"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-l-none"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid/List */}
      <div className="flex-1 overflow-y-auto p-4 md:p-6 w-full">
        <div className="w-full">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredProducts.map((product) => (
                <Card 
                  key={product.id} 
                  className="hover:shadow-lg transition-shadow cursor-pointer"
                  onClick={() => onSelectProduct(product.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="aspect-square mb-3 bg-gray-100 rounded-lg overflow-hidden">
                      {product.images.length > 0 ? (
                        <img 
                          src={product.images[0]} 
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-12 w-12 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 line-clamp-2">{product.name}</h3>
                      <p className="text-sm text-gray-600">{product.code}</p>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Badge className={getStatusColor(product.status)}>
                        {product.status}
                      </Badge>
                      <span className="text-sm text-gray-500">{product.version}</span>
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      <p>{product.department}</p>
                      <p>{product.category}</p>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {product.complianceTags.slice(0, 2).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {product.complianceTags.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{product.complianceTags.length - 2}
                        </Badge>
                      )}
                    </div>

                    <div className="text-xs text-gray-500 border-t pt-2">
                      Modified: {product.lastModified}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProducts.map((product) => (
                <Card 
                  key={product.id} 
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onSelectProduct(product.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                        {product.images.length > 0 ? (
                          <img 
                            src={product.images[0]} 
                            alt={product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-gray-900 truncate">{product.name}</h3>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(product.status)}>
                              {product.status}
                            </Badge>
                            <span className="text-sm text-gray-500">{product.version}</span>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-2">
                          <div>
                            <span className="font-medium">Code:</span> {product.code}
                          </div>
                          <div>
                            <span className="font-medium">Department:</span> {product.department}
                          </div>
                          <div>
                            <span className="font-medium">Category:</span> {product.category}
                          </div>
                          <div>
                            <span className="font-medium">Modified:</span> {product.lastModified}
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {product.complianceTags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length === 0 && (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-500 mb-4">
                No products match your current filters.
              </p>
              <div className="flex gap-2 justify-center">
                <Button variant="outline" onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('All');
                  setDepartmentFilter('All');
                }}>
                  Clear Filters
                </Button>
                <Button onClick={onCreateProduct}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight, Package, AlertTriangle, CheckCircle, Factory, Truck, GitBranch } from 'lucide-react';
import { BOMTraceDetailModal } from './BOMTraceDetailModal';

interface BOMNode {
  id: string;
  partNumber: string;
  description: string;
  quantity: number;
  unit: string;
  level: number;
  supplier?: string;
  batchNumber?: string;
  status: 'good' | 'warning' | 'critical';
  traceabilityScore: number;
  children?: BOMNode[];
  materialId?: string;
}

const mockBOMData: BOMNode = {
  id: 'bom-root',
  partNumber: 'GMP-SUPP-001',
  description: 'GMP Supplement Powder',
  quantity: 1,
  unit: 'batch',
  level: 0,
  status: 'good',
  traceabilityScore: 95,
  children: [
    {
      id: 'bom-001',
      partNumber: 'MAT-001',
      description: 'Maltodextrin',
      quantity: 200,
      unit: 'g',
      level: 1,
      supplier: 'ABC Chemicals',
      batchNumber: 'MAL-240115-001',
      status: 'good',
      traceabilityScore: 100,
      materialId: 'mat-maltodextrin',
      children: [
        {
          id: 'bom-001-01',
          partNumber: 'RAW-001',
          description: 'Corn Starch',
          quantity: 180,
          unit: 'g',
          level: 2,
          supplier: 'Corn Products Inc',
          batchNumber: 'CS-240110-003',
          status: 'good',
          traceabilityScore: 98
        },
        {
          id: 'bom-001-02',
          partNumber: 'RAW-002',
          description: 'Processing Aid',
          quantity: 20,
          unit: 'g',
          level: 2,
          supplier: 'Processing Solutions',
          batchNumber: 'PA-240112-001',
          status: 'warning',
          traceabilityScore: 85
        }
      ]
    },
    {
      id: 'bom-002',
      partNumber: 'MAT-002',
      description: 'Vitamin C (Ascorbic Acid)',
      quantity: 50,
      unit: 'mg',
      level: 1,
      supplier: 'XYZ Supplements',
      batchNumber: 'VitC-240114-002',
      status: 'good',
      traceabilityScore: 92,
      materialId: 'mat-vitamin-c'
    },
    {
      id: 'bom-003',
      partNumber: 'MAT-003',
      description: 'Magnesium Stearate',
      quantity: 5,
      unit: 'g',
      level: 1,
      supplier: 'DEF Materials',
      batchNumber: 'MgS-240113-001',
      status: 'critical',
      traceabilityScore: 65,
      materialId: 'mat-mag-stearate'
    }
  ]
};

interface BOMTreeViewProps {
  productId: string;
  searchTerm: string;
  onSelectBOM: (bomId: string) => void;
}

export const BOMTreeView: React.FC<BOMTreeViewProps> = ({ productId, searchTerm, onSelectBOM }) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['bom-root', 'bom-001']));
  const [selectedTraceNode, setSelectedTraceNode] = useState<BOMNode | null>(null);
  const [isTraceModalOpen, setIsTraceModalOpen] = useState(false);

  const handleViewTrace = (node: BOMNode) => {
    setSelectedTraceNode(node);
    setIsTraceModalOpen(true);
  };

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good": return "bg-green-100 text-green-800";
      case "warning": return "bg-orange-100 text-orange-800";
      case "critical": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "good": return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "warning": return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case "critical": return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTraceabilityBadge = (score: number) => {
    if (score >= 95) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 85) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>;
    if (score >= 70) return <Badge className="bg-orange-100 text-orange-800">Fair</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  const renderNode = (node: BOMNode) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;
    const indentLevel = node.level * 20;

    return (
      <div key={node.id} className="space-y-2">
        <Card className={`${node.level === 0 ? 'border-2 border-blue-200' : 'border-l-4 border-l-blue-200'}`} 
              style={{ marginLeft: `${indentLevel}px` }}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                {hasChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleNode(node.id)}
                    className="p-1 h-6 w-6"
                  >
                    {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </Button>
                )}
                {!hasChildren && <div className="w-6" />}
                
                {getStatusIcon(node.status)}
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{node.partNumber}</h4>
                    <Badge variant="outline" className="text-xs">
                      Level {node.level}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{node.description}</p>
                  <div className="flex gap-4 mt-1 text-xs text-gray-500">
                    <span>Qty: {node.quantity} {node.unit}</span>
                    {node.supplier && (
                      <span className="flex items-center gap-1">
                        <Factory className="h-3 w-3" />
                        {node.supplier}
                      </span>
                    )}
                    {node.batchNumber && (
                      <span className="flex items-center gap-1">
                        <Package className="h-3 w-3" />
                        {node.batchNumber}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {getTraceabilityBadge(node.traceabilityScore)}
                <Badge className={getStatusColor(node.status)}>
                  {node.status}
                </Badge>
                {node.materialId && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewTrace(node)}
                    className="text-xs"
                  >
                    View Trace
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {isExpanded && hasChildren && (
          <div className="space-y-2">
            {node.children!.map(child => renderNode(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Multi-Level BOM Structure
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setExpandedNodes(new Set(['bom-root', 'bom-001', 'bom-002', 'bom-003']))}>
                Expand All
              </Button>
              <Button variant="outline" size="sm" onClick={() => setExpandedNodes(new Set(['bom-root']))}>
                Collapse All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {renderNode(mockBOMData)}
        </CardContent>
      </Card>

      <BOMTraceDetailModal 
        open={isTraceModalOpen}
        onOpenChange={setIsTraceModalOpen}
        bomNode={selectedTraceNode}
      />
    </>
  );
};

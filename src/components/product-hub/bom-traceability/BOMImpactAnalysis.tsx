
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertTriangle, TrendingUp, Users, Package, Calendar, DollarSign, FileText, CheckCircle, XCircle, Clock, Factory } from 'lucide-react';

interface ChangeImpact {
  id: string;
  changeType: 'supplier' | 'material' | 'quantity' | 'specification';
  description: string;
  affectedItems: number;
  riskLevel: 'low' | 'medium' | 'high';
  cost: number;
  timeline: string;
  approvalRequired: boolean;
  impactDetails: {
    productionDelay: string;
    qualityRisk: string;
    supplierCapacity: string;
    regulatoryImpact: string;
    customerNotification: boolean;
  };
  affectedProducts: string[];
  mitigation: string[];
}

interface WhereUsedItem {
  id: string;
  productCode: string;
  productName: string;
  usage: string;
  impact: 'low' | 'medium' | 'high';
  lastUpdated: string;
  annualVolume: number;
  criticality: 'critical' | 'important' | 'standard';
  customerImpact: 'none' | 'low' | 'medium' | 'high';
}

const mockChangeImpacts: ChangeImpact[] = [
  {
    id: 'change-001',
    changeType: 'supplier',
    description: 'Change Maltodextrin supplier from ABC Chemicals to XYZ Corp',
    affectedItems: 15,
    riskLevel: 'medium',
    cost: 2500,
    timeline: '2-3 weeks',
    approvalRequired: true,
    impactDetails: {
      productionDelay: '3-5 business days',
      qualityRisk: 'Medium - requires qualification testing',
      supplierCapacity: '120% of current demand available',
      regulatoryImpact: 'FDA notification required',
      customerNotification: true
    },
    affectedProducts: ['Daily Multivitamin', 'Energy Boost', 'Immune Support'],
    mitigation: [
      'Parallel qualification of new supplier',
      'Maintain 2-week safety stock',
      'Customer communication plan',
      'Quality validation protocol'
    ]
  },
  {
    id: 'change-002',
    changeType: 'material',
    description: 'Replace Magnesium Stearate with alternative lubricant',
    affectedItems: 8,
    riskLevel: 'high',
    cost: 5000,
    timeline: '4-6 weeks',
    approvalRequired: true,
    impactDetails: {
      productionDelay: '2-3 weeks',
      qualityRisk: 'High - formulation change requires stability testing',
      supplierCapacity: '85% of demand available',
      regulatoryImpact: 'Major change notification required',
      customerNotification: true
    },
    affectedProducts: ['Daily Multivitamin', 'Calcium Plus', 'Joint Health'],
    mitigation: [
      'Extended stability testing protocol',
      'Bioequivalence studies',
      'Regulatory submission preparation',
      'Customer impact assessment'
    ]
  },
  {
    id: 'change-003',
    changeType: 'quantity',
    description: 'Reduce Vitamin C quantity by 10mg per batch',
    affectedItems: 3,
    riskLevel: 'low',
    cost: 500,
    timeline: '1 week',
    approvalRequired: false,
    impactDetails: {
      productionDelay: 'None',
      qualityRisk: 'Low - within specification range',
      supplierCapacity: 'Current supplier adequate',
      regulatoryImpact: 'Minor variation - notification only',
      customerNotification: false
    },
    affectedProducts: ['Vitamin C Complex'],
    mitigation: [
      'Update batch records',
      'Revise QC testing procedures',
      'Document control update'
    ]
  }
];

const mockWhereUsedData: WhereUsedItem[] = [
  {
    id: 'wu-001',
    productCode: 'PROD-001',
    productName: 'Daily Multivitamin',
    usage: 'Primary formula ingredient',
    impact: 'high',
    lastUpdated: '2024-01-15',
    annualVolume: 50000,
    criticality: 'critical',
    customerImpact: 'high'
  },
  {
    id: 'wu-002',
    productCode: 'PROD-002',
    productName: 'Energy Boost Supplement',
    usage: 'Secondary additive',
    impact: 'medium',
    lastUpdated: '2024-01-14',
    annualVolume: 25000,
    criticality: 'important',
    customerImpact: 'medium'
  },
  {
    id: 'wu-003',
    productCode: 'PROD-003',
    productName: 'Immune Support Formula',
    usage: 'Coating agent',
    impact: 'low',
    lastUpdated: '2024-01-13',
    annualVolume: 15000,
    criticality: 'standard',
    customerImpact: 'low'
  },
  {
    id: 'wu-004',
    productCode: 'PROD-004',
    productName: 'Calcium Plus',
    usage: 'Lubricant',
    impact: 'medium',
    lastUpdated: '2024-01-12',
    annualVolume: 30000,
    criticality: 'important',
    customerImpact: 'medium'
  }
];

interface BOMImpactAnalysisProps {
  productId: string;
  selectedBOM: string | null;
}

export const BOMImpactAnalysis: React.FC<BOMImpactAnalysisProps> = ({ productId, selectedBOM }) => {
  const [selectedChange, setSelectedChange] = useState<string | null>(null);
  const [selectedWhereUsed, setSelectedWhereUsed] = useState<string | null>(null);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCriticalityColor = (criticality: string) => {
    switch (criticality) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'important': return 'bg-orange-100 text-orange-800';
      case 'standard': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'medium': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'low': return <TrendingUp className="h-4 w-4 text-green-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const selectedChangeDetails = mockChangeImpacts.find(change => change.id === selectedChange);
  const selectedWhereUsedDetails = mockWhereUsedData.find(item => item.id === selectedWhereUsed);

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Impact Summary Cards - Responsive Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4">
        <Card>
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 md:h-5 md:w-5 text-red-600" />
              <div>
                <p className="text-xs md:text-sm text-gray-600">Critical Changes</p>
                <p className="text-lg md:text-2xl font-bold">2</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              <div>
                <p className="text-xs md:text-sm text-gray-600">Affected Products</p>
                <p className="text-lg md:text-2xl font-bold">26</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              <div>
                <p className="text-xs md:text-sm text-gray-600">Estimated Cost</p>
                <p className="text-lg md:text-2xl font-bold">$8K</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-3 md:p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
              <div>
                <p className="text-xs md:text-sm text-gray-600">Max Timeline</p>
                <p className="text-lg md:text-2xl font-bold">6 weeks</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="changes" className="w-full">
        <TabsList className="grid w-full grid-cols-2 h-auto">
          <TabsTrigger value="changes" className="text-xs md:text-sm py-2">Change Impact Analysis</TabsTrigger>
          <TabsTrigger value="whereused" className="text-xs md:text-sm py-2">Where Used Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="changes" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base md:text-lg">Potential BOM Changes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 md:space-y-4">
                {mockChangeImpacts.map((change) => (
                  <Card key={change.id} className="border-l-4 border-l-blue-200">
                    <CardContent className="p-3 md:p-4">
                      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-3">
                        <div className="flex-1 space-y-2">
                          <div className="flex flex-wrap items-center gap-2">
                            <Badge variant="outline" className="capitalize text-xs">
                              {change.changeType}
                            </Badge>
                            <Badge className={`${getRiskColor(change.riskLevel)} text-xs`}>
                              {change.riskLevel} risk
                            </Badge>
                            {change.approvalRequired && (
                              <Badge variant="outline" className="text-orange-600 text-xs">
                                Approval Required
                              </Badge>
                            )}
                          </div>
                          <h4 className="font-medium text-sm md:text-base">{change.description}</h4>
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 md:gap-4 text-xs md:text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <Package className="h-3 w-3 md:h-4 md:w-4" />
                              <span>{change.affectedItems} items</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3 md:h-4 md:w-4" />
                              <span>${change.cost.toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3 md:h-4 md:w-4" />
                              <span>{change.timeline}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3 md:h-4 md:w-4" />
                              <span>Quality Review</span>
                            </div>
                          </div>
                        </div>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedChange(change.id)}
                              className="text-xs"
                            >
                              View Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="text-lg">Change Impact Details</DialogTitle>
                            </DialogHeader>
                            {selectedChangeDetails && (
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium mb-2">Change Description</h4>
                                  <p className="text-sm text-gray-600">{selectedChangeDetails.description}</p>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                                    <h4 className="font-medium mb-2">Impact Assessment</h4>
                                    <div className="space-y-2 text-sm">
                                      <div className="flex justify-between">
                                        <span>Production Delay:</span>
                                        <span className="font-medium">{selectedChangeDetails.impactDetails.productionDelay}</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span>Quality Risk:</span>
                                        <span className="font-medium">{selectedChangeDetails.impactDetails.qualityRisk}</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span>Supplier Capacity:</span>
                                        <span className="font-medium">{selectedChangeDetails.impactDetails.supplierCapacity}</span>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <h4 className="font-medium mb-2">Regulatory Impact</h4>
                                    <div className="space-y-2 text-sm">
                                      <p>{selectedChangeDetails.impactDetails.regulatoryImpact}</p>
                                      <div className="flex items-center gap-2">
                                        {selectedChangeDetails.impactDetails.customerNotification ? (
                                          <CheckCircle className="h-4 w-4 text-orange-600" />
                                        ) : (
                                          <XCircle className="h-4 w-4 text-green-600" />
                                        )}
                                        <span>Customer Notification Required</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="font-medium mb-2">Affected Products</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {selectedChangeDetails.affectedProducts.map((product, index) => (
                                      <Badge key={index} variant="outline" className="text-xs">
                                        {product}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                                
                                <div>
                                  <h4 className="font-medium mb-2">Mitigation Actions</h4>
                                  <ul className="space-y-1 text-sm">
                                    {selectedChangeDetails.mitigation.map((action, index) => (
                                      <li key={index} className="flex items-start gap-2">
                                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        <span>{action}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="whereused" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base md:text-lg">Where Used Analysis</CardTitle>
              <p className="text-xs md:text-sm text-gray-600">
                Products and assemblies that use the selected BOM component
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 md:space-y-4">
                {mockWhereUsedData.map((item) => (
                  <Card key={item.id} className="border-l-4 border-l-green-200">
                    <CardContent className="p-3 md:p-4">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
                        <div className="flex items-start gap-3 flex-1">
                          {getImpactIcon(item.impact)}
                          <div className="flex-1 space-y-1">
                            <div className="flex flex-col md:flex-row md:items-center gap-2">
                              <h4 className="font-medium text-sm md:text-base">{item.productCode}</h4>
                              <div className="flex flex-wrap gap-1">
                                <Badge className={getCriticalityColor(item.criticality)} variant="outline">
                                  {item.criticality}
                                </Badge>
                                <Badge className={getRiskColor(item.customerImpact)} variant="outline">
                                  {item.customerImpact} customer impact
                                </Badge>
                              </div>
                            </div>
                            <p className="text-sm md:text-base text-gray-900">{item.productName}</p>
                            <p className="text-xs md:text-sm text-gray-500">{item.usage}</p>
                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-500">
                              <span>Annual Volume: {item.annualVolume.toLocaleString()}</span>
                              <span>Updated: {item.lastUpdated}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col lg:items-end gap-2">
                          <Badge className={getRiskColor(item.impact)}>
                            {item.impact} impact
                          </Badge>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedWhereUsed(item.id)}
                                className="text-xs"
                              >
                                View Impact
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-xl">
                              <DialogHeader>
                                <DialogTitle>Product Impact Analysis</DialogTitle>
                              </DialogHeader>
                              {selectedWhereUsedDetails && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <h4 className="font-medium mb-2">Product Details</h4>
                                      <div className="space-y-1 text-sm">
                                        <p><strong>Code:</strong> {selectedWhereUsedDetails.productCode}</p>
                                        <p><strong>Name:</strong> {selectedWhereUsedDetails.productName}</p>
                                        <p><strong>Usage:</strong> {selectedWhereUsedDetails.usage}</p>
                                      </div>
                                    </div>
                                    <div>
                                      <h4 className="font-medium mb-2">Impact Metrics</h4>
                                      <div className="space-y-1 text-sm">
                                        <p><strong>Annual Volume:</strong> {selectedWhereUsedDetails.annualVolume.toLocaleString()}</p>
                                        <p><strong>Criticality:</strong> {selectedWhereUsedDetails.criticality}</p>
                                        <p><strong>Customer Impact:</strong> {selectedWhereUsedDetails.customerImpact}</p>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <h4 className="font-medium mb-2">Recommended Actions</h4>
                                    <ul className="space-y-1 text-sm">
                                      <li className="flex items-center gap-2">
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span>Notify product manager immediately</span>
                                      </li>
                                      <li className="flex items-center gap-2">
                                        <Clock className="h-4 w-4 text-orange-600" />
                                        <span>Schedule impact assessment meeting</span>
                                      </li>
                                      <li className="flex items-center gap-2">
                                        <FileText className="h-4 w-4 text-blue-600" />
                                        <span>Update product documentation</span>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Package, Factory, Route, AlertTriangle, CheckCircle, ExternalLink } from 'lucide-react';

interface TraceDetail {
  id: string;
  stage: string;
  location: string;
  timestamp: string;
  status: 'completed' | 'in_progress' | 'pending';
  details: string;
  operator?: string;
  temperature?: string;
  humidity?: string;
}

interface BOMTraceDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bomNode: {
    id: string;
    partNumber: string;
    description: string;
    batchNumber?: string;
    supplier?: string;
  } | null;
}

export const BOMTraceDetailModal: React.FC<BOMTraceDetailModalProps> = ({
  open,
  onOpenChange,
  bomNode
}) => {
  const mockTraceDetails: TraceDetail[] = [
    {
      id: 'trace-001',
      stage: 'Raw Material Receipt',
      location: 'Warehouse A',
      timestamp: '2024-01-10T08:30:00Z',
      status: 'completed',
      details: 'Material received and quality checked',
      operator: 'John Smith',
      temperature: '20°C',
      humidity: '45%'
    },
    {
      id: 'trace-002', 
      stage: 'Quality Testing',
      location: 'QC Lab',
      timestamp: '2024-01-10T14:15:00Z',
      status: 'completed',
      details: 'COA verified, all parameters within specification',
      operator: 'Sarah Johnson'
    },
    {
      id: 'trace-003',
      stage: 'Processing',
      location: 'Production Line 2',
      timestamp: '2024-01-12T09:45:00Z',
      status: 'completed',
      details: 'Material processed according to work instruction WI-001',
      operator: 'Mike Chen',
      temperature: '75°C'
    },
    {
      id: 'trace-004',
      stage: 'Packaging',
      location: 'Packaging Area',
      timestamp: '2024-01-13T11:20:00Z',
      status: 'in_progress',
      details: 'Currently being packaged for final product',
      operator: 'Lisa Wong'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress': return <Package className="h-4 w-4 text-blue-600" />;
      case 'pending': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!bomNode) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Trace Details: {bomNode.partNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Component Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Part Number</p>
                  <p className="font-medium">{bomNode.partNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Description</p>
                  <p className="font-medium">{bomNode.description}</p>
                </div>
                {bomNode.batchNumber && (
                  <div>
                    <p className="text-sm text-gray-600">Batch Number</p>
                    <p className="font-medium">{bomNode.batchNumber}</p>
                  </div>
                )}
                {bomNode.supplier && (
                  <div>
                    <p className="text-sm text-gray-600">Supplier</p>
                    <p className="font-medium">{bomNode.supplier}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Trace Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Material Journey Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTraceDetails.map((trace, index) => (
                  <div key={trace.id} className="flex gap-4 relative">
                    {/* Timeline connector */}
                    {index < mockTraceDetails.length - 1 && (
                      <div className="absolute left-5 top-10 w-0.5 h-16 bg-gray-200" />
                    )}
                    
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                        {getStatusIcon(trace.status)}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{trace.stage}</h4>
                        <Badge className={getStatusColor(trace.status)}>
                          {trace.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-2">
                        <div className="flex items-center gap-4">
                          <span>{trace.location}</span>
                          <span>{new Date(trace.timestamp).toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <p className="text-sm mb-2">{trace.details}</p>
                      
                      <div className="flex gap-4 text-xs text-gray-500">
                        {trace.operator && <span>Operator: {trace.operator}</span>}
                        {trace.temperature && <span>Temp: {trace.temperature}</span>}
                        {trace.humidity && <span>Humidity: {trace.humidity}</span>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
            <Button className="gap-2">
              <ExternalLink className="h-4 w-4" />
              View Full Material Journey
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Factory, TrendingUp, AlertTriangle, CheckCircle, Users, Package } from 'lucide-react';

interface SupplierTrace {
  id: string;
  supplierName: string;
  bomComponents: {
    partNumber: string;
    description: string;
    level: number;
    riskScore: number;
    qualityScore: number;
  }[];
  overallRisk: 'low' | 'medium' | 'high';
  certifications: string[];
  lastAudit: string;
  performance: {
    onTimeDelivery: number;
    qualityRate: number;
    responsiveness: number;
  };
}

const mockSupplierTraces: SupplierTrace[] = [
  {
    id: 'supplier-001',
    supplierName: 'ABC Chemicals',
    bomComponents: [
      {
        partNumber: 'MAT-001',
        description: 'Maltodextrin',
        level: 1,
        riskScore: 5,
        qualityScore: 98
      },
      {
        partNumber: 'RAW-001',
        description: 'Corn Starch',
        level: 2,
        riskScore: 8,
        qualityScore: 95
      }
    ],
    overallRisk: 'low',
    certifications: ['ISO 9001', 'FDA Registered', 'GMP Certified'],
    lastAudit: '2024-01-10',
    performance: {
      onTimeDelivery: 98,
      qualityRate: 99,
      responsiveness: 96
    }
  },
  {
    id: 'supplier-002',
    supplierName: 'XYZ Supplements',
    bomComponents: [
      {
        partNumber: 'MAT-002',
        description: 'Vitamin C (Ascorbic Acid)',
        level: 1,
        riskScore: 12,
        qualityScore: 92
      }
    ],
    overallRisk: 'low',
    certifications: ['ISO 9001', 'HACCP', 'Organic Certified'],
    lastAudit: '2024-01-08',
    performance: {
      onTimeDelivery: 95,
      qualityRate: 94,
      responsiveness: 92
    }
  },
  {
    id: 'supplier-003',
    supplierName: 'DEF Materials',
    bomComponents: [
      {
        partNumber: 'MAT-003',
        description: 'Magnesium Stearate',
        level: 1,
        riskScore: 35,
        qualityScore: 78
      }
    ],
    overallRisk: 'high',
    certifications: ['ISO 9001'],
    lastAudit: '2023-11-15',
    performance: {
      onTimeDelivery: 85,
      qualityRate: 82,
      responsiveness: 78
    }
  }
];

interface BOMSupplierTraceabilityProps {
  productId: string;
}

export const BOMSupplierTraceability: React.FC<BOMSupplierTraceabilityProps> = ({ productId }) => {
  const [selectedSupplier, setSelectedSupplier] = useState<string | null>(null);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'medium': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 95) return 'text-green-600';
    if (score >= 85) return 'text-blue-600';
    if (score >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Factory className="h-5 w-5" />
            BOM Supplier Traceability
          </CardTitle>
          <p className="text-sm text-gray-600">
            Track supplier relationships and risks through BOM hierarchy
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Total Suppliers</p>
                    <p className="text-2xl font-bold">{mockSupplierTraces.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="text-sm text-gray-600">High Risk</p>
                    <p className="text-2xl font-bold">
                      {mockSupplierTraces.filter(s => s.overallRisk === 'high').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">Avg Quality</p>
                    <p className="text-2xl font-bold">92%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Supplier Overview</TabsTrigger>
          <TabsTrigger value="details">Detailed Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {mockSupplierTraces.map((supplier) => (
            <Card key={supplier.id} className="border-l-4 border-l-blue-200">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    {getRiskIcon(supplier.overallRisk)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{supplier.supplierName}</h4>
                        <Badge className={getRiskColor(supplier.overallRisk)}>
                          {supplier.overallRisk} risk
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 mb-3">
                        <div>
                          <p className="text-xs text-gray-500">BOM Components</p>
                          <p className="text-sm font-medium">{supplier.bomComponents.length} items</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Last Audit</p>
                          <p className="text-sm font-medium">{supplier.lastAudit}</p>
                        </div>
                      </div>

                      <div className="flex gap-4 text-sm">
                        <span className={`font-medium ${getScoreColor(supplier.performance.onTimeDelivery)}`}>
                          OTD: {supplier.performance.onTimeDelivery}%
                        </span>
                        <span className={`font-medium ${getScoreColor(supplier.performance.qualityRate)}`}>
                          Quality: {supplier.performance.qualityRate}%
                        </span>
                        <span className={`font-medium ${getScoreColor(supplier.performance.responsiveness)}`}>
                          Response: {supplier.performance.responsiveness}%
                        </span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedSupplier(supplier.id)}
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          {selectedSupplier ? (
            (() => {
              const supplier = mockSupplierTraces.find(s => s.id === selectedSupplier);
              if (!supplier) return <div>Supplier not found</div>;
              
              return (
                <Card>
                  <CardHeader>
                    <CardTitle>{supplier.supplierName} - Detailed Analysis</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">BOM Components</h4>
                      <div className="space-y-2">
                        {supplier.bomComponents.map((component, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded">
                            <div>
                              <p className="font-medium">{component.partNumber}</p>
                              <p className="text-sm text-gray-600">{component.description}</p>
                              <p className="text-xs text-gray-500">Level {component.level}</p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm">Risk: {component.riskScore}%</p>
                              <p className="text-sm">Quality: {component.qualityScore}%</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Certifications</h4>
                      <div className="flex gap-2 flex-wrap">
                        {supplier.certifications.map((cert, index) => (
                          <Badge key={index} variant="outline">{cert}</Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })()
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">Select a supplier from the overview to view detailed analysis</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

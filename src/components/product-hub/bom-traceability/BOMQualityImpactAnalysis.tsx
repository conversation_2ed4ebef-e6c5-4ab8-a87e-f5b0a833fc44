
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, TrendingDown, Target, Eye } from 'lucide-react';

interface QualityImpact {
  id: string;
  partNumber: string;
  description: string;
  bomLevel: number;
  issueType: 'critical' | 'major' | 'minor';
  issueDescription: string;
  affectedQuantity: string;
  downstreamImpact: {
    level: number;
    partNumber: string;
    impactType: 'quality' | 'performance' | 'compliance';
    severity: 'high' | 'medium' | 'low';
  }[];
  rootCause: string;
  correctionStatus: 'open' | 'in_progress' | 'resolved';
  dateReported: string;
  estimatedCost: number;
}

const mockQualityImpacts: QualityImpact[] = [
  {
    id: 'qi-001',
    partNumber: 'MAT-003',
    description: 'Magnesium Stearate',
    bomLevel: 1,
    issueType: 'critical',
    issueDescription: 'Contamination detected in batch MgS-240113-001',
    affectedQuantity: '500 units',
    downstreamImpact: [
      {
        level: 0,
        partNumber: 'GMP-SUPP-001',
        impactType: 'quality',
        severity: 'high'
      }
    ],
    rootCause: 'Supplier process deviation during manufacturing',
    correctionStatus: 'in_progress',
    dateReported: '2024-01-20',
    estimatedCost: 15000
  },
  {
    id: 'qi-002',
    partNumber: 'RAW-002',
    description: 'Processing Aid',
    bomLevel: 2,
    issueType: 'major',
    issueDescription: 'Moisture content out of specification',
    affectedQuantity: '200 kg',
    downstreamImpact: [
      {
        level: 1,
        partNumber: 'MAT-001',
        impactType: 'performance',
        severity: 'medium'
      },
      {
        level: 0,
        partNumber: 'GMP-SUPP-001',
        impactType: 'quality',
        severity: 'medium'
      }
    ],
    rootCause: 'Storage condition deviation during transport',
    correctionStatus: 'resolved',
    dateReported: '2024-01-18',
    estimatedCost: 5000
  }
];

interface BOMQualityImpactAnalysisProps {
  productId: string;
}

export const BOMQualityImpactAnalysis: React.FC<BOMQualityImpactAnalysisProps> = ({ productId }) => {
  const [selectedImpact, setSelectedImpact] = useState<string | null>(null);

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'major': return 'bg-orange-100 text-orange-800';
      case 'minor': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-red-100 text-red-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Critical Issues</p>
                <p className="text-2xl font-bold">
                  {mockQualityImpacts.filter(i => i.issueType === 'critical').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingDown className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Open Issues</p>
                <p className="text-2xl font-bold">
                  {mockQualityImpacts.filter(i => i.correctionStatus !== 'resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Affected Components</p>
                <p className="text-2xl font-bold">{mockQualityImpacts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="h-5 w-5 text-green-600">$</div>
              <div>
                <p className="text-sm text-gray-600">Estimated Cost</p>
                <p className="text-2xl font-bold">
                  ${mockQualityImpacts.reduce((sum, i) => sum + i.estimatedCost, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quality Issues List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            BOM Quality Impact Analysis
          </CardTitle>
          <p className="text-sm text-gray-600">
            Track quality issues and their propagation through BOM hierarchy
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockQualityImpacts.map((impact) => (
              <Card key={impact.id} className="border-l-4 border-l-red-200">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{impact.partNumber}</h4>
                        <Badge className={getIssueColor(impact.issueType)}>
                          {impact.issueType}
                        </Badge>
                        <Badge className={getStatusColor(impact.correctionStatus)}>
                          {impact.correctionStatus.replace('_', ' ')}
                        </Badge>
                        <Badge variant="outline">
                          Level {impact.bomLevel}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{impact.description}</p>
                      <p className="text-sm mb-3">{impact.issueDescription}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">Affected: </span>
                          {impact.affectedQuantity}
                        </div>
                        <div>
                          <span className="font-medium">Reported: </span>
                          {impact.dateReported}
                        </div>
                        <div>
                          <span className="font-medium">Impact: </span>
                          {impact.downstreamImpact.length} components
                        </div>
                        <div>
                          <span className="font-medium">Cost: </span>
                          ${impact.estimatedCost.toLocaleString()}
                        </div>
                      </div>

                      {impact.downstreamImpact.length > 0 && (
                        <div className="mb-3">
                          <p className="text-xs font-medium text-gray-500 mb-1">Downstream Impact:</p>
                          <div className="flex gap-2 flex-wrap">
                            {impact.downstreamImpact.map((downstream, index) => (
                              <div key={index} className="text-xs bg-gray-50 px-2 py-1 rounded flex items-center gap-1">
                                <span>{downstream.partNumber}</span>
                                <Badge className={getSeverityColor(downstream.severity)} style={{ fontSize: '10px' }}>
                                  {downstream.severity}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="text-xs text-gray-500">
                        <span className="font-medium">Root Cause: </span>
                        {impact.rootCause}
                      </div>
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedImpact(selectedImpact === impact.id ? null : impact.id)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {selectedImpact === impact.id ? 'Hide' : 'Details'}
                    </Button>
                  </div>
                  
                  {selectedImpact === impact.id && (
                    <div className="mt-4 pt-4 border-t">
                      <h5 className="font-medium mb-2">Detailed Impact Analysis</h5>
                      <div className="space-y-2">
                        {impact.downstreamImpact.map((downstream, index) => (
                          <div key={index} className="bg-gray-50 p-3 rounded">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{downstream.partNumber} (Level {downstream.level})</p>
                                <p className="text-sm text-gray-600 capitalize">{downstream.impactType} impact</p>
                              </div>
                              <Badge className={getSeverityColor(downstream.severity)}>
                                {downstream.severity} severity
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

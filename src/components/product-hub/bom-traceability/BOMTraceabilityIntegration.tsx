
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Link, Factory, AlertTriangle, ExternalLink } from 'lucide-react';
import { BOMBatchLinkage } from './BOMBatchLinkage';
import { BOMSupplierTraceability } from './BOMSupplierTraceability';
import { BOMQualityImpactAnalysis } from './BOMQualityImpactAnalysis';
import { useNavigate } from 'react-router-dom';

interface BOMTraceabilityIntegrationProps {
  productId: string;
}

export const BOMTraceabilityIntegration: React.FC<BOMTraceabilityIntegrationProps> = ({ productId }) => {
  const navigate = useNavigate();
  const [selectedBOMNode, setSelectedBOMNode] = useState<string | null>(null);

  const handleViewMaterialJourney = (batchNumber: string) => {
    // Navigate to the material journey timeline with the batch context
    navigate(`/material-journey-timeline?materialId=${batchNumber}`);
  };

  const handleViewFullTraceability = () => {
    // Navigate to the main traceability dashboard
    navigate('/traceability');
  };

  return (
    <div className="space-y-6">
      {/* Integration Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                BOM-Traceability Integration
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Integrated view of BOM hierarchy with complete supply chain traceability
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleViewFullTraceability}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Full Traceability Dashboard
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Link className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Linked Batches</p>
                    <p className="text-2xl font-bold">47/52</p>
                    <p className="text-xs text-gray-500">90% coverage</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Factory className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600">Traceable Suppliers</p>
                    <p className="text-2xl font-bold">15/18</p>
                    <p className="text-xs text-gray-500">83% coverage</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="text-sm text-gray-600">Quality Issues</p>
                    <p className="text-2xl font-bold">3</p>
                    <p className="text-xs text-gray-500">Tracked through BOM</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Integration Tabs */}
      <Tabs defaultValue="batch-linkage" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="batch-linkage">Batch Linkage</TabsTrigger>
          <TabsTrigger value="supplier-trace">Supplier Traceability</TabsTrigger>
          <TabsTrigger value="quality-impact">Quality Impact</TabsTrigger>
        </TabsList>

        <TabsContent value="batch-linkage" className="space-y-6">
          <BOMBatchLinkage 
            selectedBOMNode={selectedBOMNode}
            onViewMaterialJourney={handleViewMaterialJourney}
          />
        </TabsContent>

        <TabsContent value="supplier-trace" className="space-y-6">
          <BOMSupplierTraceability productId={productId} />
        </TabsContent>

        <TabsContent value="quality-impact" className="space-y-6">
          <BOMQualityImpactAnalysis productId={productId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { GitBranch, Package, Search, AlertTriangle, TrendingUp, Clock, Link } from 'lucide-react';
import { BOMTreeView } from './BOMTreeView';
import { BOMImpactAnalysis } from './BOMImpactAnalysis';
import { BOMTraceabilityTimeline } from './BOMTraceabilityTimeline';
import { BOMTraceabilityIntegration } from './BOMTraceabilityIntegration';
import { BOMReportsAnalytics } from './BOMReportsAnalytics';

interface BOMTraceabilityDashboardProps {
  productId: string;
}

export const BOMTraceabilityDashboard: React.FC<BOMTraceabilityDashboardProps> = ({ productId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBOM, setSelectedBOM] = useState<string | null>(null);

  return (
    <div className="space-y-6">
      {/* Header Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">BOM Levels</p>
                <p className="text-2xl font-bold">5</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <GitBranch className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Traceable Items</p>
                <p className="text-2xl font-bold">47/52</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Risk Items</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Last Updated</p>
                <p className="text-sm font-medium">2 hours ago</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">BOM Search & Navigation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search BOM items, part numbers, or suppliers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              Advanced Filters
            </Button>
          </div>
          
          <div className="flex gap-2 flex-wrap">
            <Badge variant="outline">All Levels</Badge>
            <Badge variant="outline">Critical Path</Badge>
            <Badge variant="outline">Supplier Risk</Badge>
            <Badge variant="outline">Quality Issues</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="tree" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="tree">BOM Tree View</TabsTrigger>
          <TabsTrigger value="integration">Traceability Integration</TabsTrigger>
          <TabsTrigger value="impact">Impact Analysis</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="reports">Reports & Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="tree" className="space-y-6">
          <BOMTreeView 
            productId={productId} 
            searchTerm={searchTerm}
            onSelectBOM={setSelectedBOM}
          />
        </TabsContent>

        <TabsContent value="integration" className="space-y-6">
          <BOMTraceabilityIntegration productId={productId} />
        </TabsContent>

        <TabsContent value="impact" className="space-y-6">
          <BOMImpactAnalysis 
            productId={productId}
            selectedBOM={selectedBOM}
          />
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <BOMTraceabilityTimeline 
            productId={productId}
            selectedBOM={selectedBOM}
          />
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <BOMReportsAnalytics productId={productId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

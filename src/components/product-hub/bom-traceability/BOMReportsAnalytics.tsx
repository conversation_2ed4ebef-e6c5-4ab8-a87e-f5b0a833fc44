
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BarChart3, FileText, TrendingUp, Download } from 'lucide-react';
import { BOMTraceabilityReports } from './reports/BOMTraceabilityReports';
import { BOMChangeTrackingReports } from './reports/BOMChangeTrackingReports';
import { BOMSupplierImpactAnalysis } from './reports/BOMSupplierImpactAnalysis';
import { BOMCostTraceabilityReport } from './reports/BOMCostTraceabilityReport';
import { BOMComplianceReports } from './reports/BOMComplianceReports';

interface BOMReportsAnalyticsProps {
  productId: string;
}

export const BOMReportsAnalytics: React.FC<BOMReportsAnalyticsProps> = ({ productId }) => {
  const [activeReport, setActiveReport] = useState('traceability');

  const reportStats = {
    totalReports: 24,
    recentReports: 8,
    scheduledReports: 5,
    criticalAlerts: 3
  };

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold">{reportStats.totalReports}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Recent Reports</p>
                <p className="text-2xl font-bold">{reportStats.recentReports}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold">{reportStats.scheduledReports}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="h-5 w-5 bg-red-600 rounded-full"></div>
              <div>
                <p className="text-sm text-gray-600">Critical Alerts</p>
                <p className="text-2xl font-bold text-red-600">{reportStats.criticalAlerts}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports Tabs */}
      <Tabs value={activeReport} onValueChange={setActiveReport} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="traceability">Traceability</TabsTrigger>
          <TabsTrigger value="changes">Change Tracking</TabsTrigger>
          <TabsTrigger value="supplier">Supplier Impact</TabsTrigger>
          <TabsTrigger value="cost">Cost Analysis</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="traceability" className="space-y-6">
          <BOMTraceabilityReports productId={productId} />
        </TabsContent>

        <TabsContent value="changes" className="space-y-6">
          <BOMChangeTrackingReports productId={productId} />
        </TabsContent>

        <TabsContent value="supplier" className="space-y-6">
          <BOMSupplierImpactAnalysis productId={productId} />
        </TabsContent>

        <TabsContent value="cost" className="space-y-6">
          <BOMCostTraceabilityReport productId={productId} />
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <BOMComplianceReports productId={productId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Package, Factory, Truck, AlertTriangle, CheckCircle, Clock } from 'lucide-react';

interface TimelineEvent {
  id: string;
  timestamp: string;
  eventType: 'created' | 'modified' | 'approved' | 'shipped' | 'received' | 'quality_check' | 'issue';
  title: string;
  description: string;
  actor: string;
  bomLevel: number;
  partNumber: string;
  status: 'completed' | 'in_progress' | 'pending' | 'failed';
  relatedDocuments?: string[];
}

const mockTimelineEvents: TimelineEvent[] = [
  {
    id: 'event-001',
    timestamp: '2024-01-15T14:30:00Z',
    eventType: 'quality_check',
    title: 'Quality inspection completed',
    description: 'Final quality check passed for Maltodextrin batch MAL-240115-001',
    actor: 'Quality Inspector <PERSON>',
    bomLevel: 1,
    partNumber: 'MAT-001',
    status: 'completed',
    relatedDocuments: ['QC-2024-001', 'COA-MAL-240115']
  },
  {
    id: 'event-002',
    timestamp: '2024-01-15T10:15:00Z',
    eventType: 'received',
    title: 'Material received',
    description: 'Maltodextrin shipment received from ABC Chemicals',
    actor: 'Warehouse Manager John Smith',
    bomLevel: 1,
    partNumber: 'MAT-001',
    status: 'completed'
  },
  {
    id: 'event-003',
    timestamp: '2024-01-14T16:45:00Z',
    eventType: 'shipped',
    title: 'Material shipped',
    description: 'Maltodextrin batch shipped from ABC Chemicals facility',
    actor: 'ABC Chemicals Logistics',
    bomLevel: 1,
    partNumber: 'MAT-001',
    status: 'completed'
  },
  {
    id: 'event-004',
    timestamp: '2024-01-12T09:20:00Z',
    eventType: 'issue',
    title: 'Quality deviation reported',
    description: 'Minor moisture content deviation detected in Processing Aid batch',
    actor: 'QC Analyst Mike Johnson',
    bomLevel: 2,
    partNumber: 'RAW-002',
    status: 'failed',
    relatedDocuments: ['DEV-2024-003', 'CAPA-2024-007']
  },
  {
    id: 'event-005',
    timestamp: '2024-01-11T13:00:00Z',
    eventType: 'approved',
    title: 'BOM change approved',
    description: 'BOM modification for GMP-SUPP-001 approved by engineering',
    actor: 'Engineering Manager Sarah Wilson',
    bomLevel: 0,
    partNumber: 'GMP-SUPP-001',
    status: 'completed',
    relatedDocuments: ['ECO-2024-012', 'BOM-v2.1']
  },
  {
    id: 'event-006',
    timestamp: '2024-01-10T11:30:00Z',
    eventType: 'created',
    title: 'New BOM version created',
    description: 'BOM v2.1 created for GMP Supplement Powder',
    actor: 'Product Engineer Alex Chen',
    bomLevel: 0,
    partNumber: 'GMP-SUPP-001',
    status: 'completed'
  }
];

interface BOMTraceabilityTimelineProps {
  productId: string;
  selectedBOM: string | null;
}

export const BOMTraceabilityTimeline: React.FC<BOMTraceabilityTimelineProps> = ({ productId, selectedBOM }) => {
  const [filterLevel, setFilterLevel] = useState<number | null>(null);
  const [filterType, setFilterType] = useState<string | null>(null);

  const getEventIcon = (eventType: string, status: string) => {
    if (status === 'failed') return <AlertTriangle className="h-5 w-5 text-red-600" />;
    
    switch (eventType) {
      case 'created': return <Package className="h-5 w-5 text-blue-600" />;
      case 'modified': return <Package className="h-5 w-5 text-orange-600" />;
      case 'approved': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'shipped': return <Truck className="h-5 w-5 text-purple-600" />;
      case 'received': return <Factory className="h-5 w-5 text-blue-600" />;
      case 'quality_check': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'issue': return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default: return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const filteredEvents = mockTimelineEvents.filter(event => {
    if (filterLevel !== null && event.bomLevel !== filterLevel) return false;
    if (filterType && event.eventType !== filterType) return false;
    return true;
  });

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Timeline Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap">
            <div className="flex gap-2">
              <span className="text-sm font-medium">BOM Level:</span>
              {[0, 1, 2].map((level) => (
                <Button
                  key={level}
                  variant={filterLevel === level ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterLevel(filterLevel === level ? null : level)}
                >
                  Level {level}
                </Button>
              ))}
            </div>
            <div className="flex gap-2">
              <span className="text-sm font-medium">Event Type:</span>
              {['quality_check', 'shipped', 'received', 'approved', 'issue'].map((type) => (
                <Button
                  key={type}
                  variant={filterType === type ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterType(filterType === type ? null : type)}
                  className="capitalize"
                >
                  {type.replace('_', ' ')}
                </Button>
              ))}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilterLevel(null);
                setFilterType(null);
              }}
            >
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            BOM Traceability Timeline
          </CardTitle>
          <p className="text-sm text-gray-600">
            Complete history of BOM changes, material movements, and quality events
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {filteredEvents.map((event, index) => {
              const { date, time } = formatDateTime(event.timestamp);
              
              return (
                <div key={event.id} className="relative flex gap-4">
                  {/* Timeline connector */}
                  {index < filteredEvents.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200" />
                  )}
                  
                  {/* Event icon */}
                  <div className="flex-shrink-0 w-12 h-12 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center">
                    {getEventIcon(event.eventType, event.status)}
                  </div>
                  
                  {/* Event content */}
                  <div className="flex-1 min-w-0">
                    <Card className="border-l-4 border-l-blue-200">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="font-medium">{event.title}</h4>
                            <p className="text-sm text-gray-600">{event.description}</p>
                          </div>
                          <div className="text-right flex-shrink-0 ml-4">
                            <div className="text-xs text-gray-500">{date}</div>
                            <div className="text-xs text-gray-500">{time}</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            {event.partNumber}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            Level {event.bomLevel}
                          </Badge>
                          <Badge className={getStatusColor(event.status)}>
                            {event.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        
                        <div className="text-xs text-gray-500 mb-2">
                          By: {event.actor}
                        </div>
                        
                        {event.relatedDocuments && (
                          <div className="flex gap-2 flex-wrap">
                            {event.relatedDocuments.map((doc) => (
                              <Button
                                key={doc}
                                variant="ghost"
                                size="sm"
                                className="h-6 px-2 text-xs text-blue-600"
                              >
                                {doc}
                              </Button>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

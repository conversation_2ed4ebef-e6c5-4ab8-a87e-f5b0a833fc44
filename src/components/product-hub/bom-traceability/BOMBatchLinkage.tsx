
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Package, Search, Link, AlertTriangle, CheckCircle, ExternalLink } from 'lucide-react';

interface BatchLink {
  id: string;
  bomNodeId: string;
  partNumber: string;
  batchNumber: string;
  lotNumber: string;
  supplier: string;
  linkStatus: 'linked' | 'partial' | 'unlinked';
  lastVerified: string;
  traceabilityScore: number;
}

const mockBatchLinks: BatchLink[] = [
  {
    id: 'link-001',
    bomNodeId: 'bom-001',
    partNumber: 'MAT-001',
    batchNumber: 'MAL-240115-001',
    lotNumber: 'LOT-A12345',
    supplier: 'ABC Chemicals',
    linkStatus: 'linked',
    lastVerified: '2024-01-15T14:30:00Z',
    traceabilityScore: 100
  },
  {
    id: 'link-002',
    bomNodeId: 'bom-002',
    partNumber: 'MAT-002',
    batchNumber: 'VitC-240114-002',
    lotNumber: 'LOT-B67890',
    supplier: 'XYZ Supplements',
    linkStatus: 'linked',
    lastVerified: '2024-01-14T16:45:00Z',
    traceabilityScore: 92
  },
  {
    id: 'link-003',
    bomNodeId: 'bom-003',
    partNumber: 'MAT-003',
    batchNumber: 'MgS-240113-001',
    lotNumber: 'LOT-C11111',
    supplier: 'DEF Materials',
    linkStatus: 'partial',
    lastVerified: '2024-01-13T09:20:00Z',
    traceabilityScore: 65
  }
];

interface BOMBatchLinkageProps {
  selectedBOMNode?: string;
  onViewMaterialJourney: (batchNumber: string) => void;
}

export const BOMBatchLinkage: React.FC<BOMBatchLinkageProps> = ({ 
  selectedBOMNode, 
  onViewMaterialJourney 
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'linked': return 'bg-green-100 text-green-800';
      case 'partial': return 'bg-orange-100 text-orange-800';
      case 'unlinked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'linked': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'partial': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'unlinked': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const filteredLinks = mockBatchLinks.filter(link =>
    link.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    link.batchNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    link.supplier.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          BOM-Batch Linkage
        </CardTitle>
        <p className="text-sm text-gray-600">
          Track relationships between BOM components and material batches
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by part number, batch, or supplier..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="space-y-3">
          {filteredLinks.map((link) => (
            <Card key={link.id} className="border-l-4 border-l-blue-200">
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    {getStatusIcon(link.linkStatus)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{link.partNumber}</h4>
                        <Badge className={getStatusColor(link.linkStatus)}>
                          {link.linkStatus}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                        <div>Batch: {link.batchNumber}</div>
                        <div>Lot: {link.lotNumber}</div>
                        <div>Supplier: {link.supplier}</div>
                        <div>Score: {link.traceabilityScore}%</div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Last verified: {new Date(link.lastVerified).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewMaterialJourney(link.batchNumber)}
                      className="text-xs"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View Journey
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

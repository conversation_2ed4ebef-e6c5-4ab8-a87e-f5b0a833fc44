
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Shield, AlertTriangle, CheckCircle } from 'lucide-react';

interface BOMComplianceReportsProps {
  productId: string;
}

export const BOMComplianceReports: React.FC<BOMComplianceReportsProps> = ({ productId }) => {
  const complianceStatus = [
    {
      regulation: 'FDA 21 CFR Part 820',
      status: 'compliant',
      bomCoverage: '100%',
      lastAudit: '2024-01-15',
      nextReview: '2024-07-15'
    },
    {
      regulation: 'ISO 13485:2016',
      status: 'compliant',
      bomCoverage: '98%',
      lastAudit: '2024-01-10',
      nextReview: '2024-06-10'
    },
    {
      regulation: 'EU MDR 2017/745',
      status: 'non_compliant',
      bomCoverage: '85%',
      lastAudit: '2024-01-05',
      nextReview: '2024-02-05'
    }
  ];

  const complianceIssues = [
    {
      id: 'issue-001',
      partNumber: 'MAT-003',
      issue: 'Missing CE marking documentation',
      regulation: 'EU MDR 2017/745',
      severity: 'high',
      dueDate: '2024-02-01',
      assignedTo: 'Compliance Team'
    },
    {
      id: 'issue-002',
      partNumber: 'MAT-001',
      issue: 'Supplier qualification documentation incomplete',
      regulation: 'ISO 13485:2016',
      severity: 'medium',
      dueDate: '2024-02-15',
      assignedTo: 'Quality Assurance'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800';
      case 'non_compliant': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'non_compliant': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <Shield className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Compliance Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Compliance Matrix
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Audit Trail Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Gap Analysis Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Compliance Status */}
      <Card>
        <CardHeader>
          <CardTitle>Regulatory Compliance Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {complianceStatus.map((item, index) => (
              <div key={index} className="p-4 border rounded">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(item.status)}
                    <h4 className="font-medium">{item.regulation}</h4>
                  </div>
                  <Badge className={getStatusColor(item.status)}>
                    {item.status.replace('_', ' ')}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                  <div>
                    <span className="font-medium">BOM Coverage: </span>
                    {item.bomCoverage}
                  </div>
                  <div>
                    <span className="font-medium">Last Audit: </span>
                    {item.lastAudit}
                  </div>
                  <div>
                    <span className="font-medium">Next Review: </span>
                    {item.nextReview}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Outstanding Issues */}
      <Card>
        <CardHeader>
          <CardTitle>Outstanding Compliance Issues</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {complianceIssues.map((issue) => (
              <div key={issue.id} className="p-4 border rounded border-l-4 border-l-red-200">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{issue.issue}</h4>
                      <Badge variant="outline">{issue.partNumber}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{issue.regulation}</p>
                    <div className="flex gap-4 text-xs text-gray-500">
                      <span>Due: {issue.dueDate}</span>
                      <span>Assigned to: {issue.assignedTo}</span>
                    </div>
                  </div>
                  <Badge className={getSeverityColor(issue.severity)}>
                    {issue.severity} severity
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

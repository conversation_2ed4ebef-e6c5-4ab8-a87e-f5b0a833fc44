
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Download, DollarSign, TrendingUp } from 'lucide-react';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';

interface BOMCostTraceabilityReportProps {
  productId: string;
}

export const BOMCostTraceabilityReport: React.FC<BOMCostTraceabilityReportProps> = ({ productId }) => {
  const costBreakdownData = [
    { name: 'Raw Materials', value: 65, cost: 325, color: '#3b82f6' },
    { name: 'Labor', value: 20, cost: 100, color: '#10b981' },
    { name: 'Overhead', value: 10, cost: 50, color: '#f59e0b' },
    { name: 'Packaging', value: 5, cost: 25, color: '#ef4444' }
  ];

  const costTrendData = [
    { month: 'Oct', totalCost: 480, materialCost: 310, laborCost: 95 },
    { month: 'Nov', totalCost: 495, materialCost: 320, laborCost: 98 },
    { month: 'Dec', totalCost: 510, materialCost: 330, laborCost: 102 },
    { month: 'Jan', totalCost: 500, materialCost: 325, laborCost: 100 }
  ];

  const bomCostDetails = [
    {
      level: 'Level 0',
      totalCost: 500,
      materialCost: 325,
      traceabilityScore: 100,
      costAccuracy: 99
    },
    {
      level: 'Level 1', 
      totalCost: 325,
      materialCost: 275,
      traceabilityScore: 95,
      costAccuracy: 94
    },
    {
      level: 'Level 2',
      totalCost: 275,
      materialCost: 225,
      traceabilityScore: 87,
      costAccuracy: 85
    }
  ];

  return (
    <div className="space-y-6">
      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Cost Traceability Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Cost Breakdown Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Cost Variance Analysis
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              ROI Impact Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Cost Breakdown by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={costBreakdownData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {costBreakdownData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`$${value}`, 'Cost']} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cost Trends Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={costTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="materialCost" fill="#3b82f6" name="Material Cost" />
                <Bar dataKey="laborCost" fill="#10b981" name="Labor Cost" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* BOM Cost Details */}
      <Card>
        <CardHeader>
          <CardTitle>BOM Level Cost Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {bomCostDetails.map((level, index) => (
              <div key={index} className="p-4 border rounded">
                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">BOM Level</p>
                    <p className="font-medium">{level.level}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Cost</p>
                    <p className="font-medium">${level.totalCost}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Traceability Score</p>
                    <p className="font-medium">{level.traceabilityScore}%</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Cost Accuracy</p>
                    <p className="font-medium">{level.costAccuracy}%</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Factory, TrendingDown, AlertTriangle } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface BOMSupplierImpactAnalysisProps {
  productId: string;
}

export const BOMSupplierImpactAnalysis: React.FC<BOMSupplierImpactAnalysisProps> = ({ productId }) => {
  const supplierRiskData = [
    { supplier: 'ABC Chemicals', riskScore: 15, bomItems: 5, criticalPath: true },
    { supplier: 'XYZ Supplements', riskScore: 25, bomItems: 3, criticalPath: false },
    { supplier: 'DEF Materials', riskScore: 65, bomItems: 2, criticalPath: true },
    { supplier: 'GHI Processing', riskScore: 35, bomItems: 4, criticalPath: false }
  ];

  const supplierPerformance = [
    {
      supplier: 'ABC Chemicals',
      onTime: 98,
      quality: 99,
      bomCoverage: '5/5 items',
      riskLevel: 'low'
    },
    {
      supplier: 'XYZ Supplements', 
      onTime: 95,
      quality: 94,
      bomCoverage: '3/3 items',
      riskLevel: 'low'
    },
    {
      supplier: 'DEF Materials',
      onTime: 85,
      quality: 82,
      bomCoverage: '2/2 items',
      riskLevel: 'high'
    }
  ];

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Factory className="h-5 w-5" />
            Supplier Impact Analysis Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Supplier Risk Matrix
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Performance Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Critical Path Analysis
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Supplier Risk Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Supplier Risk Score by BOM Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={supplierRiskData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="supplier" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="riskScore" fill="#ef4444" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Supplier Performance Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Supplier Performance Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {supplierPerformance.map((supplier, index) => (
              <div key={index} className="p-4 border rounded">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{supplier.supplier}</h4>
                    <Badge className={getRiskColor(supplier.riskLevel)}>
                      {supplier.riskLevel} risk
                    </Badge>
                  </div>
                  <span className="text-sm text-gray-600">{supplier.bomCoverage}</span>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-gray-600">On-Time Delivery</span>
                      <span className="text-sm font-medium">{supplier.onTime}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${supplier.onTime}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-gray-600">Quality Score</span>
                      <span className="text-sm font-medium">{supplier.quality}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${supplier.quality}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

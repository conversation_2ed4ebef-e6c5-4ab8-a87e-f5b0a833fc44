
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, GitBranch, Calendar } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface BOMChangeTrackingReportsProps {
  productId: string;
}

export const BOMChangeTrackingReports: React.FC<BOMChangeTrackingReportsProps> = ({ productId }) => {
  const changeHistoryData = [
    { month: 'Oct', changes: 12, approvals: 10, rejections: 2 },
    { month: 'Nov', changes: 18, approvals: 15, rejections: 3 },
    { month: 'Dec', changes: 25, approvals: 22, rejections: 3 },
    { month: 'Jan', changes: 15, approvals: 13, rejections: 2 }
  ];

  const recentChanges = [
    {
      id: 'chg-001',
      changeType: 'Component Substitution',
      partNumber: 'MAT-003',
      description: 'Replace Magnesium Stearate supplier',
      requestedBy: '<PERSON>',
      status: 'approved',
      dateRequested: '2024-01-18',
      impactLevel: 'medium'
    },
    {
      id: 'chg-002',
      changeType: 'Quantity Adjustment',
      partNumber: 'MAT-001',
      description: 'Increase Maltodextrin quantity from 200g to 220g',
      requestedBy: 'Sarah Johnson',
      status: 'pending_approval',
      dateRequested: '2024-01-19',
      impactLevel: 'low'
    },
    {
      id: 'chg-003',
      changeType: 'New Component Addition',
      partNumber: 'MAT-004',
      description: 'Add new stabilizer component',
      requestedBy: 'Mike Chen',
      status: 'under_review',
      dateRequested: '2024-01-20',
      impactLevel: 'high'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending_approval': return 'bg-orange-100 text-orange-800';
      case 'under_review': return 'bg-blue-100 text-blue-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = (level: string) => {
    switch (level) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            BOM Change Tracking Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Change History Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Impact Analysis Report
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Approval Workflow Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Change Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle>BOM Change Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={changeHistoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="changes" stroke="#3b82f6" name="Total Changes" />
              <Line type="monotone" dataKey="approvals" stroke="#10b981" name="Approvals" />
              <Line type="monotone" dataKey="rejections" stroke="#ef4444" name="Rejections" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Recent Changes */}
      <Card>
        <CardHeader>
          <CardTitle>Recent BOM Changes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentChanges.map((change) => (
              <div key={change.id} className="p-4 border rounded">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{change.changeType}</h4>
                      <Badge variant="outline">{change.partNumber}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{change.description}</p>
                    <div className="flex gap-4 text-xs text-gray-500">
                      <span>Requested by: {change.requestedBy}</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {change.dateRequested}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col gap-1">
                    <Badge className={getStatusColor(change.status)}>
                      {change.status.replace('_', ' ')}
                    </Badge>
                    <Badge className={getImpactColor(change.impactLevel)}>
                      {change.impactLevel} impact
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

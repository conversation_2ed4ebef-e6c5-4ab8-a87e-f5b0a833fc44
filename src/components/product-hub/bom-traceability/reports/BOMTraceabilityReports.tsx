
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, FileText, Filter } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface BOMTraceabilityReportsProps {
  productId: string;
}

export const BOMTraceabilityReports: React.FC<BOMTraceabilityReportsProps> = ({ productId }) => {
  const [reportType, setReportType] = useState('coverage');

  const traceabilityCoverageData = [
    { level: 'Level 0', coverage: 100, total: 1 },
    { level: 'Level 1', coverage: 95, total: 3 },
    { level: 'Level 2', coverage: 87, total: 8 },
    { level: 'Level 3', coverage: 72, total: 15 },
    { level: 'Level 4', coverage: 65, total: 25 }
  ];

  const traceabilityScoreData = [
    { name: 'Excellent (95-100%)', value: 65, color: '#10b981' },
    { name: 'Good (85-94%)', value: 20, color: '#3b82f6' },
    { name: 'Fair (70-84%)', value: 10, color: '#f59e0b' },
    { name: 'Poor (<70%)', value: 5, color: '#ef4444' }
  ];

  const recentReports = [
    {
      id: 'rep-001',
      name: 'BOM Traceability Coverage Report',
      type: 'Coverage Analysis',
      generatedDate: '2024-01-20',
      status: 'completed',
      size: '2.4 MB'
    },
    {
      id: 'rep-002',
      name: 'Supplier Traceability Matrix',
      type: 'Supplier Analysis',
      generatedDate: '2024-01-19',
      status: 'completed',
      size: '1.8 MB'
    },
    {
      id: 'rep-003',
      name: 'Quality Impact Traceability',
      type: 'Quality Analysis',
      generatedDate: '2024-01-18',
      status: 'in_progress',
      size: '3.2 MB'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleGenerateReport = (type: string) => {
    alert(`Generating ${type} report...`);
  };

  return (
    <div className="space-y-6">
      {/* Report Generation Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generate Traceability Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              onClick={() => handleGenerateReport('Coverage Analysis')}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Coverage Report
            </Button>
            <Button 
              onClick={() => handleGenerateReport('Batch Linkage')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Batch Linkage Report
            </Button>
            <Button 
              onClick={() => handleGenerateReport('Gap Analysis')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Gap Analysis Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Traceability Coverage by BOM Level</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={traceabilityCoverageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="level" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="coverage" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Traceability Score Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={traceabilityScoreData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {traceabilityScoreData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Traceability Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentReports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-3 border rounded">
                <div className="flex-1">
                  <h4 className="font-medium">{report.name}</h4>
                  <div className="flex gap-4 text-sm text-gray-600 mt-1">
                    <span>Type: {report.type}</span>
                    <span>Generated: {report.generatedDate}</span>
                    <span>Size: {report.size}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(report.status)}>
                    {report.status}
                  </Badge>
                  {report.status === 'completed' && (
                    <Button variant="ghost" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

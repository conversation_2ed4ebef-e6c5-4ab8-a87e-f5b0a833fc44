
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, ArrowLeft, Plus, Upload, Image } from 'lucide-react';

interface ProductCreationFormProps {
  onBack: () => void;
  onSave: (productData: any) => void;
  onCreateBOM: () => void;
  onCreateRecipe: () => void;
}

export const ProductCreationForm: React.FC<ProductCreationFormProps> = ({
  onBack,
  onSave,
  onCreateBOM,
  onCreateRecipe
}) => {
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    department: '',
    category: '',
    complianceTags: [] as string[],
    version: 'v1.0',
    status: 'draft',
    images: [] as string[]
  });

  const availableTags = [
    'ISO 9001', 'ISO 13485', 'FDA 21 CFR Part 820', 'CE Marking', 
    'GMP', 'HACCP', 'ISO 14001', 'OHSAS 18001'
  ];

  // Mock product images for different categories
  const mockProductImages = {
    tablet: [
      'https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1471864190281-a93a3070b6de?w=400&h=300&fit=crop'
    ],
    capsule: [
      'https://images.unsplash.com/photo-1550572017-edd951b55104?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1609840714821-c4effe79df10?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1585435557343-3b092031caa2?w=400&h=300&fit=crop'
    ],
    liquid: [
      'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1587854692152-cbe660dbde88?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1612077829463-3ac9d2200075?w=400&h=300&fit=crop'
    ],
    powder: [
      'https://images.unsplash.com/photo-1505751172876-fa1923c5c528?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1607619056574-7b8d3ee536b2?w=400&h=300&fit=crop'
    ],
    cream: [
      'https://images.unsplash.com/photo-1556228724-d8fdf7e13b5c?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1564121071413-20e4fd8b4f28?w=400&h=300&fit=crop'
    ]
  };

  const generateProductCode = (name: string) => {
    if (!name) return '';
    const words = name.split(' ').filter(word => word.length > 2);
    const code = words.slice(0, 2).map(word => word.substring(0, 2).toUpperCase()).join('-');
    return `PRD-${code}-${Math.floor(Math.random() * 1000)}`;
  };

  const handleNameChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      name: value,
      code: generateProductCode(value)
    }));
  };

  const handleCategoryChange = (category: string) => {
    const categoryImages = mockProductImages[category as keyof typeof mockProductImages] || [];
    setFormData(prev => ({
      ...prev,
      category,
      images: categoryImages.slice(0, 3) // Auto-assign first 3 images
    }));
  };

  const addComplianceTag = (tag: string) => {
    if (!formData.complianceTags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        complianceTags: [...prev.complianceTags, tag]
      }));
    }
  };

  const removeComplianceTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      complianceTags: prev.complianceTags.filter(t => t !== tag)
    }));
  };

  const handleImageUpload = () => {
    // Simulate image upload by adding a random mock image
    const allImages = Object.values(mockProductImages).flat();
    const randomImage = allImages[Math.floor(Math.random() * allImages.length)];
    
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, randomImage]
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (status: 'draft' | 'approval') => {
    onSave({ ...formData, status });
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 w-full">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">Add New Product</h1>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-4 md:p-6 w-full">
        <div className="w-full space-y-6">
          
          {/* Section 1: Product Info */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="productName">Product Name *</Label>
                  <Input
                    id="productName"
                    value={formData.name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    placeholder="e.g., Paracetamol 500mg Tablets"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="productCode">Product Code</Label>
                  <Input
                    id="productCode"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="Auto-generated"
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="department">Department *</Label>
                  <Select value={formData.department} onValueChange={(value) => setFormData(prev => ({ ...prev, department: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pharmaceuticals">Pharmaceuticals</SelectItem>
                      <SelectItem value="nutraceuticals">Nutraceuticals</SelectItem>
                      <SelectItem value="cosmetics">Cosmetics</SelectItem>
                      <SelectItem value="medical-devices">Medical Devices</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select value={formData.category} onValueChange={handleCategoryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tablet">Tablet</SelectItem>
                      <SelectItem value="capsule">Capsule</SelectItem>
                      <SelectItem value="liquid">Liquid</SelectItem>
                      <SelectItem value="powder">Powder</SelectItem>
                      <SelectItem value="cream">Cream</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Compliance Tags</Label>
                <div className="mt-2 space-y-3">
                  <Select onValueChange={addComplianceTag}>
                    <SelectTrigger>
                      <SelectValue placeholder="Add compliance tags" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableTags.filter(tag => !formData.complianceTags.includes(tag)).map(tag => (
                        <SelectItem key={tag} value={tag}>{tag}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formData.complianceTags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.complianceTags.map(tag => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => removeComplianceTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 2: Product Images */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Product Images</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative group">
                    <img 
                      src={image} 
                      alt={`Product ${index + 1}`}
                      className="w-full h-32 object-cover rounded-lg border"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
                
                <button
                  onClick={handleImageUpload}
                  className="h-32 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center hover:border-gray-400 transition-colors"
                >
                  <Upload className="h-6 w-6 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-500">Upload Image</span>
                </button>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start gap-3">
                  <Image className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-900">Image Guidelines</p>
                    <p className="text-sm text-blue-700 mt-1">
                      Upload high-quality images showing product packaging, labels, and different angles. 
                      Recommended size: 800x600px. Supported formats: JPG, PNG, WebP.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 3: Recipe + BOM */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Recipe & Bill of Materials</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <Label>Recipe</Label>
                  <Button 
                    variant="outline" 
                    className="w-full justify-start"
                    onClick={onCreateRecipe}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Recipe
                  </Button>
                </div>
                <div className="space-y-3">
                  <Label>Bill of Materials</Label>
                  <div className="space-y-2">
                    <Button 
                      variant="outline" 
                      className="w-full justify-start"
                      onClick={onCreateBOM}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create New BOM
                    </Button>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Or select existing BOM" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bom1">BOM-PA-500-V1</SelectItem>
                        <SelectItem value="bom2">BOM-VC-1000-V2</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 4: Versioning */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Version Control</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div>
                  <Label>Current Version</Label>
                  <div className="text-lg font-mono text-blue-600 mt-1">{formData.version}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Sticky Bottom Actions */}
      <div className="bg-white border-t border-gray-200 p-4 md:p-6 w-full">
        <div className="w-full flex flex-col sm:flex-row gap-3 justify-end">
          <Button variant="outline" onClick={onBack} className="sm:w-auto">
            Cancel
          </Button>
          <Button 
            variant="outline" 
            onClick={() => handleSubmit('draft')}
            className="sm:w-auto"
          >
            Save as Draft
          </Button>
          <Button 
            onClick={() => handleSubmit('approval')}
            className="sm:w-auto"
          >
            Submit for Approval
          </Button>
        </div>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ProductOverviewProps {
  productId: string;
}

export const ProductOverview: React.FC<ProductOverviewProps> = ({ productId }) => {
  const [isApproved, setIsApproved] = useState(true);
  const [formData, setFormData] = useState({
    name: 'GMP Supplement Powder',
    code: 'GMP-POW-001',
    category: 'powder',
    department: 'nutraceuticals'
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="productName">Product Name</Label>
              <Input
                id="productName"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productCode">Product Code</Label>
              <Input
                id="productCode"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                placeholder="Auto-generated"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="powder">Powder</SelectItem>
                  <SelectItem value="tablet">Tablet</SelectItem>
                  <SelectItem value="liquid">Liquid</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nutraceuticals">Nutraceuticals</SelectItem>
                  <SelectItem value="pharma">Pharma</SelectItem>
                  <SelectItem value="cosmetics">Cosmetics</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Regulatory Tags</Label>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">ISO 9001</Badge>
                <Badge variant="secondary">FSSAI</Badge>
                <Button variant="outline" size="sm">+ Add Tag</Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="createdBy">Created By</Label>
              <Input
                id="createdBy"
                value="John Smith"
                disabled
                className="bg-gray-50"
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="status">Status</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm">Draft</span>
                <Switch
                  id="status"
                  checked={isApproved}
                  onCheckedChange={setIsApproved}
                />
                <span className="text-sm">Approved</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sticky Bottom Buttons */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
        <div className="flex gap-3">
          <Button variant="outline" className="flex-1">Cancel</Button>
          <Button className="flex-1">Save</Button>
        </div>
      </div>
    </div>
  );
};

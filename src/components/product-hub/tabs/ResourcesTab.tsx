
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON>, Setting<PERSON>, Wrench, Zap } from 'lucide-react';

interface Resource {
  id: string;
  name: string;
  code: string;
  assignedSteps: string[];
  availability: 'Available' | 'In Use' | 'Maintenance';
}

const mockResources = {
  operators: [
    {
      id: '1',
      name: '<PERSON>',
      code: 'OP-001',
      assignedSteps: ['Mixing', 'Packaging'],
      availability: 'Available' as const
    },
    {
      id: '2',
      name: '<PERSON>',
      code: 'OP-002',
      assignedSteps: ['Quality Control'],
      availability: 'In Use' as const
    }
  ],
  machines: [
    {
      id: '1',
      name: 'Mixer 2000',
      code: 'MACH-002',
      assignedSteps: ['Mixing', 'Granulation'],
      availability: 'Available' as const
    },
    {
      id: '2',
      name: 'Tablet Press',
      code: 'MACH-003',
      assignedSteps: ['Compression'],
      availability: 'In Use' as const
    }
  ],
  tools: [
    {
      id: '1',
      name: 'Digital Scale',
      code: 'TOOL-001',
      assignedSteps: ['Weighing'],
      availability: 'Available' as const
    }
  ],
  utilities: [
    {
      id: '1',
      name: 'Compressed Air',
      code: 'UTIL-001',
      assignedSteps: ['Mixing', 'Cleaning'],
      availability: 'Available' as const
    }
  ]
};

interface ResourcesTabProps {
  productId: string;
}

export const ResourcesTab: React.FC<ResourcesTabProps> = ({ productId }) => {
  const [selectedCategory, setSelectedCategory] = useState('operators');

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'Available': return 'bg-green-100 text-green-800';
      case 'In Use': return 'bg-yellow-100 text-yellow-800';
      case 'Maintenance': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'operators': return <Users className="h-5 w-5" />;
      case 'machines': return <Settings className="h-5 w-5" />;
      case 'tools': return <Wrench className="h-5 w-5" />;
      case 'utilities': return <Zap className="h-5 w-5" />;
      default: return null;
    }
  };

  const renderResourceCards = (resources: Resource[]) => (
    <div className="grid gap-4">
      {resources.map((resource) => (
        <Card key={resource.id} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-medium text-gray-900">{resource.name}</h3>
                <p className="text-sm text-gray-600">Code: {resource.code}</p>
              </div>
              <Badge className={getAvailabilityColor(resource.availability)}>
                {resource.availability}
              </Badge>
            </div>

            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium text-gray-700">Assigned Steps:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {resource.assignedSteps.map((step, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {step}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between pt-2 border-t">
                <span className="text-sm font-medium text-gray-700">Available</span>
                <Switch checked={resource.availability === 'Available'} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-4">Resources</h2>

        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-4 mb-4">
            <TabsTrigger value="operators" className="flex items-center gap-2 text-xs">
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">Operators</span>
            </TabsTrigger>
            <TabsTrigger value="machines" className="flex items-center gap-2 text-xs">
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Machines</span>
            </TabsTrigger>
            <TabsTrigger value="tools" className="flex items-center gap-2 text-xs">
              <Wrench className="h-4 w-4" />
              <span className="hidden sm:inline">Tools</span>
            </TabsTrigger>
            <TabsTrigger value="utilities" className="flex items-center gap-2 text-xs">
              <Zap className="h-4 w-4" />
              <span className="hidden sm:inline">Utilities</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="operators">
            {renderResourceCards(mockResources.operators)}
          </TabsContent>

          <TabsContent value="machines">
            {renderResourceCards(mockResources.machines)}
          </TabsContent>

          <TabsContent value="tools">
            {renderResourceCards(mockResources.tools)}
          </TabsContent>

          <TabsContent value="utilities">
            {renderResourceCards(mockResources.utilities)}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Plus, Upload, Trash } from 'lucide-react';

interface BOMItem {
  id: string;
  name: string;
  code: string;
  quantity: string;
  unit: string;
  supplier: string;
  substituteAllowed: boolean;
}

const mockBOMItems: BOMItem[] = [
  {
    id: '1',
    name: 'Maltodextrin',
    code: 'MAT-001',
    quantity: '200',
    unit: 'g',
    supplier: 'ABC Chemicals',
    substituteAllowed: true
  },
  {
    id: '2',
    name: 'Vitamin C',
    code: 'MAT-002',
    quantity: '50',
    unit: 'mg',
    supplier: 'XYZ Supplements',
    substituteAllowed: false
  },
  {
    id: '3',
    name: 'Magnesium Stearate',
    code: 'MAT-003',
    quantity: '5',
    unit: 'g',
    supplier: 'DEF Materials',
    substituteAllowed: true
  }
];

interface BOMTabProps {
  productId: string;
}

export const BOMTab: React.FC<BOMTabProps> = ({ productId }) => {
  const [bomItems, setBomItems] = useState(mockBOMItems);

  const handleSwipeDelete = (itemId: string) => {
    setBomItems(prev => prev.filter(item => item.id !== itemId));
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold">Bill of Materials</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="gap-2 hidden md:flex">
              <Upload className="h-4 w-4" />
              Bulk Upload
            </Button>
            <Button size="sm" className="gap-2">
              <Plus className="h-4 w-4" />
              Add Material
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          {bomItems.map((item) => (
            <Card key={item.id} className="relative overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.name}</h3>
                    <p className="text-sm text-gray-600">Code: {item.code}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => handleSwipeDelete(item.id)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Quantity:</span>
                    <p className="text-gray-900">{item.quantity} {item.unit}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Supplier:</span>
                    <p className="text-gray-900">{item.supplier}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-3 pt-3 border-t">
                  <span className="text-sm font-medium text-gray-700">Substitute Allowed</span>
                  <div className="flex items-center gap-2">
                    <Switch checked={item.substituteAllowed} />
                    {item.substituteAllowed && (
                      <Badge variant="secondary" className="text-xs">Yes</Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {bomItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No materials added yet</p>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add First Material
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Plus, GripVertical, Edit, Trash, ChevronDown, Star } from 'lucide-react';

interface ProcessStep {
  id: string;
  name: string;
  type: 'Machine' | 'Manual' | 'Quality Check';
  estimatedTime: string;
  conditional: boolean;
  instruction: string;
  dataCaptureFields: { name: string; type: string }[];
  isCritical: boolean;
}

const mockSteps: ProcessStep[] = [
  {
    id: '1',
    name: 'Mixing',
    type: 'Machine',
    estimatedTime: '30 mins',
    conditional: true,
    instruction: 'Mix at 60 RPM for 30 mins',
    dataCaptureFields: [
      { name: 'RPM', type: 'numeric' },
      { name: 'Time', type: 'timer' }
    ],
    isCritical: true
  },
  {
    id: '2',
    name: 'Granulation',
    type: 'Machine',
    estimatedTime: '45 mins',
    conditional: false,
    instruction: 'Granulate using wet granulation method',
    dataCaptureFields: [
      { name: 'Moisture Level', type: 'numeric' },
      { name: 'Temperature', type: 'numeric' }
    ],
    isCritical: false
  }
];

interface RecipeTabProps {
  productId: string;
}

export const RecipeTab: React.FC<RecipeTabProps> = ({ productId }) => {
  const [steps, setSteps] = useState(mockSteps);
  const [expandedSteps, setExpandedSteps] = useState<string[]>(['1']);

  const toggleStep = (stepId: string) => {
    setExpandedSteps(prev =>
      prev.includes(stepId)
        ? prev.filter(id => id !== stepId)
        : [...prev, stepId]
    );
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Machine': return 'bg-blue-100 text-blue-800';
      case 'Manual': return 'bg-green-100 text-green-800';
      case 'Quality Check': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold">Process Steps</h2>
          <Button size="sm" className="gap-2">
            <Plus className="h-4 w-4" />
            Add Step
          </Button>
        </div>

        {steps.map((step) => (
          <Card key={step.id} className="overflow-hidden">
            <Collapsible
              open={expandedSteps.includes(step.id)}
              onOpenChange={() => toggleStep(step.id)}
            >
              <CollapsibleTrigger asChild>
                <div className="p-4 cursor-pointer hover:bg-gray-50 transition-colors">
                  <div className="flex items-center gap-3">
                    <GripVertical className="h-4 w-4 text-gray-400" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{step.name}</h3>
                        {step.isCritical && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Badge className={getTypeColor(step.type)} variant="secondary">
                          {step.type}
                        </Badge>
                        <span>{step.estimatedTime}</span>
                        {step.conditional && (
                          <Badge variant="outline">Conditional</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <Trash className="h-4 w-4 text-red-500" />
                      </Button>
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <div className="px-4 pb-4 space-y-4 border-t bg-gray-50">
                  <div className="space-y-2 pt-4">
                    <label className="text-sm font-medium">Instruction</label>
                    <Textarea
                      value={step.instruction}
                      placeholder="Enter detailed instructions..."
                      className="min-h-[80px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Data Capture Fields</label>
                    <div className="space-y-2">
                      {step.dataCaptureFields.map((field, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            value={field.name}
                            placeholder="Field name"
                            className="flex-1"
                          />
                          <Select value={field.type}>
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="numeric">Numeric</SelectItem>
                              <SelectItem value="timer">Timer</SelectItem>
                              <SelectItem value="text">Text</SelectItem>
                              <SelectItem value="dropdown">Dropdown</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      ))}
                      <Button variant="outline" size="sm">+ Add Field</Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Mark as Critical</label>
                    <Switch checked={step.isCritical} />
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { GitCompare, Calendar, User } from 'lucide-react';

interface Version {
  id: string;
  version: string;
  status: 'Approved' | 'Draft' | 'Archived';
  createdDate: string;
  createdBy: string;
  summary: string;
  changes: string[];
}

const mockVersions: Version[] = [
  {
    id: '1',
    version: '1.2',
    status: 'Approved',
    createdDate: '2025-06-01',
    createdBy: '<PERSON>',
    summary: 'Updated mixing parameters',
    changes: [
      'Increased mixing time from 25 to 30 minutes',
      'Updated RPM from 55 to 60',
      'Added temperature monitoring step'
    ]
  },
  {
    id: '2',
    version: '1.1',
    status: 'Archived',
    createdDate: '2025-05-15',
    createdBy: '<PERSON>',
    summary: 'Initial quality improvements',
    changes: [
      'Added quality control checkpoints',
      'Updated material specifications',
      'Refined granulation process'
    ]
  },
  {
    id: '3',
    version: '1.0',
    status: 'Archived',
    createdDate: '2025-04-20',
    createdBy: 'Mike Johnson',
    summary: 'Initial version',
    changes: [
      'Created base recipe',
      'Defined process steps',
      'Set initial parameters'
    ]
  }
];

interface VersionsTabProps {
  productId: string;
}

export const VersionsTab: React.FC<VersionsTabProps> = ({ productId }) => {
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);
  const [compareVersion, setCompareVersion] = useState<Version | null>(null);
  const [showCompareModal, setShowCompareModal] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Draft': return 'bg-yellow-100 text-yellow-800';
      case 'Archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCompare = (version1: Version, version2: Version) => {
    setSelectedVersion(version1);
    setCompareVersion(version2);
    setShowCompareModal(true);
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <h2 className="text-lg font-semibold">Version History</h2>

        <div className="space-y-3">
          {mockVersions.map((version, index) => (
            <Card key={version.id} className="relative">
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="flex flex-col items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-800">
                          v{version.version}
                        </span>
                      </div>
                      {index < mockVersions.length - 1 && (
                        <div className="w-px h-8 bg-gray-200 mt-2" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900">
                          Version {version.version}
                        </h3>
                        <Badge className={getStatusColor(version.status)}>
                          {version.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{version.summary}</p>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {version.createdDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {version.createdBy}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {index > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-2"
                      onClick={() => handleCompare(version, mockVersions[index - 1])}
                    >
                      <GitCompare className="h-4 w-4" />
                      Compare
                    </Button>
                  )}
                </div>

                <div className="ml-11 space-y-1">
                  {version.changes.map((change, changeIndex) => (
                    <div key={changeIndex} className="text-sm text-gray-600 flex items-start gap-2">
                      <span className="text-green-600 mt-0.5">•</span>
                      <span>{change}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Compare Modal */}
      <Dialog open={showCompareModal} onOpenChange={setShowCompareModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Compare Versions: v{selectedVersion?.version} vs v{compareVersion?.version}
            </DialogTitle>
          </DialogHeader>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-green-700 mb-3">
                Version {selectedVersion?.version} (Current)
              </h3>
              <div className="space-y-2">
                {selectedVersion?.changes.map((change, index) => (
                  <div key={index} className="p-2 bg-green-50 rounded text-sm">
                    + {change}
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-red-700 mb-3">
                Version {compareVersion?.version} (Previous)
              </h3>
              <div className="space-y-2">
                {compareVersion?.changes.map((change, index) => (
                  <div key={index} className="p-2 bg-red-50 rounded text-sm">
                    - {change}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

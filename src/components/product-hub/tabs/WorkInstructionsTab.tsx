
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Plus, ChevronDown, Upload, Eye, FileText } from 'lucide-react';

interface WorkInstruction {
  id: string;
  stepName: string;
  instruction: string;
  linkedSOP?: {
    filename: string;
    type: 'pdf' | 'image';
  };
  notes: string;
}

const mockInstructions: WorkInstruction[] = [
  {
    id: '1',
    stepName: 'Sampling',
    instruction: 'Collect 50g and label sample ID',
    linkedSOP: {
      filename: 'SOP-Sampling-001.pdf',
      type: 'pdf'
    },
    notes: 'Ensure proper labeling with date and batch number'
  },
  {
    id: '2',
    stepName: 'Quality Control',
    instruction: 'Perform visual inspection and weight verification',
    linkedSOP: {
      filename: 'QC-Checklist.pdf',
      type: 'pdf'
    },
    notes: 'Document any deviations in the QC log'
  }
];

interface WorkInstructionsTabProps {
  productId: string;
}

export const WorkInstructionsTab: React.FC<WorkInstructionsTabProps> = ({ productId }) => {
  const [instructions, setInstructions] = useState(mockInstructions);
  const [expandedInstructions, setExpandedInstructions] = useState<string[]>(['1']);

  const toggleInstruction = (instructionId: string) => {
    setExpandedInstructions(prev =>
      prev.includes(instructionId)
        ? prev.filter(id => id !== instructionId)
        : [...prev, instructionId]
    );
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-semibold">Work Instructions</h2>
          <Button size="sm" className="gap-2">
            <Plus className="h-4 w-4" />
            Add Step Instruction
          </Button>
        </div>

        <div className="space-y-3">
          {instructions.map((instruction) => (
            <Card key={instruction.id}>
              <Collapsible
                open={expandedInstructions.includes(instruction.id)}
                onOpenChange={() => toggleInstruction(instruction.id)}
              >
                <CollapsibleTrigger asChild>
                  <div className="p-4 cursor-pointer hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">{instruction.stepName}</h3>
                        <p className="text-sm text-gray-600 mt-1">{instruction.instruction}</p>
                      </div>
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <div className="px-4 pb-4 border-t bg-gray-50 space-y-4">
                    <div className="space-y-2 pt-4">
                      <label className="text-sm font-medium">Detailed Instruction</label>
                      <Textarea
                        value={instruction.instruction}
                        placeholder="Enter detailed step instructions..."
                        className="min-h-[100px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Linked SOP</label>
                      {instruction.linkedSOP ? (
                        <div className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">{instruction.linkedSOP.filename}</p>
                            <p className="text-xs text-gray-500">PDF Document</p>
                          </div>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Eye className="h-4 w-4" />
                            Preview
                          </Button>
                        </div>
                      ) : (
                        <Button variant="outline" className="w-full gap-2">
                          <Upload className="h-4 w-4" />
                          Upload SOP Document
                        </Button>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Notes</label>
                      <Textarea
                        value={instruction.notes}
                        placeholder="Additional notes or special considerations..."
                        className="min-h-[80px]"
                      />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>

        {instructions.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No work instructions added yet</p>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add First Instruction
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};


import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Clock, XCircle, FileSignature } from 'lucide-react';

interface Approval {
  id: string;
  role: string;
  approver: string;
  date: string;
  status: 'Approved' | 'Pending' | 'Rejected';
  comments: string;
}

const mockApprovals: Approval[] = [
  {
    id: '1',
    role: 'QA Reviewer',
    approver: '<PERSON>',
    date: '2025-06-01',
    status: 'Approved',
    comments: 'Reviewed and accepted'
  },
  {
    id: '2',
    role: 'Production Manager',
    approver: '<PERSON>',
    date: '2025-06-02',
    status: 'Pending',
    comments: ''
  },
  {
    id: '3',
    role: 'Regulatory Affairs',
    approver: '<PERSON>',
    date: '2025-05-30',
    status: 'Rejected',
    comments: 'Requires additional documentation'
  }
];

interface ApprovalsTabProps {
  productId: string;
}

export const ApprovalsTab: React.FC<ApprovalsTabProps> = ({ productId }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'Pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'Rejected': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const groupedApprovals = mockApprovals.reduce((acc, approval) => {
    if (!acc[approval.status]) {
      acc[approval.status] = [];
    }
    acc[approval.status].push(approval);
    return acc;
  }, {} as Record<string, Approval[]>);

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <h2 className="text-lg font-semibold">Approval Records</h2>

        {Object.entries(groupedApprovals).map(([status, approvals]) => (
          <div key={status} className="space-y-3">
            <div className="flex items-center gap-2">
              {getStatusIcon(status)}
              <h3 className="font-medium text-gray-900">{status}</h3>
              <Badge className={getStatusColor(status)}>
                {approvals.length}
              </Badge>
            </div>

            <div className="space-y-2">
              {approvals.map((approval) => (
                <Card key={approval.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{approval.role}</h4>
                        <p className="text-sm text-gray-600">{approval.approver}</p>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(approval.status)}>
                          {approval.status}
                        </Badge>
                        <p className="text-xs text-gray-500 mt-1">{approval.date}</p>
                      </div>
                    </div>

                    {approval.comments && (
                      <>
                        <Separator className="my-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-700 mb-1">Comments:</p>
                          <p className="text-sm text-gray-600">{approval.comments}</p>
                        </div>
                      </>
                    )}

                    {approval.status === 'Approved' && (
                      <div className="flex items-center gap-2 mt-3 pt-3 border-t">
                        <FileSignature className="h-4 w-4 text-gray-400" />
                        <span className="text-xs text-gray-500">Digital signature verified</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}

        {mockApprovals.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No approval records yet</p>
          </div>
        )}
      </div>

      {/* Sticky Bottom Button */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
        <Button className="w-full gap-2">
          <FileSignature className="h-4 w-4" />
          Send for Approval
        </Button>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProductOverview } from './tabs/ProductOverview';
import { RecipeTab } from './tabs/RecipeTab';
import { BOMTab } from './tabs/BOMTab';
import { WorkInstructionsTab } from './tabs/WorkInstructionsTab';
import { ResourcesTab } from './tabs/ResourcesTab';
import { ApprovalsTab } from './tabs/ApprovalsTab';
import { VersionsTab } from './tabs/VersionsTab';

interface ProductDetailsProps {
  productId: string;
  onBack: () => void;
}

export const ProductDetails: React.FC<ProductDetailsProps> = ({ productId, onBack }) => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Product Details</h1>
            <p className="text-sm text-gray-600">GMP Supplement Powder</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="bg-white border-b border-gray-200 px-4">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
            <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
            <TabsTrigger value="recipe" className="text-xs">Recipe</TabsTrigger>
            <TabsTrigger value="bom" className="text-xs">BOM</TabsTrigger>
            <TabsTrigger value="instructions" className="text-xs">Instructions</TabsTrigger>
            <TabsTrigger value="resources" className="text-xs">Resources</TabsTrigger>
            <TabsTrigger value="approvals" className="text-xs">Approvals</TabsTrigger>
            <TabsTrigger value="versions" className="text-xs">Versions</TabsTrigger>
          </TabsList>
        </div>

        <div className="flex-1 overflow-hidden">
          <TabsContent value="overview" className="h-full m-0">
            <ProductOverview productId={productId} />
          </TabsContent>
          <TabsContent value="recipe" className="h-full m-0">
            <RecipeTab productId={productId} />
          </TabsContent>
          <TabsContent value="bom" className="h-full m-0">
            <BOMTab productId={productId} />
          </TabsContent>
          <TabsContent value="instructions" className="h-full m-0">
            <WorkInstructionsTab productId={productId} />
          </TabsContent>
          <TabsContent value="resources" className="h-full m-0">
            <ResourcesTab productId={productId} />
          </TabsContent>
          <TabsContent value="approvals" className="h-full m-0">
            <ApprovalsTab productId={productId} />
          </TabsContent>
          <TabsContent value="versions" className="h-full m-0">
            <VersionsTab productId={productId} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

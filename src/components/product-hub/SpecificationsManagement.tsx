
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, Card<PERSON><PERSON>le } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Search, Plus, FileText, Download, Edit } from 'lucide-react';

const mockSpecifications = [
  {
    id: 'spec-001',
    name: 'GMP Supplement Powder Specification',
    version: '2.1',
    product: 'GMP Supplement Powder',
    status: 'Approved',
    lastModified: '2024-01-15',
    modifiedBy: 'Dr. <PERSON>',
    type: 'Product Specification'
  },
  {
    id: 'spec-002',
    name: 'Protein Blend Formula Standards',
    version: '1.3',
    product: 'Protein Blend Formula',
    status: 'Under Review',
    lastModified: '2024-01-10',
    modifiedBy: '<PERSON>',
    type: 'Quality Standard'
  },
  {
    id: 'spec-003',
    name: 'Vitamin Complex Testing Protocol',
    version: '1.0',
    product: 'Vitamin Complex Tablets',
    status: 'Draft',
    lastModified: '2024-01-08',
    modifiedBy: 'Lisa Rodriguez',
    type: 'Test Protocol'
  }
];

export const SpecificationsManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const filteredSpecs = mockSpecifications.filter(spec => {
    const matchesSearch = spec.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         spec.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || spec.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      'Approved': 'bg-green-100 text-green-800',
      'Under Review': 'bg-yellow-100 text-yellow-800',
      'Draft': 'bg-gray-100 text-gray-800',
      'Rejected': 'bg-red-100 text-red-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search specifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="all">All Status</option>
            <option value="approved">Approved</option>
            <option value="under review">Under Review</option>
            <option value="draft">Draft</option>
          </select>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Specification
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {mockSpecifications.length}
              </div>
              <div className="text-sm text-gray-600">Total Specifications</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {mockSpecifications.filter(s => s.status === 'Approved').length}
              </div>
              <div className="text-sm text-gray-600">Approved</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {mockSpecifications.filter(s => s.status === 'Under Review').length}
              </div>
              <div className="text-sm text-gray-600">Under Review</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {mockSpecifications.filter(s => s.status === 'Draft').length}
              </div>
              <div className="text-sm text-gray-600">Drafts</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Specifications Table */}
      <Card>
        <CardHeader>
          <CardTitle>Specifications ({filteredSpecs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Specification</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSpecs.map((spec) => (
                <TableRow key={spec.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{spec.name}</div>
                      <div className="text-sm text-gray-500">ID: {spec.id}</div>
                    </div>
                  </TableCell>
                  <TableCell>{spec.product}</TableCell>
                  <TableCell>
                    <Badge variant="outline">v{spec.version}</Badge>
                  </TableCell>
                  <TableCell>{spec.type}</TableCell>
                  <TableCell>{getStatusBadge(spec.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm">{spec.lastModified}</div>
                      <div className="text-xs text-gray-500">by {spec.modifiedBy}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <FileText className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Plus, X, Package, Save } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface BOMItem {
  id: string;
  itemCode: string;
  description: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  supplier: string;
  leadTime: string;
}

interface CreateBOMModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateBOMModal: React.FC<CreateBOMModalProps> = ({ open, onOpenChange }) => {
  const { toast } = useToast();
  const [bomName, setBomName] = useState('');
  const [productCode, setProductCode] = useState('');
  const [bomItems, setBomItems] = useState<BOMItem[]>([]);
  const [newItem, setNewItem] = useState({
    itemCode: '',
    description: '',
    quantity: '',
    unit: 'each',
    unitCost: '',
    supplier: '',
    leadTime: ''
  });

  const units = ['each', 'kg', 'g', 'L', 'mL', 'm', 'cm', 'pieces'];

  const addBOMItem = () => {
    if (!newItem.itemCode || !newItem.description || !newItem.quantity) {
      toast({
        title: "Missing Information",
        description: "Please fill in item code, description, and quantity",
        variant: "destructive"
      });
      return;
    }

    const quantity = parseFloat(newItem.quantity);
    const unitCost = parseFloat(newItem.unitCost) || 0;
    
    const bomItem: BOMItem = {
      id: `bom-${Date.now()}`,
      itemCode: newItem.itemCode,
      description: newItem.description,
      quantity,
      unit: newItem.unit,
      unitCost,
      totalCost: quantity * unitCost,
      supplier: newItem.supplier,
      leadTime: newItem.leadTime
    };

    setBomItems([...bomItems, bomItem]);
    setNewItem({
      itemCode: '',
      description: '',
      quantity: '',
      unit: 'each',
      unitCost: '',
      supplier: '',
      leadTime: ''
    });

    toast({
      title: "Item Added",
      description: `${bomItem.description} has been added to the BOM`
    });
  };

  const removeBOMItem = (id: string) => {
    setBomItems(bomItems.filter(item => item.id !== id));
  };

  const getTotalCost = () => {
    return bomItems.reduce((total, item) => total + item.totalCost, 0);
  };

  const saveBOM = () => {
    if (!bomName || bomItems.length === 0) {
      toast({
        title: "Incomplete BOM",
        description: "Please enter BOM name and add at least one item",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "BOM Created",
      description: `Bill of Materials "${bomName}" has been created successfully`
    });
    
    // Reset form
    setBomName('');
    setProductCode('');
    setBomItems([]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create Bill of Materials
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* BOM Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="bomName">BOM Name *</Label>
              <Input
                id="bomName"
                value={bomName}
                onChange={(e) => setBomName(e.target.value)}
                placeholder="e.g., Paracetamol 500mg BOM"
              />
            </div>
            <div>
              <Label htmlFor="productCode">Product Code</Label>
              <Input
                id="productCode"
                value={productCode}
                onChange={(e) => setProductCode(e.target.value)}
                placeholder="e.g., PRD-PA-500"
              />
            </div>
          </div>

          {/* Add BOM Item */}
          <div className="border rounded-lg p-4 space-y-4">
            <h3 className="font-semibold">Add BOM Item</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="itemCode">Item Code *</Label>
                <Input
                  id="itemCode"
                  value={newItem.itemCode}
                  onChange={(e) => setNewItem({...newItem, itemCode: e.target.value})}
                  placeholder="e.g., API-001"
                />
              </div>
              <div>
                <Label htmlFor="description">Description *</Label>
                <Input
                  id="description"
                  value={newItem.description}
                  onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                  placeholder="Item description"
                />
              </div>
              <div>
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={newItem.quantity}
                  onChange={(e) => setNewItem({...newItem, quantity: e.target.value})}
                  placeholder="Amount"
                />
              </div>
              <div>
                <Label htmlFor="unit">Unit</Label>
                <Select value={newItem.unit} onValueChange={(value) => setNewItem({...newItem, unit: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {units.map(unit => (
                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="unitCost">Unit Cost</Label>
                <Input
                  id="unitCost"
                  type="number"
                  value={newItem.unitCost}
                  onChange={(e) => setNewItem({...newItem, unitCost: e.target.value})}
                  placeholder="Cost per unit"
                />
              </div>
              <div>
                <Label htmlFor="supplier">Supplier</Label>
                <Input
                  id="supplier"
                  value={newItem.supplier}
                  onChange={(e) => setNewItem({...newItem, supplier: e.target.value})}
                  placeholder="Supplier name"
                />
              </div>
              <div>
                <Label htmlFor="leadTime">Lead Time</Label>
                <Input
                  id="leadTime"
                  value={newItem.leadTime}
                  onChange={(e) => setNewItem({...newItem, leadTime: e.target.value})}
                  placeholder="e.g., 14 days"
                />
              </div>
              <div>
                <Label className="invisible">Action</Label>
                <Button onClick={addBOMItem} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>
            </div>
          </div>

          {/* BOM Items Table */}
          {bomItems.length > 0 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">BOM Items ({bomItems.length})</h3>
                <div className="text-lg font-bold">Total Cost: ${getTotalCost().toFixed(2)}</div>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item Code</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit Cost</TableHead>
                      <TableHead>Total Cost</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Lead Time</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {bomItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.itemCode}</TableCell>
                        <TableCell>{item.description}</TableCell>
                        <TableCell>{item.quantity} {item.unit}</TableCell>
                        <TableCell>${item.unitCost.toFixed(2)}</TableCell>
                        <TableCell>${item.totalCost.toFixed(2)}</TableCell>
                        <TableCell>{item.supplier || 'N/A'}</TableCell>
                        <TableCell>{item.leadTime || 'N/A'}</TableCell>
                        <TableCell>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => removeBOMItem(item.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex flex-col sm:flex-row gap-3 justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="sm:w-auto">
            Cancel
          </Button>
          <Button onClick={saveBOM} className="sm:w-auto">
            <Save className="h-4 w-4 mr-2" />
            Create BOM
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

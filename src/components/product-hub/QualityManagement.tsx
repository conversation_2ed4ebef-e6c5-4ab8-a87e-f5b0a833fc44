
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { AlertTriangle, CheckCircle, Clock, TrendingUp } from 'lucide-react';

const qualityMetrics = [
  { month: 'Jan', defectRate: 2.1, complaints: 5, passRate: 97.9 },
  { month: 'Feb', defectRate: 1.8, complaints: 3, passRate: 98.2 },
  { month: 'Mar', defectRate: 2.3, complaints: 7, passRate: 97.7 },
  { month: 'Apr', defectRate: 1.5, complaints: 2, passRate: 98.5 },
  { month: 'May', defectRate: 1.2, complaints: 1, passRate: 98.8 },
  { month: 'Jun', defectRate: 1.6, complaints: 4, passRate: 98.4 }
];

const mockQualityIssues = [
  {
    id: 'qi-001',
    product: 'GMP Supplement Powder',
    issue: 'Moisture content variation',
    severity: 'Medium',
    status: 'Under Investigation',
    reportedDate: '2024-01-10',
    assignedTo: 'Quality Team A'
  },
  {
    id: 'qi-002',
    product: 'Protein Blend Formula',
    issue: 'Color inconsistency',
    severity: 'Low',
    status: 'Resolved',
    reportedDate: '2024-01-08',
    assignedTo: 'Quality Team B'
  },
  {
    id: 'qi-003',
    product: 'Vitamin Complex Tablets',
    issue: 'Hardness test failure',
    severity: 'High',
    status: 'Critical',
    reportedDate: '2024-01-12',
    assignedTo: 'Senior QA Manager'
  }
];

export const QualityManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('metrics');

  const getSeverityBadge = (severity: string) => {
    const variants = {
      'High': 'bg-red-100 text-red-800',
      'Medium': 'bg-yellow-100 text-yellow-800',
      'Low': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[severity as keyof typeof variants]}>{severity}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Resolved': 'bg-green-100 text-green-800',
      'Under Investigation': 'bg-blue-100 text-blue-800',
      'Critical': 'bg-red-100 text-red-800',
      'Pending': 'bg-yellow-100 text-yellow-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Quality Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Pass Rate</p>
                <p className="text-2xl font-bold text-green-600">98.4%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Defect Rate</p>
                <p className="text-2xl font-bold text-red-600">1.6%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Open Issues</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {mockQualityIssues.filter(i => i.status !== 'Resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Trend</p>
                <p className="text-2xl font-bold text-blue-600">Improving</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quality Management Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="metrics">Quality Metrics</TabsTrigger>
          <TabsTrigger value="issues">Quality Issues</TabsTrigger>
          <TabsTrigger value="reports">Reports & Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Defect Rate Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={qualityMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="defectRate" 
                      stroke="#EF4444" 
                      strokeWidth={2} 
                      name="Defect Rate %" 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pass Rate by Month</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={qualityMetrics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="passRate" fill="#10B981" name="Pass Rate %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="issues" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Quality Issues</h3>
            <Button>Report New Issue</Button>
          </div>

          <div className="grid gap-4">
            {mockQualityIssues.map((issue) => (
              <Card key={issue.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{issue.issue}</h4>
                        {getSeverityBadge(issue.severity)}
                        {getStatusBadge(issue.status)}
                      </div>
                      <p className="text-sm text-gray-600">Product: {issue.product}</p>
                      <p className="text-sm text-gray-600">Reported: {issue.reportedDate}</p>
                      <p className="text-sm text-gray-600">Assigned to: {issue.assignedTo}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">View Details</Button>
                      <Button variant="outline" size="sm">Update Status</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quality Reports & Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                  <TrendingUp className="h-6 w-6" />
                  <span>Monthly Quality Report</span>
                </Button>
                <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                  <AlertTriangle className="h-6 w-6" />
                  <span>Issue Analysis Report</span>
                </Button>
                <Button variant="outline" className="h-24 flex flex-col items-center justify-center gap-2">
                  <CheckCircle className="h-6 w-6" />
                  <span>Compliance Summary</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

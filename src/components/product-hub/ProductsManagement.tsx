
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Package, Image, FileText, Settings } from 'lucide-react';

const mockProducts = [
  {
    id: 'prod-001',
    name: 'GMP Supplement Powder',
    sku: 'GSP-001',
    status: 'Active',
    image: '/lovable-uploads/143bb4e6-dd7c-41f3-9b80-09442b5622ba.png',
    description: 'High-quality supplement powder manufactured under GMP conditions',
    category: 'Supplements',
    batchSize: '500kg',
    leadTime: '14 days'
  },
  {
    id: 'prod-002', 
    name: 'Protein Blend Formula',
    sku: 'PBF-002',
    status: 'Active',
    image: '/lovable-uploads/206ab8af-c1cb-4dc3-94a5-bdebe69467c8.png',
    description: 'Premium protein blend for athletic performance',
    category: 'Supplements',
    batchSize: '1000kg',
    leadTime: '10 days'
  },
  {
    id: 'prod-003',
    name: 'Vitamin Complex Tablets',
    sku: 'VCT-003', 
    status: 'Development',
    image: '/lovable-uploads/55d3f27c-4c35-47eb-9aea-174ad8fbad1f.png',
    description: 'Multi-vitamin complex in tablet form',
    category: 'Supplements',
    batchSize: '250kg',
    leadTime: '21 days'
  }
];

interface ProductsManagementProps {
  onProductSelect: (productId: string) => void;
}

export const ProductsManagement: React.FC<ProductsManagementProps> = ({ onProductSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const variant = status === 'Active' ? 'default' : status === 'Development' ? 'secondary' : 'destructive';
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-80"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="all">All Categories</option>
            <option value="Supplements">Supplements</option>
            <option value="Pharmaceuticals">Pharmaceuticals</option>
            <option value="Food">Food Products</option>
          </select>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Add Product
        </Button>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Product Image */}
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                  {product.image ? (
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Package className="h-16 w-16 text-gray-400" />
                  )}
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-lg">{product.name}</h3>
                      <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                    </div>
                    {getStatusBadge(product.status)}
                  </div>
                  
                  <p className="text-sm text-gray-700">{product.description}</p>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-600">Batch Size:</span>
                      <p className="font-medium">{product.batchSize}</p>
                    </div>
                    <div>
                      <span className="text-gray-600">Lead Time:</span>
                      <p className="font-medium">{product.leadTime}</p>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button 
                    onClick={() => onProductSelect(product.id)}
                    className="flex-1"
                    size="sm"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>No products found matching your criteria.</p>
        </div>
      )}
    </div>
  );
};

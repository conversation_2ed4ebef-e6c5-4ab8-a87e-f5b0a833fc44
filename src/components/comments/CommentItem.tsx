
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Comment, CommentPermissions } from '@/types/comment';
import { CommentForm } from './CommentForm';
import { formatDate } from '@/utils/dateUtils';
import { 
  MoreVertical, 
  Reply, 
  Edit, 
  Trash2, 
  Heart, 
  ThumbsUp, 
  ChevronDown, 
  ChevronRight,
  Image as ImageIcon,
  Clock
} from 'lucide-react';

interface CommentItemProps {
  comment: Comment;
  permissions: CommentPermissions;
  currentUser: { id: string; name: string; role: string };
  onReply: (content: string, parentId?: string, attachments?: File[]) => Promise<Comment>;
  onEdit: (id: string, content: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  onToggleReaction: (commentId: string, type: 'like' | 'heart' | 'thumbs_up') => Promise<void>;
  onToggleExpanded: (commentId: string) => void;
  level?: number;
}

export const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  permissions,
  currentUser,
  onReply,
  onEdit,
  onDelete,
  onToggleReaction,
  onToggleExpanded,
  level = 0
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditHistory, setShowEditHistory] = useState(false);

  const isOwner = comment.author.id === currentUser.id;
  const canEdit = permissions.canEdit && isOwner && !comment.isDeleted;
  const canDelete = permissions.canDelete && (isOwner || permissions.canModerate) && !comment.isDeleted;
  const canReply = permissions.canReply && !comment.isDeleted;

  const handleReply = async (content: string, attachments?: File[]) => {
    await onReply(content, comment.id, attachments);
    setShowReplyForm(false);
  };

  const handleEdit = async (content: string) => {
    await onEdit(comment.id, content);
    setIsEditing(false);
  };

  const handleDelete = async () => {
    await onDelete(comment.id);
    setShowDeleteDialog(false);
  };

  const getReactionCount = (type: string) => {
    return comment.reactions?.filter(r => r.type === type).length || 0;
  };

  const hasUserReacted = (type: string) => {
    return comment.reactions?.some(r => r.userId === currentUser.id && r.type === type) || false;
  };

  const marginLeft = Math.min(level * 2, 8); // Limit nesting depth visually

  return (
    <div className={`ml-${marginLeft} border-l-2 ${level > 0 ? 'border-gray-200 pl-4' : 'border-transparent'}`}>
      <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-3">
        {/* Comment header */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
              <span className="text-teal-600 font-medium text-sm">
                {comment.author.name.split(' ').map(n => n[0]).join('')}
              </span>
            </div>
            
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-gray-900">{comment.author.name}</span>
                <Badge variant="outline" className="text-xs">
                  {comment.author.role}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>{formatDate(comment.createdAt.toISOString())}</span>
                {comment.updatedAt && (
                  <>
                    <span>•</span>
                    <button
                      onClick={() => setShowEditHistory(!showEditHistory)}
                      className="flex items-center gap-1 hover:text-gray-700"
                    >
                      <Clock className="h-3 w-3" />
                      <span>edited</span>
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Comment actions menu */}
          {(canEdit || canDelete) && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {canEdit && (
                  <DropdownMenuItem onClick={() => setIsEditing(true)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                )}
                {canDelete && (
                  <DropdownMenuItem 
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Comment content */}
        {isEditing ? (
          <CommentForm
            onSubmit={handleEdit}
            onCancel={() => setIsEditing(false)}
            initialContent={comment.content}
            placeholder="Edit your comment..."
            isEditing={true}
          />
        ) : (
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
          </div>
        )}

        {/* Edit history */}
        {showEditHistory && comment.editHistory && comment.editHistory.length > 0 && (
          <div className="bg-gray-50 rounded-md p-3 text-sm">
            <h4 className="font-medium text-gray-700 mb-2">Edit History</h4>
            <div className="space-y-2">
              {comment.editHistory.map((edit, index) => (
                <div key={edit.id} className="border-l-2 border-gray-300 pl-2">
                  <div className="text-gray-500 text-xs">
                    Version {comment.editHistory!.length - index} - {formatDate(edit.editedAt.toISOString())}
                  </div>
                  <p className="text-gray-600 mt-1">{edit.content}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Attachments */}
        {comment.attachments && comment.attachments.length > 0 && (
          <div className="space-y-2">
            {comment.attachments.map((attachment) => (
              <div key={attachment.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <ImageIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-700">{attachment.fileName}</span>
                <Button variant="ghost" size="sm" className="ml-auto">
                  View
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Reactions and actions */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex items-center gap-2">
            {/* Reaction buttons */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleReaction(comment.id, 'like')}
              className={hasUserReacted('like') ? 'text-blue-600 bg-blue-50' : ''}
            >
              <ThumbsUp className="h-4 w-4 mr-1" />
              {getReactionCount('like') || ''}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleReaction(comment.id, 'heart')}
              className={hasUserReacted('heart') ? 'text-red-600 bg-red-50' : ''}
            >
              <Heart className="h-4 w-4 mr-1" />
              {getReactionCount('heart') || ''}
            </Button>
          </div>

          {/* Reply button */}
          {canReply && level < 3 && ( // Limit nesting depth
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowReplyForm(!showReplyForm)}
            >
              <Reply className="h-4 w-4 mr-1" />
              Reply
            </Button>
          )}
        </div>

        {/* Reply form */}
        {showReplyForm && (
          <div className="mt-3 p-3 bg-gray-50 rounded-md">
            <CommentForm
              onSubmit={handleReply}
              onCancel={() => setShowReplyForm(false)}
              placeholder="Write a reply..."
              isReply={true}
            />
          </div>
        )}

        {/* Replies */}
        {comment.replies && comment.replies.length > 0 && (
          <div className="space-y-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleExpanded(comment.id)}
              className="text-gray-600"
            >
              {comment.isExpanded ? (
                <ChevronDown className="h-4 w-4 mr-1" />
              ) : (
                <ChevronRight className="h-4 w-4 mr-1" />
              )}
              {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
            </Button>

            {comment.isExpanded && (
              <div className="space-y-3">
                {comment.replies.map((reply) => (
                  <CommentItem
                    key={reply.id}
                    comment={reply}
                    permissions={permissions}
                    currentUser={currentUser}
                    onReply={onReply}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    onToggleReaction={onToggleReaction}
                    onToggleExpanded={onToggleExpanded}
                    level={level + 1}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

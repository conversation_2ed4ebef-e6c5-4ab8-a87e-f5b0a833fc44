
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useComments } from '@/hooks/useComments';
import { CommentForm } from './CommentForm';
import { CommentItem } from './CommentItem';
import { MessageSquare, Search, Filter, SortDesc } from 'lucide-react';

interface CommentSystemProps {
  documentId: string;
}

export const CommentSystem: React.FC<CommentSystemProps> = ({ documentId }) => {
  const {
    comments,
    loading,
    error,
    permissions,
    currentUser,
    createComment,
    updateComment,
    deleteComment,
    toggleReaction,
    toggleExpanded,
    refetch
  } = useComments(documentId);

  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'mostReplies'>('newest');

  const handleCreateComment = async (content: string, attachments?: File[]) => {
    await createComment(content, undefined, attachments);
    setShowForm(false);
  };

  const handleReplyToComment = async (content: string, parentId?: string, attachments?: File[]) => {
    return await createComment(content, parentId, attachments);
  };

  const filteredComments = comments.filter(comment =>
    comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    comment.author.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedComments = [...filteredComments].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'mostReplies':
        return (b.replies?.length || 0) - (a.replies?.length || 0);
      case 'newest':
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  const totalComments = comments.reduce((acc, comment) => {
    return acc + 1 + (comment.replies?.length || 0);
  }, 0);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-16 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={refetch}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments
            <Badge variant="outline">{totalComments}</Badge>
          </CardTitle>
          
          {permissions.canCreate && (
            <Button onClick={() => setShowForm(!showForm)}>
              {showForm ? 'Cancel' : 'Add Comment'}
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Search and filter controls */}
        {comments.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search comments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortBy(sortBy === 'newest' ? 'oldest' : 'newest')}
              >
                <SortDesc className="h-4 w-4 mr-1" />
                {sortBy === 'newest' ? 'Newest' : 'Oldest'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortBy('mostReplies')}
                className={sortBy === 'mostReplies' ? 'bg-gray-100' : ''}
              >
                <Filter className="h-4 w-4 mr-1" />
                Most Replies
              </Button>
            </div>
          </div>
        )}

        {/* New comment form */}
        {showForm && permissions.canCreate && (
          <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <CommentForm
              onSubmit={handleCreateComment}
              onCancel={() => setShowForm(false)}
              placeholder="Share your thoughts about this document..."
            />
          </div>
        )}

        {/* Comments list */}
        {sortedComments.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'No comments match your search.' : 'No comments yet.'}
            </p>
            {permissions.canCreate && !showForm && (
              <Button onClick={() => setShowForm(true)}>
                Be the first to comment
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {sortedComments.map((comment) => (
              <CommentItem
                key={comment.id}
                comment={comment}
                permissions={permissions}
                currentUser={currentUser}
                onReply={handleReplyToComment}
                onEdit={updateComment}
                onDelete={deleteComment}
                onToggleReaction={toggleReaction}
                onToggleExpanded={toggleExpanded}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

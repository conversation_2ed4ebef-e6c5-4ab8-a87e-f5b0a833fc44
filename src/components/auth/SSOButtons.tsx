
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Shield } from "lucide-react";

interface SSOButtonsProps {
  isEnterpriseEmail: boolean;
  onSSOLogin: (provider: string) => void;
}

export const SSOButtons: React.FC<SSOButtonsProps> = ({ isEnterpriseEmail, onSSOLogin }) => {
  return (
    <div className="space-y-3">
      <Button
        variant="outline"
        onClick={() => onSSOLogin("Google")}
        className="w-full h-11 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 dark:bg-slate-700/50 dark:text-slate-300"
      >
        <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
          <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
      </Button>

      <Button
        variant="outline"
        onClick={() => onSSOLogin("Microsoft 365")}
        className="w-full h-11 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 dark:bg-slate-700/50 dark:text-slate-300"
      >
        <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
          <path fill="#F25022" d="M1 1h10v10H1z"/>
          <path fill="#00A4EF" d="M13 1h10v10H13z"/>
          <path fill="#7FBA00" d="M1 13h10v10H1z"/>
          <path fill="#FFB900" d="M13 13h10v10H13z"/>
        </svg>
        Continue with Microsoft 365
      </Button>

      {isEnterpriseEmail && (
        <Button
          variant="outline"
          onClick={() => onSSOLogin("Enterprise SSO")}
          className="w-full h-11 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600 dark:bg-slate-700/50 dark:text-slate-300"
        >
          <Shield className="mr-2 h-4 w-4" />
          Enterprise SSO Login
        </Button>
      )}
    </div>
  );
};


import React from "react";
import { LoginHeader } from "./LoginHeader";
import { LoginCard } from "./LoginCard";

interface LoginPanelProps {
  step: "email" | "methods" | "otp";
  darkMode: boolean;
  setDarkMode: (darkMode: boolean) => void;
  children: React.ReactNode;
}

export const LoginPanel: React.FC<LoginPanelProps> = ({ step, darkMode, setDarkMode, children }) => {
  return (
    <div className="w-full h-full flex items-center justify-center p-4 relative bg-gradient-to-br from-teal-600 to-teal-700">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-grid-slate-200 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]" />
      </div>

      <div className="w-full max-w-md relative z-10">
        <LoginHeader />
        
        <LoginCard step={step} darkMode={darkMode} setDarkMode={setDarkMode}>
          {children}
        </LoginCard>

        {/* Footer */}
        <div className="text-center mt-8 text-xs text-teal-100">
          <p>Need help? Contact your system administrator</p>
        </div>
      </div>
    </div>
  );
};

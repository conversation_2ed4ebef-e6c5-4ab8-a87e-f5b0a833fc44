
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { Label } from "@/components/ui/label";
import { ArrowLeft, RotateCcw } from "lucide-react";
import { toast } from "sonner";

interface OTPStepProps {
  email: string;
  onVerifySuccess: () => void;
  onChangeEmail: () => void;
}

export const OTPStep: React.FC<OTPStepProps> = ({ email, onVerifySuccess, onChangeEmail }) => {
  const [otpCode, setOtpCode] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendTimer, setResendTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendTimer]);

  const handleVerifyOTP = async () => {
    if (otpCode.length !== 6) {
      setError("Please enter a 6-digit code");
      return;
    }

    setIsVerifying(true);
    setError("");

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simulate validation - accept "123456" as valid code
    if (otpCode === "123456") {
      toast.success("Login successful!");
      onVerifySuccess();
    } else {
      setError("Invalid code. Please try again.");
      setOtpCode("");
    }
    
    setIsVerifying(false);
  };

  const handleResendCode = async () => {
    setCanResend(false);
    setResendTimer(30);
    setError("");
    setOtpCode("");
    
    // Simulate resend API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    toast.success("New code sent to your email!");
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
          Enter verification code
        </h3>
        <p className="text-sm text-slate-600 dark:text-slate-400">
          We sent a 6-digit code to
        </p>
        <p className="text-sm font-medium text-slate-900 dark:text-white mb-4">
          {email}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={onChangeEmail}
          className="text-xs border-slate-300 dark:border-slate-600"
        >
          <ArrowLeft className="h-3 w-3 mr-1" />
          Change email
        </Button>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Verification Code
          </Label>
          <div className="flex justify-center">
            <InputOTP
              maxLength={6}
              value={otpCode}
              onChange={(value) => {
                setOtpCode(value);
                setError("");
              }}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>
          {error && (
            <p className="text-sm text-red-600 dark:text-red-400 text-center">
              {error}
            </p>
          )}
        </div>

        <Button
          onClick={handleVerifyOTP}
          disabled={otpCode.length !== 6 || isVerifying}
          className="w-full h-12 bg-teal-600 hover:bg-teal-700 text-white font-medium"
        >
          {isVerifying ? "Verifying..." : "Verify Code"}
        </Button>

        <div className="text-center">
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
            Didn't receive the code?
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={handleResendCode}
            disabled={!canResend}
            className="border-slate-300 dark:border-slate-600"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            {canResend ? "Resend code" : `Resend in ${resendTimer}s`}
          </Button>
        </div>
      </div>

      <div className="text-center text-xs text-slate-500 dark:text-slate-400">
        <p>For demo purposes, use code: <span className="font-mono font-bold">123456</span></p>
      </div>
    </div>
  );
};

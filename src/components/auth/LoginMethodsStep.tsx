
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Mail } from "lucide-react";
import { SSOButtons } from "./SSOButtons";

interface LoginMethodsStepProps {
  email: string;
  isEnterpriseEmail: boolean;
  isLoading: boolean;
  onOTPLogin: () => void;
  onSSOLogin: (provider: string) => void;
  onChangeEmail: () => void;
}

export const LoginMethodsStep: React.FC<LoginMethodsStepProps> = ({
  email,
  isEnterpriseEmail,
  isLoading,
  onOTPLogin,
  onSSOLogin,
  onChangeEmail
}) => {
  return (
    <div className="space-y-4 animate-fade-in">
      <div className="text-center">
        <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
          Signing in as: <span className="font-medium text-slate-900 dark:text-white">{email}</span>
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={onChangeEmail}
          className="text-xs border-slate-300 dark:border-slate-600 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600"
        >
          Change email
        </Button>
      </div>

      {isEnterpriseEmail && (
        <div className="bg-blue-50 dark:bg-slate-700/50 border border-blue-200 dark:border-slate-600 rounded-lg p-3 mb-4">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            We detected your organization uses SSO. Please log in using your company provider.
          </p>
        </div>
      )}

      {/* Primary Login Method */}
      <Button
        onClick={onOTPLogin}
        disabled={isLoading}
        className="w-full h-12 bg-teal-600 hover:bg-teal-700 text-white font-medium"
      >
        <Mail className="mr-2 h-4 w-4" />
        {isLoading ? "Sending..." : "Send login code to email"}
      </Button>

      <div className="relative">
        <Separator className="my-4 dark:bg-slate-600" />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="bg-white dark:bg-slate-800 px-3 text-xs text-slate-500 dark:text-slate-400">
            OR CONTINUE WITH
          </span>
        </div>
      </div>

      <SSOButtons isEnterpriseEmail={isEnterpriseEmail} onSSOLogin={onSSOLogin} />
    </div>
  );
};


import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowRight } from "lucide-react";

interface EmailStepProps {
  email: string;
  setEmail: (email: string) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export const EmailStep: React.FC<EmailStepProps> = ({ email, setEmail, onSubmit }) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium text-slate-700 dark:text-slate-300">
          Enter your work email to continue
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          className="h-12 text-base border-slate-300 dark:border-slate-600 focus:border-teal-500 focus:ring-teal-500 dark:bg-slate-700 dark:text-white dark:placeholder:text-slate-400"
          required
        />
      </div>
      <Button 
        type="submit" 
        className="w-full h-12 bg-teal-600 hover:bg-teal-700 text-white font-medium text-base"
        disabled={!email}
      >
        Continue
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </form>
  );
};

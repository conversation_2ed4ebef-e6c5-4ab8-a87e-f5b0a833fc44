
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON><PERSON>ontent, Too<PERSON>ipProvider } from "@/components/ui/tooltip";
import { BadgeCheck, ShieldCheck } from "lucide-react";

// tweaked for better color harmony
const badgeGradients = {
  shield: "from-blue-800 via-blue-500 to-green-400",
  badge: "from-orange-700 via-orange-400 to-yellow-300",
};

interface ComplianceBadgeProps {
  label: string;
  icon?: "shield" | "badge";
  description: string;
  footerText?: string;
}

export const ComplianceBadge: React.FC<ComplianceBadgeProps> = ({
  label,
  icon = "shield",
  description,
  footerText = "Certified",
}) => {
  const IconComponent = icon === "badge" ? BadgeCheck : ShieldCheck;
  const gradient = badgeGradients[icon ?? "shield"];

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex flex-col items-center gap-1">
            <button
              tabIndex={0}
              aria-label={`${label} compliance badge: ${description}`}
              className={`
                w-20 h-20 
                rounded-full 
                bg-gradient-to-br ${gradient}
                border-2 border-white/70
                shadow-lg flex flex-col items-center justify-center 
                transition-all ring-0 focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-300
                text-white
                group
              `}
              type="button"
            >
              <IconComponent className="w-8 h-8 mb-1 drop-shadow" aria-hidden="true" />
              <span className="text-sm font-semibold drop-shadow">{label}</span>
            </button>
            {/* Below badge: Certified or similar */}
            <span className="text-[11px] mt-1 font-semibold uppercase tracking-wide text-white/80 bg-black/30 rounded px-2 py-0.5 shadow-sm">
              {footerText}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs text-center">
          <div className="font-bold mb-1 text-green-700">{label}</div>
          <div className="text-slate-700">{description}</div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

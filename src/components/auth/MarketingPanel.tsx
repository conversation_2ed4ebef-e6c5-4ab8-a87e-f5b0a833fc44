import React from "react";
import { Cog, Factory, HardHat } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ComplianceBadge } from "./ComplianceBadge";

export const MarketingPanel: React.FC = () => {
  return (
    <div className="w-full h-full relative overflow-hidden flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
      {/* Factory Skyline Background */}
      <div className="absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-t from-slate-800/60 to-transparent">
        <svg className="absolute bottom-0 w-full h-full" viewBox="0 0 1200 200" fill="none">
          <path d="M0 200V120L50 120V80L100 80V120L150 120V60L200 60V120L250 120V90L300 90V120L350 120V100L400 100V120L450 120V70L500 70V120L550 120V110L600 110V120L1200 120V200Z" fill="rgba(15, 23, 42, 0.8)"/>
          <rect x="60" y="85" width="8" height="15" fill="rgba(251, 191, 36, 0.6)" className="animate-pulse" style={{ animationDelay: '0s' }}/>
          <rect x="160" y="65" width="8" height="15" fill="rgba(251, 191, 36, 0.6)" className="animate-pulse" style={{ animationDelay: '1s' }}/>
          <rect x="260" y="95" width="8" height="15" fill="rgba(251, 191, 36, 0.6)" className="animate-pulse" style={{ animationDelay: '2s' }}/>
          <rect x="360" y="105" width="8" height="15" fill="rgba(251, 191, 36, 0.6)" className="animate-pulse" style={{ animationDelay: '0.5s' }}/>
          <rect x="460" y="75" width="8" height="15" fill="rgba(251, 191, 36, 0.6)" className="animate-pulse" style={{ animationDelay: '1.5s' }}/>
        </svg>
      </div>

      {/* Animated Blueprint Grid */}
      <div className="absolute inset-0 opacity-15">
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px),
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '40px 40px, 40px 40px, 8px 8px, 8px 8px',
            animation: 'blueprintMove 25s linear infinite'
          }}
        />
      </div>

      {/* Floating Industrial Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated Cogs */}
        <div className="absolute top-20 left-16">
          <Cog className="w-16 h-16 text-blue-400/20 animate-spin" style={{ animationDuration: '8s' }} />
        </div>
        <div className="absolute top-32 right-20">
          <Cog className="w-12 h-12 text-orange-400/25 animate-spin" style={{ animationDuration: '12s', animationDirection: 'reverse' }} />
        </div>
        <div className="absolute bottom-40 left-24">
          <Cog className="w-20 h-20 text-blue-500/15 animate-spin" style={{ animationDuration: '15s' }} />
        </div>
        <div className="absolute bottom-32 right-32">
          <Cog className="w-14 h-14 text-orange-500/20 animate-spin" style={{ animationDuration: '10s', animationDirection: 'reverse' }} />
        </div>

        {/* Floating Factory Icons */}
        <div className="absolute top-40 left-1/4">
          <Factory className="w-8 h-8 text-blue-300/30 animate-bounce" style={{ animationDuration: '4s' }} />
        </div>
        <div className="absolute bottom-1/3 right-1/4">
          <HardHat className="w-10 h-10 text-yellow-400/35 animate-bounce" style={{ animationDuration: '3s', animationDelay: '1s' }} />
        </div>

        {/* Assembly Line Moving Parts */}
        <div className="absolute top-1/2 left-0 w-full h-2">
          <div className="w-8 h-2 bg-gradient-to-r from-blue-500/30 to-orange-500/30 rounded animate-pulse" style={{ animation: 'assemblyMove 6s linear infinite' }} />
        </div>
        <div className="absolute top-1/2 mt-8 left-0 w-full h-2">
          <div className="w-6 h-2 bg-gradient-to-r from-orange-500/25 to-blue-500/25 rounded animate-pulse" style={{ animation: 'assemblyMove 8s linear infinite', animationDelay: '2s' }} />
        </div>
      </div>

      {/* Metallic Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-orange-600/10" />
      
      {/* Production Status Indicator */}
      <div className="absolute top-6 right-6">
        <div className="flex items-center gap-2 px-3 py-1 bg-green-500/20 rounded-full border border-green-400/30">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
          <span className="text-xs text-green-300 font-medium">PRODUCTION ONLINE</span>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col justify-center items-center p-8 lg:p-12 text-white w-full max-w-2xl">
        <div className="text-center w-full">
          {/* Main headline with industrial styling */}
          <div className="mb-8">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 bg-gradient-to-r from-white via-blue-100 to-orange-100 bg-clip-text text-transparent animate-fade-in">
              Industrial Excellence
            </h2>
            <h3 className="text-2xl lg:text-3xl font-semibold text-blue-200 animate-fade-in" style={{ animationDelay: '0.2s' }}>
              Zero Defect Manufacturing
            </h3>
          </div>

          {/* Manufacturing Feature Points */}
          <div className="space-y-6 text-slate-300 mb-8">
            <div className="flex items-start gap-4 animate-fade-in" style={{ animationDelay: '0.4s' }}>
              <div className="relative">
                <div className="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0" />
                <div className="absolute inset-0 w-3 h-3 bg-orange-400 rounded-full animate-ping opacity-30" />
              </div>
              <p className="text-lg leading-relaxed">Streamline APQP and PPAP with intelligent automation</p>
            </div>
            
            <div className="flex items-start gap-4 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="relative">
                <div className="w-3 h-3 bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                <div className="absolute inset-0 w-3 h-3 bg-blue-400 rounded-full animate-ping opacity-30" />
              </div>
              <p className="text-lg leading-relaxed">Real-time OEE monitoring across your production lines</p>
            </div>
            
            <div className="flex items-start gap-4 animate-fade-in" style={{ animationDelay: '0.8s' }}>
              <div className="relative">
                <div className="w-3 h-3 bg-orange-400 rounded-full mt-2 flex-shrink-0" />
                <div className="absolute inset-0 w-3 h-3 bg-orange-400 rounded-full animate-ping opacity-30" />
              </div>
              <p className="text-lg leading-relaxed">ISO 9001:2015 compliant quality management system</p>
            </div>
          </div>

          {/* Enhanced testimonial with industrial theme */}
          <div className="relative animate-scale-in" style={{ animationDelay: '1s' }}>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-orange-500/10 rounded-xl blur-xl" />
            <div className="relative p-6 bg-slate-800/40 rounded-xl backdrop-blur-md border border-slate-600/30 shadow-2xl">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-blue-600 rounded-full flex items-center justify-center">
                  <HardHat className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="w-24 h-2 bg-slate-400/30 rounded mb-1" />
                  <div className="w-16 h-2 bg-slate-500/20 rounded" />
                </div>
              </div>
              <p className="text-sm italic text-blue-100 mb-3">
                "BPR Hub increased our First Pass Yield to 99.2% and reduced cycle time by 40%. ISO audit compliance is now seamless."
              </p>
              <p className="text-xs text-slate-400">— Plant Manager, Tier 1 Automotive Supplier</p>
            </div>
          </div>

          {/* Manufacturing KPI metrics */}
          <div className="mt-8 grid grid-cols-3 gap-6 animate-fade-in" style={{ animationDelay: '1.2s' }}>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">99.8%</div>
              <div className="text-xs text-slate-400">OEE</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">6σ</div>
              <div className="text-xs text-slate-400">Quality</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">Zero</div>
              <div className="text-xs text-slate-400">Defects</div>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance Badges with Tooltip and Icons */}
      <div className="absolute bottom-6 left-0 w-full flex flex-wrap justify-center items-end gap-7 z-20">
        <ComplianceBadge
          label="SOC 2"
          icon="shield"
          description="SOC 2: Our platform's controls are reviewed by independent auditors, ensuring data confidentiality, integrity, and privacy."
          footerText="Certified"
        />
        <ComplianceBadge
          label="ISO 27001"
          icon="badge"
          description="ISO 27001: We maintain robust, certified information security management systems to safeguard your data at every layer."
          footerText="Certified"
        />
        <ComplianceBadge
          label="GDPR"
          icon="shield"
          description="GDPR: We strictly comply with EU privacy laws, so your personal and company data is handled with the highest protection."
          footerText="Compliant"
        />
        <ComplianceBadge
          label="HIPAA"
          icon="badge"
          description="HIPAA: For health data, our platform meets rigorous security standards for privacy and safe handling of medical information."
          footerText="Compliant"
        />
      </div>
      
      {/* CSS animations */}
      <style>{`
        @keyframes blueprintMove {
          0% { transform: translateX(0) translateY(0); }
          100% { transform: translateX(40px) translateY(40px); }
        }
        
        @keyframes assemblyMove {
          0% { transform: translateX(-50px); opacity: 0; }
          50% { opacity: 1; }
          100% { transform: translateX(calc(100vw + 50px)); opacity: 0; }
        }
      `}</style>
    </div>
  );
};


import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Moon, Sun } from "lucide-react";

interface LoginCardProps {
  step: "email" | "methods" | "otp";
  darkMode: boolean;
  setDarkMode: (darkMode: boolean) => void;
  children: React.ReactNode;
}

export const LoginCard: React.FC<LoginCardProps> = ({ step, darkMode, setDarkMode, children }) => {
  const getStepTitle = () => {
    switch (step) {
      case "email": return "Sign In";
      case "methods": return "Choose Login Method";
      case "otp": return "Verify Your Email";
      default: return "Sign In";
    }
  };

  return (
    <Card className="backdrop-blur-sm border-white/20 shadow-xl animate-scale-in bg-white/95 dark:bg-slate-800/95">
      <CardHeader className="text-center pb-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
            {getStepTitle()}
          </h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setDarkMode(!darkMode)}
            className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white"
          >
            {darkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {children}

        {/* Security Message */}
        <div className="text-center pt-4 border-t border-slate-100 dark:border-slate-700">
          <p className="text-xs text-slate-500 dark:text-slate-400 flex items-center justify-center gap-1">
            <Shield className="h-3 w-3" />
            We don't store passwords. Your access is secure and encrypted.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

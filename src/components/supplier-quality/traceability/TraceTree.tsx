
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Route, MapPin, Clock, Package, CheckCircle, AlertTriangle } from "lucide-react";
import { formatDate } from "@/utils/dateUtils";
import { BatchDetailDialog } from "./BatchDetailDialog";

interface TraceTreeProps {
  timelineMonth: number;
}

export const TraceTree: React.FC<TraceTreeProps> = ({ timelineMonth }) => {
  const [detailDialog, setDetailDialog] = useState(false);
  
  const mockTraceData = [
    {
      id: "TRACE-001",
      stage: "Raw Material",
      location: "Supplier A - Warehouse",
      date: "2024-01-15",
      status: "Completed",
      details: "Steel components received and inspected",
      batchId: "BT-2024-001"
    },
    {
      id: "TRACE-002", 
      stage: "Manufacturing",
      location: "Production Line 1",
      date: "2024-01-20",
      status: "In Progress",
      details: "Components being processed",
      batchId: "BT-2024-002"
    },
    {
      id: "TRACE-003",
      stage: "Quality Control",
      location: "QC Lab",
      date: "2024-01-25",
      status: "Pending",
      details: "Awaiting quality inspection",
      batchId: "BT-2024-003"
    },
    {
      id: "TRACE-004",
      stage: "Packaging",
      location: "Packaging Line B",
      date: "2024-01-30",
      status: "Pending",
      details: "Ready for final packaging",
      batchId: "BT-2024-004"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Completed
        </Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
          <Clock className="h-3 w-3" />
          In Progress
        </Badge>;
      case "Pending":
        return <Badge className="bg-orange-100 text-orange-800 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          Pending
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewDetails = (batchId: string) => {
    setDetailDialog(true);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Material Trace Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockTraceData.map((trace, index) => (
              <div key={trace.id} className="relative">
                {index < mockTraceData.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
                )}
                
                <div className="flex items-start gap-4 p-4 border rounded-lg">
                  <div className="flex-shrink-0">
                    <Package className="h-8 w-8 text-blue-600" />
                  </div>
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold">{trace.stage}</h4>
                      {getStatusBadge(trace.status)}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {trace.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(trace.date)}
                      </div>
                    </div>
                    
                    <p className="text-sm">{trace.details}</p>
                    
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleViewDetails(trace.batchId)}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <BatchDetailDialog
        open={detailDialog}
        onOpenChange={setDetailDialog}
        batchId="BT-2024-001"
      />
    </>
  );
};

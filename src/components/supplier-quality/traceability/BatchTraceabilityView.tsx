
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Package, 
  AlertCircle,
  MapPin,
  FileText,
  TrendingUp,
  BarChart3
} from "lucide-react";
import { BatchTraceabilityCards } from "../header-components/BatchTraceabilityCards";
import { ReportGenerationDialog } from "./ReportGenerationDialog";
import { TraceTree } from "./TraceTree";
import { BatchDetailDialog } from "./BatchDetailDialog";
import { formatDate } from "@/utils/dateUtils";

export const BatchTraceabilityView: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [reportDialog, setReportDialog] = useState<{open: boolean, type: string, title: string}>({
    open: false,
    type: "",
    title: ""
  });
  const [batchDetailDialog, setBatchDetailDialog] = useState<{open: boolean, batchId?: string}>({
    open: false,
    batchId: undefined
  });

  const mockBatches = [
    {
      id: "BT-2024-001",
      lotNumber: "LOT-A12345",
      product: "Steel Component A",
      supplier: "ABC Manufacturing",
      status: "Active",
      createdDate: "2024-01-15",
      location: "Warehouse A"
    },
    {
      id: "BT-2024-002",
      lotNumber: "LOT-B67890",
      product: "Plastic Housing B",
      supplier: "XYZ Industries",
      status: "Quality Hold",
      createdDate: "2024-01-20",
      location: "Warehouse B"
    },
    {
      id: "BT-2024-003",
      lotNumber: "LOT-C11111",
      product: "Electronic Module C",
      supplier: "TechCorp Ltd",
      status: "Released",
      createdDate: "2024-01-25",
      location: "Production Floor"
    },
    {
      id: "BT-2024-004",
      lotNumber: "LOT-D22222",
      product: "Aluminum Frame D",
      supplier: "MetalWorks Inc",
      status: "Active",
      createdDate: "2024-01-28",
      location: "Warehouse C"
    },
    {
      id: "BT-2024-005",
      lotNumber: "LOT-E33333",
      product: "Rubber Gasket E",
      supplier: "FlexSeal Corp",
      status: "Released",
      createdDate: "2024-02-01",
      location: "Assembly Line"
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Quality Hold":
        return <Badge className="bg-red-100 text-red-800">Quality Hold</Badge>;
      case "Released":
        return <Badge className="bg-blue-100 text-blue-800">Released</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleReportGeneration = (type: string, title: string) => {
    setReportDialog({ open: true, type, title });
  };

  const handleBatchDetails = (batchId: string) => {
    setBatchDetailDialog({ open: true, batchId });
  };

  const filteredBatches = mockBatches.filter(batch =>
    batch.lotNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
    batch.product.toLowerCase().includes(searchQuery.toLowerCase()) ||
    batch.supplier.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6 w-full">
      <BatchTraceabilityCards />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Quick Batch Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Enter lot number, product, or supplier..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <Button>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">
                  {searchQuery ? `Search Results (${filteredBatches.length})` : 'Recent Batches'}
                </h3>
                {filteredBatches.slice(0, 5).map((batch) => (
                  <Card key={batch.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{batch.lotNumber}</span>
                          {getStatusBadge(batch.status)}
                        </div>
                        <p className="text-sm text-gray-600">{batch.product}</p>
                        <p className="text-xs text-gray-500">
                          Supplier: {batch.supplier} • Created: {formatDate(batch.createdDate)}
                        </p>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <MapPin className="h-3 w-3" />
                          {batch.location}
                        </div>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleBatchDetails(batch.id)}
                        >
                          View Details
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
                
                {filteredBatches.length === 0 && searchQuery && (
                  <div className="text-center py-4 text-gray-500">
                    No batches found matching "{searchQuery}"
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-auto p-3 flex-col"
                  onClick={() => handleReportGeneration("batch_summary", "Batch Summary Report")}
                >
                  <FileText className="h-5 w-5 mb-2 text-blue-600" />
                  <span className="text-xs">Batch Summary</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-auto p-3 flex-col"
                  onClick={() => handleReportGeneration("supply_chain_analysis", "Supply Chain Analysis")}
                >
                  <TrendingUp className="h-5 w-5 mb-2 text-green-600" />
                  <span className="text-xs">Supply Chain</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-auto p-3 flex-col"
                  onClick={() => handleReportGeneration("quality_incident", "Quality Incident Report")}
                >
                  <AlertCircle className="h-5 w-5 mb-2 text-red-600" />
                  <span className="text-xs">Quality Issues</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="h-auto p-3 flex-col"
                  onClick={() => handleReportGeneration("traceability_timeline", "Traceability Timeline")}
                >
                  <BarChart3 className="h-5 w-5 mb-2 text-purple-600" />
                  <span className="text-xs">Timeline</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <TraceTree timelineMonth={1} />
        </div>
      </div>

      <ReportGenerationDialog
        open={reportDialog.open}
        onOpenChange={(open) => setReportDialog({ ...reportDialog, open })}
        reportType={reportDialog.type}
        reportTitle={reportDialog.title}
      />
      
      <BatchDetailDialog
        open={batchDetailDialog.open}
        onOpenChange={(open) => setBatchDetailDialog({ ...batchDetailDialog, open })}
        batchId={batchDetailDialog.batchId}
      />
    </div>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Package, MapPin, Calendar, Shield, FileText, Globe, Truck, Factory } from "lucide-react";
import { mockMaterialOrigins } from "@/data/mockBatchTraceabilityData";

interface MaterialOriginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const MaterialOriginDialog: React.FC<MaterialOriginDialogProps> = ({ open, onOpenChange }) => {
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    const statusColors = {
      'Verified': 'bg-green-100 text-green-800',
      'Pending': 'bg-amber-100 text-amber-800',
      'Expired': 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    );
  };

  const selectedMaterialData = mockMaterialOrigins.find(m => m.id === selectedMaterial);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl h-[85vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Material Origin Tracking
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="origins" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="origins">Material Origins</TabsTrigger>
            <TabsTrigger value="tracking">Supply Chain Tracking</TabsTrigger>
            <TabsTrigger value="compliance">Compliance & Certifications</TabsTrigger>
          </TabsList>

          <TabsContent value="origins" className="flex-1 flex gap-6">
            {/* Materials List */}
            <div className="w-2/3 space-y-3 overflow-y-auto">
              <h3 className="font-semibold">Tracked Materials ({mockMaterialOrigins.length})</h3>
              {mockMaterialOrigins.map((material) => (
                <Card 
                  key={material.id} 
                  className={`cursor-pointer hover:shadow-md transition-shadow ${
                    selectedMaterial === material.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedMaterial(material.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-blue-600" />
                          <span className="font-medium">{material.materialName}</span>
                          {getStatusBadge(material.status)}
                        </div>
                        <p className="text-sm font-mono text-gray-600">{material.batchNumber}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">Risk: {material.riskScore}/100</p>
                        <p className="text-xs text-gray-500">Quality: {material.qualityScore}%</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 text-xs mb-3">
                      <div className="flex items-center gap-1">
                        <Globe className="h-3 w-3 text-gray-500" />
                        <span>{material.origin}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Factory className="h-3 w-3 text-gray-500" />
                        <span>{material.supplier}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-gray-500" />
                        <span>Extracted: {material.extractionDate}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Truck className="h-3 w-3 text-gray-500" />
                        <span>Processed: {material.processingDate}</span>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-1">
                      {material.certifications.slice(0, 2).map((cert, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {cert}
                        </Badge>
                      ))}
                      {material.certifications.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{material.certifications.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Material Details */}
            <div className="w-1/3 space-y-4">
              {selectedMaterialData ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{selectedMaterialData.materialName}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Origin Details</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span>{selectedMaterialData.origin}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Factory className="h-4 w-4 text-gray-500" />
                          <span>{selectedMaterialData.supplier}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Timeline</h4>
                      <div className="space-y-1 text-sm">
                        <p>Extraction: {selectedMaterialData.extractionDate}</p>
                        <p>Processing: {selectedMaterialData.processingDate}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Quality Metrics</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div className="text-center p-2 bg-green-50 rounded">
                          <div className="font-bold text-green-600">{selectedMaterialData.qualityScore}%</div>
                          <div className="text-gray-600 text-xs">Quality</div>
                        </div>
                        <div className="text-center p-2 bg-amber-50 rounded">
                          <div className="font-bold text-amber-600">{selectedMaterialData.riskScore}</div>
                          <div className="text-gray-600 text-xs">Risk Score</div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Certifications</h4>
                      <div className="space-y-1">
                        {selectedMaterialData.certifications.map((cert, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <Shield className="h-3 w-3 text-green-600" />
                            <span>{cert}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Documents</h4>
                      <div className="space-y-1">
                        {selectedMaterialData.documents?.map((doc, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm">
                            <FileText className="h-3 w-3 text-blue-600" />
                            <span>{doc}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        View Details
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        Documents
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-4 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Select a material to view origin details
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="tracking" className="flex-1">
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8 h-full flex items-center justify-center">
              <div className="text-center">
                <Truck className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Material Journey Visualization</h3>
                <p className="text-gray-600 mb-6 max-w-md">
                  Interactive timeline showing the complete journey of materials from extraction to final product
                </p>
                <Button size="lg">Launch Tracking View</Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="compliance" className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    Compliance Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">RMI Compliant</span>
                      <Badge className="bg-green-100 text-green-800">Verified</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">OECD Guidelines</span>
                      <Badge className="bg-green-100 text-green-800">Compliant</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Conflict Minerals</span>
                      <Badge className="bg-amber-100 text-amber-800">Under Review</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Certification Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="text-center p-3 bg-green-50 rounded">
                      <div className="text-2xl font-bold text-green-600">15</div>
                      <div className="text-sm text-green-700">Active Certificates</div>
                    </div>
                    <div className="text-center p-3 bg-amber-50 rounded">
                      <div className="text-2xl font-bold text-amber-600">3</div>
                      <div className="text-sm text-amber-700">Expiring Soon</div>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded">
                      <div className="text-2xl font-bold text-red-600">1</div>
                      <div className="text-sm text-red-700">Expired</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Actions Required</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-2" />
                      Update Certificate
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <Shield className="h-4 w-4 mr-2" />
                      Compliance Review
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <MapPin className="h-4 w-4 mr-2" />
                      Site Audit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

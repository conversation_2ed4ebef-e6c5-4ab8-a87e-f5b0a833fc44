
import React, { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Route, MapPin, Calendar, Package, Truck, Factory, CheckCircle, Clock, AlertTriangle } from "lucide-react";
import { TimelineFilters } from "./TimelineFilters";

interface MaterialJourneyTimelineProps {
  materialId?: string;
}

export const MaterialJourneyTimeline: React.FC<MaterialJourneyTimelineProps> = ({ 
  materialId = "BATT-2319-AL" 
}) => {
  const [filters, setFilters] = useState({
    materialType: '',
    supplierTier: '',
    region: '',
    complianceStatus: ''
  });

  const mockJourneyData = [
    {
      id: "STAGE-001",
      stage: "Raw Material Extraction",
      location: "Salar de Atacama, Chile",
      date: "2024-01-02",
      status: "Completed",
      supplier: "SQM Lithium",
      description: "Lithium carbonate extracted from brine pools",
      icon: Factory,
      details: {
        temperature: "25°C",
        purity: "99.5%",
        quantity: "500kg"
      }
    },
    {
      id: "STAGE-002", 
      stage: "Material Processing",
      location: "Shanghai, China",
      date: "2024-01-15",
      status: "Completed",
      supplier: "ChemCorp Processing",
      description: "Battery-grade lithium carbonate processing",
      icon: Package,
      details: {
        processTime: "48 hours",
        yield: "98.2%",
        quality: "Grade A"
      }
    },
    {
      id: "STAGE-003",
      stage: "Component Manufacturing", 
      location: "Seoul, South Korea",
      date: "2024-02-01",
      status: "Completed",
      supplier: "PowerCell Technologies",
      description: "Lithium-ion battery cell manufacturing",
      icon: Factory,
      details: {
        capacity: "3.7V 3000mAh",
        cycleLife: "2000 cycles",
        efficiency: "95%"
      }
    },
    {
      id: "STAGE-004",
      stage: "Assembly",
      location: "San Jose, USA",
      date: "2024-02-15",
      status: "In Progress",
      supplier: "Advanced Battery Systems",
      description: "Battery pack assembly and testing",
      icon: Package,
      details: {
        progress: "75%",
        expectedCompletion: "2024-02-28",
        qualityCheck: "Pending"
      }
    },
    {
      id: "STAGE-005",
      stage: "Final Delivery",
      location: "Customer Site",
      date: "2024-03-01",
      status: "Pending",
      supplier: "Logistics Partner",
      description: "Final delivery to customer location",
      icon: Truck,
      details: {
        estimatedArrival: "2024-03-05",
        trackingNumber: "TRK-789456",
        deliveryMethod: "Ground Transport"
      }
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "In Progress":
        return <Clock className="h-5 w-5 text-blue-600" />;
      case "Pending":
        return <AlertTriangle className="h-5 w-5 text-amber-600" />;
      default:
        return <Package className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Pending":
        return <Badge className="bg-amber-100 text-amber-800">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Material Journey Timeline - {materialId}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            Complete traceability from raw material extraction to final delivery
          </p>
        </CardContent>
      </Card>

      <TimelineFilters 
        filters={filters}
        onFiltersChange={setFilters}
      />

      <div className="space-y-4">
        {mockJourneyData.map((stage, index) => (
          <Card key={stage.id} className="relative">
            {index < mockJourneyData.length - 1 && (
              <div className="absolute left-8 top-16 w-0.5 h-20 bg-gray-200 z-0" />
            )}
            
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 relative z-10">
                  <div className="w-12 h-12 bg-white border-2 border-blue-200 rounded-full flex items-center justify-center">
                    {getStatusIcon(stage.status)}
                  </div>
                </div>
                
                <div className="flex-1 space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">{stage.stage}</h3>
                    {getStatusBadge(stage.status)}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{stage.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>{stage.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Factory className="h-4 w-4" />
                      <span>{stage.supplier}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-700">{stage.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-3">
                    {Object.entries(stage.details).map(([key, value]) => (
                      <div key={key} className="bg-gray-50 p-2 rounded text-xs">
                        <div className="font-medium text-gray-700 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                        <div className="text-gray-600">{value}</div>
                      </div>
                    ))}
                  </div>
                  
                  <Button size="sm" variant="outline" className="mt-2">
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

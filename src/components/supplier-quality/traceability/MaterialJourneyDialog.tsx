
import React from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Route, MapPin, Calendar, Package } from "lucide-react";

interface MaterialJourneyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  materialId: string;
}

export const MaterialJourneyDialog: React.FC<MaterialJourneyDialogProps> = ({
  open,
  onOpenChange,
  materialId
}) => {
  const mockJourneyData = [
    {
      stage: "Raw Material Sourcing",
      location: "Western Australia",
      date: "2024-01-02",
      status: "Completed",
      description: "Aluminum ore extracted and processed"
    },
    {
      stage: "Material Processing",
      location: "Melbourne, Australia",
      date: "2024-01-20",
      status: "Completed",
      description: "Aluminum sheets rolled and quality tested"
    },
    {
      stage: "Component Manufacturing",
      location: "Singapore",
      date: "2024-02-05",
      status: "Completed",
      description: "Enclosure brackets manufactured and inspected"
    },
    {
      stage: "Final Assembly",
      location: "Phoenix, AZ",
      date: "2024-03-01",
      status: "Completed",
      description: "Components assembled into final product"
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Material Journey - {materialId}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Complete journey tracking from raw material to final product
          </p>
          
          <div className="space-y-4">
            {mockJourneyData.map((stage, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                  </div>
                </div>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{stage.stage}</h4>
                    <Badge className="bg-green-100 text-green-800">{stage.status}</Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span>{stage.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{stage.date}</span>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-700">{stage.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

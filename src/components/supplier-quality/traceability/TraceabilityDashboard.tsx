
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileCheck, Shield, BarChart3 } from "lucide-react";
import { BatchTraceabilityView } from "./BatchTraceabilityView";
import { TraceTree } from "./TraceTree";

export const TraceabilityDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700 flex items-center gap-2">
              <FileCheck className="h-4 w-4" />
              Certificates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-800">234</div>
            <p className="text-xs text-emerald-600">Active certificates</p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full mt-2 text-emerald-700 border-emerald-300"
              onClick={() => alert('Certificate management feature available in dashboard!')}
            >
              View Certs
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-700 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Risk Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">15</div>
            <p className="text-xs text-red-600">High risk suppliers</p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full mt-2 text-red-700 border-red-300"
              onClick={() => alert('Risk assessment available in dashboard!')}
            >
              View Risks
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Scorecards
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">87%</div>
            <p className="text-xs text-blue-600">Average score</p>
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full mt-2 text-blue-700 border-blue-300"
              onClick={() => alert('Scorecard analytics available in dashboard!')}
            >
              View Scores
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <BatchTraceabilityView />
        </div>
        <div>
          <TraceTree timelineMonth={1} />
        </div>
      </div>
    </div>
  );
};

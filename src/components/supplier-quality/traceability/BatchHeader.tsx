
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Battery } from "lucide-react";

interface BatchHeaderProps {
  onRecallReport: () => void;
}

export const BatchHeader: React.FC<BatchHeaderProps> = ({ onRecallReport }) => {
  return (
    <Card className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Battery className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-900">Lithium Battery Pack v3.1</h1>
              <div className="flex items-center gap-4 mt-2">
                <div>
                  <span className="text-sm text-slate-600">Batch ID:</span>
                  <span className="ml-2 font-mono font-medium text-slate-900">BATT-2319-AL</span>
                </div>
                <Badge className="bg-green-100 text-green-800">Valid</Badge>
              </div>
              <div className="mt-3 flex flex-wrap gap-3 text-sm text-slate-600">
                <span>Production Date: Jan 15, 2024</span>
                <span>Quantity: 2,500 units</span>
                <span>Status: In Production</span>
              </div>
            </div>
          </div>
          
          <Button onClick={onRecallReport} className="gap-2 bg-red-600 hover:bg-red-700">
            <Download className="h-4 w-4" />
            Recall Report
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

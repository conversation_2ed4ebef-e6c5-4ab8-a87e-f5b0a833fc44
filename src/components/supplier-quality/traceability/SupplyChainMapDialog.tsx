import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Route, MapPin, Factory, Package, TrendingUp, AlertTriangle, BarChart3, Activity, ShieldCheck, Truck, Clock, CheckCircle } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from "recharts";

interface SupplyChainMapDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const SupplyChainMapDialog: React.FC<SupplyChainMapDialogProps> = ({ open, onOpenChange }) => {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [activeView, setActiveView] = useState("map");

  const supplyChainNodes = [
    {
      id: "tier1-1",
      name: "Advanced Battery Systems",
      tier: "Tier 1",
      location: "San Jose, CA",
      status: "Active",
      risk: "Low",
      coordinates: { x: 20, y: 30 },
      products: ["Lithium Battery Packs", "Battery Management Systems"],
      lastDelivery: "2024-01-20",
      performance: { onTime: 98, quality: 96, cost: 85 },
      riskScore: 12,
      materialFlow: 2500
    },
    {
      id: "tier2-1", 
      name: "Cell Manufacturing Corp",
      tier: "Tier 2",
      location: "Seoul, South Korea",
      status: "Active",
      risk: "Medium",
      coordinates: { x: 70, y: 25 },
      products: ["Battery Cells", "Electrolytes"],
      lastDelivery: "2024-01-18",
      performance: { onTime: 92, quality: 89, cost: 78 },
      riskScore: 25,
      materialFlow: 1800
    },
    {
      id: "tier3-1",
      name: "Lithium Mining Co",
      tier: "Tier 3", 
      location: "Atacama, Chile",
      status: "Active",
      risk: "High",
      coordinates: { x: 40, y: 80 },
      products: ["Lithium Carbonate", "Raw Materials"],
      lastDelivery: "2024-01-15",
      performance: { onTime: 85, quality: 82, cost: 92 },
      riskScore: 42,
      materialFlow: 950
    }
  ];

  // Mock data for analytics
  const performanceData = [
    { month: 'Jan', onTime: 95, quality: 92, cost: 88 },
    { month: 'Feb', onTime: 97, quality: 94, cost: 85 },
    { month: 'Mar', onTime: 93, quality: 96, cost: 90 },
    { month: 'Apr', onTime: 98, quality: 91, cost: 87 },
    { month: 'May', onTime: 96, quality: 95, cost: 89 }
  ];

  const riskDistribution = [
    { name: 'Low Risk', value: 45, color: '#10b981' },
    { name: 'Medium Risk', value: 35, color: '#f59e0b' },
    { name: 'High Risk', value: 20, color: '#ef4444' }
  ];

  const materialFlowData = [
    { supplier: 'Advanced Battery', flow: 2500, trend: 'up' },
    { supplier: 'Cell Manufacturing', flow: 1800, trend: 'stable' },
    { supplier: 'Lithium Mining', flow: 950, trend: 'down' },
    { supplier: 'Metal Forming', flow: 1200, trend: 'up' }
  ];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low": return "bg-green-500";
      case "Medium": return "bg-yellow-500";
      case "High": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const selectedNodeData = supplyChainNodes.find(node => node.id === selectedNode);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Route className="h-5 w-5" />
            Enhanced Supply Chain Analytics
          </DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeView} onValueChange={setActiveView} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="map">Supply Chain Map</TabsTrigger>
            <TabsTrigger value="performance">Performance Analytics</TabsTrigger>
            <TabsTrigger value="risk">Risk Assessment</TabsTrigger>
            <TabsTrigger value="flow">Material Flow</TabsTrigger>
          </TabsList>

          <TabsContent value="map" className="flex-1 flex gap-6">
            <div className="flex-1 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 relative min-h-[500px]">
              {/* Connection Lines */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                <line x1="20%" y1="30%" x2="70%" y2="25%" stroke="#e5e7eb" strokeWidth="2" strokeDasharray="5,5" />
                <line x1="70%" y1="25%" x2="40%" y2="80%" stroke="#e5e7eb" strokeWidth="2" strokeDasharray="5,5" />
              </svg>
              
              {/* Supply Chain Nodes */}
              {supplyChainNodes.map((node) => (
                <div
                  key={node.id}
                  className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all hover:scale-110 ${
                    selectedNode === node.id ? 'scale-110' : ''
                  }`}
                  style={{ 
                    left: `${node.coordinates.x}%`, 
                    top: `${node.coordinates.y}%` 
                  }}
                  onClick={() => setSelectedNode(selectedNode === node.id ? null : node.id)}
                >
                  <div className={`w-4 h-4 rounded-full ${getRiskColor(node.risk)} border-2 border-white shadow-lg`}></div>
                  <div className="mt-2 text-xs text-center bg-white rounded px-2 py-1 shadow-sm max-w-24 truncate">
                    {node.name}
                  </div>
                </div>
              ))}
              
              {/* Legend */}
              <div className="absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-lg">
                <h4 className="text-sm font-semibold mb-2">Risk Levels</h4>
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span>Low Risk</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <span>Medium Risk</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span>High Risk</span>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="absolute top-4 right-4 bg-white rounded-lg p-3 shadow-lg">
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div className="text-center">
                    <div className="text-lg font-bold text-blue-600">142</div>
                    <div className="text-gray-600">Suppliers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600">24</div>
                    <div className="text-gray-600">Active</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-orange-600">8</div>
                    <div className="text-gray-600">Regions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-purple-600">95%</div>
                    <div className="text-gray-600">On-Time</div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Node Details Sidebar */}
            <div className="w-80 space-y-4">
              {selectedNodeData ? (
                <Card>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold">{selectedNodeData.name}</h3>
                        <Badge variant={selectedNodeData.risk === "Low" ? "default" : selectedNodeData.risk === "Medium" ? "secondary" : "destructive"}>
                          {selectedNodeData.risk} Risk
                        </Badge>
                      </div>
                      
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Factory className="h-4 w-4 text-gray-500" />
                          <span>{selectedNodeData.tier}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-500" />
                          <span>{selectedNodeData.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-gray-500" />
                          <span>Last Delivery: {selectedNodeData.lastDelivery}</span>
                        </div>
                      </div>
                      
                      {/* Performance Metrics */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Performance</h4>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="text-center p-2 bg-green-50 rounded">
                            <div className="font-bold text-green-600">{selectedNodeData.performance.onTime}%</div>
                            <div className="text-gray-600">On Time</div>
                          </div>
                          <div className="text-center p-2 bg-blue-50 rounded">
                            <div className="font-bold text-blue-600">{selectedNodeData.performance.quality}%</div>
                            <div className="text-gray-600">Quality</div>
                          </div>
                          <div className="text-center p-2 bg-orange-50 rounded">
                            <div className="font-bold text-orange-600">{selectedNodeData.performance.cost}%</div>
                            <div className="text-gray-600">Cost</div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium mb-2">Products</h4>
                        <div className="space-y-1">
                          {selectedNodeData.products.map((product, idx) => (
                            <div key={idx} className="text-xs bg-gray-100 rounded px-2 py-1">
                              {product}
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          Contact
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-4 text-center">
                    <Route className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Click on a supplier node to view detailed information
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="flex-1 space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Performance Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={performanceData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis domain={[70, 100]} />
                        <Tooltip />
                        <Line type="monotone" dataKey="onTime" stroke="#10b981" strokeWidth={2} name="On Time %" />
                        <Line type="monotone" dataKey="quality" stroke="#3b82f6" strokeWidth={2} name="Quality %" />
                        <Line type="monotone" dataKey="cost" stroke="#f59e0b" strokeWidth={2} name="Cost Efficiency %" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Key Performance Indicators</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                      <div>
                        <p className="text-sm text-green-700">Average On-Time Delivery</p>
                        <p className="text-2xl font-bold text-green-800">95.8%</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded">
                      <div>
                        <p className="text-sm text-blue-700">Quality Score</p>
                        <p className="text-2xl font-bold text-blue-800">93.6%</p>
                      </div>
                      <ShieldCheck className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="flex justify-between items-center p-3 bg-orange-50 rounded">
                      <div>
                        <p className="text-sm text-orange-700">Cost Efficiency</p>
                        <p className="text-2xl font-bold text-orange-800">87.8%</p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Supplier Performance Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={supplyChainNodes}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="performance.onTime" fill="#10b981" name="On Time %" />
                        <Bar dataKey="performance.quality" fill="#3b82f6" name="Quality %" />
                        <Bar dataKey="performance.cost" fill="#f59e0b" name="Cost %" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="risk" className="flex-1 space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Risk Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={riskDistribution}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {riskDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Risk Assessment Matrix</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {supplyChainNodes.map((node) => (
                      <div key={node.id} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getRiskColor(node.risk)}`}></div>
                          <div>
                            <p className="font-medium text-sm">{node.name}</p>
                            <p className="text-xs text-gray-600">{node.tier}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold">{node.riskScore}/100</p>
                          <p className="text-xs text-gray-600">{node.risk} Risk</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Risk Mitigation Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 bg-red-50 rounded border border-red-200">
                      <AlertTriangle className="h-6 w-6 text-red-600 mb-2" />
                      <h4 className="font-semibold text-red-800">High Priority</h4>
                      <p className="text-sm text-red-700">3 suppliers need immediate attention</p>
                      <Button size="sm" variant="outline" className="mt-2 text-red-700 border-red-300">
                        Review Now
                      </Button>
                    </div>
                    <div className="p-4 bg-yellow-50 rounded border border-yellow-200">
                      <Clock className="h-6 w-6 text-yellow-600 mb-2" />
                      <h4 className="font-semibold text-yellow-800">Medium Priority</h4>
                      <p className="text-sm text-yellow-700">5 suppliers require monitoring</p>
                      <Button size="sm" variant="outline" className="mt-2 text-yellow-700 border-yellow-300">
                        Schedule Review
                      </Button>
                    </div>
                    <div className="p-4 bg-green-50 rounded border border-green-200">
                      <CheckCircle className="h-6 w-6 text-green-600 mb-2" />
                      <h4 className="font-semibold text-green-800">Low Priority</h4>
                      <p className="text-sm text-green-700">134 suppliers performing well</p>
                      <Button size="sm" variant="outline" className="mt-2 text-green-700 border-green-300">
                        View Report
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="flow" className="flex-1 space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Material Flow Volume
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={materialFlowData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="supplier" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="flow" fill="#6366f1" name="Units/Month" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Flow Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {materialFlowData.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <Truck className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="font-medium text-sm">{item.supplier}</p>
                            <p className="text-xs text-gray-600">{item.flow} units/month</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {item.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-600" />}
                          {item.trend === 'down' && <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />}
                          {item.trend === 'stable' && <Activity className="h-4 w-4 text-gray-600" />}
                          <span className="text-xs capitalize">{item.trend}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Supply Chain Flow Map</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 h-64 relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <Truck className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">Interactive Material Flow</h3>
                        <p className="text-gray-600 mb-4">
                          Real-time visualization of material movement across your supply chain network
                        </p>
                        <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                          <div className="bg-white rounded-lg p-3 shadow-sm">
                            <div className="text-lg font-bold text-blue-600">5,250</div>
                            <div className="text-xs text-gray-600">Total Units</div>
                          </div>
                          <div className="bg-white rounded-lg p-3 shadow-sm">
                            <div className="text-lg font-bold text-green-600">24h</div>
                            <div className="text-xs text-gray-600">Avg Transit</div>
                          </div>
                          <div className="bg-white rounded-lg p-3 shadow-sm">
                            <div className="text-lg font-bold text-purple-600">98%</div>
                            <div className="text-xs text-gray-600">Efficiency</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};


import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Package, MapPin, Clock, FileText, AlertTriangle, CheckCircle, User, Building } from "lucide-react";
import { formatDate } from "@/utils/dateUtils";

interface BatchDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  batchId?: string;
}

export const BatchDetailDialog: React.FC<BatchDetailDialogProps> = ({
  open,
  onOpenChange,
  batchId
}) => {
  const mockBatchDetail = {
    id: "BT-2024-001",
    lotNumber: "LOT-A12345",
    product: "Steel Component A",
    supplier: "ABC Manufacturing",
    status: "Active",
    createdDate: "2024-01-15",
    location: "Warehouse A",
    quantity: 500,
    qualityScore: 95,
    traceabilitySteps: [
      {
        id: "STEP-001",
        stage: "Raw Material Receipt",
        location: "Receiving Dock A",
        date: "2024-01-15",
        status: "Completed",
        operator: "<PERSON>",
        details: "Materials received and inspected. All documentation verified.",
        temperature: "22°C",
        humidity: "45%"
      },
      {
        id: "STEP-002",
        stage: "Quality Inspection",
        location: "QC Lab",
        date: "2024-01-16",
        status: "Completed",
        operator: "Sarah Johnson",
        details: "Dimensional checks completed. Chemical composition verified.",
        testResults: [
          { test: "Tensile Strength", result: "550 MPa", specification: "≥500 MPa", status: "Pass" },
          { test: "Hardness", result: "28 HRC", specification: "25-30 HRC", status: "Pass" },
          { test: "Surface Finish", result: "1.6 Ra", specification: "≤2.0 Ra", status: "Pass" }
        ]
      },
      {
        id: "STEP-003",
        stage: "Processing",
        location: "Production Line 1",
        date: "2024-01-18",
        status: "Completed",
        operator: "Mike Wilson",
        details: "Material processed according to work instruction WI-001-Rev3",
        processParameters: {
          pressure: "150 bar",
          temperature: "180°C",
          cycleTime: "45 seconds"
        }
      },
      {
        id: "STEP-004",
        stage: "Final Inspection",
        location: "Final QC",
        date: "2024-01-20",
        status: "In Progress",
        operator: "Emma Davis",
        details: "Final dimensional and visual inspection in progress",
        estimatedCompletion: "2024-01-21"
      }
    ],
    documents: [
      { name: "Certificate of Analysis", type: "PDF", uploadDate: "2024-01-15" },
      { name: "Material Test Report", type: "PDF", uploadDate: "2024-01-16" },
      { name: "Process Control Record", type: "Excel", uploadDate: "2024-01-18" }
    ]
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Completed
        </Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
          <Clock className="h-3 w-3" />
          In Progress
        </Badge>;
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Batch Details: {mockBatchDetail.lotNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Batch Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Product</p>
                    <p className="font-semibold">{mockBatchDetail.product}</p>
                  </div>
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Supplier</p>
                    <p className="font-semibold">{mockBatchDetail.supplier}</p>
                  </div>
                  <Building className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Quality Score</p>
                    <p className="font-semibold text-green-600">{mockBatchDetail.qualityScore}%</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Traceability Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Traceability Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockBatchDetail.traceabilitySteps.map((step, index) => (
                  <div key={step.id} className="relative">
                    {index < mockBatchDetail.traceabilitySteps.length - 1 && (
                      <div className="absolute left-6 top-16 w-0.5 h-16 bg-gray-200" />
                    )}
                    
                    <div className="flex items-start gap-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">{index + 1}</span>
                        </div>
                      </div>
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold">{step.stage}</h4>
                          {getStatusBadge(step.status)}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {step.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(step.date)}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {step.operator}
                          </div>
                        </div>
                        
                        <p className="text-sm">{step.details}</p>
                        
                        {step.testResults && (
                          <div className="mt-3">
                            <h5 className="font-medium text-sm mb-2">Test Results:</h5>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                              {step.testResults.map((test, testIndex) => (
                                <div key={testIndex} className="p-2 bg-gray-50 rounded text-xs">
                                  <div className="font-medium">{test.test}</div>
                                  <div>Result: {test.result}</div>
                                  <div>Spec: {test.specification}</div>
                                  <Badge className={test.status === 'Pass' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                                    {test.status}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Related Documents */}
          <Card>
            <CardHeader>
              <CardTitle>Related Documents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {mockBatchDetail.documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      <div>
                        <p className="font-medium text-sm">{doc.name}</p>
                        <p className="text-xs text-gray-500">Uploaded: {formatDate(doc.uploadDate)}</p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">View</Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

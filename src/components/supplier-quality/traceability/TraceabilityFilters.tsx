
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

interface TraceabilityFiltersProps {
  supplierRisk: string;
  batchAge: string;
  defectReports: string;
  onSupplierRiskChange: (value: string) => void;
  onBatchAgeChange: (value: string) => void;
  onDefectReportsChange: (value: string) => void;
}

export const TraceabilityFilters: React.FC<TraceabilityFiltersProps> = ({
  supplierRisk,
  batchAge,
  defectReports,
  onSupplierRiskChange,
  onBatchAgeChange,
  onDefectReportsChange
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="supplierRisk">Supplier Risk</Label>
            <Select value={supplierRisk} onValueChange={onSupplierRiskChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select risk level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risk Levels</SelectItem>
                <SelectItem value="low">Low Risk</SelectItem>
                <SelectItem value="medium">Medium Risk</SelectItem>
                <SelectItem value="high">High Risk</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="batchAge">Batch Age</Label>
            <Select value={batchAge} onValueChange={onBatchAgeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select batch age" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ages</SelectItem>
                <SelectItem value="0-30">0-30 days</SelectItem>
                <SelectItem value="31-90">31-90 days</SelectItem>
                <SelectItem value="91-180">91-180 days</SelectItem>
                <SelectItem value="180+">180+ days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="defectReports">Defect Reports</Label>
            <Select value={defectReports} onValueChange={onDefectReportsChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select defect status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="none">No Defects</SelectItem>
                <SelectItem value="minor">Minor Defects</SelectItem>
                <SelectItem value="major">Major Defects</SelectItem>
                <SelectItem value="critical">Critical Defects</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Calendar, 
  CheckCircle, 
  AlertTriangle,
  Package,
  Factory
} from "lucide-react";

interface MaterialStage {
  id: string;
  stage: string;
  material: string;
  supplier: string;
  tier: string;
  location: string;
  date: string;
  batchId?: string;
  status: 'Verified' | 'Flagged' | 'Under Review' | 'Completed';
  product?: string;
  documents: string[];
  riskScore: number;
}

interface TimelineStageProps {
  stage: MaterialStage;
  isSelected: boolean;
  onClick: () => void;
}

export const TimelineStage: React.FC<TimelineStageProps> = ({ 
  stage, 
  isSelected, 
  onClick 
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Verified": return "bg-green-100 text-green-800 border-green-300";
      case "Completed": return "bg-blue-100 text-blue-800 border-blue-300";
      case "Flagged": return "bg-red-100 text-red-800 border-red-300";
      case "Under Review": return "bg-yellow-100 text-yellow-800 border-yellow-300";
      default: return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Verified":
      case "Completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Flagged":
      case "Under Review":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTierIcon = (tier: string) => {
    if (tier.includes("3")) return <Package className="h-3 w-3" />;
    if (tier.includes("2")) return <Factory className="h-3 w-3" />;
    return <CheckCircle className="h-3 w-3" />;
  };

  return (
    <Card 
      className={`w-72 cursor-pointer transition-all duration-200 hover:shadow-md flex-shrink-0 ${
        isSelected 
          ? 'ring-2 ring-blue-500 shadow-lg scale-105' 
          : 'hover:scale-102'
      }`}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              {getStatusIcon(stage.status)}
              <h4 className="font-semibold text-sm leading-tight">{stage.stage}</h4>
            </div>
            <Badge className={`text-xs ${getStatusColor(stage.status)}`}>
              {stage.status}
            </Badge>
          </div>

          {/* Material/Product */}
          <div className="space-y-1">
            <p className="font-medium text-sm text-blue-700">{stage.material}</p>
            {stage.product && (
              <p className="text-xs text-gray-600">→ {stage.product}</p>
            )}
          </div>

          {/* Supplier Info */}
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              {getTierIcon(stage.tier)}
              <span className="text-sm font-medium">{stage.supplier}</span>
            </div>
            <p className="text-xs text-gray-600">{stage.tier}</p>
          </div>

          {/* Location & Date */}
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{stage.location}</span>
            </div>
            <div className="flex items-center gap-1 text-xs text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{new Date(stage.date).toLocaleDateString()}</span>
            </div>
          </div>

          {/* Batch ID */}
          {stage.batchId && (
            <div className="bg-gray-50 rounded px-2 py-1">
              <p className="text-xs font-mono text-gray-700">{stage.batchId}</p>
            </div>
          )}

          {/* Risk Score */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Risk Score:</span>
            <span className={`text-xs font-bold ${
              stage.riskScore <= 15 ? 'text-green-600' : 
              stage.riskScore <= 25 ? 'text-yellow-600' : 'text-red-600'
            }`}>
              {stage.riskScore}/100
            </span>
          </div>

          {/* Documents Count */}
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>Documents:</span>
            <span className="font-medium">{stage.documents.length}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Package, MapPin, Calendar, Factory, FileText, AlertCircle } from "lucide-react";
import { mockBatches } from "@/data/mockBatchTraceabilityData";

interface BatchLookupDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const BatchLookupDialog: React.FC<BatchLookupDialogProps> = ({ open, onOpenChange }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState(mockBatches);

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults(mockBatches);
      return;
    }
    
    const filtered = mockBatches.filter(batch => 
      batch.lotNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      batch.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      batch.supplier.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setSearchResults(filtered);
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      'Valid': 'bg-green-100 text-green-800',
      'At Risk': 'bg-amber-100 text-amber-800',
      'Expired': 'bg-red-100 text-red-800',
      'Recalled': 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Batch Lookup
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 flex-1">
          {/* Search Section */}
          <div className="flex gap-2">
            <Input
              placeholder="Enter lot number, batch ID, or supplier name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Search Results */}
          <div className="flex-1 overflow-y-auto space-y-3">
            <h3 className="font-semibold">Search Results ({searchResults.length})</h3>
            {searchResults.map((batch) => (
              <Card key={batch.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-blue-600" />
                        <span className="font-medium">{batch.name}</span>
                        {getStatusBadge(batch.status)}
                      </div>
                      <p className="text-sm font-mono text-gray-600">{batch.lotNumber}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{batch.quantity} units</p>
                      <p className="text-xs text-gray-500">{batch.productionDate}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs mb-3">
                    <div className="flex items-center gap-1">
                      <Factory className="h-3 w-3 text-gray-500" />
                      <span>{batch.supplier}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-gray-500" />
                      <span>{batch.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-gray-500" />
                      <span>{batch.productionDate}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3 text-gray-500" />
                      <span>{batch.documents?.length || 0} docs</span>
                    </div>
                  </div>

                  {/* Quality Metrics */}
                  <div className="flex gap-4 mb-3 text-xs">
                    <div className="flex items-center gap-1">
                      <span className="text-gray-500">Risk Score:</span>
                      <span className={`font-bold ${
                        (batch.riskScore || 0) <= 15 ? 'text-green-600' : 
                        (batch.riskScore || 0) <= 30 ? 'text-amber-600' : 'text-red-600'
                      }`}>
                        {batch.riskScore || 'N/A'}/100
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="text-gray-500">Quality Score:</span>
                      <span className="font-bold text-blue-600">{batch.qualityScore || 'N/A'}%</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      Trace History
                    </Button>
                    <Button size="sm" variant="outline">
                      Documents
                    </Button>
                    {batch.status === 'At Risk' && (
                      <Button size="sm" variant="outline" className="text-amber-600 border-amber-300">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Review
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

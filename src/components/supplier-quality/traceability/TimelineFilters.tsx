
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FilterSection } from "../filter-components/FilterSection";
import { 
  Filter, 
  X, 
  Package, 
  Building, 
  MapPin, 
  ShieldCheck 
} from "lucide-react";

interface TimelineFiltersProps {
  filters: {
    materialType: string;
    supplierTier: string;
    region: string;
    complianceStatus: string;
  };
  onFiltersChange: (filters: any) => void;
}

export const TimelineFilters: React.FC<TimelineFiltersProps> = ({ 
  filters, 
  onFiltersChange 
}) => {
  const materialTypes = [
    "Aluminum Ore", "Steel", "Copper", "Plastic Resin", "Lithium", "Silicon"
  ];

  const supplierTiers = ["Tier 1", "Tier 2", "Tier 3", "Assembly"];
  
  const regions = [
    "North America", "South America", "Europe", "Asia Pacific", 
    "Middle East", "Africa", "Australia"
  ];

  const complianceStatuses = ["Verified", "Flagged", "Under Review", "Completed"];

  const updateFilter = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: filters[key as keyof typeof filters] === value ? '' : value
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      materialType: '',
      supplierTier: '',
      region: '',
      complianceStatus: ''
    });
  };

  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <h4 className="font-medium">Timeline Filters</h4>
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {activeFiltersCount} active
                </Badge>
              )}
            </div>
            {activeFiltersCount > 0 && (
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={clearAllFilters}
                className="text-gray-600 hover:text-gray-800"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
          </div>

          {/* Filter Groups */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Material Type */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <Package className="h-4 w-4" />
                Material Type
              </div>
              <div className="flex flex-wrap gap-1">
                {materialTypes.map((type) => (
                  <Button
                    key={type}
                    size="sm"
                    variant={filters.materialType === type ? "default" : "outline"}
                    className="h-7 text-xs"
                    onClick={() => updateFilter('materialType', type)}
                  >
                    {type}
                  </Button>
                ))}
              </div>
            </div>

            {/* Supplier Tier */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <Building className="h-4 w-4" />
                Supplier Tier
              </div>
              <div className="flex flex-wrap gap-1">
                {supplierTiers.map((tier) => (
                  <Button
                    key={tier}
                    size="sm"
                    variant={filters.supplierTier === tier ? "default" : "outline"}
                    className="h-7 text-xs"
                    onClick={() => updateFilter('supplierTier', tier)}
                  >
                    {tier}
                  </Button>
                ))}
              </div>
            </div>

            {/* Region */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <MapPin className="h-4 w-4" />
                Region
              </div>
              <div className="flex flex-wrap gap-1">
                {regions.map((region) => (
                  <Button
                    key={region}
                    size="sm"
                    variant={filters.region === region ? "default" : "outline"}
                    className="h-7 text-xs"
                    onClick={() => updateFilter('region', region)}
                  >
                    {region}
                  </Button>
                ))}
              </div>
            </div>

            {/* Compliance Status */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <ShieldCheck className="h-4 w-4" />
                Compliance Status
              </div>
              <div className="flex flex-wrap gap-1">
                {complianceStatuses.map((status) => (
                  <Button
                    key={status}
                    size="sm"
                    variant={filters.complianceStatus === status ? "default" : "outline"}
                    className="h-7 text-xs"
                    onClick={() => updateFilter('complianceStatus', status)}
                  >
                    {status}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, Clock, CheckCircle, XCircle, User, Calendar, Package, TrendingUp } from "lucide-react";
import { mockQualityAlerts } from "@/data/mockBatchTraceabilityData";

interface QualityAlertsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const QualityAlertsDialog: React.FC<QualityAlertsDialogProps> = ({ open, onOpenChange }) => {
  const [selectedAlert, setSelectedAlert] = useState<string | null>(null);
  
  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'Critical':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'Warning':
        return <AlertCircle className="h-5 w-5 text-amber-600" />;
      case 'Info':
        return <CheckCircle className="h-5 w-5 text-blue-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getAlertBadge = (alertType: string) => {
    const colors = {
      'Critical': 'bg-red-100 text-red-800',
      'Warning': 'bg-amber-100 text-amber-800',
      'Info': 'bg-blue-100 text-blue-800'
    };
    return (
      <Badge className={colors[alertType as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {alertType}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      'Open': 'bg-red-100 text-red-800',
      'Investigating': 'bg-amber-100 text-amber-800',
      'Resolved': 'bg-green-100 text-green-800'
    };
    return (
      <Badge className={colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      'High': 'bg-red-100 text-red-800',
      'Medium': 'bg-amber-100 text-amber-800',
      'Low': 'bg-green-100 text-green-800'
    };
    return (
      <Badge className={colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {priority} Priority
      </Badge>
    );
  };

  const selectedAlertData = mockQualityAlerts.find(alert => alert.id === selectedAlert);

  const alertsByStatus = {
    open: mockQualityAlerts.filter(alert => alert.status === 'Open'),
    investigating: mockQualityAlerts.filter(alert => alert.status === 'Investigating'),
    resolved: mockQualityAlerts.filter(alert => alert.status === 'Resolved')
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl h-[85vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Quality Alerts Management
          </DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="alerts" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="alerts">Active Alerts</TabsTrigger>
            <TabsTrigger value="trends">Alert Trends</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="alerts" className="flex-1 flex gap-6">
            {/* Alerts List */}
            <div className="w-2/3 space-y-3 overflow-y-auto">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">Quality Alerts ({mockQualityAlerts.length})</h3>
                <div className="flex gap-2">
                  <Badge className="bg-red-100 text-red-800">{alertsByStatus.open.length} Open</Badge>
                  <Badge className="bg-amber-100 text-amber-800">{alertsByStatus.investigating.length} Investigating</Badge>
                  <Badge className="bg-green-100 text-green-800">{alertsByStatus.resolved.length} Resolved</Badge>
                </div>
              </div>
              
              {mockQualityAlerts.map((alert) => (
                <Card 
                  key={alert.id} 
                  className={`cursor-pointer hover:shadow-md transition-shadow ${
                    selectedAlert === alert.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedAlert(alert.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start gap-3">
                        {getAlertIcon(alert.alertType)}
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{alert.title}</span>
                            {getAlertBadge(alert.alertType)}
                          </div>
                          <p className="text-sm text-gray-600 line-clamp-2">{alert.description}</p>
                        </div>
                      </div>
                      <div className="flex flex-col gap-1">
                        {getStatusBadge(alert.status)}
                        {getPriorityBadge(alert.priority)}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs mb-3">
                      <div className="flex items-center gap-1">
                        <Package className="h-3 w-3 text-gray-500" />
                        <span>{alert.batchId}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-gray-500" />
                        <span>{alert.dateCreated}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3 text-gray-500" />
                        <span>{alert.assignedTo?.split(' - ')[1] || 'Unassigned'}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-gray-500">Impact: </span>
                        <span className="font-medium">{alert.affectedQuantity}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{alert.supplier}</span>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                        {alert.status === 'Open' && (
                          <Button size="sm" variant="outline" className="text-amber-600 border-amber-300">
                            Investigate
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Alert Details */}
            <div className="w-1/3 space-y-4">
              {selectedAlertData ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getAlertIcon(selectedAlertData.alertType)}
                      Alert Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">{selectedAlertData.title}</h4>
                      <p className="text-sm text-gray-600">{selectedAlertData.description}</p>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Alert Information</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Type:</span>
                          <span>{selectedAlertData.alertType}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Priority:</span>
                          <span>{selectedAlertData.priority}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Status:</span>
                          <span>{selectedAlertData.status}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Created:</span>
                          <span>{selectedAlertData.dateCreated}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Affected Batch</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Batch ID:</span>
                          <span className="font-mono">{selectedAlertData.batchId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Supplier:</span>
                          <span>{selectedAlertData.supplier}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Quantity:</span>
                          <span>{selectedAlertData.affectedQuantity}</span>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Impact Assessment</h4>
                      <div className="p-3 bg-amber-50 rounded border border-amber-200">
                        <p className="text-sm text-amber-800">{selectedAlertData.estimatedImpact}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Assignment</h4>
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-4 w-4 text-gray-500" />
                        <span>{selectedAlertData.assignedTo}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        Update Status
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        Add Comment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-4 text-center">
                    <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">
                      Select an alert to view details
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="trends" className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Alert Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-6 h-64 flex items-center justify-center">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Alert Trend Analysis</h3>
                      <p className="text-gray-600 text-sm">
                        Interactive charts showing alert patterns over time
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Alert Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                      <div>
                        <p className="font-medium text-red-800">Critical Alerts</p>
                        <p className="text-sm text-red-600">Requires immediate attention</p>
                      </div>
                      <div className="text-2xl font-bold text-red-600">3</div>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-amber-50 rounded">
                      <div>
                        <p className="font-medium text-amber-800">Warning Alerts</p>
                        <p className="text-sm text-amber-600">Monitor closely</p>
                      </div>
                      <div className="text-2xl font-bold text-amber-600">3</div>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50 rounded">
                      <div>
                        <p className="font-medium text-blue-800">Info Alerts</p>
                        <p className="text-sm text-blue-600">Informational updates</p>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">2</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full">
              <Card>
                <CardHeader>
                  <CardTitle>Response Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">2.4h</div>
                    <p className="text-sm text-gray-600">Average Response Time</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Resolution Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">87%</div>
                    <p className="text-sm text-gray-600">Within SLA</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Alert Volume</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600">23</div>
                    <p className="text-sm text-gray-600">This Month</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

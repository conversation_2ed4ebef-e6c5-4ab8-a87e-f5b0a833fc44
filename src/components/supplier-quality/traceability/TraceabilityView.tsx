
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GitBranch, Package, Truck, Shield, FileText, BarChart3, Search } from 'lucide-react';
import { BatchTraceabilityView } from './BatchTraceabilityView';
import { BatchLookupDialog } from './BatchLookupDialog';
import { MaterialJourneyDialog } from './MaterialJourneyDialog';

export const TraceabilityView: React.FC = () => {
  const [batchLookupOpen, setBatchLookupOpen] = useState(false);
  const [materialJourneyOpen, setMaterialJourneyOpen] = useState(false);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Products Tracked</p>
                <p className="text-2xl font-bold">1,247</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Truck className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Shipments</p>
                <p className="text-2xl font-bold">89</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <GitBranch className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Supply Chain Links</p>
                <p className="text-2xl font-bold">156</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Compliance Rate</p>
                <p className="text-2xl font-bold">98%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Advanced Batch Lookup
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Search and trace batch/lot information</p>
            <Button 
              className="w-full"
              onClick={() => setBatchLookupOpen(true)}
            >
              Search Batches
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Material Journey
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Track material journey through supply chain</p>
            <Button 
              className="w-full"
              onClick={() => setMaterialJourneyOpen(true)}
            >
              View Journey
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Timeline Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Detailed timeline analysis and reporting</p>
            <Button className="w-full">View Analytics</Button>
          </CardContent>
        </Card>
      </div>

      <BatchTraceabilityView />

      <BatchLookupDialog
        open={batchLookupOpen}
        onOpenChange={setBatchLookupOpen}
      />

      <MaterialJourneyDialog
        open={materialJourneyOpen}
        onOpenChange={setMaterialJourneyOpen}
        materialId="MAT-001"
      />
    </div>
  );
};

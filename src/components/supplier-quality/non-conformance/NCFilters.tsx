
import React from "react";
import { NCStatus, NCSeverity } from "@/types/supplierNonConformance";
import { SearchBar } from "@/components/assets/search-filter/SearchBar";
import { NCStatusBadge } from "./NCStatusBadge";
import { NCSeverityBadge } from "./NCSeverityBadge";
import { Button } from "@/components/ui/button";
import { Download, Filter, Plus } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useIsMobile } from "@/hooks/use-mobile";

interface NCFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  statusFilter: NCStatus | "all";
  setStatusFilter: (status: NCStatus | "all") => void;
  severityFilter: NCSeverity | "all";
  setSeverityFilter: (severity: NCSeverity | "all") => void;
  onExport: () => void;
}

export const NCFilters: React.FC<NCFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  severityFilter,
  setSeverityFilter,
  onExport
}) => {
  const isMobile = useIsMobile();

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div className="w-full md:w-1/2">
          <SearchBar
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            placeholder="Search by NC #, supplier, issue description..."
          />
        </div>
        
        <div className="flex flex-wrap md:flex-nowrap items-center gap-2">
          <Button onClick={onExport} variant="outline" className="flex-1 md:flex-none gap-2">
            <Download className="h-4 w-4" />
            Export CSV
          </Button>
          
          <Button variant="default" className="flex-1 md:flex-none gap-2">
            <Plus className="h-4 w-4" />
            New NC
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-grow">
          <Tabs 
            value={statusFilter} 
            onValueChange={(value) => setStatusFilter(value as NCStatus | "all")}
            className="w-full"
          >
            <TabsList className="w-full grid grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="Open" className="flex gap-1 items-center">
                <span>Open</span>
                {!isMobile && <NCStatusBadge status="Open" />}
              </TabsTrigger>
              <TabsTrigger value="In Review" className="flex gap-1 items-center">
                <span>In Review</span>
                {!isMobile && <NCStatusBadge status="In Review" />}
              </TabsTrigger>
              <TabsTrigger value="Closed" className="flex gap-1 items-center">
                <span>Closed</span>
                {!isMobile && <NCStatusBadge status="Closed" />}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div>
          <Tabs
            value={severityFilter}
            onValueChange={(value) => setSeverityFilter(value as NCSeverity | "all")}
            className="w-full"
          >
            <TabsList className="w-full grid grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="Critical" className="flex gap-1 items-center">
                <span>Critical</span>
                {!isMobile && <NCSeverityBadge severity="Critical" />}
              </TabsTrigger>
              <TabsTrigger value="Major" className="flex gap-1 items-center">
                <span>Major</span>
                {!isMobile && <NCSeverityBadge severity="Major" />}
              </TabsTrigger>
              <TabsTrigger value="Minor" className="flex gap-1 items-center">
                <span>Minor</span>
                {!isMobile && <NCSeverityBadge severity="Minor" />}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

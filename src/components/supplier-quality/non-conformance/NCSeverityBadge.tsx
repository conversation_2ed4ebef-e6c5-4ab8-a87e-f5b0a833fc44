
import React from "react";
import { NCSeverity } from "@/types/supplierNonConformance";
import { Badge } from "@/components/ui/badge";

interface NCSeverityBadgeProps {
  severity: NCSeverity;
}

export const NCSeverityBadge: React.FC<NCSeverityBadgeProps> = ({ severity }) => {
  switch (severity) {
    case "Critical":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100">
          Critical
        </Badge>
      );
    case "Major":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100">
          Major
        </Badge>
      );
    case "Minor":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
          Minor
        </Badge>
      );
    default:
      return <Badge>{severity}</Badge>;
  }
};


import React from "react";
import { SupplierNonConformance } from "@/types/supplierNonConformance";
import { TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { NCStatusBadge } from "./NCStatusBadge";
import { NCSeverityBadge } from "./NCSeverityBadge";
import { Check, X, FileSearch, ClipboardList } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useTableSort } from "@/components/assets/table/hooks/useTableSort";

interface NCTableProps {
  nonConformances: SupplierNonConformance[];
}

export const NCTable: React.FC<NCTableProps> = ({ nonConformances }) => {
  const isMobile = useIsMobile();
  const { sortField, sortDirection, handleSort, sortedItems } = 
    useTableSort<SupplierNonConformance>(nonConformances);

  if (nonConformances.length === 0) {
    return (
      <div className="text-center p-8 bg-gray-50 border rounded-lg">
        <p className="text-gray-500">No non-conformances found matching your criteria.</p>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="space-y-4">
        {sortedItems.map((nc) => (
          <div key={nc.id} className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-medium">NC #{nc.ncNumber}</h3>
                <p className="text-sm text-gray-500">{nc.supplierName}</p>
              </div>
              <div className="flex flex-col items-end">
                <NCStatusBadge status={nc.status} />
                <span className="text-xs mt-1 text-gray-500">{nc.dateReported}</span>
              </div>
            </div>
            
            <div className="mt-2 text-sm">
              <p className="font-medium">{nc.issueDescription}</p>
              <div className="mt-2 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <NCSeverityBadge severity={nc.severity} />
                  <span className="flex items-center">
                    {nc.capaLinked ? (
                      <>
                        <Check className="h-3 w-3 text-green-600 mr-1" />
                        <span className="text-xs">CAPA Linked</span>
                      </>
                    ) : (
                      <>
                        <X className="h-3 w-3 text-red-600 mr-1" />
                        <span className="text-xs">No CAPA</span>
                      </>
                    )}
                  </span>
                </div>
                <Button variant="ghost" size="sm" className="text-xs">
                  <FileSearch className="h-3 w-3 mr-1" />
                  Details
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <ResizableTable className="w-full" minColumnWidth={100}>
        <TableHeader>
          <TableRow>
            <TableHead 
              sortable 
              onSort={() => handleSort('ncNumber')} 
              sortDirection={sortField === 'ncNumber' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              NC #
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('supplierName')} 
              sortDirection={sortField === 'supplierName' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              Supplier
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('issueDescription')} 
              sortDirection={sortField === 'issueDescription' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              Issue
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('status')} 
              sortDirection={sortField === 'status' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              Status
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('severity')} 
              sortDirection={sortField === 'severity' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              Severity
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('capaLinked')} 
              sortDirection={sortField === 'capaLinked' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              CAPA Linked
            </TableHead>
            <TableHead 
              sortable 
              onSort={() => handleSort('dateReported')} 
              sortDirection={sortField === 'dateReported' ? sortDirection : null}
              className="font-medium text-xs uppercase tracking-wider"
            >
              Date Reported
            </TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedItems.map((nc) => (
            <TableRow key={nc.id}>
              <TableCell className="text-sm font-medium">{nc.ncNumber}</TableCell>
              <TableCell className="text-sm">{nc.supplierName}</TableCell>
              <TableCell className="text-sm max-w-[200px] truncate">{nc.issueDescription}</TableCell>
              <TableCell><NCStatusBadge status={nc.status} /></TableCell>
              <TableCell><NCSeverityBadge severity={nc.severity} /></TableCell>
              <TableCell>
                {nc.capaLinked ? (
                  <span className="flex items-center text-green-600">
                    <Check className="h-4 w-4 mr-1" />
                    Yes
                  </span>
                ) : (
                  <span className="flex items-center text-red-600">
                    <X className="h-4 w-4 mr-1" />
                    No
                  </span>
                )}
              </TableCell>
              <TableCell className="text-sm">{nc.dateReported}</TableCell>
              <TableCell>
                <Button variant="ghost" size="sm">
                  <FileSearch className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </ResizableTable>
    </div>
  );
};

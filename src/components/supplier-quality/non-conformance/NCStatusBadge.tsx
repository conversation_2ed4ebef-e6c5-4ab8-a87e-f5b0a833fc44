
import React from "react";
import { NCStatus } from "@/types/supplierNonConformance";
import { Badge } from "@/components/ui/badge";

interface NCStatusBadgeProps {
  status: NCStatus;
}

export const NCStatusBadge: React.FC<NCStatusBadgeProps> = ({ status }) => {
  switch (status) {
    case "Open":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100">
          Open
        </Badge>
      );
    case "In Review":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100">
          In Review
        </Badge>
      );
    case "Closed":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100">
          Closed
        </Badge>
      );
    default:
      return <Badge>{status}</Badge>;
  }
};

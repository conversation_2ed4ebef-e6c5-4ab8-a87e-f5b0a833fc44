
import React, { useState } from "react";
import { SupplierScorecard as ISupplierScorecard } from "@/types/supplierScorecard";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ScoreCardBadge } from "./ScoreCardBadge";
import { ScoreBreakdown } from "./ScoreBreakdown";
import { ScoreComments } from "./ScoreComments";
import { Button } from "@/components/ui/button";
import { Send, FileDown } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface SupplierScorecardProps {
  scorecard: ISupplierScorecard;
  onUpdateScorecard: (updated: ISupplierScorecard) => void;
}

export const SupplierScorecard: React.FC<SupplierScorecardProps> = ({ 
  scorecard,
  onUpdateScorecard 
}) => {
  const { toast } = useToast();
  const [localScorecard, setLocalScorecard] = useState<ISupplierScorecard>(scorecard);

  const handleCommentsChange = (comments: string) => {
    const updated = { ...localScorecard, comments };
    setLocalScorecard(updated);
    onUpdateScorecard(updated);
    
    toast({
      title: "Comments Updated",
      description: "Your comments have been saved successfully."
    });
  };

  const handleSendToSupplier = () => {
    const updated = { 
      ...localScorecard, 
      sentToSupplier: true,
      sentDate: new Date().toISOString().split('T')[0]
    };
    setLocalScorecard(updated);
    onUpdateScorecard(updated);
    
    toast({
      title: "Scorecard Sent",
      description: `Scorecard for ${scorecard.supplierName} (${scorecard.period} ${scorecard.year}) has been sent.`
    });
  };

  const handleExportPdf = () => {
    // In a real implementation, this would generate a PDF
    toast({
      title: "Export Started",
      description: "Your PDF is being generated and will download shortly."
    });
    
    // Mock the download after a short delay
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: `${scorecard.supplierName}_${scorecard.period}_${scorecard.year}_Scorecard.pdf has been downloaded.`
      });
    }, 1000);
  };
  
  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>{scorecard.supplierName}</CardTitle>
          <div className="text-sm text-gray-500 mt-1">
            {scorecard.period} {scorecard.year} Performance Scorecard
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-sm font-medium mr-2">Final Score:</div>
          <ScoreCardBadge score={scorecard.finalScore} />
        </div>
      </CardHeader>
      
      <CardContent>
        <ScoreBreakdown scores={scorecard.scores} />
        
        <ScoreComments 
          comments={localScorecard.comments}
          onCommentsChange={handleCommentsChange}
          readOnly={localScorecard.sentToSupplier}
        />
        
        {localScorecard.sentToSupplier ? (
          <div className="mt-6 bg-blue-50 text-blue-700 p-3 rounded-md text-sm">
            This scorecard was sent to the supplier on {localScorecard.sentDate}.
          </div>
        ) : (
          <div className="mt-6 flex flex-wrap gap-3">
            <Button onClick={handleSendToSupplier} className="gap-2">
              <Send className="h-4 w-4" />
              Send to Supplier
            </Button>
            <Button variant="outline" onClick={handleExportPdf} className="gap-2">
              <FileDown className="h-4 w-4" />
              Export as PDF
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

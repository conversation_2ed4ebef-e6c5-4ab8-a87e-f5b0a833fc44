
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { MessageSquare } from "lucide-react";

interface ScoreCommentsProps {
  comments: string;
  onCommentsChange: (comments: string) => void;
  readOnly?: boolean;
}

export const ScoreComments: React.FC<ScoreCommentsProps> = ({
  comments,
  onCommentsChange,
  readOnly = false
}) => {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Comments & Feedback
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea
          value={comments}
          onChange={(e) => onCommentsChange(e.target.value)}
          placeholder="Add comments about supplier performance..."
          className="min-h-[100px]"
          readOnly={readOnly}
        />
        {readOnly && (
          <p className="text-sm text-gray-500 mt-2">
            Comments are read-only as this scorecard has been sent to the supplier.
          </p>
        )}
      </CardContent>
    </Card>
  );
};

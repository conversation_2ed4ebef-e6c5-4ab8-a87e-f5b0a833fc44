
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BarChart3, Eye, Calendar, User } from "lucide-react";
import { SupplierScorecard } from "@/types/supplierScorecard";

interface ScorecardListProps {
  scorecards: SupplierScorecard[];
  onViewScorecard: (scorecard: SupplierScorecard) => void;
}

export const ScorecardList: React.FC<ScorecardListProps> = ({
  scorecards,
  onViewScorecard
}) => {
  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 80) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>;
    if (score >= 70) return <Badge className="bg-yellow-100 text-yellow-800">Fair</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {scorecards.map((scorecard) => (
        <Card key={scorecard.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <span className="text-lg">{scorecard.supplierName}</span>
              <BarChart3 className="h-5 w-5 text-blue-600" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Period</span>
                <span className="font-medium">{scorecard.period} {scorecard.year}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Final Score</span>
                <div className="flex items-center gap-2">
                  <span className="font-bold text-lg">{scorecard.finalScore}%</span>
                  {getScoreBadge(scorecard.finalScore)}
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <Badge variant={scorecard.sentToSupplier ? "default" : "outline"}>
                  {scorecard.sentToSupplier ? "Sent" : "Draft"}
                </Badge>
              </div>
              
              {scorecard.sentDate && (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  <span>Sent: {scorecard.sentDate}</span>
                </div>
              )}
              
              <Button 
                onClick={() => onViewScorecard(scorecard)}
                className="w-full mt-4"
                variant="outline"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

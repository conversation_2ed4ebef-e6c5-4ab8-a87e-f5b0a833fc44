
import React from "react";
import { Badge } from "@/components/ui/badge";

interface ScoreCardBadgeProps {
  score: number;
}

export const ScoreCardBadge: React.FC<ScoreCardBadgeProps> = ({ score }) => {
  const getBadgeVariant = (score: number) => {
    if (score >= 90) return { className: "bg-green-100 text-green-800", label: "Excellent" };
    if (score >= 80) return { className: "bg-blue-100 text-blue-800", label: "Good" };
    if (score >= 70) return { className: "bg-yellow-100 text-yellow-800", label: "Fair" };
    return { className: "bg-red-100 text-red-800", label: "Poor" };
  };

  const { className, label } = getBadgeVariant(score);

  return (
    <div className="flex items-center gap-2">
      <span className="text-2xl font-bold">{score}%</span>
      <Badge className={className}>{label}</Badge>
    </div>
  );
};

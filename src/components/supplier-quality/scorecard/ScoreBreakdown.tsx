
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { ScoreCategory } from "@/types/supplierScorecard";

interface ScoreBreakdownProps {
  scores: ScoreCategory[];
}

export const ScoreBreakdown: React.FC<ScoreBreakdownProps> = ({ scores }) => {
  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Score Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {scores.map((score, index) => (
            <div key={index} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{score.category}</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Weight: {score.weight}%</span>
                  <span className="text-sm font-bold">{score.score}/{score.maxScore}</span>
                </div>
              </div>
              <Progress value={score.score} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

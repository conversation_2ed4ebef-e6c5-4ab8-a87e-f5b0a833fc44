
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Eye, Settings } from 'lucide-react';

interface FormBuilderTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const FormBuilderTabs: React.FC<FormBuilderTabsProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'builder', label: 'Builder', icon: Hammer },
    { id: 'preview', label: 'Preview', icon: Eye },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
      {tabs.map((tab) => {
        const IconComponent = tab.icon;
        return (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? "default" : "ghost"}
            size="sm"
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center gap-2 ${
              activeTab === tab.id 
                ? 'bg-white shadow-sm' 
                : 'hover:bg-gray-50'
            }`}
          >
            <IconComponent className="h-4 w-4" />
            {tab.label}
          </Button>
        );
      })}
    </div>
  );
};


import React, { createContext, useContext, useState, ReactNode } from 'react';
import { FormSection, FormField, FieldType, SupportedLanguage } from '@/types/formBuilder';

interface FormBuilderContextType {
  sections: FormSection[];
  currentLanguage: SupportedLanguage;
  addSection: () => void;
  updateSection: (sectionId: string, updates: Partial<FormSection>) => void;
  deleteSection: (sectionId: string) => void;
  addField: (sectionId: string, type: FieldType) => void;
  updateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  deleteField: (sectionId: string, fieldId: string) => void;
  setCurrentLanguage: (language: SupportedLanguage) => void;
}

const FormBuilderContext = createContext<FormBuilderContextType | null>(null);

export const useFormBuilder = () => {
  const context = useContext(FormBuilderContext);
  if (!context) {
    throw new Error('useFormBuilder must be used within a FormBuilderProvider');
  }
  return context;
};

interface FormBuilderProviderProps {
  children: ReactNode;
}

export const FormBuilderProvider: React.FC<FormBuilderProviderProps> = ({ children }) => {
  const [sections, setSections] = useState<FormSection[]>([
    {
      id: 'section-1',
      title: 'General Information',
      description: 'Basic information section',
      fields: []
    }
  ]);
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>('en');

  const addSection = () => {
    const newSection: FormSection = {
      id: `section-${Date.now()}`,
      title: 'New Section',
      description: '',
      fields: []
    };
    setSections(prev => [...prev, newSection]);
  };

  const updateSection = (sectionId: string, updates: Partial<FormSection>) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId ? { ...section, ...updates } : section
    ));
  };

  const deleteSection = (sectionId: string) => {
    setSections(prev => prev.filter(section => section.id !== sectionId));
  };

  const addField = (sectionId: string, type: FieldType) => {
    const newField: FormField = {
      id: `field-${Date.now()}`,
      type,
      label: `New ${type} field`,
      required: false
    };

    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, fields: [...section.fields, newField] }
        : section
    ));
  };

  const updateField = (sectionId: string, fieldId: string, updates: Partial<FormField>) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            fields: section.fields.map(field => 
              field.id === fieldId ? { ...field, ...updates } : field
            )
          }
        : section
    ));
  };

  const deleteField = (sectionId: string, fieldId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? {
            ...section,
            fields: section.fields.filter(field => field.id !== fieldId)
          }
        : section
    ));
  };

  const value: FormBuilderContextType = {
    sections,
    currentLanguage,
    addSection,
    updateSection,
    deleteSection,
    addField,
    updateField,
    deleteField,
    setCurrentLanguage
  };

  return (
    <FormBuilderContext.Provider value={value}>
      {children}
    </FormBuilderContext.Provider>
  );
};

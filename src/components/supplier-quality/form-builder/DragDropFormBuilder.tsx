
import React, { useState } from 'react';
import { DndContext, DragEndEvent, DragOverEvent, DragStartEvent } from '@dnd-kit/core';
import { FormSection, FormField, FieldType, SupportedLanguage } from '@/types/formBuilder';
import { FormBuilderSidebar } from './FormBuilderSidebar';
import { FormBuilderContent } from './FormBuilderContent';
import { FormBuilderTabs } from './FormBuilderTabs';

interface DragDropFormBuilderProps {
  sections: FormSection[];
  onUpdateSection: (sectionId: string, updates: Partial<FormSection>) => void;
  onAddField: (sectionId: string, type: FieldType) => void;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  onDeleteField: (sectionId: string, fieldId: string) => void;
  onAddSection: () => void;
  onDeleteSection: (sectionId: string) => void;
  currentLanguage: SupportedLanguage;
  supportedLanguages: SupportedLanguage[];
}

export const DragDropFormBuilder: React.FC<DragDropFormBuilderProps> = ({
  sections,
  onUpdateSection,
  onAddField,
  onUpdateField,
  onDeleteField,
  onAddSection,
  onDeleteSection,
  currentLanguage,
  supportedLanguages
}) => {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedField, setSelectedField] = useState<{ sectionId: string; fieldId: string } | null>(null);
  const [dragOverSection, setDragOverSection] = useState<string | null>(null);
  const [activeId, setActiveId] = useState<string | null>(null);

  const handleSelectField = (sectionId: string, fieldId: string) => {
    setSelectedField({ sectionId, fieldId });
  };

  const handleAddField = (sectionId: string, type: FieldType) => {
    // If no sections exist, create one first
    if (sections.length === 0) {
      onAddSection();
      // Use the first section that will be created
      onAddField("section-1", type);
    } else {
      // If sectionId is "default-section", use the first available section
      const targetSectionId = sectionId === "default-section" ? sections[0].id : sectionId;
      onAddField(targetSectionId, type);
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    console.log('🎯 Drag started:', event.active.id);
    setActiveId(event.active.id as string);
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;
    
    if (over) {
      const overId = over.id as string;
      console.log('🎯 Dragging over:', overId);
      
      // Check if we're over a section
      if (sections.some(section => section.id === overId)) {
        setDragOverSection(overId);
      }
    } else {
      setDragOverSection(null);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    console.log('🎯 Drag ended:', event);
    const { active, over } = event;
    
    setActiveId(null);
    setDragOverSection(null);
    
    if (!over) {
      console.log('🎯 No drop target found');
      return;
    }
    
    const activeId = active.id as string;
    const overId = over.id as string;
    
    console.log('🎯 Active ID:', activeId, 'Over ID:', overId);
    
    // Handle dropping field type from palette
    if (activeId.startsWith('palette-')) {
      const fieldType = activeId.replace('palette-', '') as FieldType;
      console.log('🎯 Dropping field type:', fieldType);
      
      // Find the target section
      let targetSectionId = overId;
      
      // If dropped on a section, use that section
      if (sections.some(section => section.id === overId)) {
        targetSectionId = overId;
        console.log('🎯 Dropping on section:', targetSectionId);
      } else {
        // If no sections exist or dropped elsewhere, use first section or create one
        if (sections.length === 0) {
          console.log('🎯 No sections exist, creating new section');
          onAddSection();
          targetSectionId = "section-1";
        } else {
          targetSectionId = sections[0].id;
          console.log('🎯 Using first section:', targetSectionId);
        }
      }
      
      console.log('🎯 Adding field:', fieldType, 'to section:', targetSectionId);
      onAddField(targetSectionId, fieldType);
    }
  };

  return (
    <DndContext 
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="flex h-full bg-gray-50">
        {/* Sidebar */}
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          <div className="p-4 border-b">
            <FormBuilderTabs activeTab={activeTab} onTabChange={setActiveTab} />
          </div>
          <FormBuilderSidebar
            selectedField={selectedField}
            onUpdateField={onUpdateField}
            onAddField={handleAddField}
            currentLanguage={currentLanguage}
            sections={sections}
            supportedLanguages={supportedLanguages}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          <FormBuilderContent
            activeTab={activeTab}
            sections={sections}
            selectedField={selectedField}
            onSelectField={handleSelectField}
            onUpdateSection={onUpdateSection}
            onDeleteSection={onDeleteSection}
            onAddField={handleAddField}
            onUpdateField={onUpdateField}
            onDeleteField={onDeleteField}
            onAddSection={onAddSection}
            currentLanguage={currentLanguage}
            dragOverSection={dragOverSection}
          />
        </div>
      </div>
    </DndContext>
  );
};

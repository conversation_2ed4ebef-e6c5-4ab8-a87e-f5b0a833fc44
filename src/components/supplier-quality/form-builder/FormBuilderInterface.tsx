
import React from "react";
import { Drag<PERSON><PERSON><PERSON><PERSON><PERSON>uilder } from "./DragDropFormBuilder";
import { FormBuilderProvider, useFormBuilder } from "./form-builder-state/FormBuilderContext";
import { SidebarProvider } from "@/components/ui/sidebar";
import { SupportedLanguage } from "@/types/formBuilder";

const FormBuilderContent: React.FC = () => {
  console.log("FormBuilderContent rendering...");
  
  try {
    const context = useFormBuilder();
    console.log("FormBuilder context:", context);
    
    if (!context) {
      console.error("FormBuilder context is null or undefined");
      return (
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Context Error</h2>
          <p className="text-gray-600">FormBuilder context is not available.</p>
        </div>
      );
    }
    
    const {
      sections,
      addField,
      updateField,
      deleteField,
      updateSection,
      deleteSection,
      addSection,
      currentLanguage,
    } = context;
    
    console.log("FormBuilder context loaded:", {
      sectionsCount: sections?.length || 0,
      currentLanguage,
      addField: typeof addField,
      updateField: typeof updateField,
      deleteField: typeof deleteField,
      updateSection: typeof updateSection,
      deleteSection: typeof deleteSection,
      addSection: typeof addSection
    });

    // Define supported languages - in a real app, this would come from context or config
    const supportedLanguages: SupportedLanguage[] = ['en', 'de', 'es-mx', 'fr'];
    
    return (
      <div className="h-full w-full bg-gray-50">
        <DragDropFormBuilder
          sections={sections || []}
          onUpdateSection={updateSection}
          onAddField={addField}
          onUpdateField={updateField}
          onDeleteField={deleteField}
          onAddSection={addSection}
          onDeleteSection={deleteSection}
          currentLanguage={currentLanguage || 'en'}
          supportedLanguages={supportedLanguages}
        />
      </div>
    );
  } catch (error) {
    console.error("Error in FormBuilderContent:", error);
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold text-red-600 mb-2">Form Builder Error</h2>
        <p className="text-gray-600">There was an error loading the form builder.</p>
        <pre className="mt-4 p-4 bg-gray-100 rounded text-sm text-left overflow-auto">
          {error instanceof Error ? error.message : 'Unknown error'}
        </pre>
      </div>
    );
  }
};

export const FormBuilderInterface: React.FC = () => {
  console.log("FormBuilderInterface rendering...");
  
  try {
    return (
      <SidebarProvider>
        <div className="w-full h-full">
          <FormBuilderProvider>
            <FormBuilderContent />
          </FormBuilderProvider>
        </div>
      </SidebarProvider>
    );
  } catch (error) {
    console.error("Error in FormBuilderInterface:", error);
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold text-red-600 mb-2">Form Builder Error</h2>
        <p className="text-gray-600">Failed to initialize form builder.</p>
        <pre className="mt-4 p-4 bg-gray-100 rounded text-sm text-left overflow-auto">
          {error instanceof Error ? error.message : 'Unknown error'}
        </pre>
      </div>
    );
  }
};


import React from "react";
import { FormField, FormSection } from "@/types/formBuilder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ConditionalLogicProps {
  field: FormField;
  sectionId: string;
  fieldId: string;
  sections: FormSection[];
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
}

export const ConditionalLogic: React.FC<ConditionalLogicProps> = ({
  field,
  sectionId,
  fieldId,
  sections,
  onUpdateField
}) => {
  const { toast } = useToast();

  const getFieldIdOptions = () => {
    const options: { id: string; label: string }[] = [];
    sections.forEach((section) => {
      section.fields.forEach((f) => {
        if (f.id !== fieldId) {
          options.push({
            id: f.id,
            label: typeof f.label === 'string' ? f.label : f.label?.en || f.id,
          });
        }
      });
    });
    return options;
  };

  return (
    <div className="space-y-4 p-4 border rounded-md bg-gray-50">
      <Label className="text-sm font-medium">Conditional Display Logic</Label>
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="has-condition"
          checked={!!field.conditionalLogic}
          onChange={(e) => {
            if (e.target.checked) {
              onUpdateField(sectionId, fieldId, {
                conditionalLogic: {
                  dependsOn: '',
                  showWhen: '',
                  conditions: []
                }
              });
            } else {
              onUpdateField(sectionId, fieldId, { conditionalLogic: undefined });
            }
          }}
        />
        <Label htmlFor="has-condition" className="text-sm">Show this field conditionally</Label>
      </div>
      
      {field.conditionalLogic && (
        <div className="space-y-4 mt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">If</span>
            <Select
              value={field.conditionalLogic.dependsOn}
              onValueChange={(value) => {
                onUpdateField(sectionId, fieldId, {
                  conditionalLogic: {
                    ...field.conditionalLogic!,
                    dependsOn: value
                  }
                });
              }}
            >
              <SelectTrigger className="flex-1 h-10">
                <SelectValue placeholder="Select field" />
              </SelectTrigger>
              <SelectContent>
                {getFieldIdOptions().map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {field.conditionalLogic.dependsOn && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">is</span>
              <Select
                value={field.conditionalLogic.showWhen}
                onValueChange={(value) => {
                  onUpdateField(sectionId, fieldId, {
                    conditionalLogic: {
                      ...field.conditionalLogic!,
                      showWhen: value
                    }
                  });
                }}
              >
                <SelectTrigger className="flex-1 h-10">
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          <div className="pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                toast({
                  title: "Advanced Logic",
                  description: "Multiple conditions with AND/OR logic will be available in the next version."
                });
              }}
              className="w-full h-10"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Condition
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

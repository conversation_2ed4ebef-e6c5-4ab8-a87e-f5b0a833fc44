
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { FormField, FormSection, SupportedLanguage } from "@/types/formBuilder";
import { Globe } from "lucide-react";
import { BasicProperties } from "./BasicProperties";
import { TableProperties } from "./TableProperties";
import { OptionProperties } from "./OptionProperties";
import { ConditionalLogic } from "./ConditionalLogic";

interface PropertiesPanelProps {
  field: FormField | null;
  sectionId?: string;
  fieldId?: string;
  sections: FormSection[];
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  currentLanguage: SupportedLanguage;
  supportedLanguages: SupportedLanguage[];
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  field,
  sectionId,
  fieldId,
  sections,
  onUpdateField,
  currentLanguage,
  supportedLanguages
}) => {
  if (!field || !sectionId || !fieldId) {
    return (
      <Card className="h-full shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="text-lg">Properties</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-gray-500 text-center my-10">
            Select a field to edit its properties
          </p>
        </CardContent>
      </Card>
    );
  }

  const languageName = currentLanguage === 'en' ? 'English' : 
                        currentLanguage === 'de' ? 'German' : 
                        currentLanguage === 'es-mx' ? 'Spanish' : 'French';
  const languageFlag = currentLanguage === 'en' ? '🇺🇸' : 
                        currentLanguage === 'de' ? '🇩🇪' : 
                        currentLanguage === 'es-mx' ? '🇲🇽' : '🇫🇷';

  return (
    <Card className="h-full shadow-sm">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle className="text-lg">Properties</CardTitle>
      </CardHeader>
      <CardContent className="p-4 space-y-4 overflow-y-auto" style={{ maxHeight: "calc(100vh - 250px)" }}>
        <div className="flex items-center gap-2 mb-2">
          <Globe className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-blue-700">
            Editing in {languageFlag} {languageName}
          </span>
        </div>
        
        <BasicProperties 
          field={field} 
          sectionId={sectionId} 
          fieldId={fieldId} 
          onUpdateField={onUpdateField}
          currentLanguage={currentLanguage}
        />

        {field.type === 'table' && (
          <TableProperties
            field={field}
            sectionId={sectionId}
            fieldId={fieldId}
            onUpdateField={onUpdateField}
            currentLanguage={currentLanguage}
          />
        )}

        {(field.type === 'select' || field.type === 'radio' || field.type === 'checkbox') && (
          <OptionProperties
            field={field}
            sectionId={sectionId}
            fieldId={fieldId}
            onUpdateField={onUpdateField}
            currentLanguage={currentLanguage}
            supportedLanguages={supportedLanguages}
          />
        )}
        
        <ConditionalLogic
          field={field}
          sectionId={sectionId}
          fieldId={fieldId}
          sections={sections}
          onUpdateField={onUpdateField}
        />
      </CardContent>
    </Card>
  );
};

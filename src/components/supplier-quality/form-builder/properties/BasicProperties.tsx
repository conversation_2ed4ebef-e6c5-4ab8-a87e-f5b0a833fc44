
import React from "react";
import { FormField, SupportedLanguage } from "@/types/formBuilder";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { getTranslatedText, updateTranslation } from "@/utils/translationUtils";

interface BasicPropertiesProps {
  field: FormField;
  sectionId: string;
  fieldId: string;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  currentLanguage: SupportedLanguage;
}

export const BasicProperties: React.FC<BasicPropertiesProps> = ({
  field,
  sectionId,
  fieldId,
  onUpdateField,
  currentLanguage
}) => {
  const translatedLabel = getTranslatedText(field.label, currentLanguage);
  const translatedDescription = getTranslatedText(field.description, currentLanguage);
  const translatedPlaceholder = getTranslatedText(field.placeholder, currentLanguage);
  const translatedHelpText = getTranslatedText(field.helpText, currentLanguage);

  const handleLabelChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      label: updateTranslation(field.label, currentLanguage, value)
    });
  };

  const handleDescriptionChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      description: updateTranslation(field.description, currentLanguage, value)
    });
  };

  const handlePlaceholderChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      placeholder: updateTranslation(field.placeholder, currentLanguage, value)
    });
  };

  const handleHelpTextChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      helpText: updateTranslation(field.helpText, currentLanguage, value)
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <Label htmlFor="label" className="text-sm font-medium">Field Label</Label>
        <Input
          id="label"
          value={translatedLabel}
          onChange={(e) => handleLabelChange(e.target.value)}
          placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter field label"}
          className="h-10"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="field-type" className="text-sm font-medium">Field Type</Label>
        <Input id="field-type" value={field.type} disabled className="h-10 bg-gray-50" />
      </div>
      
      <div className="flex items-center space-x-3">
        <Switch
          id="required"
          checked={field.required}
          onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { required: checked })}
        />
        <Label htmlFor="required" className="text-sm font-medium">Required Field</Label>
      </div>

      {(field.type === "text" || field.type === "textarea" || field.type === "number") && (
        <div className="space-y-3">
          <Label htmlFor="placeholder" className="text-sm font-medium">Placeholder Text</Label>
          <Input
            id="placeholder"
            value={translatedPlaceholder || ""}
            onChange={(e) => handlePlaceholderChange(e.target.value)}
            placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter placeholder text"}
            className="h-10"
          />
        </div>
      )}

      <div className="space-y-3">
        <Label htmlFor="description" className="text-sm font-medium">Description</Label>
        <Textarea
          id="description"
          value={translatedDescription || ""}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter field description"}
          className="min-h-[80px] resize-none"
          rows={3}
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="helpText" className="text-sm font-medium">Help Text / Tooltip</Label>
        <Textarea
          id="helpText"
          value={translatedHelpText || ""}
          onChange={(e) => handleHelpTextChange(e.target.value)}
          placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter help text for tooltips"}
          className="min-h-[60px] resize-none"
          rows={2}
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="scoreWeight" className="text-sm font-medium">Score Weight</Label>
        <Input
          id="scoreWeight"
          type="number"
          min="0"
          value={field.scoreWeight || ""}
          onChange={(e) => onUpdateField(sectionId, fieldId, { scoreWeight: Number(e.target.value) })}
          placeholder="Enter weight (e.g. 5)"
          className="h-10"
        />
        <p className="text-xs text-gray-500">Used for calculating form scores in evaluations</p>
      </div>

      <div className="flex items-center space-x-3">
        <Switch
          id="allowAttachments"
          checked={field.allowAttachments || false}
          onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { allowAttachments: checked })}
        />
        <Label htmlFor="allowAttachments" className="text-sm font-medium">Allow Attachments</Label>
      </div>

      <div className="flex items-center space-x-3">
        <Switch
          id="reviewerNotes"
          checked={field.reviewerNotes || false}
          onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { reviewerNotes: checked })}
        />
        <Label htmlFor="reviewerNotes" className="text-sm font-medium">Enable Reviewer Notes</Label>
      </div>
    </div>
  );
};

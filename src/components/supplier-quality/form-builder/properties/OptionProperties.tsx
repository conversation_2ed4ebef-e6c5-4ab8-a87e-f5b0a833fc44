
import React from "react";
import { FormField, SupportedLanguage, TranslatedArray } from "@/types/formBuilder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Trash } from "lucide-react";
import { getTranslatedText, updateTranslation, SUPPORTED_LANGUAGES } from "@/utils/translationUtils";

interface OptionPropertiesProps {
  field: FormField;
  sectionId: string;
  fieldId: string;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  currentLanguage: SupportedLanguage;
  supportedLanguages: SupportedLanguage[];
}

export const OptionProperties: React.FC<OptionPropertiesProps> = ({
  field,
  sectionId,
  fieldId,
  onUpdateField,
  currentLanguage,
  supportedLanguages
}) => {
  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">Options</Label>
      <Tabs defaultValue="options">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="options">Options</TabsTrigger>
          <TabsTrigger value="translations">Translations</TabsTrigger>
        </TabsList>
        <TabsContent value="options" className="space-y-3 mt-4">
          {(field.options || [] as TranslatedArray).map((option, index) => {
            const optionText = typeof option === 'string' ? option : getTranslatedText(option, currentLanguage);
            return (
              <div key={index} className="flex items-center gap-2">
                <Input
                  value={optionText}
                  onChange={(e) => {
                    const newOptions = [...(field.options || [] as TranslatedArray)];
                    const updatedOption = updateTranslation(
                      typeof option === 'string' ? option : option,
                      currentLanguage,
                      e.target.value
                    );
                    newOptions[index] = updatedOption;
                    onUpdateField(sectionId, fieldId, { options: newOptions });
                  }}
                  placeholder={`Option ${index + 1}`}
                  className="h-10"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(field.options || [] as TranslatedArray)];
                    newOptions.splice(index, 1);
                    onUpdateField(sectionId, fieldId, { options: newOptions });
                  }}
                  className="h-10 w-10 p-0"
                  disabled={(field.options || []).length <= 1}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const newOptions = [...(field.options || [] as TranslatedArray), `Option ${(field.options || []).length + 1}`];
              onUpdateField(sectionId, fieldId, { options: newOptions });
            }}
            className="w-full mt-3 h-10"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Option
          </Button>
        </TabsContent>
        <TabsContent value="translations" className="space-y-4">
          {supportedLanguages.filter(lang => lang !== 'en').map((lang) => (
            <div key={lang} className="border-t pt-4">
              <div className="flex items-center mb-3">
                <span className="mr-2">{SUPPORTED_LANGUAGES[lang].flag}</span>
                <span className="font-medium">{SUPPORTED_LANGUAGES[lang].name}</span>
              </div>
              <div className="space-y-3">
                {(field.options || [] as TranslatedArray).map((option, index) => {
                  const englishOption = typeof option === 'string' ? option : option.en;
                  const translatedOption = typeof option === 'string' ? '' : getTranslatedText(option, lang);
                  
                  return (
                    <div key={`${lang}-${index}`} className="flex items-center gap-2">
                      <div className="text-xs text-gray-500 w-1/3 truncate">"{englishOption}"</div>
                      <Input
                        value={translatedOption}
                        onChange={(e) => {
                          const newOptions = [...(field.options || [] as TranslatedArray)];
                          const updatedOption = updateTranslation(newOptions[index], lang, e.target.value);
                          newOptions[index] = updatedOption;
                          onUpdateField(sectionId, fieldId, { options: newOptions });
                        }}
                        placeholder={`Translate "${englishOption}"`}
                        className="w-2/3 h-10"
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React from "react";
import { FormField, SupportedLanguage, TranslatedArray } from "@/types/formBuilder";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Plus, Trash } from "lucide-react";
import { getTranslatedText, updateTranslation } from "@/utils/translationUtils";

interface TablePropertiesProps {
  field: FormField;
  sectionId: string;
  fieldId: string;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  currentLanguage: SupportedLanguage;
}

export const TableProperties: React.FC<TablePropertiesProps> = ({
  field,
  sectionId,
  fieldId,
  onUpdateField,
  currentLanguage
}) => {
  return (
    <div className="space-y-6 border p-4 rounded-md">
      <h3 className="font-medium">Table Configuration</h3>
      
      <div className="space-y-4">
        <Label className="text-sm font-medium">Columns</Label>
        <div className="space-y-3">
          {(field.columns || ['Column 1', 'Column 2'] as TranslatedArray).map((column, index) => {
            const columnText = typeof column === 'string' ? column : getTranslatedText(column, currentLanguage);
            return (
              <div key={index} className="flex items-center gap-2">
                <Input 
                  value={columnText}
                  onChange={(e) => {
                    const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray)];
                    const updatedColumn = updateTranslation(
                      typeof column === 'string' ? column : column,
                      currentLanguage,
                      e.target.value
                    );
                    newColumns[index] = updatedColumn;
                    onUpdateField(sectionId, fieldId, { columns: newColumns });
                  }}
                  placeholder={`Column ${index + 1}`}
                  className="h-10"
                />
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray)];
                    newColumns.splice(index, 1);
                    onUpdateField(sectionId, fieldId, { columns: newColumns });
                  }}
                  className="h-10 w-10 p-0"
                  disabled={(field.columns || []).length <= 1}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray), `Column ${(field.columns || []).length + 1}`];
              onUpdateField(sectionId, fieldId, { columns: newColumns });
            }}
            className="w-full mt-3 h-10"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Column
          </Button>
        </div>
      </div>
      
      <div className="space-y-4">
        <Label className="text-sm font-medium">Rows</Label>
        <div className="space-y-3">
          {(field.rows || ['Row 1', 'Row 2'] as TranslatedArray).map((row, index) => {
            const rowText = typeof row === 'string' ? row : getTranslatedText(row, currentLanguage);
            return (
              <div key={index} className="flex items-center gap-2">
                <Input 
                  value={rowText}
                  onChange={(e) => {
                    const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray)];
                    const updatedRow = updateTranslation(
                      typeof row === 'string' ? row : row,
                      currentLanguage,
                      e.target.value
                    );
                    newRows[index] = updatedRow;
                    onUpdateField(sectionId, fieldId, { rows: newRows });
                  }}
                  placeholder={`Row ${index + 1}`}
                  className="h-10"
                />
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray)];
                    newRows.splice(index, 1);
                    onUpdateField(sectionId, fieldId, { rows: newRows });
                  }}
                  className="h-10 w-10 p-0"
                  disabled={(field.rows || []).length <= 1}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray), `Row ${(field.rows || []).length + 1}`];
              onUpdateField(sectionId, fieldId, { rows: newRows });
            }}
            className="w-full mt-3 h-10"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Row
          </Button>
        </div>
      </div>
    </div>
  );
};

import React from 'react';
import { FormSection, FormField, FieldType, SupportedLanguage } from '@/types/formBuilder';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Trash2, Edit, Plus, GripVertical } from 'lucide-react';
import { useDroppable } from '@dnd-kit/core';
import { getTranslatedText, updateTranslation } from '@/utils/translationUtils';

interface FormSectionComponentProps {
  section: FormSection;
  selectedField: { sectionId: string; fieldId: string } | null;
  onSelectField: (sectionId: string, fieldId: string) => void;
  onUpdateSection: (sectionId: string, updates: Partial<FormSection>) => void;
  onDeleteSection: (sectionId: string) => void;
  onAddField: (sectionId: string, type: FieldType) => void;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  onDeleteField: (sectionId: string, fieldId: string) => void;
  currentLanguage: SupportedLanguage;
  isDragOver?: boolean;
}

export const FormSectionComponent: React.FC<FormSectionComponentProps> = ({
  section,
  selectedField,
  onSelectField,
  onUpdateSection,
  onDeleteSection,
  onAddField,
  onUpdateField,
  onDeleteField,
  currentLanguage,
  isDragOver = false
}) => {
  const { setNodeRef } = useDroppable({
    id: `section-${section.id}`
  });

  const handleSectionTitleChange = (title: string) => {
    const updatedTitle = updateTranslation(section.title, currentLanguage, title);
    onUpdateSection(section.id, {
      title: updatedTitle
    });
  };

  const renderField = (field: FormField) => {
    const isSelected = selectedField?.sectionId === section.id && selectedField?.fieldId === field.id;
    
    return (
      <div
        key={field.id}
        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
          isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={() => onSelectField(section.id, field.id)}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <GripVertical className="h-4 w-4 text-gray-400" />
            <span className="font-medium">{getTranslatedText(field.label, currentLanguage)}</span>
            <Badge variant="outline" className="text-xs">
              {field.type}
            </Badge>
          </div>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onSelectField(section.id, field.id);
              }}
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteField(section.id, field.id);
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        {/* Field Preview */}
        <div className="text-sm text-gray-600">
          {getTranslatedText(field.placeholder, currentLanguage) || 'No placeholder'}
        </div>
        
        {field.required && (
          <Badge variant="secondary" className="mt-1 text-xs">
            Required
          </Badge>
        )}
      </div>
    );
  };

  const handleDescriptionChange = (description: string) => {
    const updatedDescription = updateTranslation(section.description, currentLanguage, description);
    onUpdateSection(section.id, {
      description: updatedDescription
    });
  };

  return (
    <Card 
      ref={setNodeRef}
      className={`transition-colors ${isDragOver ? 'border-blue-500 bg-blue-50' : ''}`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <Input
              value={getTranslatedText(section.title, currentLanguage)}
              onChange={(e) => handleSectionTitleChange(e.target.value)}
              placeholder="Section title"
              className="text-lg font-semibold border-none p-0 h-auto bg-transparent"
            />
          </div>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onDeleteSection(section.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
        
        {section.description && (
          <Textarea
            value={getTranslatedText(section.description, currentLanguage)}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            placeholder="Section description"
            className="text-sm border-none p-0 bg-transparent resize-none"
            rows={2}
          />
        )}
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Fields */}
        {section.fields.map(renderField)}
        
        {/* Add Field Buttons */}
        <div className="flex flex-wrap gap-2 pt-3 border-t">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAddField(section.id, 'text')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Text
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAddField(section.id, 'textarea')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Textarea
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAddField(section.id, 'select')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Select
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAddField(section.id, 'radio')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Radio
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => onAddField(section.id, 'checkbox')}
          >
            <Plus className="h-3 w-3 mr-1" />
            Checkbox
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FormField, FormSection, SupportedLanguage, TranslatedArray } from "@/types/formBuilder";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SUPPORTED_LANGUAGES, getTranslatedText, updateTranslation } from "@/utils/translationUtils";
import { Button } from "@/components/ui/button";
import { Globe, Plus, Trash } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

interface PropertiesPanelProps {
  field: FormField | null;
  sectionId?: string;
  fieldId?: string;
  sections: FormSection[];
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  currentLanguage: SupportedLanguage;
  supportedLanguages: SupportedLanguage[];
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  field,
  sectionId,
  fieldId,
  sections,
  onUpdateField,
  currentLanguage,
  supportedLanguages
}) => {
  const { toast } = useToast();
  
  if (!field || !sectionId || !fieldId) {
    return (
      <Card className="h-full shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="text-lg">Properties</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <p className="text-gray-500 text-center my-10">
            Select a field to edit its properties
          </p>
        </CardContent>
      </Card>
    );
  }

  const translatedLabel = getTranslatedText(field.label, currentLanguage);
  const translatedDescription = getTranslatedText(field.description, currentLanguage);
  const translatedPlaceholder = getTranslatedText(field.placeholder, currentLanguage);
  const translatedHelpText = getTranslatedText(field.helpText, currentLanguage);

  const handleLabelChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      label: updateTranslation(field.label, currentLanguage, value)
    });
  };

  const handleDescriptionChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      description: updateTranslation(field.description, currentLanguage, value)
    });
  };

  const handlePlaceholderChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      placeholder: updateTranslation(field.placeholder, currentLanguage, value)
    });
  };

  const handleHelpTextChange = (value: string) => {
    onUpdateField(sectionId, fieldId, {
      helpText: updateTranslation(field.helpText, currentLanguage, value)
    });
  };

  const getFieldIdOptions = () => {
    const options: { id: string; label: string }[] = [];
    sections.forEach((section) => {
      section.fields.forEach((f) => {
        if (f.id !== fieldId) {
          options.push({
            id: f.id,
            label: typeof f.label === 'string' ? f.label : f.label?.en || f.id,
          });
        }
      });
    });
    return options;
  };
  
  return (
    <Card className="h-full shadow-sm">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle className="text-lg">Properties</CardTitle>
      </CardHeader>
      <CardContent className="p-4 space-y-4 overflow-y-auto" style={{ maxHeight: "calc(100vh - 250px)" }}>
        <div className="flex items-center gap-2 mb-2">
          <Globe className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-blue-700">
            Editing in {SUPPORTED_LANGUAGES[currentLanguage].name}
          </span>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="label">Field Label</Label>
          <Input
            id="label"
            value={translatedLabel}
            onChange={(e) => handleLabelChange(e.target.value)}
            placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter field label"}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="field-type">Field Type</Label>
          <Input id="field-type" value={field.type} disabled />
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch
            id="required"
            checked={field.required}
            onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { required: checked })}
          />
          <Label htmlFor="required">Required</Label>
        </div>

        {(field.type === "text" || field.type === "textarea" || field.type === "number") && (
          <div className="space-y-2">
            <Label htmlFor="placeholder">Placeholder</Label>
            <Input
              id="placeholder"
              value={translatedPlaceholder || ""}
              onChange={(e) => handlePlaceholderChange(e.target.value)}
              placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter placeholder text"}
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={translatedDescription || ""}
            onChange={(e) => handleDescriptionChange(e.target.value)}
            placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter field description"}
            className="resize-none"
            rows={3}
          />
        </div>

        {/* Help Text Field */}
        <div className="space-y-2">
          <Label htmlFor="helpText">Help Text / Tooltip</Label>
          <Textarea
            id="helpText"
            value={translatedHelpText || ""}
            onChange={(e) => handleHelpTextChange(e.target.value)}
            placeholder={currentLanguage !== 'en' ? "Enter translation..." : "Enter help text for tooltips"}
            className="resize-none"
            rows={2}
          />
        </div>

        {/* Score Weight for Graded Questions */}
        <div className="space-y-2">
          <Label htmlFor="scoreWeight">Score Weight</Label>
          <Input
            id="scoreWeight"
            type="number"
            min="0"
            value={field.scoreWeight || ""}
            onChange={(e) => onUpdateField(sectionId, fieldId, { scoreWeight: Number(e.target.value) })}
            placeholder="Enter weight (e.g. 5)"
          />
          <p className="text-xs text-gray-500">Used for calculating form scores in evaluations</p>
        </div>

        {/* Attachments Allowed Toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="allowAttachments"
            checked={field.allowAttachments || false}
            onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { allowAttachments: checked })}
          />
          <Label htmlFor="allowAttachments">Allow Attachments</Label>
        </div>

        {/* Reviewer Notes Toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="reviewerNotes"
            checked={field.reviewerNotes || false}
            onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { reviewerNotes: checked })}
          />
          <Label htmlFor="reviewerNotes">Enable Reviewer Notes</Label>
        </div>

        {/* Signature Requirements */}
        {field.type === "signature" && (
          <div className="flex items-center space-x-2">
            <Switch
              id="requireSignature"
              checked={field.requireSignature || false}
              onCheckedChange={(checked) => onUpdateField(sectionId, fieldId, { requireSignature: checked })}
            />
            <Label htmlFor="requireSignature">Require Supplier Signature</Label>
          </div>
        )}

        {(field.type === 'table') && (
          <div className="space-y-4 border p-3 rounded-md">
            <h3 className="font-medium">Table Configuration</h3>
            
            <div className="space-y-2">
              <Label>Columns</Label>
              <div className="space-y-2">
                {(field.columns || ['Column 1', 'Column 2'] as TranslatedArray).map((column, index) => {
                  const columnText = typeof column === 'string' ? column : getTranslatedText(column, currentLanguage);
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <Input 
                        value={columnText}
                        onChange={(e) => {
                          const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray)];
                          const updatedColumn = updateTranslation(
                            typeof column === 'string' ? column : column,
                            currentLanguage,
                            e.target.value
                          );
                          newColumns[index] = updatedColumn;
                          onUpdateField(sectionId, fieldId, { columns: newColumns });
                        }}
                        placeholder={`Column ${index + 1}`}
                      />
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray)];
                          newColumns.splice(index, 1);
                          onUpdateField(sectionId, fieldId, { columns: newColumns });
                        }}
                        className="h-8 w-8 p-0"
                        type="button"
                        disabled={(field.columns || []).length <= 1}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newColumns = [...(field.columns || ['Column 1', 'Column 2'] as TranslatedArray), `Column ${(field.columns || []).length + 1}`];
                    onUpdateField(sectionId, fieldId, { columns: newColumns });
                  }}
                  className="w-full mt-2"
                  type="button"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Column
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Rows</Label>
              <div className="space-y-2">
                {(field.rows || ['Row 1', 'Row 2'] as TranslatedArray).map((row, index) => {
                  const rowText = typeof row === 'string' ? row : getTranslatedText(row, currentLanguage);
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <Input 
                        value={rowText}
                        onChange={(e) => {
                          const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray)];
                          const updatedRow = updateTranslation(
                            typeof row === 'string' ? row : row,
                            currentLanguage,
                            e.target.value
                          );
                          newRows[index] = updatedRow;
                          onUpdateField(sectionId, fieldId, { rows: newRows });
                        }}
                        placeholder={`Row ${index + 1}`}
                      />
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray)];
                          newRows.splice(index, 1);
                          onUpdateField(sectionId, fieldId, { rows: newRows });
                        }}
                        className="h-8 w-8 p-0"
                        type="button"
                        disabled={(field.rows || []).length <= 1}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newRows = [...(field.rows || ['Row 1', 'Row 2'] as TranslatedArray), `Row ${(field.rows || []).length + 1}`];
                    onUpdateField(sectionId, fieldId, { rows: newRows });
                  }}
                  className="w-full mt-2"
                  type="button"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Row
                </Button>
              </div>
            </div>
          </div>
        )}

        {(field.type === 'select' || field.type === 'radio' || field.type === 'checkbox') && (
          <div className="space-y-2">
            <Label>Options</Label>
            {/* Show options for each supported language */}
            <Tabs defaultValue="options">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="options">Options</TabsTrigger>
                <TabsTrigger value="translations">Translations</TabsTrigger>
              </TabsList>
              <TabsContent value="options" className="space-y-2 mt-2">
                {(field.options || [] as TranslatedArray).map((option, index) => {
                  const optionText = typeof option === 'string' ? option : getTranslatedText(option, currentLanguage);
                  return (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={optionText}
                        onChange={(e) => {
                          const newOptions = [...(field.options || [] as TranslatedArray)];
                          const updatedOption = updateTranslation(
                            typeof option === 'string' ? option : option,
                            currentLanguage,
                            e.target.value
                          );
                          newOptions[index] = updatedOption;
                          onUpdateField(sectionId, fieldId, { options: newOptions });
                        }}
                        placeholder={`Option ${index + 1}`}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const newOptions = [...(field.options || [] as TranslatedArray)];
                          newOptions.splice(index, 1);
                          onUpdateField(sectionId, fieldId, { options: newOptions });
                        }}
                        className="h-8 w-8 p-0"
                        disabled={(field.options || []).length <= 1}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(field.options || [] as TranslatedArray), `Option ${(field.options || []).length + 1}`];
                    onUpdateField(sectionId, fieldId, { options: newOptions });
                  }}
                  className="w-full mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </Button>
              </TabsContent>
              <TabsContent value="translations" className="space-y-4">
                {supportedLanguages.filter(lang => lang !== 'en').map((lang) => (
                  <div key={lang} className="border-t pt-2">
                    <div className="flex items-center mb-2">
                      <span className="mr-2">{SUPPORTED_LANGUAGES[lang].flag}</span>
                      <span className="font-medium">{SUPPORTED_LANGUAGES[lang].name}</span>
                    </div>
                    <div className="space-y-2">
                      {(field.options || [] as TranslatedArray).map((option, index) => {
                        const englishOption = typeof option === 'string' ? option : option.en;
                        const translatedOption = typeof option === 'string' ? '' : getTranslatedText(option, lang);
                        
                        return (
                          <div key={`${lang}-${index}`} className="flex items-center gap-2">
                            <div className="text-xs text-gray-500 w-1/3 truncate">"{englishOption}"</div>
                            <Input
                              value={translatedOption}
                              onChange={(e) => {
                                const newOptions = [...(field.options || [] as TranslatedArray)];
                                const updatedOption = updateTranslation(newOptions[index], lang, e.target.value);
                                newOptions[index] = updatedOption;
                                onUpdateField(sectionId, fieldId, { options: newOptions });
                              }}
                              placeholder={`Translate "${englishOption}"`}
                              className="w-2/3"
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </TabsContent>
            </Tabs>
          </div>
        )}
        
        {/* Conditional Logic */}
        <div className="space-y-2 p-3 border rounded-md bg-gray-50">
          <Label>Conditional Display Logic</Label>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="has-condition"
              checked={!!field.conditionalLogic}
              onChange={(e) => {
                if (e.target.checked) {
                  onUpdateField(sectionId, fieldId, {
                    conditionalLogic: {
                      dependsOn: '',
                      showWhen: '',
                      conditions: []
                    }
                  });
                } else {
                  onUpdateField(sectionId, fieldId, { conditionalLogic: undefined });
                }
              }}
            />
            <Label htmlFor="has-condition">Show this field conditionally</Label>
          </div>
          
          {field.conditionalLogic && (
            <div className="space-y-3 mt-2">
              <div className="flex items-center">
                <span className="text-sm font-medium mr-2">If</span>
                <Select
                  value={field.conditionalLogic.dependsOn}
                  onValueChange={(value) => {
                    onUpdateField(sectionId, fieldId, {
                      conditionalLogic: {
                        ...field.conditionalLogic!,
                        dependsOn: value
                      }
                    });
                  }}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select field" />
                  </SelectTrigger>
                  <SelectContent>
                    {getFieldIdOptions().map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {field.conditionalLogic.dependsOn && (
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">is</span>
                  <Select
                    value={field.conditionalLogic.showWhen}
                    onValueChange={(value) => {
                      onUpdateField(sectionId, fieldId, {
                        conditionalLogic: {
                          ...field.conditionalLogic!,
                          showWhen: value
                        }
                      });
                    }}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Select condition" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Add additional condition (in a real app, this would be more complex)
                    toast({
                      title: "Advanced Logic",
                      description: "Multiple conditions with AND/OR logic will be available in the next version."
                    });
                  }}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Condition
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

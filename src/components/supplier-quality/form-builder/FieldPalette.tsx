
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { FieldType } from "@/types/formBuilder";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  AlignLeft, 
  List, 
  CheckSquare, 
  FileText, 
  Calendar, 
  Star, 
  CheckCheck,
  Plus,
  ToggleLeft,
  Heading,
  Table,
  PenTool,
  DollarSign,
  Link as LinkIcon,
  Image,
  Mail,
  Phone,
  MapPin,
  Clock,
  CalendarDays,
  Sliders,
  Grid3X3,
  Navigation,
  QrCode,
  Calculator,
  GitBranch,
  Copy
} from "lucide-react";

interface FieldPaletteProps {
  onAddField: (type: FieldType) => void;
}

export const FieldPalette: React.FC<FieldPaletteProps> = ({ onAddField }) => {
  const basicFields = [
    { type: "text" as FieldType, label: "Text Input", icon: <AlignLeft className="h-4 w-4 mr-2" /> },
    { type: "textarea" as FieldType, label: "Multi-line Text", icon: <AlignLeft className="h-4 w-4 mr-2" /> },
    { type: "email" as FieldType, label: "Email", icon: <Mail className="h-4 w-4 mr-2" /> },
    { type: "phone" as FieldType, label: "Phone Number", icon: <Phone className="h-4 w-4 mr-2" /> },
    { type: "number" as FieldType, label: "Number", icon: <AlignLeft className="h-4 w-4 mr-2" /> },
    { type: "currency" as FieldType, label: "Currency", icon: <DollarSign className="h-4 w-4 mr-2" /> },
  ];

  const selectionFields = [
    { type: "select" as FieldType, label: "Dropdown", icon: <List className="h-4 w-4 mr-2" /> },
    { type: "multiselect" as FieldType, label: "Multi-Select", icon: <List className="h-4 w-4 mr-2" /> },
    { type: "checkbox" as FieldType, label: "Checkbox Group", icon: <CheckSquare className="h-4 w-4 mr-2" /> },
    { type: "radio" as FieldType, label: "Radio Group", icon: <CheckCheck className="h-4 w-4 mr-2" /> },
    { type: "yesno" as FieldType, label: "Yes/No", icon: <ToggleLeft className="h-4 w-4 mr-2" /> },
    { type: "rating" as FieldType, label: "Rating Scale", icon: <Star className="h-4 w-4 mr-2" /> },
    { type: "slider" as FieldType, label: "Slider", icon: <Sliders className="h-4 w-4 mr-2" /> },
  ];

  const dateTimeFields = [
    { type: "date" as FieldType, label: "Date", icon: <Calendar className="h-4 w-4 mr-2" /> },
    { type: "time" as FieldType, label: "Time", icon: <Clock className="h-4 w-4 mr-2" /> },
    { type: "datetime" as FieldType, label: "Date & Time", icon: <CalendarDays className="h-4 w-4 mr-2" /> },
  ];

  const fileMediaFields = [
    { type: "file" as FieldType, label: "File Upload", icon: <FileText className="h-4 w-4 mr-2" /> },
    { type: "image" as FieldType, label: "Image Upload", icon: <Image className="h-4 w-4 mr-2" /> },
    { type: "signature" as FieldType, label: "Digital Signature", icon: <PenTool className="h-4 w-4 mr-2" /> },
  ];

  const advancedFields = [
    { type: "address" as FieldType, label: "Address", icon: <MapPin className="h-4 w-4 mr-2" /> },
    { type: "location" as FieldType, label: "GPS Location", icon: <Navigation className="h-4 w-4 mr-2" /> },
    { type: "table" as FieldType, label: "Table Input", icon: <Table className="h-4 w-4 mr-2" /> },
    { type: "matrix" as FieldType, label: "Matrix/Grid", icon: <Grid3X3 className="h-4 w-4 mr-2" /> },
    { type: "repeater" as FieldType, label: "Repeater Section", icon: <Copy className="h-4 w-4 mr-2" /> },
  ];

  const enterpriseFields = [
    { type: "barcode" as FieldType, label: "Barcode/QR Scanner", icon: <QrCode className="h-4 w-4 mr-2" /> },
    { type: "calculation" as FieldType, label: "Calculation Field", icon: <Calculator className="h-4 w-4 mr-2" /> },
    { type: "conditional" as FieldType, label: "Conditional Logic", icon: <GitBranch className="h-4 w-4 mr-2" /> },
    { type: "link" as FieldType, label: "External Link", icon: <LinkIcon className="h-4 w-4 mr-2" /> },
  ];

  const layoutFields = [
    { type: "sectionHeader" as FieldType, label: "Section Header", icon: <Heading className="h-4 w-4 mr-2" /> },
  ];

  const renderFieldSection = (title: string, fields: typeof basicFields) => (
    <div className="mb-4">
      <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-2">{title}</h4>
      <div className="space-y-1">
        {fields.map((field) => (
          <div 
            key={field.type}
            className="flex items-center p-2 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors text-sm"
            onClick={() => onAddField(field.type)}
            draggable
          >
            <div className="bg-blue-50 p-1.5 rounded mr-2">
              {field.icon}
            </div>
            <span className="font-medium">{field.label}</span>
            <Plus className="h-3 w-3 ml-auto text-gray-400" />
          </div>
        ))}
      </div>
    </div>
  );
  
  return (
    <Card className="h-full shadow-sm">
      <CardHeader className="bg-gray-50 border-b pb-3">
        <CardTitle className="text-lg">Form Elements</CardTitle>
        <p className="text-xs text-gray-500">Drag or click to add fields</p>
      </CardHeader>
      <CardContent className="p-3 overflow-y-auto" style={{ maxHeight: "calc(100vh - 180px)" }}>
        {renderFieldSection("Basic Fields", basicFields)}
        {renderFieldSection("Selection & Rating", selectionFields)}
        {renderFieldSection("Date & Time", dateTimeFields)}
        {renderFieldSection("File & Media", fileMediaFields)}
        {renderFieldSection("Advanced Fields", advancedFields)}
        {renderFieldSection("Enterprise Features", enterpriseFields)}
        {renderFieldSection("Layout", layoutFields)}
      </CardContent>
    </Card>
  );
};

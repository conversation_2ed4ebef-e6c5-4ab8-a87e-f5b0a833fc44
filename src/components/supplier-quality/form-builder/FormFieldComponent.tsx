
import React from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { GripVertical, Trash, Settings } from "lucide-react";
import { FormField, SupportedLanguage } from "@/types/formBuilder";

interface FormFieldComponentProps {
  field: FormField;
  sectionId: string;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<FormField>) => void;
  onDelete: () => void;
  currentLanguage: SupportedLanguage;
}

export const FormFieldComponent: React.FC<FormFieldComponentProps> = ({
  field,
  sectionId,
  isSelected,
  onSelect,
  onUpdate,
  onDelete,
  currentLanguage
}) => {
  const getFieldLabel = () => {
    if (typeof field.label === 'string') return field.label;
    return field.label?.[currentLanguage] || field.label?.en || '';
  };

  const getFieldPlaceholder = () => {
    if (typeof field.placeholder === 'string') return field.placeholder;
    return field.placeholder?.[currentLanguage] || field.placeholder?.en || '';
  };

  const renderFieldPreview = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            placeholder={getFieldPlaceholder() || "Enter text..."}
            disabled
            className="pointer-events-none"
          />
        );
      
      case 'textarea':
        return (
          <Textarea
            placeholder={getFieldPlaceholder() || "Enter description..."}
            disabled
            className="pointer-events-none"
          />
        );
      
      case 'select':
        return (
          <Select disabled>
            <SelectTrigger className="pointer-events-none">
              <SelectValue placeholder="Select an option..." />
            </SelectTrigger>
          </Select>
        );
      
      case 'checkbox':
        return (
          <div className="space-y-2">
            {field.options?.slice(0, 3).map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox disabled />
                <Label className="text-sm">{typeof option === 'string' ? option : option.en}</Label>
              </div>
            ))}
          </div>
        );
      
      case 'radio':
        return (
          <RadioGroup disabled>
            {field.options?.slice(0, 3).map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={index.toString()} disabled />
                <Label className="text-sm">{typeof option === 'string' ? option : option.en}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      
      case 'yesno':
        return (
          <RadioGroup disabled>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" disabled />
              <Label className="text-sm">Yes</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" disabled />
              <Label className="text-sm">No</Label>
            </div>
          </RadioGroup>
        );
      
      case 'file':
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-500">Click to upload or drag and drop</p>
          </div>
        );
      
      default:
        return (
          <div className="p-4 bg-gray-50 rounded border text-center text-gray-500">
            {field.type} field preview
          </div>
        );
    }
  };

  return (
    <Card 
      className={`p-4 cursor-pointer transition-colors ${
        isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2 flex-1">
          <GripVertical className="h-4 w-4 text-gray-400" />
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h4 className="font-medium text-sm">{getFieldLabel() || `${field.type} field`}</h4>
              {field.required && (
                <span className="text-red-500 text-xs">*</span>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">Type: {field.type}</p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onSelect();
            }}
            className="h-8 w-8 p-0"
          >
            <Settings className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
          >
            <Trash className="h-3 w-3" />
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <Label className="text-sm font-medium">{getFieldLabel()}</Label>
        {renderFieldPreview()}
      </div>
    </Card>
  );
};

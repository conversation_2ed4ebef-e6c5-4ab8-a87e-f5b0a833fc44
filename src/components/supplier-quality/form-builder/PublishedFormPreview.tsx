
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Star, FileText, Calendar, DollarSign } from "lucide-react";

const PublishedFormPreview: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Supplier Capability Assessment</h1>
            <p className="text-gray-600">Comprehensive assessment of supplier capabilities and quality systems</p>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Published
          </Badge>
        </div>
      </div>

      <div className="space-y-8">
        {/* Company Information Section */}
        <Card>
          <CardHeader className="bg-blue-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Company Information
            </CardTitle>
            <p className="text-sm text-gray-600">Basic company details and contact information</p>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name <span className="text-red-500">*</span>
              </label>
              <Input 
                placeholder="Enter your company name" 
                value="Delta Manufacturing Inc."
                readOnly
                className="bg-gray-50"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Address <span className="text-red-500">*</span>
              </label>
              <Textarea 
                placeholder="Enter your complete address"
                value="1234 Industrial Way, Detroit, MI 48201"
                readOnly
                className="bg-gray-50"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Contact Person <span className="text-red-500">*</span>
                </label>
                <Input 
                  value="John Smith"
                  readOnly
                  className="bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email <span className="text-red-500">*</span>
                </label>
                <Input 
                  placeholder="<EMAIL>"
                  value="<EMAIL>"
                  readOnly
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quality Management Systems */}
        <Card>
          <CardHeader className="bg-green-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-green-600" />
              Quality Management Systems
            </CardTitle>
            <p className="text-sm text-gray-600">Information about your quality management certifications and processes</p>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Do you have ISO 9001 certification? <span className="text-red-500">*</span>
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input type="radio" name="iso9001" checked readOnly className="mr-2" />
                  <span>Yes</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="iso9001" readOnly className="mr-2" />
                  <span>No</span>
                </label>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Other Quality Certifications
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {["ISO 14001", "ISO 45001", "TS 16949", "AS9100", "Other"].map((cert, index) => (
                  <label key={cert} className="flex items-center">
                    <input 
                      type="checkbox" 
                      checked={index < 2} 
                      readOnly 
                      className="mr-2" 
                    />
                    <span className="text-sm">{cert}</span>
                  </label>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Quality Manual
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <div className="flex items-center justify-between bg-blue-50 p-3 rounded">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm">ISO9001_QualityManual_2024.pdf</span>
                  </div>
                  <Button variant="outline" size="sm">View</Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Manufacturing Capabilities */}
        <Card>
          <CardHeader className="bg-purple-50 border-b">
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              Manufacturing Capabilities
            </CardTitle>
            <p className="text-sm text-gray-600">Details about your manufacturing processes and capabilities</p>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Manufacturing Processes <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {["Machining", "Welding", "Assembly", "Testing", "Coating", "Heat Treatment"].map((process, index) => (
                  <label key={process} className="flex items-center">
                    <input 
                      type="checkbox" 
                      checked={index < 4} 
                      readOnly 
                      className="mr-2" 
                    />
                    <span className="text-sm">{process}</span>
                  </label>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Overall Production Capacity <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center space-x-2">
                {[1, 2, 3, 4, 5].map((rating) => (
                  <Star 
                    key={rating} 
                    className={`h-6 w-6 ${rating <= 4 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                  />
                ))}
                <span className="ml-2 text-sm text-gray-600">4/5</span>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Annual Revenue
              </label>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-500 mr-2">USD</span>
                <Input 
                  value="$12,500,000"
                  readOnly
                  className="bg-gray-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-center mt-8 pt-6 border-t">
        <Button size="lg" className="px-8">
          Submit Assessment
        </Button>
      </div>
    </div>
  );
};

export default PublishedFormPreview;

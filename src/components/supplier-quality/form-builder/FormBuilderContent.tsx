
import React from 'react';
import { FormSection, FormField, FieldType, SupportedLanguage } from '@/types/formBuilder';
import { FormSectionComponent } from './FormSectionComponent';
import { FormPreview } from './FormPreview';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Eye, Settings } from 'lucide-react';
import { useDroppable } from '@dnd-kit/core';

interface FormBuilderContentProps {
  activeTab: string;
  sections: FormSection[];
  selectedField: { sectionId: string; fieldId: string } | null;
  onSelectField: (sectionId: string, fieldId: string) => void;
  onUpdateSection: (sectionId: string, updates: Partial<FormSection>) => void;
  onDeleteSection: (sectionId: string) => void;
  onAddField: (sectionId: string, type: FieldType) => void;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  onDeleteField: (sectionId: string, fieldId: string) => void;
  onAddSection: () => void;
  currentLanguage: SupportedLanguage;
  dragOverSection: string | null;
}

export const FormBuilderContent: React.FC<FormBuilderContentProps> = ({
  activeTab,
  sections,
  selectedField,
  onSelectField,
  onUpdateSection,
  onDeleteSection,
  onAddField,
  onUpdateField,
  onDeleteField,
  onAddSection,
  currentLanguage,
  dragOverSection
}) => {
  const { setNodeRef: setDroppableRef } = useDroppable({
    id: 'form-builder-content'
  });

  if (activeTab === 'preview') {
    return (
      <div className="p-6 h-full bg-gray-50">
        <FormPreview sections={sections} currentLanguage={currentLanguage} />
      </div>
    );
  }

  if (activeTab === 'settings') {
    return (
      <div className="p-6 h-full bg-gray-50">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Form Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Form Configuration</h3>
                <p className="text-sm text-gray-600">Configure form-wide settings, validation rules, and submission handling.</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-2">General Settings</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Form title and description</li>
                    <li>• Language settings</li>
                    <li>• Submission confirmation</li>
                  </ul>
                </Card>
                <Card className="p-4">
                  <h4 className="font-medium mb-2">Advanced Options</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Conditional logic</li>
                    <li>• Custom validation</li>
                    <li>• Integration settings</li>
                  </ul>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div ref={setDroppableRef} className="flex-1 p-6 bg-gray-50 min-h-full">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Form Builder</h2>
            <p className="text-gray-600">Drag components from the sidebar to build your form</p>
          </div>
          <Button onClick={onAddSection} className="gap-2">
            <Plus className="h-4 w-4" />
            Add Section
          </Button>
        </div>

        {/* Form Sections */}
        <div className="space-y-6">
          {sections.length === 0 ? (
            <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <div className="text-6xl mb-4">📝</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Building Your Form</h3>
                  <p className="text-gray-600 mb-4">
                    Add a section to get started, then drag field components from the sidebar
                  </p>
                  <Button onClick={onAddSection} className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Your First Section
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            sections.map((section) => (
              <FormSectionComponent
                key={section.id}
                section={section}
                selectedField={selectedField}
                onSelectField={onSelectField}
                onUpdateSection={onUpdateSection}
                onDeleteSection={onDeleteSection}
                onAddField={onAddField}
                onUpdateField={onUpdateField}
                onDeleteField={onDeleteField}
                currentLanguage={currentLanguage}
                isDragOver={dragOverSection === section.id}
              />
            ))
          )}
        </div>

        {/* Quick Actions */}
        <Card className="border border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-blue-900">Quick Actions</h3>
                <p className="text-sm text-blue-700">Common form building actions</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={onAddSection}>
                  <Plus className="h-4 w-4 mr-1" />
                  Section
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};


import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { FormSection, FormField, SupportedLanguage, TranslatedText } from '@/types/formBuilder';
import { Eye, FileText } from 'lucide-react';

interface FormPreviewProps {
  sections: FormSection[];
  currentLanguage: SupportedLanguage;
}

export const FormPreview: React.FC<FormPreviewProps> = ({ sections, currentLanguage }) => {
  const getTranslatedText = (text: string | TranslatedText): string => {
    if (typeof text === 'string') return text;
    return text[currentLanguage] || text.en || '';
  };

  const renderField = (field: FormField) => {
    const label = getTranslatedText(field.label);
    const placeholder = field.placeholder ? getTranslatedText(field.placeholder) : '';
    const description = field.description ? getTranslatedText(field.description) : '';

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Input id={field.id} placeholder={placeholder} disabled />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'textarea':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Textarea id={field.id} placeholder={placeholder} disabled />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'number':
      case 'currency':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Input 
              id={field.id} 
              type="number" 
              placeholder={placeholder} 
              min={field.min}
              max={field.max}
              step={field.step}
              disabled 
            />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'date':
      case 'time':
      case 'datetime':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Input id={field.id} type={field.type} disabled />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'select':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={field.id}>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Select disabled>
              <SelectTrigger>
                <SelectValue placeholder={placeholder || "Select an option"} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    {typeof option === 'string' ? option : getTranslatedText(option)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'radio':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <RadioGroup disabled>
              {field.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={index.toString()} id={`${field.id}-${index}`} />
                  <Label htmlFor={`${field.id}-${index}`}>
                    {typeof option === 'string' ? option : getTranslatedText(option)}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'checkbox':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <div className="space-y-2">
              {field.options?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox id={`${field.id}-${index}`} disabled />
                  <Label htmlFor={`${field.id}-${index}`}>
                    {typeof option === 'string' ? option : getTranslatedText(option)}
                  </Label>
                </div>
              ))}
            </div>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'yesno':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <RadioGroup disabled>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id={`${field.id}-yes`} />
                <Label htmlFor={`${field.id}-yes`}>Yes</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id={`${field.id}-no`} />
                <Label htmlFor={`${field.id}-no`}>No</Label>
              </div>
            </RadioGroup>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'rating':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  className="text-gray-300 hover:text-yellow-400 disabled:cursor-not-allowed"
                  disabled
                >
                  ⭐
                </button>
              ))}
            </div>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'slider':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Slider
              defaultValue={[field.min || 0]}
              max={field.max || 100}
              min={field.min || 0}
              step={field.step || 1}
              disabled
              className="w-full"
            />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'file':
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <FileText className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm text-gray-500">Click to upload or drag and drop</p>
            </div>
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );

      case 'sectionHeader':
        return (
          <div key={field.id} className="py-4">
            <h3 className="text-lg font-semibold text-gray-900">{label}</h3>
            {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
          </div>
        );

      default:
        return (
          <div key={field.id} className="space-y-2">
            <Label>
              {label} {field.required && <span className="text-red-500">*</span>}
            </Label>
            <Input placeholder={placeholder} disabled />
            {description && <p className="text-xs text-gray-500">{description}</p>}
          </div>
        );
    }
  };

  if (sections.length === 0) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Form Preview
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-gray-500">
            <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No form sections to preview</p>
            <p className="text-sm">Add sections and fields to see the preview</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Form Preview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 max-h-[600px] overflow-y-auto">
        <form className="space-y-6">
          {sections.map((section) => (
            <div key={section.id} className="space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">{getTranslatedText(section.title)}</h3>
                {section.description && (
                  <p className="text-sm text-gray-600">{getTranslatedText(section.description)}</p>
                )}
              </div>
              <div className="space-y-4">
                {section.fields.map(renderField)}
              </div>
            </div>
          ))}
          
          {sections.length > 0 && (
            <div className="pt-4 border-t">
              <Button type="submit" disabled className="w-full">
                Submit Form (Preview Mode)
              </Button>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

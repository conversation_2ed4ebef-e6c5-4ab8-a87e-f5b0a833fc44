
import React from "react";
import { useDraggable } from "@dnd-kit/core";
import { FormField, SupportedLanguage } from "@/types/formBuilder";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Trash, GripVertical, Settings, FileText } from "lucide-react";
import { getTranslatedText } from "@/utils/translationUtils";

interface DraggableFormFieldProps {
  field: FormField;
  sectionId: string;
  onUpdate: (updates: Partial<FormField>) => void;
  onDelete: () => void;
  currentLanguage: SupportedLanguage;
}

export const DraggableFormField: React.FC<DraggableFormFieldProps> = ({
  field,
  sectionId,
  onUpdate,
  onDelete,
  currentLanguage
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({ id: field.id });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.5 : 1,
  } : {};

  const translatedLabel = getTranslatedText(field.label, currentLanguage);
  const translatedPlaceholder = getTranslatedText(field.placeholder, currentLanguage);

  const renderFieldPreview = () => {
    switch (field.type) {
      case "text":
        return (
          <Input
            placeholder={translatedPlaceholder || "Enter text"}
            disabled
            className="bg-gray-50"
          />
        );
      case "textarea":
        return (
          <Textarea
            placeholder={translatedPlaceholder || "Enter detailed response"}
            disabled
            className="bg-gray-50"
            rows={3}
          />
        );
      case "select":
        return (
          <select disabled className="w-full p-2 bg-gray-50 border rounded">
            <option>Select option...</option>
            {field.options?.map((option, index) => (
              <option key={index}>
                {typeof option === 'string' ? option : getTranslatedText(option, currentLanguage)}
              </option>
            ))}
          </select>
        );
      case "yesno":
        return (
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input type="radio" name={field.id} disabled className="mr-2" />
              Yes
            </label>
            <label className="flex items-center">
              <input type="radio" name={field.id} disabled className="mr-2" />
              No
            </label>
          </div>
        );
      case "file":
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <FileText className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <span className="text-sm text-gray-500">Click to upload or drag files here</span>
          </div>
        );
      case "number":
        return (
          <Input
            type="number"
            placeholder={translatedPlaceholder || "Enter number"}
            disabled
            className="bg-gray-50"
          />
        );
      case "date":
        return (
          <Input
            type="date"
            disabled
            className="bg-gray-50"
          />
        );
      case "rating":
        return (
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map(n => (
              <span key={n} className="text-xl text-yellow-400 cursor-pointer">★</span>
            ))}
          </div>
        );
      default:
        return <div className="p-2 bg-gray-50 rounded text-gray-500">Field preview</div>;
    }
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <Card className="border-2 hover:border-blue-300 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center flex-1">
              <div {...listeners} className="cursor-grab mr-2">
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
              <div className="flex-1">
                <Input
                  value={translatedLabel || ""}
                  onChange={(e) => onUpdate({ label: e.target.value })}
                  placeholder="Field Label"
                  className="font-medium border-none p-0 focus:ring-0 bg-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                {field.type}
              </div>
              {field.required && (
                <div className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                  Required
                </div>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="h-6 w-6 p-0 text-red-500 hover:text-red-600"
              >
                <Trash className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="mb-3">
            {renderFieldPreview()}
          </div>

          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <Switch
                checked={field.required}
                onCheckedChange={(checked) => onUpdate({ required: checked })}
                id={`required-${field.id}`}
              />
              <Label htmlFor={`required-${field.id}`} className="text-xs">Required</Label>
            </div>
            
            <Button variant="ghost" size="sm" className="text-xs">
              <Settings className="h-3 w-3 mr-1" />
              Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { useDraggable } from "@dnd-kit/core";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FieldType } from "@/types/formBuilder";
import {
  AlignLeft,
  FileText,
  List,
  ListChecks,
  CheckSquare,
  ToggleLeft,
  Calendar,
  Hash,
  Star,
  File,
  Table,
  PenTool,
  DollarSign,
  Link,
  Type,
  Image,
  Mail,
  Phone,
  MapPin,
  Clock,
  CalendarDays,
  Sliders,
  Grid3X3,
  Navigation,
  QrCode,
  Calculator,
  GitBranch,
  Copy
} from "lucide-react";

const fieldTypes: { type: FieldType; label: string; icon: React.ReactNode; category: string }[] = [
  // Basic Fields
  { type: "text", label: "Text Input", icon: <AlignLeft className="h-4 w-4" />, category: "Basic" },
  { type: "textarea", label: "Text Area", icon: <FileText className="h-4 w-4" />, category: "Basic" },
  { type: "email", label: "Email", icon: <Mail className="h-4 w-4" />, category: "Basic" },
  { type: "phone", label: "Phone", icon: <Phone className="h-4 w-4" />, category: "Basic" },
  { type: "number", label: "Number", icon: <Hash className="h-4 w-4" />, category: "Basic" },
  { type: "currency", label: "Currency", icon: <DollarSign className="h-4 w-4" />, category: "Basic" },
  
  // Selection & Rating
  { type: "select", label: "Dropdown", icon: <List className="h-4 w-4" />, category: "Selection" },
  { type: "multiselect", label: "Multi-Select", icon: <List className="h-4 w-4" />, category: "Selection" },
  { type: "radio", label: "Radio Buttons", icon: <ListChecks className="h-4 w-4" />, category: "Selection" },
  { type: "checkbox", label: "Checkboxes", icon: <CheckSquare className="h-4 w-4" />, category: "Selection" },
  { type: "yesno", label: "Yes/No", icon: <ToggleLeft className="h-4 w-4" />, category: "Selection" },
  { type: "rating", label: "Rating", icon: <Star className="h-4 w-4" />, category: "Selection" },
  { type: "slider", label: "Slider", icon: <Sliders className="h-4 w-4" />, category: "Selection" },
  
  // Date & Time
  { type: "date", label: "Date Picker", icon: <Calendar className="h-4 w-4" />, category: "DateTime" },
  { type: "time", label: "Time", icon: <Clock className="h-4 w-4" />, category: "DateTime" },
  { type: "datetime", label: "Date & Time", icon: <CalendarDays className="h-4 w-4" />, category: "DateTime" },
  
  // File & Media
  { type: "file", label: "File Upload", icon: <File className="h-4 w-4" />, category: "Media" },
  { type: "image", label: "Image Upload", icon: <Image className="h-4 w-4" />, category: "Media" },
  { type: "signature", label: "Signature", icon: <PenTool className="h-4 w-4" />, category: "Media" },
  
  // Advanced
  { type: "address", label: "Address", icon: <MapPin className="h-4 w-4" />, category: "Advanced" },
  { type: "location", label: "GPS Location", icon: <Navigation className="h-4 w-4" />, category: "Advanced" },
  { type: "table", label: "Table", icon: <Table className="h-4 w-4" />, category: "Advanced" },
  { type: "matrix", label: "Matrix/Grid", icon: <Grid3X3 className="h-4 w-4" />, category: "Advanced" },
  { type: "repeater", label: "Repeater", icon: <Copy className="h-4 w-4" />, category: "Advanced" },
  
  // Enterprise
  { type: "barcode", label: "Barcode/QR", icon: <QrCode className="h-4 w-4" />, category: "Enterprise" },
  { type: "calculation", label: "Calculation", icon: <Calculator className="h-4 w-4" />, category: "Enterprise" },
  { type: "conditional", label: "Conditional", icon: <GitBranch className="h-4 w-4" />, category: "Enterprise" },
  { type: "link", label: "Link", icon: <Link className="h-4 w-4" />, category: "Enterprise" },
  
  // Layout
  { type: "sectionHeader", label: "Section Header", icon: <Type className="h-4 w-4" />, category: "Layout" }
];

interface DraggableFieldProps {
  type: FieldType;
  label: string;
  icon: React.ReactNode;
}

const DraggableField: React.FC<DraggableFieldProps> = ({ type, label, icon }) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `palette-${type}`,
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`p-2 bg-white border border-gray-200 rounded-md cursor-grab hover:bg-gray-50 transition-colors ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <div className="flex items-center gap-2 text-xs">
        {icon}
        <span>{label}</span>
      </div>
    </div>
  );
};

export const DraggableFieldPalette: React.FC = () => {
  console.log("DraggableFieldPalette rendering...");
  
  const categories = ["Basic", "Selection", "DateTime", "Media", "Advanced", "Enterprise", "Layout"];
  
  return (
    <Card className="h-full">
      <CardHeader className="bg-gray-50 border-b">
        <CardTitle className="text-lg">Field Types</CardTitle>
        <p className="text-xs text-gray-500">Drag to add fields</p>
      </CardHeader>
      <CardContent className="p-3 space-y-3 overflow-y-auto" style={{ maxHeight: "calc(100vh - 150px)" }}>
        {categories.map((category) => {
          const categoryFields = fieldTypes.filter(field => field.category === category);
          return (
            <div key={category} className="space-y-1">
              <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide">{category}</h4>
              <div className="grid grid-cols-1 gap-1">
                {categoryFields.map((fieldType) => (
                  <DraggableField
                    key={fieldType.type}
                    type={fieldType.type}
                    label={fieldType.label}
                    icon={fieldType.icon}
                  />
                ))}
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
};

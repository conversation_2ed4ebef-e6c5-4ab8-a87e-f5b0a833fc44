
import React from "react";
import { DraggableFieldPalette } from "./DraggableFieldPalette";
import { PropertiesPanel } from "./properties/PropertiesPanel";
import { FormField, FieldType, SupportedLanguage, FormSection } from "@/types/formBuilder";

interface FormBuilderSidebarProps {
  selectedField: { sectionId: string; fieldId: string } | null;
  onUpdateField: (sectionId: string, fieldId: string, updates: Partial<FormField>) => void;
  onAddField: (sectionId: string, type: FieldType) => void;
  currentLanguage: SupportedLanguage;
  sections: FormSection[];
  supportedLanguages: SupportedLanguage[];
}

export const FormBuilderSidebar: React.FC<FormBuilderSidebarProps> = ({
  selectedField,
  onUpdateField,
  onAddField,
  currentLanguage,
  sections,
  supportedLanguages
}) => {
  // Find the selected field from sections
  const getSelectedFieldData = () => {
    if (!selectedField) return null;
    
    const section = sections.find(s => s.id === selectedField.sectionId);
    if (!section) return null;
    
    const field = section.fields.find(f => f.id === selectedField.fieldId);
    return field || null;
  };

  const selectedFieldData = getSelectedFieldData();

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {selectedField && selectedFieldData ? (
        <PropertiesPanel
          field={selectedFieldData}
          sectionId={selectedField.sectionId}
          fieldId={selectedField.fieldId}
          sections={sections}
          onUpdateField={onUpdateField}
          currentLanguage={currentLanguage}
          supportedLanguages={supportedLanguages}
        />
      ) : (
        <DraggableFieldPalette />
      )}
    </div>
  );
};


import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { HelpCircle, Save } from "lucide-react";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { SupportedLanguage, Language } from "@/types/formBuilder";

interface FormBuilderHeaderProps {
  currentLanguage: SupportedLanguage;
  languages: Language[];
  onSaveForm: () => void;
  onShowTutorial: () => void;
}

export const FormBuilderHeader: React.FC<FormBuilderHeaderProps> = ({
  currentLanguage,
  languages,
  onSaveForm,
  onShowTutorial
}) => {
  return (
    <div className="border-b border-gray-200 p-4 bg-white flex-shrink-0">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Form Builder</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onShowTutorial}
            className="gap-2"
          >
            <HelpCircle className="h-4 w-4" />
            Tutorial
          </Button>
          <LanguageSwitcher 
            selectedLanguage={currentLanguage}
            onLanguageChange={() => {}}
            languages={languages}
          />
          <Button onClick={onSaveForm} size="sm" className="gap-2">
            <Save className="h-4 w-4" />
            Save Form
          </Button>
        </div>
      </div>
    </div>
  );
};

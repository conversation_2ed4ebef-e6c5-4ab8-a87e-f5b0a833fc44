
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from "recharts";
import { TrendingUp, DollarSign, Award, Users } from "lucide-react";
import { RFQ } from "@/types/eSourcing";

interface SourcingAnalyticsProps {
  rfqs: RFQ[];
}

export const SourcingAnalytics: React.FC<SourcingAnalyticsProps> = ({ rfqs }) => {
  // Calculate analytics data
  const totalRFQs = rfqs.length;
  const awardedRFQs = rfqs.filter(rfq => rfq.awardedTo).length;
  const totalBidders = rfqs.reduce((sum, rfq) => sum + rfq.bidders.length, 0);
  const avgBiddersPerRFQ = totalBidders / totalRFQs;

  // Category distribution
  const categoryData = rfqs.reduce((acc, rfq) => {
    acc[rfq.category] = (acc[rfq.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const categoryChartData = Object.entries(categoryData).map(([category, count]) => ({
    category,
    count,
  }));

  // Status distribution
  const statusData = rfqs.reduce((acc, rfq) => {
    acc[rfq.status] = (acc[rfq.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const statusChartData = Object.entries(statusData).map(([status, count]) => ({
    status,
    count,
  }));

  // Calculate savings (mock calculation)
  const estimatedSavings = rfqs.reduce((total, rfq) => {
    if (rfq.bidders.length > 0 && rfq.estimatedBudget) {
      const lowestBid = Math.min(...rfq.bidders.map(b => b.bidAmount));
      const estimatedUnit = rfq.estimatedBudget / rfq.quantity;
      const savings = Math.max(0, estimatedUnit - lowestBid);
      return total + (savings * rfq.quantity);
    }
    return total;
  }, 0);

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Award Rate</p>
                <p className="text-2xl font-bold">{((awardedRFQs / totalRFQs) * 100).toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Avg. Bidders</p>
                <p className="text-2xl font-bold">{avgBiddersPerRFQ.toFixed(1)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Est. Savings</p>
                <p className="text-2xl font-bold">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    notation: 'compact'
                  }).format(estimatedSavings)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Active RFQs</p>
                <p className="text-2xl font-bold">
                  {rfqs.filter(rfq => rfq.status === 'Published').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* RFQ by Category */}
        <Card>
          <CardHeader>
            <CardTitle>RFQs by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={categoryChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* RFQ Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>RFQ Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ status, count }) => `${status}: ${count}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {statusChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent RFQ Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {rfqs
              .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime())
              .slice(0, 5)
              .map((rfq) => (
                <div key={rfq.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{rfq.title}</p>
                    <p className="text-sm text-gray-600">
                      {rfq.bidders.length} bids • Published {new Date(rfq.publishDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{rfq.status}</p>
                    {rfq.awardedTo && (
                      <p className="text-sm text-green-600">Awarded to {rfq.awardedTo}</p>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

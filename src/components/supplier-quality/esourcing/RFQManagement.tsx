
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  DollarSign, 
  Users, 
  FileText,
  Award,
  Eye
} from "lucide-react";
import { RFQ } from "@/types/eSourcing";

interface RFQManagementProps {
  rfqs: RFQ[];
}

export const RFQManagement: React.FC<RFQManagementProps> = ({ rfqs }) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Published":
        return <Badge className="bg-blue-100 text-blue-800">Published</Badge>;
      case "Closed":
        return <Badge className="bg-gray-100 text-gray-800">Closed</Badge>;
      case "Awarded":
        return <Badge className="bg-green-100 text-green-800">Awarded</Badge>;
      case "Draft":
        return <Badge className="bg-yellow-100 text-yellow-800">Draft</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getLowestBid = (rfq: RFQ) => {
    if (rfq.bidders.length === 0) return null;
    return Math.min(...rfq.bidders.map(bidder => bidder.bidAmount));
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Request for Quotations</h3>
        <Button>
          <FileText className="h-4 w-4 mr-2" />
          Create RFQ
        </Button>
      </div>

      {rfqs.map((rfq) => {
        const lowestBid = getLowestBid(rfq);
        return (
          <Card key={rfq.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {rfq.title}
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">{rfq.description}</p>
                  <p className="text-xs text-gray-500">RFQ ID: {rfq.id}</p>
                </div>
                <div className="flex gap-2">
                  {getStatusBadge(rfq.status)}
                  <Badge variant="outline">{rfq.category}</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Closing Date</p>
                    <p className="font-medium">{new Date(rfq.closingDate).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Bidders</p>
                    <p className="font-medium">{rfq.bidders.length}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Quantity</p>
                    <p className="font-medium">{rfq.quantity.toLocaleString()} {rfq.unit}</p>
                  </div>
                </div>
                
                {lowestBid && (
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Lowest Bid</p>
                      <p className="font-medium text-green-600">{formatCurrency(lowestBid)}/{rfq.unit}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Bidders Table */}
              {rfq.bidders.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Current Bids:</h4>
                  <div className="border rounded-lg overflow-hidden">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="text-left p-3">Supplier</th>
                          <th className="text-left p-3">Bid Amount</th>
                          <th className="text-left p-3">Submitted</th>
                          <th className="text-left p-3">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {rfq.bidders
                          .sort((a, b) => a.bidAmount - b.bidAmount)
                          .map((bidder, index) => (
                            <tr key={bidder.supplierId} className={index === 0 ? "bg-green-50" : ""}>
                              <td className="p-3 font-medium">
                                {bidder.supplierName}
                                {index === 0 && <span className="ml-2 text-green-600 text-xs">(Lowest)</span>}
                              </td>
                              <td className="p-3">{formatCurrency(bidder.bidAmount)}/{rfq.unit}</td>
                              <td className="p-3">{new Date(bidder.submittedAt).toLocaleDateString()}</td>
                              <td className="p-3">
                                <Badge 
                                  className={
                                    bidder.status === 'Submitted' 
                                      ? "bg-green-100 text-green-800" 
                                      : "bg-yellow-100 text-yellow-800"
                                  }
                                >
                                  {bidder.status}
                                </Badge>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
                {rfq.status === 'Published' && (
                  <Button variant="outline" size="sm">
                    Close RFQ
                  </Button>
                )}
                {rfq.status === 'Closed' && !rfq.awardedTo && (
                  <Button size="sm">
                    <Award className="h-4 w-4 mr-2" />
                    Award Contract
                  </Button>
                )}
                {rfq.awardedTo && (
                  <Button variant="outline" size="sm">
                    View Contract
                  </Button>
                )}
              </div>

              {rfq.awardedTo && (
                <div className="mt-3 p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-800">
                    <Award className="h-4 w-4 inline mr-1" />
                    Awarded to: <strong>{rfq.awardedTo}</strong>
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};


import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Gavel, Clock, DollarSign, Users, TrendingDown } from 'lucide-react';

interface LiveBid {
  id: string;
  supplierName: string;
  amount: number;
  timestamp: string;
  isWinning: boolean;
}

const mockAuction = {
  id: 'auction-001',
  title: 'Industrial Components Supply Contract',
  description: 'Annual supply contract for industrial components - 12 month term',
  startTime: '2024-01-20T10:00:00Z',
  endTime: '2024-01-20T16:00:00Z',
  currentLeader: 'TechCorp Industries',
  currentBid: 485000,
  startingBid: 500000,
  participants: 8,
  timeRemaining: 3600 // seconds
};

const mockBids: LiveBid[] = [
  { id: 'bid-001', supplierName: 'TechCorp Industries', amount: 485000, timestamp: '14:35:22', isWinning: true },
  { id: 'bid-002', supplierName: 'Global Manufacturing', amount: 487000, timestamp: '14:33:15', isWinning: false },
  { id: 'bid-003', supplierName: 'Precision Parts Ltd', amount: 489000, timestamp: '14:30:08', isWinning: false },
  { id: 'bid-004', supplierName: 'Quality Systems Inc', amount: 492000, timestamp: '14:27:44', isWinning: false },
  { id: 'bid-005', supplierName: 'Elite Components', amount: 495000, timestamp: '14:25:12', isWinning: false }
];

export const BiddingInterface: React.FC = () => {
  const [bidAmount, setBidAmount] = useState('');
  const [timeRemaining, setTimeRemaining] = useState(mockAuction.timeRemaining);
  const [bids, setBids] = useState(mockBids);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => Math.max(0, prev - 1));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlaceBid = () => {
    if (bidAmount && Number(bidAmount) < mockAuction.currentBid) {
      const newBid: LiveBid = {
        id: `bid-${Date.now()}`,
        supplierName: 'Your Company',
        amount: Number(bidAmount),
        timestamp: new Date().toLocaleTimeString(),
        isWinning: true
      };
      
      setBids(prev => [newBid, ...prev.map(bid => ({ ...bid, isWinning: false }))]);
      setBidAmount('');
    }
  };

  const savings = mockAuction.startingBid - mockAuction.currentBid;
  const savingsPercentage = ((savings / mockAuction.startingBid) * 100).toFixed(1);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Gavel className="h-5 w-5" />
              {mockAuction.title}
            </span>
            <Badge variant="outline" className="bg-green-100 text-green-800">Live Auction</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">{mockAuction.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <Clock className="h-6 w-6 mx-auto mb-2 text-red-600" />
              <div className="text-lg font-bold text-red-600">{formatTime(timeRemaining)}</div>
              <div className="text-sm text-red-700">Time Remaining</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <DollarSign className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <div className="text-lg font-bold text-green-600">${mockAuction.currentBid.toLocaleString()}</div>
              <div className="text-sm text-green-700">Current Winning Bid</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Users className="h-6 w-6 mx-auto mb-2 text-blue-600" />
              <div className="text-lg font-bold text-blue-600">{mockAuction.participants}</div>
              <div className="text-sm text-blue-700">Participants</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <TrendingDown className="h-6 w-6 mx-auto mb-2 text-purple-600" />
              <div className="text-lg font-bold text-purple-600">{savingsPercentage}%</div>
              <div className="text-sm text-purple-700">Savings</div>
            </div>
          </div>

          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                type="number"
                placeholder="Enter your bid amount"
                value={bidAmount}
                onChange={(e) => setBidAmount(e.target.value)}
                max={mockAuction.currentBid - 1}
              />
              <p className="text-xs text-gray-500 mt-1">
                Bid must be lower than current winning bid of ${mockAuction.currentBid.toLocaleString()}
              </p>
            </div>
            <Button 
              onClick={handlePlaceBid}
              disabled={!bidAmount || Number(bidAmount) >= mockAuction.currentBid || timeRemaining === 0}
              className="px-8"
            >
              Place Bid
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Live Bid History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {bids.map((bid) => (
                <div 
                  key={bid.id} 
                  className={`flex items-center justify-between p-3 rounded-lg ${
                    bid.isWinning ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {bid.isWinning && <Badge className="bg-green-100 text-green-800">Winning</Badge>}
                    <div>
                      <p className="font-medium">{bid.supplierName}</p>
                      <p className="text-sm text-gray-600">{bid.timestamp}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-lg font-bold ${bid.isWinning ? 'text-green-600' : 'text-gray-600'}`}>
                      ${bid.amount.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Auction Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Starting Bid</span>
                <span className="font-bold">${mockAuction.startingBid.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Current Winning Bid</span>
                <span className="font-bold text-green-600">${mockAuction.currentBid.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Total Savings</span>
                <span className="font-bold text-purple-600">${savings.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Current Leader</span>
                <span className="font-bold">{mockAuction.currentLeader}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium">Total Bids</span>
                <span className="font-bold">{bids.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

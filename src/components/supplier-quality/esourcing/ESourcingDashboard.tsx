
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShoppingCart, Gavel, FileText, TrendingUp } from 'lucide-react';
import { RFQManagement } from './RFQManagement';
import { LiveAuctions } from './LiveAuctions';
import { BiddingInterface } from './BiddingInterface';
import { ContractManagement } from './ContractManagement';
import { mockRFQs, mockLiveAuctions } from '@/data/mockESourcingData';

export const ESourcingDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('rfq');

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Active RFQs</p>
                <p className="text-2xl font-bold">{mockRFQs.filter(rfq => rfq.status === 'Published').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Gavel className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Live Auctions</p>
                <p className="text-2xl font-bold">{mockLiveAuctions.filter(auction => auction.status === 'Live').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ShoppingCart className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Bids</p>
                <p className="text-2xl font-bold">{mockRFQs.reduce((acc, rfq) => acc + rfq.bidders.length, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Savings</p>
                <p className="text-2xl font-bold">$2.4M</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="rfq">RFQ Management</TabsTrigger>
          <TabsTrigger value="auctions">Live Auctions</TabsTrigger>
          <TabsTrigger value="bidding">Bidding Interface</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
        </TabsList>

        <TabsContent value="rfq" className="space-y-6">
          <RFQManagement rfqs={mockRFQs} />
        </TabsContent>

        <TabsContent value="auctions" className="space-y-6">
          <LiveAuctions auctions={mockLiveAuctions} />
        </TabsContent>

        <TabsContent value="bidding" className="space-y-6">
          <BiddingInterface />
        </TabsContent>

        <TabsContent value="contracts" className="space-y-6">
          <ContractManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
};

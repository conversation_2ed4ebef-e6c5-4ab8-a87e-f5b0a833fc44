
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Eye, Plus } from 'lucide-react';

const mockContracts = [
  {
    id: 'CNT-001',
    supplier: 'TechChip Industries',
    contractType: 'Master Service Agreement',
    value: '$2,350,000',
    startDate: '2024-01-15',
    endDate: '2025-01-14',
    status: 'Active',
    renewalDate: '2024-10-15'
  },
  {
    id: 'CNT-002',
    supplier: 'MicroElectronics Ltd',
    contractType: 'Purchase Agreement',
    value: '$820,000',
    startDate: '2024-02-01',
    endDate: '2024-12-31',
    status: 'Active',
    renewalDate: '2024-09-01'
  },
  {
    id: 'CNT-003',
    supplier: 'Global Logistics Inc',
    contractType: 'Transportation Services',
    value: '$125,000',
    startDate: '2024-03-01',
    endDate: '2024-08-31',
    status: 'Expiring Soon',
    renewalDate: '2024-07-01'
  }
];

export const ContractManagement: React.FC = () => {
  const [contracts] = useState(mockContracts);

  const getStatusBadge = (status: string) => {
    const variant = status === 'Active' ? 'default' : status === 'Expiring Soon' ? 'destructive' : 'secondary';
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Contract Management</h3>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Contract
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Contract ID</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {contracts.map((contract) => (
                <TableRow key={contract.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      {contract.id}
                    </div>
                  </TableCell>
                  <TableCell>{contract.supplier}</TableCell>
                  <TableCell>{contract.contractType}</TableCell>
                  <TableCell className="font-medium">{contract.value}</TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm">{contract.startDate} - {contract.endDate}</div>
                      <div className="text-xs text-gray-500">Renewal: {contract.renewalDate}</div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(contract.status)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

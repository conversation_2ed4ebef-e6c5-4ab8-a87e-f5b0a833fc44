
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Clock, 
  DollarSign, 
  Users, 
  TrendingUp,
  Play,
  Pause,
  Eye
} from "lucide-react";
import { LiveAuction } from "@/types/eSourcing";

interface LiveAuctionsProps {
  auctions: LiveAuction[];
}

export const LiveAuctions: React.FC<LiveAuctionsProps> = ({ auctions }) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Live":
        return <Badge className="bg-green-100 text-green-800 animate-pulse">Live</Badge>;
      case "Upcoming":
        return <Badge className="bg-blue-100 text-blue-800">Upcoming</Badge>;
      case "Ended":
        return <Badge className="bg-gray-100 text-gray-800">Ended</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getTimeRemaining = (endTime: string) => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const diff = end - now;
    
    if (diff <= 0) return "Ended";
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m remaining`;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Live Auctions</h3>
        <Button>
          <Play className="h-4 w-4 mr-2" />
          Start New Auction
        </Button>
      </div>

      {auctions.map((auction) => (
        <Card key={auction.id} className={auction.status === 'Live' ? 'border-green-500' : ''}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {auction.title}
                </CardTitle>
                <p className="text-xs text-gray-500 mt-1">Auction ID: {auction.id}</p>
              </div>
              {getStatusBadge(auction.status)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-600">Start Time</p>
                  <p className="font-medium">{new Date(auction.startTime).toLocaleString()}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Current Bid</p>
                  <p className="font-medium text-green-600">{formatCurrency(auction.currentBid)}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Participants</p>
                  <p className="font-medium">{auction.participants}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Leading Bidder</p>
                  <p className="font-medium">{auction.currentLeader}</p>
                </div>
              </div>
            </div>

            {auction.status === 'Live' && (
              <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-green-800">Auction is Live</span>
                  </div>
                  <span className="text-sm text-green-700">{getTimeRemaining(auction.endTime)}</span>
                </div>
              </div>
            )}

            {auction.status === 'Upcoming' && (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-blue-800">Auction Starting Soon</span>
                  <span className="text-sm text-blue-700">
                    Starts: {new Date(auction.startTime).toLocaleString()}
                  </span>
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
              
              {auction.status === 'Live' && (
                <>
                  <Button variant="outline" size="sm">
                    <Pause className="h-4 w-4 mr-2" />
                    Pause Auction
                  </Button>
                  <Button size="sm">
                    Monitor Bids
                  </Button>
                </>
              )}
              
              {auction.status === 'Upcoming' && (
                <Button size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  Start Now
                </Button>
              )}
              
              {auction.status === 'Ended' && (
                <Button variant="outline" size="sm">
                  View Results
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

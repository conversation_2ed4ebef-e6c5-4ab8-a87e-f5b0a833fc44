
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Users,
  FileText,
  Shield,
  BarChart3,
  Eye
} from "lucide-react";
import { useSupplierQualityFilters } from "@/hooks/useSupplierQualityFilters";
import { CertificatesView } from "./certificates/CertificatesView";
import { RiskAssessmentView } from "./risk/RiskAssessmentView";
import { ScorecardAnalyticsView } from "./scorecards/ScorecardAnalyticsView";

export const SupplierQualityDashboard: React.FC = () => {
  const { filteredData } = useSupplierQualityFilters();
  const [activeDialog, setActiveDialog] = useState<string | null>(null);

  const stats = [
    {
      title: "Audit Score",
      value: `${filteredData.auditScore}%`,
      icon: <BarChart3 className="h-5 w-5" />,
      trend: "up",
      color: "text-green-600"
    },
    {
      title: "Open CAPAs",
      value: filteredData.openCapas,
      icon: <AlertTriangle className="h-5 w-5" />,
      trend: "down",
      color: "text-yellow-600"
    },
    {
      title: "Expiring Certificates",
      value: filteredData.expiringCertificates,
      icon: <FileText className="h-5 w-5" />,
      trend: "stable",
      color: "text-red-600"
    },
    {
      title: "Approved Suppliers",
      value: filteredData.approvedSuppliers,
      icon: <CheckCircle className="h-5 w-5" />,
      trend: "up",
      color: "text-green-600"
    }
  ];

  const openDialog = (dialogType: string) => {
    setActiveDialog(dialogType);
  };

  const closeDialog = () => {
    setActiveDialog(null);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={stat.color}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Supplier Status Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Approved</span>
                <Badge className="bg-green-100 text-green-800">
                  {filteredData.approvedSuppliers}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Pending Approval</span>
                <Badge className="bg-yellow-100 text-yellow-800">
                  {filteredData.pendingApproval}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Requires Follow-up</span>
                <Badge className="bg-orange-100 text-orange-800">
                  {filteredData.requiresFollowUp}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Disapproved</span>
                <Badge className="bg-red-100 text-red-800">
                  {filteredData.disapproved}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Risk Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredData.riskData.map((risk, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{risk.name}</span>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: risk.color }}
                    />
                    <span className="text-sm font-medium">{risk.value}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Certificates
              </span>
              <Button size="sm" variant="outline" className="gap-2" onClick={() => openDialog('certificates')}>
                <Eye className="h-4 w-4" />
                View Certs
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">234</div>
            <p className="text-xs text-blue-600">Active certificates</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Risk Assessment
              </span>
              <Button size="sm" variant="outline" className="gap-2" onClick={() => openDialog('risks')}>
                <Eye className="h-4 w-4" />
                View Risks
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">15</div>
            <p className="text-xs text-red-600">High risk suppliers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Scorecards
              </span>
              <Button size="sm" variant="outline" className="gap-2" onClick={() => openDialog('scores')}>
                <Eye className="h-4 w-4" />
                View Scores
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">87%</div>
            <p className="text-xs text-blue-600">Average score</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div className="flex-1">
                <p className="font-medium">Supplier audit completed</p>
                <p className="text-sm text-gray-600">Omega Polymers - Score: 92%</p>
              </div>
              <Badge variant="outline">Today</Badge>
            </div>
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div className="flex-1">
                <p className="font-medium">CAPA action due</p>
                <p className="text-sm text-gray-600">MetalTech Inc. - Corrective action required</p>
              </div>
              <Badge variant="outline">2 days</Badge>
            </div>
            <div className="flex items-center gap-4 p-4 border rounded-lg">
              <FileText className="h-5 w-5 text-blue-600" />
              <div className="flex-1">
                <p className="font-medium">Certificate renewal submitted</p>
                <p className="text-sm text-gray-600">Falcon Alloys - ISO 9001:2015</p>
              </div>
              <Badge variant="outline">3 days ago</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dialog for Certificates */}
      <Dialog open={activeDialog === 'certificates'} onOpenChange={closeDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Certificate Management</DialogTitle>
          </DialogHeader>
          <CertificatesView />
        </DialogContent>
      </Dialog>

      {/* Dialog for Risk Assessment */}
      <Dialog open={activeDialog === 'risks'} onOpenChange={closeDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Risk Assessment</DialogTitle>
          </DialogHeader>
          <RiskAssessmentView />
        </DialogContent>
      </Dialog>

      {/* Dialog for Scorecards */}
      <Dialog open={activeDialog === 'scores'} onOpenChange={closeDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Scorecard Analytics</DialogTitle>
          </DialogHeader>
          <ScorecardAnalyticsView />
        </DialogContent>
      </Dialog>
    </div>
  );
};

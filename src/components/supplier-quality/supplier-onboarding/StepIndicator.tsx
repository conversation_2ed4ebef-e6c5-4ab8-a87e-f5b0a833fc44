
import React from "react";
import { Progress } from "@/components/ui/progress";

interface StepIndicatorProps {
  currentStep: number;
  steps: string[];
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, steps }) => {
  return (
    <div className="mt-4">
      <div className="flex justify-between mb-2">
        {steps.map((step, index) => (
          <div 
            key={index}
            className={`text-xs font-medium ${
              index <= currentStep ? "text-primary" : "text-gray-400"
            } ${index === 0 ? "text-left" : index === steps.length - 1 ? "text-right" : "text-center"}`}
            style={{ width: `${100 / steps.length}%` }}
          >
            {step}
          </div>
        ))}
      </div>
      <Progress value={(currentStep / (steps.length - 1)) * 100} className="h-2" />
    </div>
  );
};

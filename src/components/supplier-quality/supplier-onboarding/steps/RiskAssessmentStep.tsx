
import React from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle } from "lucide-react";
import { UseFormReturn } from "react-hook-form";

interface RiskAssessmentStepProps {
  form: UseFormReturn<any>;
  riskScore: number;
}

export const RiskAssessmentStep: React.FC<RiskAssessmentStepProps> = ({ form, riskScore }) => {
  return (
    <>
      <div className="mb-6">
        <h4 className="font-medium mb-2">Risk Assessment Score</h4>
        <div className="flex items-center gap-3">
          <Progress value={riskScore} className="h-4" />
          <span className="font-medium">{riskScore}/100</span>
        </div>
        
        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-md flex items-start">
          <AlertTriangle className="h-5 w-5 text-amber-600 mr-2 mt-0.5" />
          <div>
            <p className="text-amber-800 font-medium">Medium Risk Supplier</p>
            <p className="text-amber-700 text-sm mt-1">
              This supplier has a risk score that may require additional oversight. 
              Consider implementing enhanced monitoring procedures.
            </p>
          </div>
        </div>
      </div>
      
      <FormField
        control={form.control}
        name="acknowledgeRisk"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>
                I acknowledge the risk assessment and will implement appropriate controls
              </FormLabel>
            </div>
          </FormItem>
        )}
      />
    </>
  );
};

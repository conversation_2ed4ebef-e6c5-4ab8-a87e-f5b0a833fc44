
import React from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";

interface ApprovalStepProps {
  form: UseFormReturn<any>;
  formData: {
    companyName: string;
    country: string;
    commodity: string;
    riskScore: number;
  };
}

export const ApprovalStep: React.FC<ApprovalStepProps> = ({ form, formData }) => {
  return (
    <>
      <div className="mb-6">
        <h4 className="font-medium mb-3">Approval Summary</h4>
        
        <div className="border rounded-md p-4 space-y-3">
          <div className="grid grid-cols-2 gap-2">
            <div className="text-gray-500">Company Name:</div>
            <div className="font-medium">{formData.companyName}</div>
            
            <div className="text-gray-500">Country:</div>
            <div className="font-medium">{formData.country}</div>
            
            <div className="text-gray-500">Commodity:</div>
            <div className="font-medium">{formData.commodity}</div>
            
            <div className="text-gray-500">Risk Score:</div>
            <div className="font-medium">{formData.riskScore}/100</div>
          </div>
        </div>
      </div>
      
      <FormField
        control={form.control}
        name="reviewer"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Assign Reviewer</FormLabel>
            <FormControl>
              <Input {...field} placeholder="Enter reviewer name" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

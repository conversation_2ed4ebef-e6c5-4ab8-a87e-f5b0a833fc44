
import React from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { UploadedCertificate } from "../UploadedCertificate";

interface CertificatesStepProps {
  form: UseFormReturn<any>;
  certificates: { name: string; size: string; type: string }[];
}

export const CertificatesStep: React.FC<CertificatesStepProps> = ({ form, certificates }) => {
  return (
    <>
      <FormField
        control={form.control}
        name="hasCertificates"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>
                Supplier has provided required certifications
              </FormLabel>
            </div>
          </FormItem>
        )}
      />
      
      <div className="mt-4">
        <h4 className="font-medium mb-3">Uploaded Certificates</h4>
        <div className="space-y-2">
          {certificates.map((cert, index) => (
            <UploadedCertificate 
              key={index}
              name={cert.name}
              size={cert.size}
              type={cert.type}
            />
          ))}
        </div>
      </div>
      
      <FormField
        control={form.control}
        name="notes"
        render={({ field }) => (
          <FormItem className="mt-4">
            <FormLabel>Notes</FormLabel>
            <FormControl>
              <Textarea 
                {...field} 
                placeholder="Add any notes about the certifications" 
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};


import React from "react";
import { SupplierOnboardingForm } from "./SupplierOnboardingForm";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { HeaderTitle } from "../header-components/HeaderTitle";

export function SupplierOnboardingPage() {
  return (
    <div className="w-full px-4 py-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div className="mb-4 md:mb-0">
          <HeaderTitle 
            title="Supplier Onboarding" 
            subtitle="Add new suppliers to your supply chain"
          />
        </div>
        <Link to="/supplier-quality">
          <Button variant="outline" size="sm" className="gap-2">
            <ArrowLeft className="h-4 w-4" /> Back to Dashboard
          </Button>
        </Link>
      </div>
      
      <div className="w-full">
        <SupplierOnboardingForm />
      </div>
    </div>
  );
}

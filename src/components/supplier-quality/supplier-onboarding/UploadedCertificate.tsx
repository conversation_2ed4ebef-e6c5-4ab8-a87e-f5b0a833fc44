
import React from "react";
import { FileText } from "lucide-react";

interface UploadedCertificateProps {
  name: string;
  size: string;
  type: string;
}

export function UploadedCertificate({ name, size, type }: UploadedCertificateProps) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 border rounded-md">
      <div className="flex items-center">
        <FileText className="h-4 w-4 text-blue-600 mr-2" />
        <div>
          <p className="text-sm font-medium">{name}</p>
          <p className="text-xs text-gray-500">{size}</p>
        </div>
      </div>
      <a 
        href="#" 
        onClick={(e) => { e.preventDefault(); }}
        className="text-xs text-blue-600 hover:text-blue-800"
      >
        View
      </a>
    </div>
  );
}


import React, { useState } from "react";
import { 
  Form
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { 
  <PERSON>,
  CardHeader,
  Card<PERSON>ontent,
  <PERSON>Footer,
  CardTitle
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { StepIndicator } from "./StepIndicator";
import { BasicInfoStep } from "./steps/BasicInfoStep";
import { ContactDetailsStep } from "./steps/ContactDetailsStep";
import { CertificatesStep } from "./steps/CertificatesStep";
import { RiskAssessmentStep } from "./steps/RiskAssessmentStep";
import { ApprovalStep } from "./steps/ApprovalStep";
import { formSchemas, steps } from "./SupplierOnboardingFormSchema";
import { mockSupplierData } from "@/data/mockSupplierOnboarding";

export function SupplierOnboardingForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    companyName: mockSupplierData.companyName,
    country: mockSupplierData.country,
    commodity: mockSupplierData.commodity,
    contactName: mockSupplierData.contactName,
    email: mockSupplierData.email,
    phone: mockSupplierData.phone,
    hasCertificates: true,
    certificates: mockSupplierData.certificates,
    notes: "",
    acknowledgeRisk: false,
    riskScore: mockSupplierData.riskScore,
    reviewer: mockSupplierData.assignedReviewer
  });
  
  const { toast } = useToast();
  
  // Determine which schema to use based on the current step
  const currentSchema = formSchemas[currentStep];
  
  const form = useForm({
    resolver: zodResolver(currentSchema),
    defaultValues: {
      companyName: formData.companyName,
      country: formData.country,
      commodity: formData.commodity,
      contactName: formData.contactName,
      email: formData.email,
      phone: formData.phone,
      hasCertificates: formData.hasCertificates,
      notes: formData.notes,
      acknowledgeRisk: formData.acknowledgeRisk,
      reviewer: formData.reviewer
    }
  });
  
  const nextStep = (data: any) => {
    setFormData({...formData, ...data});
    
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit the form
      toast({
        title: "Supplier onboarding complete",
        description: `${formData.companyName} has been assigned to ${formData.reviewer} for qualification.`,
      });
      console.log("Form submitted:", {...formData, ...data});
    }
  };
  
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const onSubmit = (data: any) => {
    nextStep(data);
  };
  
  // Render current step based on step index
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <BasicInfoStep form={form} />;
      case 1:
        return <ContactDetailsStep form={form} />;
      case 2:
        return <CertificatesStep form={form} certificates={formData.certificates} />;
      case 3:
        return <RiskAssessmentStep form={form} riskScore={formData.riskScore} />;
      case 4:
        return <ApprovalStep 
          form={form} 
          formData={{
            companyName: formData.companyName,
            country: formData.country,
            commodity: formData.commodity,
            riskScore: formData.riskScore
          }}
        />;
      default:
        return null;
    }
  };
  
  return (
    <div className="max-w-3xl mx-auto">
      <Card className="shadow-sm border">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <CardTitle className="text-xl">Supplier Onboarding</CardTitle>
          <StepIndicator currentStep={currentStep} steps={steps} />
        </CardHeader>
        
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {renderCurrentStep()}
            </form>
          </Form>
        </CardContent>
        
        <CardFooter className="flex justify-between border-t border-gray-200 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            Back
          </Button>
          
          <Button
            type="button"
            onClick={form.handleSubmit(onSubmit)}
          >
            {currentStep === steps.length - 1 ? "Assign for Qualification" : "Next"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

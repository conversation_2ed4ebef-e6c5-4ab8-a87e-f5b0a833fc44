
import { z } from "zod";

export const basicInfoSchema = z.object({
  companyName: z.string().min(3, "Company name must be at least 3 characters"),
  country: z.string().min(2, "Please select a country"),
  commodity: z.string().min(2, "Please enter the commodity")
});

export const contactInfoSchema = z.object({
  contactName: z.string().min(3, "Contact name must be at least 3 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number")
});

export const certificatesSchema = z.object({
  hasCertificates: z.boolean(),
  notes: z.string().optional()
});

export const riskAssessmentSchema = z.object({
  acknowledgeRisk: z.boolean().refine(val => val === true, {
    message: "You must acknowledge the risk assessment"
  })
});

export const approvalSchema = z.object({
  reviewer: z.string().min(3, "Please assign a reviewer")
});

// Combined schema for the entire form
export const formSchemas = [
  basicInfoSchema,
  contactInfoSchema,
  certificatesSchema,
  riskAssessmentSchema,
  approvalSchema
];

export const steps = [
  "Basic Information",
  "Contact Details",
  "Certifications",
  "Risk Assessment",
  "Approval"
];

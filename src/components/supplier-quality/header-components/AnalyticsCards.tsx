
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { 
  TrendingUp, 
  BarChart3,
  Info,
  PieChart, 
  ActivitySquare 
} from "lucide-react";

export const AnalyticsCards: React.FC = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4 mt-2">
      <Link to="/supplier-quality/risk-heatmap" className="block">
        <Card className="p-4 h-full hover:shadow-md transition-shadow flex flex-col items-center justify-center text-center">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mb-2">
            <TrendingUp className="h-5 w-5 text-red-600" />
          </div>
          <span className="font-medium">Risk Heatmap</span>
        </Card>
      </Link>
      
      <Link to="/supplier-quality/scorecard" className="block">
        <Card className="p-4 h-full hover:shadow-md transition-shadow flex flex-col items-center justify-center text-center">
          <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mb-2">
            <BarChart3 className="h-5 w-5 text-teal-600" />
          </div>
          <span className="font-medium">Supplier Scorecard</span>
        </Card>
      </Link>
      
      <Link to="/supplier-quality/ai-insights" className="block">
        <Card className="p-4 h-full hover:shadow-md transition-shadow flex flex-col items-center justify-center text-center">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
            <Info className="h-5 w-5 text-blue-600" />
          </div>
          <span className="font-medium">AI Insights</span>
        </Card>
      </Link>
      
      <Link to="/supplier-quality/performance-analysis" className="block">
        <Card className="p-4 h-full hover:shadow-md transition-shadow flex flex-col items-center justify-center text-center">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
            <PieChart className="h-5 w-5 text-purple-600" />
          </div>
          <span className="font-medium">Performance Analysis</span>
        </Card>
      </Link>
      
      <Link to="/supplier-quality/activity-timeline" className="block">
        <Card className="p-4 h-full hover:shadow-md transition-shadow flex flex-col items-center justify-center text-center">
          <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center mb-2">
            <ActivitySquare className="h-5 w-5 text-amber-600" />
          </div>
          <span className="font-medium">Activity Timeline</span>
        </Card>
      </Link>
    </div>
  );
};

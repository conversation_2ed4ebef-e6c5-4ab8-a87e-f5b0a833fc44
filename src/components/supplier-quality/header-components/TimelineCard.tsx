
import React from "react";
import { Link } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Calendar, FileText } from "lucide-react";

export const TimelineCard: React.FC = () => {
  const cards = [
    {
      title: "Vendor Timeline",
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      description: "View audit milestones and timeline",
      path: "/vendor-timeline"
    },
    {
      title: "Audit Templates",
      icon: <FileText className="h-5 w-5 text-purple-600" />,
      description: "Manage audit templates",
      path: "/audit-templates"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2">
      {cards.map((card) => (
        <Link to={card.path} key={card.title} className="block">
          <Card className="p-4 h-full hover:shadow-md transition-shadow">
            <div className="flex flex-col items-center justify-center text-center h-full">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                {card.icon}
              </div>
              <h3 className="font-medium mb-1">{card.title}</h3>
              <p className="text-xs text-muted-foreground">{card.description}</p>
            </div>
          </Card>
        </Link>
      ))}
    </div>
  );
};

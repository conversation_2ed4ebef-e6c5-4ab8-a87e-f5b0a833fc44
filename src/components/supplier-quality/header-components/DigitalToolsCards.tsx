
import React from "react";
import { Link } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { 
  FormInput, 
  ShoppingCart, 
  Calendar, 
  Info,
  MessageSquare,
  Users,
  FileText,
  Settings
} from "lucide-react";

export const DigitalToolsCards: React.FC = () => {
  const cards = [
    {
      title: "Form Builder",
      icon: <FormInput className="h-5 w-5 text-green-600" />,
      description: "Create supplier forms",
      path: "/supplier-quality",
      tab: "forms"
    },
    {
      title: "E-Sourcing",
      icon: <ShoppingCart className="h-5 w-5 text-purple-600" />,
      description: "Strategic sourcing platform",
      path: "/supplier-quality",
      tab: "esourcing"
    },
    {
      title: "Vendor Timeline",
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      description: "View audit milestones",
      path: "/vendor-timeline"
    },
    {
      title: "AI Insights",
      icon: <Info className="h-5 w-5 text-blue-600" />,
      description: "AI-powered insights",
      path: "/supplier-quality",
      tab: "insights"
    },
    {
      title: "SRM Dashboard",
      icon: <Users className="h-5 w-5 text-teal-600" />,
      description: "Supplier relationship management",
      path: "/supplier-quality",
      tab: "management"
    },
    {
      title: "Feedback Center",
      icon: <MessageSquare className="h-5 w-5 text-orange-600" />,
      description: "Supplier feedback",
      path: "/supplier-quality",
      tab: "feedback"
    }
  ];

  const handleTabNavigation = (tab: string) => {
    window.location.hash = `#${tab}`;
    if (window.location.pathname !== '/supplier-quality') {
      window.location.href = `/supplier-quality#${tab}`;
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      {cards.map((card) => (
        card.tab ? (
          <div key={card.title} onClick={() => handleTabNavigation(card.tab)} className="cursor-pointer">
            <Card className="p-3 h-full hover:shadow-md transition-shadow">
              <div className="flex flex-col items-center justify-center text-center h-full">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                  {card.icon}
                </div>
                <h3 className="font-medium text-sm mb-1">{card.title}</h3>
                <p className="text-xs text-muted-foreground">{card.description}</p>
              </div>
            </Card>
          </div>
        ) : (
          <Link to={card.path} key={card.title} className="block">
            <Card className="p-3 h-full hover:shadow-md transition-shadow">
              <div className="flex flex-col items-center justify-center text-center h-full">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                  {card.icon}
                </div>
                <h3 className="font-medium text-sm mb-1">{card.title}</h3>
                <p className="text-xs text-muted-foreground">{card.description}</p>
              </div>
            </Card>
          </Link>
        )
      ))}
    </div>
  );
};

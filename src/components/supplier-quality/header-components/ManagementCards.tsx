
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users,
  ShoppingCart,
  User,
  Leaf,
  Calendar,
  Brain,
  MessageSquare,
  FileText
} from "lucide-react";

export const ManagementCards: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full overflow-x-auto">
      <Link to="/supplier-relationship-management">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Supplier Relationship Management
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              Active relationships
            </p>
            <Badge variant="outline" className="mt-2">
              360° View
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/esourcing">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              eSourcing Platform
            </CardTitle>
            <ShoppingCart className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">
              Active RFQs
            </p>
            <Badge variant="outline" className="mt-2">
              Procurement
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/vendor-timeline">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Vendor Timeline
            </CardTitle>
            <Calendar className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">
              Audit milestones
            </p>
            <Badge variant="outline" className="mt-2">
              Timeline View
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/supplier-quality/ai-insights">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              AI Insights
            </CardTitle>
            <Brain className="h-4 w-4 text-indigo-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">
              Risk insights
            </p>
            <Badge variant="outline" className="mt-2">
              AI Powered
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/supplier-portal">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Supplier Portal
            </CardTitle>
            <User className="h-4 w-4 text-teal-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">142</div>
            <p className="text-xs text-muted-foreground">
              Active suppliers
            </p>
            <Badge variant="outline" className="mt-2">
              Self-Service
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/supplier-sustainability">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Sustainability Management
            </CardTitle>
            <Leaf className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">95%</div>
            <p className="text-xs text-muted-foreground">
              Compliance rate
            </p>
            <Badge variant="outline" className="mt-2">
              ESG Ready
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/supplier-messages">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Supplier Messages
            </CardTitle>
            <MessageSquare className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">
              Unread messages
            </p>
            <Badge variant="outline" className="mt-2">
              Communication
            </Badge>
          </CardContent>
        </Card>
      </Link>

      <Link to="/supplier-forms">
        <Card className="hover:shadow-md transition-shadow cursor-pointer min-w-[250px]">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Supplier Forms
            </CardTitle>
            <FileText className="h-4 w-4 text-rose-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              Pending forms
            </p>
            <Badge variant="outline" className="mt-2">
              Form Builder
            </Badge>
          </CardContent>
        </Card>
      </Link>
    </div>
  );
};

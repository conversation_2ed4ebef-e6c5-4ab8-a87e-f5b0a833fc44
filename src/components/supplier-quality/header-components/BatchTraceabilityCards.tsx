
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Package, Truck, AlertTriangle, CheckCircle } from "lucide-react";

export const BatchTraceabilityCards: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">Active Batches</p>
              <p className="text-2xl font-bold text-blue-800">234</p>
              <Badge className="bg-blue-100 text-blue-800 mt-1">+12 today</Badge>
            </div>
            <Package className="h-8 w-8 text-blue-600" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">In Transit</p>
              <p className="text-2xl font-bold text-green-800">89</p>
              <Badge className="bg-green-100 text-green-800 mt-1">On schedule</Badge>
            </div>
            <Truck className="h-8 w-8 text-green-600" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-amber-600 font-medium">Quality Alerts</p>
              <p className="text-2xl font-bold text-amber-800">15</p>
              <Badge className="bg-amber-100 text-amber-800 mt-1">3 critical</Badge>
            </div>
            <AlertTriangle className="h-8 w-8 text-amber-600" />
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-emerald-600 font-medium">Verified</p>
              <p className="text-2xl font-bold text-emerald-800">98.2%</p>
              <Badge className="bg-emerald-100 text-emerald-800 mt-1">+0.5%</Badge>
            </div>
            <CheckCircle className="h-8 w-8 text-emerald-600" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  Grid, 
  Target, 
  Award, 
  FileCheck, 
  Users,
  TrendingUp,
  Shield,
  CheckCircle2
} from "lucide-react";

export const QualificationMatrixCards: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
      <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/supplier-quality/qualification-matrix")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-indigo-700 flex items-center gap-2">
            <Grid className="h-4 w-4" />
            View Matrix
          </Card<PERSON><PERSON>le>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-indigo-600 mb-3">Supplier qualification overview</p>
          <Button size="sm" variant="outline" className="w-full text-indigo-700 border-indigo-300 hover:bg-indigo-50">
            Open Matrix
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/certificates")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-emerald-700 flex items-center gap-2">
            <Award className="h-4 w-4" />
            Certifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-emerald-600 mb-3">Manage supplier certificates</p>
          <Button size="sm" variant="outline" className="w-full text-emerald-700 border-emerald-300 hover:bg-emerald-50">
            View Certs
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/supplier-quality/scorecard")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-orange-700 flex items-center gap-2">
            <Target className="h-4 w-4" />
            Scorecards
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-orange-600 mb-3">Performance scorecards</p>
          <Button size="sm" variant="outline" className="w-full text-orange-700 border-orange-300 hover:bg-orange-50">
            View Scores
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/supplier-quality/risk-heatmap")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-teal-700 flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Risk Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-teal-600 mb-3">Supplier risk evaluation</p>
          <Button size="sm" variant="outline" className="w-full text-teal-700 border-teal-300 hover:bg-teal-50">
            View Risks
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

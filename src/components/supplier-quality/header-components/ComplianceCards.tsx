
import React from "react";
import { Link } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { 
  Award, 
  File<PERSON>heck, 
  ShieldCheck, 
  BarChart3,
  Clipboard
} from "lucide-react";

export const ComplianceCards: React.FC = () => {
  const cards = [
    {
      title: "Qualification Matrix",
      icon: <Award className="h-5 w-5 text-emerald-600" />,
      description: "Manage supplier qualifications",
      path: "/supplier-quality",
      tab: "matrix"
    },
    {
      title: "Certificates",
      icon: <FileCheck className="h-5 w-5 text-cyan-600" />,
      description: "Track supplier certifications",
      path: "/certificates"
    },
    {
      title: "Risk Assessment",
      icon: <ShieldCheck className="h-5 w-5 text-red-600" />,
      description: "Evaluate supplier risks",
      path: "/supplier-quality",
      tab: "insights"
    },
    {
      title: "Scorecards",
      icon: <BarChart3 className="h-5 w-5 text-amber-600" />,
      description: "Monitor supplier performance",
      path: "/supplier-quality/scorecard"
    },
    {
      title: "Audits & CAPAs",
      icon: <Clipboard className="h-5 w-5 text-violet-600" />,
      description: "Audit findings and corrective actions",
      path: "/supplier-quality",
      tab: "audit-dashboard"
    }
  ];

  const handleTabNavigation = (tab: string) => {
    window.location.hash = `#${tab}`;
    if (window.location.pathname !== '/supplier-quality') {
      window.location.href = `/supplier-quality#${tab}`;
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      {cards.map((card) => (
        card.tab ? (
          <div key={card.title} onClick={() => handleTabNavigation(card.tab)} className="cursor-pointer">
            <Card className="p-3 h-full hover:shadow-md transition-shadow">
              <div className="flex flex-col items-center justify-center text-center h-full">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                  {card.icon}
                </div>
                <h3 className="font-medium text-sm mb-1">{card.title}</h3>
                <p className="text-xs text-muted-foreground">{card.description}</p>
              </div>
            </Card>
          </div>
        ) : (
          <Link to={card.path} key={card.title} className="block">
            <Card className="p-3 h-full hover:shadow-md transition-shadow">
              <div className="flex flex-col items-center justify-center text-center h-full">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                  {card.icon}
                </div>
                <h3 className="font-medium text-sm mb-1">{card.title}</h3>
                <p className="text-xs text-muted-foreground">{card.description}</p>
              </div>
            </Card>
          </Link>
        )
      ))}
    </div>
  );
};

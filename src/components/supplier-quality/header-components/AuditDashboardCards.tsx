
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { 
  Calendar, 
  ClipboardCheck, 
  <PERSON><PERSON><PERSON><PERSON>gle, 
  <PERSON><PERSON>dingUp, 
  Clock,
  CheckCircle,
  BarChart3,
  FileText
} from "lucide-react";

export const AuditDashboardCards: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/audit-management")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule Audits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-blue-600 mb-3">Plan and schedule supplier audits</p>
          <Button size="sm" variant="outline" className="w-full text-blue-700 border-blue-300 hover:bg-blue-50">
            View Calendar
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/audits")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-green-700 flex items-center gap-2">
            <ClipboardCheck className="h-4 w-4" />
            Active Audits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-green-600 mb-3">Monitor ongoing audit activities</p>
          <Button size="sm" variant="outline" className="w-full text-green-700 border-green-300 hover:bg-green-50">
            View Active
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/supplier-audits-capas")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-amber-700 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            CAPA Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-amber-600 mb-3">Track corrective actions</p>
          <Button size="sm" variant="outline" className="w-full text-amber-700 border-amber-300 hover:bg-amber-50">
            View CAPAs
          </Button>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate("/supplier-quality/performance-analysis")}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-purple-700 flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Audit Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-purple-600 mb-3">View audit performance trends</p>
          <Button size="sm" variant="outline" className="w-full text-purple-700 border-purple-300 hover:bg-purple-50">
            View Analytics
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

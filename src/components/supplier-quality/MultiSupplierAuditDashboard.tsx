
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  FileText,
  Clock,
  Target
} from 'lucide-react';

export const MultiSupplierAuditDashboard: React.FC = () => {
  const [suppliers] = useState([
    {
      id: 'supp-001',
      name: 'TechCorp Manufacturing',
      lastAudit: '2024-01-15',
      nextAudit: '2024-04-15',
      openNCs: 3,
      status: 'compliant',
      riskLevel: 'low',
      auditor: '<PERSON>'
    },
    {
      id: 'supp-002',
      name: 'Precision Parts Ltd',
      lastAudit: '2023-11-20',
      nextAudit: '2024-02-20',
      openNCs: 7,
      status: 'action_required',
      riskLevel: 'high',
      auditor: '<PERSON>'
    },
    {
      id: 'supp-003',
      name: 'Global Components Inc',
      lastAudit: '2024-01-08',
      nextAudit: '2024-03-08',
      openNCs: 1,
      status: 'compliant',
      riskLevel: 'medium',
      auditor: 'Alex Rodriguez'
    }
  ]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'compliant':
        return <Badge className="bg-green-100 text-green-800">Compliant</Badge>;
      case 'action_required':
        return <Badge className="bg-red-100 text-red-800">Action Required</Badge>;
      case 'under_review':
        return <Badge className="bg-yellow-100 text-yellow-800">Under Review</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'high':
        return <Badge variant="destructive">High Risk</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'low':
        return <Badge className="bg-green-100 text-green-800">Low Risk</Badge>;
      default:
        return <Badge variant="outline">{risk}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Multi-Supplier Audit Dashboard</h2>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Bulk Audits
          </Button>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">{suppliers.length}</p>
                <p className="text-sm text-gray-600">Total Suppliers</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {suppliers.filter(s => s.status === 'compliant').length}
                </p>
                <p className="text-sm text-gray-600">Compliant</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-red-600">
                  {suppliers.reduce((sum, s) => sum + s.openNCs, 0)}
                </p>
                <p className="text-sm text-gray-600">Open NCs</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-orange-600">
                  {suppliers.filter(s => s.riskLevel === 'high').length}
                </p>
                <p className="text-sm text-gray-600">High Risk</p>
              </div>
              <Target className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Supplier Overview</TabsTrigger>
          <TabsTrigger value="scheduling">Audit Scheduling</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Status Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {suppliers.map((supplier) => (
                  <Card key={supplier.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-medium">{supplier.name}</h4>
                          {getStatusBadge(supplier.status)}
                          {getRiskBadge(supplier.riskLevel)}
                        </div>
                        <div className="grid grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <p className="font-medium">Last Audit</p>
                            <p>{new Date(supplier.lastAudit).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p className="font-medium">Next Audit</p>
                            <p>{new Date(supplier.nextAudit).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <p className="font-medium">Open NCs</p>
                            <p className={supplier.openNCs > 5 ? 'text-red-600 font-bold' : ''}>{supplier.openNCs}</p>
                          </div>
                          <div>
                            <p className="font-medium">Assigned Auditor</p>
                            <p>{supplier.auditor}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          Schedule Audit
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduling">
          <Card>
            <CardHeader>
              <CardTitle>Audit Scheduling Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input type="month" placeholder="Select month" className="max-w-xs" />
                  <Button variant="outline">Filter by Auditor</Button>
                  <Button variant="outline">Filter by Risk Level</Button>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-200 p-3 text-left">Supplier</th>
                        <th className="border border-gray-200 p-3 text-left">Current Status</th>
                        <th className="border border-gray-200 p-3 text-left">Next Audit Due</th>
                        <th className="border border-gray-200 p-3 text-left">Assigned Auditor</th>
                        <th className="border border-gray-200 p-3 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {suppliers.map((supplier) => (
                        <tr key={supplier.id}>
                          <td className="border border-gray-200 p-3">{supplier.name}</td>
                          <td className="border border-gray-200 p-3">
                            {getStatusBadge(supplier.status)}
                          </td>
                          <td className="border border-gray-200 p-3">
                            {new Date(supplier.nextAudit).toLocaleDateString()}
                          </td>
                          <td className="border border-gray-200 p-3">{supplier.auditor}</td>
                          <td className="border border-gray-200 p-3">
                            <Button size="sm" variant="outline">
                              Schedule
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Performance Analytics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Compliance Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Fully Compliant</span>
                        <span className="text-green-600 font-medium">67%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Action Required</span>
                        <span className="text-red-600 font-medium">33%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Risk Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>High Risk</span>
                        <span className="text-red-600 font-medium">33%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Medium Risk</span>
                        <span className="text-yellow-600 font-medium">33%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Low Risk</span>
                        <span className="text-green-600 font-medium">34%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

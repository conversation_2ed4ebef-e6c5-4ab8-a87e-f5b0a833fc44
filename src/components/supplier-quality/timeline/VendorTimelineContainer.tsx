
import React from "react";
import { VendorTimeline, VendorTimelineMilestone } from "./VendorTimeline";

export const VendorTimelineContainer: React.FC = () => {
  const auditTimeline: VendorTimelineMilestone[] = [
    {
      id: "audit-1",
      date: "Jan 15-17, 2024",
      title: "Initial Quality System Audit",
      description: "Comprehensive initial assessment of quality management system, processes, and documentation. Covered ISO 9001 compliance and manufacturing capabilities.",
      type: "audit",
      auditType: "Initial",
      status: "completed",
      statusColor: "green",
      score: 86,
      result: "Passed",
      hasReport: true,
      hasEvidence: true,
      reportId: "AUD-2024-001",
      linkedCapa: "CAPA-2024-003"
    },
    {
      id: "capa-1",
      date: "Feb 8, 2024",
      title: "CAPA Implementation - Documentation Control",
      description: "Corrective action implemented for documentation control findings from initial audit. Updated procedures and training completed.",
      type: "capa",
      status: "completed",
      statusColor: "amber",
      hasReport: true,
      hasEvidence: true,
      reportId: "CAPA-2024-003"
    },
    {
      id: "audit-2",
      date: "Mar 12-13, 2024",
      title: "Follow-up Audit",
      description: "Follow-up audit to verify CAPA implementation and assess improvement in documentation control processes.",
      type: "audit",
      auditType: "Follow-up",
      status: "completed",
      statusColor: "green",
      score: 91,
      result: "Passed",
      hasReport: true,
      hasEvidence: true,
      reportId: "AUD-2024-015"
    },
    {
      id: "audit-3",
      date: "May 20, 2024",
      title: "Re-audit - Process Improvements",
      description: "Scheduled re-audit to evaluate process improvements and overall system maturity. Significant improvements observed.",
      type: "audit",
      auditType: "Re-audit",
      status: "completed",
      statusColor: "green",
      score: 94,
      result: "Passed",
      hasReport: true,
      hasEvidence: true,
      reportId: "AUD-2024-032"
    },
    {
      id: "ncr-1",
      date: "Jul 3, 2024",
      title: "Spot Audit - Issue Flagged",
      description: "Unscheduled spot audit triggered by customer complaint. Minor non-conformance identified in incoming inspection process.",
      type: "audit",
      auditType: "Spot Audit",
      status: "completed",
      statusColor: "amber",
      score: 78,
      result: "Conditional",
      hasReport: true,
      hasEvidence: true,
      reportId: "AUD-2024-045",
      linkedNcr: "NCR-2024-028",
      linkedCapa: "CAPA-2024-018"
    },
    {
      id: "capa-2",
      date: "Aug 15, 2024",
      title: "CAPA Implementation - Inspection Process",
      description: "Corrective action for incoming inspection process improvement. New inspection checklist and training program implemented.",
      type: "capa",
      status: "completed",
      statusColor: "green",
      hasReport: true,
      hasEvidence: true,
      reportId: "CAPA-2024-018"
    },
    {
      id: "audit-4",
      date: "Oct 8-9, 2024",
      title: "Annual Surveillance Audit",
      description: "Annual surveillance audit showing excellent progress and sustained improvements across all processes.",
      type: "audit",
      auditType: "Follow-up",
      status: "completed",
      statusColor: "green",
      score: 96,
      result: "Passed",
      hasReport: true,
      hasEvidence: true,
      reportId: "AUD-2024-067"
    }
  ];

  return (
    <div className="w-full">
      <VendorTimeline 
        milestones={auditTimeline} 
        vendorName="PT Sat Nusapersada Tbk"
      />
    </div>
  );
};

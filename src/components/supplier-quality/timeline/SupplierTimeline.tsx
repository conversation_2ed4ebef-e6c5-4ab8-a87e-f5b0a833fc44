
import React from "react";
import { Check, Calendar, FileText, Wrench, CheckCircle2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";

export interface TimelineMilestone {
  id: string;
  date: string;
  title: string;
  description: string;
  icon: "onboarded" | "audit" | "ncr" | "capa" | "scorecard" | "approved";
  status?: string;
  statusColor?: "green" | "amber" | "red" | "blue" | "purple";
}

interface SupplierTimelineProps {
  milestones: TimelineMilestone[];
}

export const SupplierTimeline: React.FC<SupplierTimelineProps> = ({ milestones }) => {
  const handleMilestoneClick = (milestone: TimelineMilestone) => {
    toast({
      title: milestone.title,
      description: `${milestone.date}: ${milestone.description}`,
    });
  };

  const renderIcon = (icon: TimelineMilestone["icon"]) => {
    switch (icon) {
      case "onboarded":
        return <Calendar className="h-5 w-5 text-blue-600" />;
      case "audit":
        return <Check className="h-5 w-5 text-green-600" />;
      case "ncr":
        return <FileText className="h-5 w-5 text-red-600" />;
      case "capa":
        return <Wrench className="h-5 w-5 text-amber-600" />;
      case "scorecard":
        return <FileText className="h-5 w-5 text-purple-600" />;
      case "approved":
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      default:
        return <Calendar className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (color?: TimelineMilestone["statusColor"]) => {
    switch (color) {
      case "green":
        return "bg-green-100 text-green-800 border-green-300";
      case "amber":
        return "bg-amber-100 text-amber-800 border-amber-300";
      case "red":
        return "bg-red-100 text-red-800 border-red-300";
      case "blue":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "purple":
        return "bg-purple-100 text-purple-800 border-purple-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <Card className="w-full shadow-sm hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-6">Supplier Lifecycle Timeline</h3>
        
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-0 md:left-1/2 transform md:translate-x-0 h-full w-1 bg-gray-200 top-8"></div>
          
          <div className="space-y-12 relative">
            {milestones.map((milestone, index) => (
              <div 
                key={milestone.id}
                className={`flex flex-col md:flex-row md:items-center ${
                  index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                }`}
              >
                {/* Timeline content for desktop - alternating sides */}
                <div className={`hidden md:block md:w-1/2 ${index % 2 === 0 ? "md:pr-8 text-right" : "md:pl-8"}`}>
                  <button 
                    onClick={() => handleMilestoneClick(milestone)}
                    className="text-left hover:bg-gray-50 p-3 rounded-md transition-colors w-full"
                  >
                    <p className="text-sm text-gray-500">{milestone.date}</p>
                    <h4 className="text-md font-medium mb-1">{milestone.title}</h4>
                    <p className="text-sm text-gray-600">{milestone.description}</p>
                    
                    {milestone.status && (
                      <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(milestone.statusColor)}`}>
                        {milestone.status}
                      </span>
                    )}
                  </button>
                </div>
                
                {/* Timeline dot */}
                <div className="absolute left-0 md:left-1/2 transform -translate-x-1/2 flex items-center justify-center">
                  <div className="bg-white rounded-full border-2 border-primary p-2 z-10">
                    {renderIcon(milestone.icon)}
                  </div>
                </div>
                
                {/* Timeline content for mobile - all on right side */}
                <div className="md:hidden ml-8 pb-2">
                  <button 
                    onClick={() => handleMilestoneClick(milestone)}
                    className="text-left hover:bg-gray-50 p-3 rounded-md transition-colors w-full"
                  >
                    <p className="text-sm text-gray-500">{milestone.date}</p>
                    <h4 className="text-md font-medium mb-1">{milestone.title}</h4>
                    <p className="text-sm text-gray-600">{milestone.description}</p>
                    
                    {milestone.status && (
                      <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(milestone.statusColor)}`}>
                        {milestone.status}
                      </span>
                    )}
                  </button>
                </div>
                
                {/* Empty div to maintain spacing for desktop alternate layout */}
                <div className="hidden md:block md:w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

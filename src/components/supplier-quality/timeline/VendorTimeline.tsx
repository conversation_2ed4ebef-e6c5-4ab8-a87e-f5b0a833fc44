
import React from "react";
import { Calendar, FileText, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, Award, Target } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export interface VendorTimelineMilestone {
  id: string;
  date: string;
  title: string;
  description: string;
  type: "audit" | "capa" | "follow-up" | "ncr" | "approval";
  auditType?: "Initial" | "Follow-up" | "Re-audit" | "Spot Audit";
  status: "completed" | "in-progress" | "pending";
  statusColor: "green" | "amber" | "red" | "blue";
  score?: number;
  result?: "Passed" | "Failed" | "Conditional";
  hasReport: boolean;
  hasEvidence: boolean;
  reportId: string;
  linkedCapa?: string;
  linkedNcr?: string;
}

interface VendorTimelineProps {
  milestones: VendorTimelineMilestone[];
  vendorName: string;
}

const getTypeIcon = (type: string, auditType?: string) => {
  switch (type) {
    case "audit":
      return <Target className="h-5 w-5" />;
    case "capa":
      return <AlertTriangle className="h-5 w-5" />;
    case "follow-up":
      return <Calendar className="h-5 w-5" />;
    case "ncr":
      return <AlertTriangle className="h-5 w-5" />;
    case "approval":
      return <Award className="h-5 w-5" />;
    default:
      return <FileText className="h-5 w-5" />;
  }
};

const getResultColor = (result?: string, score?: number) => {
  if (result === "Passed" || (score && score >= 85)) {
    return "bg-green-100 text-green-800 border-green-200";
  } else if (result === "Failed" || (score && score < 70)) {
    return "bg-red-100 text-red-800 border-red-200";
  } else {
    return "bg-amber-100 text-amber-800 border-amber-200";
  }
};

const getTimelineNodeColor = (result?: string, score?: number) => {
  if (result === "Passed" || (score && score >= 85)) {
    return "border-green-600 bg-green-100";
  } else if (result === "Failed" || (score && score < 70)) {
    return "border-red-600 bg-red-100";
  } else {
    return "border-amber-600 bg-amber-100";
  }
};

export const VendorTimeline: React.FC<VendorTimelineProps> = ({ milestones, vendorName }) => {
  const handleDrillDown = (milestoneId: string) => {
    console.log(`Drilling down into audit: ${milestoneId}`);
    // Navigate to detailed audit view
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Audit Timeline for {vendorName}</h2>
        <p className="text-gray-600">Complete chronological history of all audit events and related activities</p>
      </div>

      <div className="w-full">
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-6 md:left-8 top-0 bottom-0 w-0.5 bg-gray-300"></div>
          
          {/* Timeline Items */}
          <div className="space-y-6 md:space-y-8">
            {milestones.map((milestone, index) => (
              <div key={milestone.id} className="relative flex items-start w-full">
                {/* Timeline Node - Color coded based on result */}
                <div className={`absolute left-5 md:left-7 w-3 h-3 border-2 rounded-full z-10 mt-2 ${getTimelineNodeColor(milestone.result, milestone.score)}`}></div>
                
                {/* Content Card */}
                <div className="ml-12 md:ml-16 w-full">
                  <Card className="w-full shadow-sm hover:shadow-md transition-shadow duration-200 border-l-4 border-l-gray-200">
                    <CardHeader className="pb-3">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="flex items-center gap-3">
                          <div className="text-gray-600">
                            {getTypeIcon(milestone.type, milestone.auditType)}
                          </div>
                          <div>
                            <CardTitle className="text-base font-semibold text-gray-900 flex items-center gap-2">
                              {milestone.title}
                              {milestone.auditType && (
                                <span className="text-sm font-normal text-gray-500">({milestone.auditType})</span>
                              )}
                            </CardTitle>
                            <p className="text-sm text-gray-600 mt-1">{milestone.date}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {milestone.score && (
                            <Badge 
                              variant="outline" 
                              className={`${getResultColor(milestone.result, milestone.score)} text-xs font-medium`}
                            >
                              Score: {milestone.score}%
                            </Badge>
                          )}
                          {milestone.result && (
                            <Badge 
                              variant="outline" 
                              className={`${getResultColor(milestone.result, milestone.score)} text-xs font-medium`}
                            >
                              {milestone.result}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-4">
                        <p className="text-gray-700 text-sm leading-relaxed">
                          {milestone.description}
                        </p>
                        
                        {/* Linked Items */}
                        {(milestone.linkedCapa || milestone.linkedNcr) && (
                          <div className="flex flex-wrap gap-2">
                            {milestone.linkedCapa && (
                              <Badge variant="secondary" className="text-xs">
                                CAPA: {milestone.linkedCapa}
                              </Badge>
                            )}
                            {milestone.linkedNcr && (
                              <Badge variant="secondary" className="text-xs">
                                NCR: {milestone.linkedNcr}
                              </Badge>
                            )}
                          </div>
                        )}
                        
                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-3 pt-2">
                          {milestone.type === "audit" && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleDrillDown(milestone.id)}
                              className="text-blue-600 border-blue-200 hover:bg-blue-50 justify-start sm:justify-center"
                            >
                              <Target className="mr-2 h-4 w-4" />
                              View Audit Details
                            </Button>
                          )}
                          
                          {milestone.hasReport && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="text-gray-600 border-gray-200 hover:bg-gray-50 justify-start sm:justify-center"
                            >
                              <FileText className="mr-2 h-4 w-4" />
                              View Report ({milestone.reportId})
                            </Button>
                          )}
                          
                          {milestone.hasEvidence && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="text-gray-600 border-gray-200 hover:bg-gray-50 justify-start sm:justify-center"
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              View Evidence
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};


import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { SupplierTimeline, TimelineMilestone } from "./SupplierTimeline";
import { VendorTimelineContainer } from "./VendorTimelineContainer";

export const SupplierTimelineContainer: React.FC = () => {
  const [activeView, setActiveView] = useState<"general" | "audit">("general");

  const supplierMilestones: TimelineMilestone[] = [
    {
      id: "milestone-1",
      date: "Jan 2024",
      title: "Onboarded",
      description: "Supplier successfully completed onboarding process",
      icon: "onboarded",
      status: "Completed",
      statusColor: "blue"
    },
    {
      id: "milestone-2",
      date: "Mar 2024",
      title: "Initial Audit",
      description: "First quality audit conducted",
      icon: "audit",
      status: "Passed (87%)",
      statusColor: "green"
    },
    {
      id: "milestone-3",
      date: "Jul 2024",
      title: "1st NCR Logged",
      description: "Non-conformance report for packaging issues",
      icon: "ncr",
      status: "Resolved",
      statusColor: "amber"
    },
    {
      id: "milestone-4",
      date: "Sep 2024",
      title: "CAPA Implemented",
      description: "Corrective and preventive actions completed",
      icon: "capa",
      status: "Verified",
      statusColor: "green"
    },
    {
      id: "milestone-5",
      date: "Nov 2024",
      title: "Quarterly Scorecard",
      description: "Performance evaluation shared with supplier",
      icon: "scorecard",
      status: "Score: 91%",
      statusColor: "green"
    },
    {
      id: "milestone-6",
      date: "Current",
      title: "Approved Supplier",
      description: "Maintained approved status in supplier program",
      icon: "approved",
      status: "Active",
      statusColor: "green"
    }
  ];

  return (
    <div className="w-full space-y-6">
      <div className="flex gap-2">
        <Button
          variant={activeView === "general" ? "default" : "outline"}
          onClick={() => setActiveView("general")}
          size="sm"
        >
          General Timeline
        </Button>
        <Button
          variant={activeView === "audit" ? "default" : "outline"}
          onClick={() => setActiveView("audit")}
          size="sm"
        >
          Audit Timeline
        </Button>
      </div>

      {activeView === "general" ? (
        <SupplierTimeline milestones={supplierMilestones} />
      ) : (
        <VendorTimelineContainer />
      )}
    </div>
  );
};

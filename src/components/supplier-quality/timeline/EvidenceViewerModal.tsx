
import React from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Download, Eye, Image, File } from 'lucide-react';

interface EvidenceViewerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  evidenceId: string | null;
}

const mockEvidence = {
  'timeline-001': {
    title: 'Quality System Audit Evidence',
    documents: [
      { name: 'Audit Checklist.pdf', type: 'pdf', size: '2.3 MB', url: '#' },
      { name: 'Process Photos.zip', type: 'zip', size: '15.7 MB', url: '#' },
      { name: 'Corrective Actions.xlsx', type: 'excel', size: '456 KB', url: '#' },
      { name: 'Certificate ISO9001.pdf', type: 'pdf', size: '1.2 MB', url: '#' }
    ],
    images: [
      { name: 'Production Line 1', url: '/lovable-uploads/143bb4e6-dd7c-41f3-9b80-09442b5622ba.png' },
      { name: 'Quality Control Station', url: '/lovable-uploads/206ab8af-c1cb-4dc3-94a5-bdebe69467c8.png' },
      { name: 'Documentation Area', url: '/lovable-uploads/55d3f27c-4c35-47eb-9aea-174ad8fbad1f.png' }
    ]
  },
  'timeline-002': {
    title: 'CAPA Implementation Evidence',
    documents: [
      { name: 'CAPA Plan.pdf', type: 'pdf', size: '1.8 MB', url: '#' },
      { name: 'Training Records.xlsx', type: 'excel', size: '2.1 MB', url: '#' }
    ],
    images: [
      { name: 'Updated Procedure', url: '/lovable-uploads/143bb4e6-dd7c-41f3-9b80-09442b5622ba.png' }
    ]
  }
};

export const EvidenceViewerModal: React.FC<EvidenceViewerModalProps> = ({
  open,
  onOpenChange,
  evidenceId
}) => {
  const evidenceData = evidenceId ? mockEvidence[evidenceId as keyof typeof mockEvidence] : null;

  if (!evidenceData) return null;

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />;
      case 'excel':
        return <File className="h-5 w-5 text-green-600" />;
      case 'zip':
        return <File className="h-5 w-5 text-yellow-600" />;
      default:
        return <File className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Evidence Viewer</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{evidenceData.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-3">Documents</h4>
                  <div className="space-y-2">
                    {evidenceData.documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getFileIcon(doc.type)}
                          <div>
                            <p className="font-medium text-sm">{doc.name}</p>
                            <p className="text-xs text-gray-500">{doc.size}</p>
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Images</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {evidenceData.images.map((image, index) => (
                      <div key={index} className="space-y-2">
                        <img 
                          src={image.url} 
                          alt={image.name}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        <p className="text-xs text-center text-gray-600">{image.name}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

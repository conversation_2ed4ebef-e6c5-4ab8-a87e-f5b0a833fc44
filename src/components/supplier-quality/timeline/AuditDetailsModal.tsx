
import React from 'react';
import { Di<PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>Circle, AlertTriangle, FileText, User } from 'lucide-react';

interface AuditDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  auditId: string | null;
}

const mockAuditDetails = {
  'timeline-001': {
    title: 'Quality System Audit - TechCorp',
    auditor: '<PERSON>',
    date: '2024-01-20',
    score: 92,
    findings: [
      { type: 'major', description: 'Documentation control needs improvement', status: 'resolved' },
      { type: 'minor', description: 'Training records require updates', status: 'pending' },
      { type: 'observation', description: 'Consider implementing digital workflow', status: 'noted' }
    ],
    recommendations: [
      'Implement document management system',
      'Schedule quarterly training reviews',
      'Consider automation tools'
    ]
  }
};

export const AuditDetailsModal: React.FC<AuditDetailsModalProps> = ({
  open,
  onOpenChange,
  auditId
}) => {
  const auditData = auditId ? mockAuditDetails[auditId as keyof typeof mockAuditDetails] : null;

  if (!auditData) return null;

  const getFindingIcon = (type: string) => {
    switch (type) {
      case 'major':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'minor':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <FileText className="h-4 w-4 text-blue-600" />;
    }
  };

  const getFindingBadge = (type: string) => {
    const variants = {
      'major': 'bg-red-100 text-red-800',
      'minor': 'bg-yellow-100 text-yellow-800',
      'observation': 'bg-blue-100 text-blue-800'
    };
    return <Badge className={variants[type as keyof typeof variants]}>{type}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'resolved': 'bg-green-100 text-green-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'noted': 'bg-gray-100 text-gray-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Audit Details</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{auditData.title}</span>
                <div className="text-3xl font-bold text-green-600">{auditData.score}%</div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-600" />
                  <span className="text-sm">Auditor: {auditData.auditor}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-600" />
                  <span className="text-sm">Date: {auditData.date}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Findings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {auditData.findings.map((finding, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    {getFindingIcon(finding.type)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getFindingBadge(finding.type)}
                        {getStatusBadge(finding.status)}
                      </div>
                      <p className="text-sm">{finding.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {auditData.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">{rec}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

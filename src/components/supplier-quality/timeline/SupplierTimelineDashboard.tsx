
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, CheckCircle, AlertTriangle } from "lucide-react";

export const SupplierTimelineDashboard: React.FC = () => {
  const timelineEvents = [
    {
      id: 1,
      date: "2025-01-15",
      title: "Supplier Audit Completed",
      description: "Annual quality audit for Omega Polymers completed with score of 92%",
      type: "audit",
      status: "completed"
    },
    {
      id: 2,
      date: "2025-01-12",
      title: "CAPA Action Submitted",
      description: "MetalTech Inc. submitted corrective action plan for dimensional issues",
      type: "capa",
      status: "in-progress"
    },
    {
      id: 3,
      date: "2025-01-10",
      title: "Certificate Renewal",
      description: "ISO 9001 certificate renewed for Falcon Alloys",
      type: "certificate",
      status: "completed"
    },
    {
      id: 4,
      date: "2025-01-08",
      title: "Supplier Onboarding",
      description: "New supplier Global Packaging Solutions approved and onboarded",
      type: "onboarding",
      status: "completed"
    },
    {
      id: 5,
      date: "2025-01-05",
      title: "Risk Assessment Update",
      description: "Quarterly risk assessment completed for all Tier 1 suppliers",
      type: "risk",
      status: "completed"
    }
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case "audit":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "capa":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "certificate":
        return <Calendar className="h-4 w-4 text-blue-600" />;
      case "onboarding":
        return <Clock className="h-4 w-4 text-purple-600" />;
      case "risk":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "in-progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Supplier Activity Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {timelineEvents.map((event, index) => (
              <div key={event.id} className="flex items-start gap-4">
                <div className="flex flex-col items-center">
                  <div className="p-2 bg-white border rounded-full">
                    {getEventIcon(event.type)}
                  </div>
                  {index < timelineEvents.length - 1 && (
                    <div className="w-0.5 h-12 bg-gray-200 mt-2" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{event.title}</h4>
                    {getStatusBadge(event.status)}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                  <p className="text-xs text-gray-400">{event.date}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Calendar, FileText, Eye, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { AuditDetailsModal } from './AuditDetailsModal';
import { EvidenceViewerModal } from './EvidenceViewerModal';

const mockTimelineItems = [
  {
    id: 'timeline-001',
    date: '2024-01-20',
    title: 'Quality System Audit - TechCorp',
    type: 'audit',
    status: 'completed',
    hasReport: true,
    hasEvidence: true,
    description: 'Annual quality system audit completed with score of 92%'
  },
  {
    id: 'timeline-002',
    date: '2024-01-18',
    title: 'CAPA Implementation - Global Manufacturing',
    type: 'capa',
    status: 'in-progress',
    hasReport: false,
    hasEvidence: true,
    description: 'Corrective action for calibration procedures in progress'
  },
  {
    id: 'timeline-003',
    date: '2024-01-15',
    title: 'Supplier Assessment - Precision Parts',
    type: 'assessment',
    status: 'scheduled',
    hasReport: false,
    hasEvidence: false,
    description: 'Initial supplier assessment scheduled for next week'
  }
];

export const TimelineView: React.FC = () => {
  const [selectedAudit, setSelectedAudit] = useState<string | null>(null);
  const [selectedEvidence, setSelectedEvidence] = useState<string | null>(null);
  const [timelineItems] = useState(mockTimelineItems);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'in-progress':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'scheduled':
        return <Calendar className="h-5 w-5 text-blue-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'completed': 'bg-green-100 text-green-800',
      'in-progress': 'bg-yellow-100 text-yellow-800',
      'scheduled': 'bg-blue-100 text-blue-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const handleViewAuditDetails = (itemId: string) => {
    setSelectedAudit(itemId);
  };

  const handleViewEvidence = (itemId: string) => {
    setSelectedEvidence(itemId);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Audit Timeline</h3>
        <div className="flex gap-2">
          <Button variant="outline">Filter by Date</Button>
          <Button variant="outline">Export Timeline</Button>
        </div>
      </div>

      <div className="space-y-4">
        {timelineItems.map((item, index) => (
          <Card key={item.id} className="relative">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="flex flex-col items-center">
                    {getStatusIcon(item.status)}
                    {index < timelineItems.length - 1 && (
                      <div className="w-px h-16 bg-gray-200 mt-2" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold">{item.title}</h4>
                      {getStatusBadge(item.status)}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                    <p className="text-xs text-gray-500">{item.date}</p>
                  </div>
                </div>

                <div className="flex gap-2">
                  {item.hasReport && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewAuditDetails(item.id)}
                      className="gap-2"
                    >
                      <FileText className="h-3 w-3" />
                      View Audit Details
                    </Button>
                  )}
                  {item.hasEvidence && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewEvidence(item.id)}
                      className="gap-2"
                    >
                      <Eye className="h-3 w-3" />
                      View Evidence
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Audit Details Modal */}
      <AuditDetailsModal 
        open={!!selectedAudit}
        onOpenChange={() => setSelectedAudit(null)}
        auditId={selectedAudit}
      />

      {/* Evidence Viewer Modal */}
      <EvidenceViewerModal 
        open={!!selectedEvidence}
        onOpenChange={() => setSelectedEvidence(null)}
        evidenceId={selectedEvidence}
      />
    </div>
  );
};

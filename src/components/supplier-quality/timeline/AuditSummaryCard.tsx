
import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Target, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react";

interface AuditSummaryCardProps {
  vendorName: string;
  totalAudits: number;
  averageScore: number;
  lastAuditScore: number;
  openCapas: number;
  trend: "up" | "down" | "stable";
}

export const AuditSummaryCard: React.FC<AuditSummaryCardProps> = ({
  vendorName,
  totalAudits,
  averageScore,
  lastAuditScore,
  openCapas,
  trend
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-600" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-amber-600";
    return "text-red-600";
  };

  return (
    <Card className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5 text-blue-600" />
          Audit Performance Summary - {vendorName}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{totalAudits}</p>
            <p className="text-sm text-gray-600">Total Audits</p>
          </div>
          
          <div className="text-center">
            <p className={`text-2xl font-bold ${getScoreColor(averageScore)}`}>{averageScore}%</p>
            <p className="text-sm text-gray-600">Average Score</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1">
              <p className={`text-2xl font-bold ${getScoreColor(lastAuditScore)}`}>{lastAuditScore}%</p>
              {getTrendIcon()}
            </div>
            <p className="text-sm text-gray-600">Latest Score</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-1">
              <p className="text-2xl font-bold text-gray-900">{openCapas}</p>
              {openCapas === 0 ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              )}
            </div>
            <p className="text-sm text-gray-600">Open CAPAs</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TrendingUp, Activity, Calendar } from "lucide-react";

interface KpiCardsProps {
  auditScore: number;
  openCapas: number;
  expiringCertificates: number;
}

export const KpiCards: React.FC<KpiCardsProps> = ({
  auditScore,
  openCapas,
  expiringCertificates
}) => {
  return (
    <Card className="col-span-1 lg:col-span-2 shadow-sm hover:shadow-md transition-shadow h-fit">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Key Performance Indicators</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-4 rounded-lg bg-blue-50 border border-blue-100 h-20">
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
              <span className="font-medium text-blue-700 text-sm">Avg Audit Score</span>
            </div>
            <span className="text-xl font-bold text-blue-700">{auditScore}%</span>
          </div>
          
          <div className="flex items-center justify-between p-4 rounded-lg bg-purple-50 border border-purple-100 h-20">
            <div className="flex items-center">
              <Activity className="h-5 w-5 text-purple-600 mr-2" />
              <span className="font-medium text-purple-700 text-sm">Open CAPAs</span>
            </div>
            <span className="text-xl font-bold text-purple-700">{openCapas}</span>
          </div>
          
          <div className="flex items-center justify-between p-4 rounded-lg bg-orange-50 border border-orange-100 h-20">
            <div className="flex items-center">
              <Calendar className="h-5 w-5 text-orange-600 mr-2" />
              <span className="font-medium text-orange-700 text-sm">Certificates Expiring (30d)</span>
            </div>
            <span className="text-xl font-bold text-orange-700">{expiringCertificates}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

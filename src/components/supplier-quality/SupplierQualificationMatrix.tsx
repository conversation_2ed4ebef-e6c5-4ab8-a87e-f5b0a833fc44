
import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { SupplierQualification } from "@/types/supplierQualification";
import { useSupplierQualification } from "@/hooks/useSupplierQualification";
import { MatrixFilterBar } from "./MatrixFilterBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileMatrixView } from "./matrix/MobileMatrixView";
import { MatrixTable } from "./matrix/MatrixTable";
import { mockSupplierQualifications } from "@/data/mockSupplierQualifications";

interface SupplierQualificationMatrixProps {
  suppliers?: SupplierQualification[];
}

export const SupplierQualificationMatrix: React.FC<SupplierQualificationMatrixProps> = ({ 
  suppliers = mockSupplierQualifications 
}) => {
  const isMobile = useIsMobile();
  const { 
    suppliers: filteredSuppliers, 
    sortField, 
    sortDirection, 
    handleSort, 
    handleFilterChange 
  } = useSupplierQualification(suppliers);

  const handleStatusChange = (value: string) => {
    handleFilterChange("status", value);
  };

  const handleRiskChange = (value: string) => {
    handleFilterChange("riskScore", value);
  };

  return (
    <div>
      <MatrixFilterBar 
        onStatusChange={handleStatusChange}
        onRiskChange={handleRiskChange}
      />
      
      {isMobile ? (
        <MobileMatrixView suppliers={filteredSuppliers} />
      ) : (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl">Supplier Qualification Matrix</CardTitle>
          </CardHeader>
          <CardContent>
            <MatrixTable 
              suppliers={filteredSuppliers}
              sortField={sortField}
              sortDirection={sortDirection}
              handleSort={handleSort}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from "recharts";
import { BarChart3, TrendingUp, Award, Users } from "lucide-react";

export const ScorecardAnalyticsView: React.FC = () => {
  const scoreData = [
    { month: "Jan", average: 87.2, topPerformer: 95.1, bottomPerformer: 72.3 },
    { month: "Feb", average: 88.1, topPerformer: 96.2, bottomPerformer: 74.1 },
    { month: "Mar", average: 87.8, topPerformer: 94.8, bottomPerformer: 73.9 },
    { month: "Apr", average: 89.2, topPerformer: 97.1, bottomPerformer: 75.2 },
    { month: "May", average: 88.9, topPerformer: 96.8, bottomPerformer: 76.1 },
    { month: "Jun", average: 90.1, topPerformer: 98.2, bottomPerformer: 77.3 }
  ];

  const categoryScores = [
    { category: "Quality", score: 91.2, trend: "up" },
    { category: "Delivery", score: 88.7, trend: "stable" },
    { category: "Service", score: 89.5, trend: "up" },
    { category: "Cost", score: 86.3, trend: "down" },
    { category: "Innovation", score: 84.1, trend: "up" }
  ];

  const topPerformers = [
    { name: "Omega Polymers", score: 98.2, change: "****", category: "Excellent" },
    { name: "FastChip Electronics", score: 96.8, change: "****", category: "Excellent" },
    { name: "Precision Manufacturing", score: 94.7, change: "+0.8", category: "Very Good" },
    { name: "Global Components", score: 93.1, change: "-0.3", category: "Very Good" }
  ];

  const getPerformanceBadge = (category: string) => {
    switch (category) {
      case "Excellent":
        return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
      case "Very Good":
        return <Badge className="bg-blue-100 text-blue-800">Very Good</Badge>;
      case "Good":
        return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
      default:
        return <Badge variant="outline">{category}</Badge>;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Average Score</p>
                <p className="text-2xl font-bold">90.1%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Top Performer</p>
                <p className="text-2xl font-bold text-green-600">98.2%</p>
              </div>
              <Award className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Suppliers</p>
                <p className="text-2xl font-bold">156</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Improvement</p>
                <p className="text-2xl font-bold text-green-600">+3.2%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Scorecard Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={scoreData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[60, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="average" stroke="#3B82F6" strokeWidth={2} name="Average" />
                <Line type="monotone" dataKey="topPerformer" stroke="#10B981" strokeWidth={2} name="Top Performer" />
                <Line type="monotone" dataKey="bottomPerformer" stroke="#EF4444" strokeWidth={2} name="Bottom Performer" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Category Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={categoryScores} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="category" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="score" fill="#8B5CF6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Category Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categoryScores.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="font-medium">{category.category}</div>
                    {getTrendIcon(category.trend)}
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{category.score}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Suppliers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.map((supplier, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-semibold">{supplier.name}</h4>
                    <p className="text-sm text-gray-600">Score: {supplier.score}% ({supplier.change})</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getPerformanceBadge(supplier.category)}
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

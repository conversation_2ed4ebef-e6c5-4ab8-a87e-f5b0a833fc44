
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { SupplierForm } from "@/types/supplierForms";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { SidebarProvider } from "@/components/ui/sidebar";

interface CreateFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateForm: (form: Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

const CreateFormDialogContent: React.FC<CreateFormDialogProps> = ({ 
  open, 
  onOpenChange, 
  onCreateForm 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    dueDate: '',
    template: ''
  });
  
  const templates = [
    { id: 'quality-audit', name: 'Quality Audit Checklist' },
    { id: 'environmental', name: 'Environmental Compliance Survey' },
    { id: 'supplier-self-audit', name: 'Supplier Self-Audit Form' },
    { id: 'process-validation', name: 'Process Validation Questionnaire' },
  ];
  
  const handleCreateForm = () => {
    if (!formData.name.trim()) return;
    
    const sections = [];
    
    // In a real app, we would add template-specific sections and questions
    if (formData.template === 'quality-audit') {
      sections.push({
        id: `section-${Date.now()}`,
        title: 'Quality Management System',
        description: 'Information about your quality management system',
        questions: [
          {
            id: `q-${Date.now()}-1`,
            type: 'yesno',
            text: 'Do you have an ISO 9001 certified quality management system?',
            required: true
          }
        ]
      });
    }
    
    onCreateForm({
      name: formData.name,
      description: formData.description,
      dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : undefined,
      sections: sections,
      recipientCount: 0,
      status: {
        completed: 0,
        pending: 0,
        overdue: 0
      }
    });
    
    setFormData({
      name: '',
      description: '',
      dueDate: '',
      template: ''
    });
    
    onOpenChange(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Form</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="form-name">Form Name</Label>
            <Input 
              id="form-name" 
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              placeholder="Enter form name..."
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="form-description">Description</Label>
            <Textarea 
              id="form-description" 
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Enter form description..."
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="form-due-date">Due Date</Label>
            <Input 
              id="form-due-date" 
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData({...formData, dueDate: e.target.value})}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="form-template">Start from Template (Optional)</Label>
            <Select 
              value={formData.template} 
              onValueChange={(value) => setFormData({...formData, template: value})}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a template..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Blank Form</SelectItem>
                {templates.map(template => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleCreateForm} disabled={!formData.name.trim()}>
            Create Form
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export const CreateFormDialog: React.FC<CreateFormDialogProps> = (props) => {
  return (
    <SidebarProvider>
      <CreateFormDialogContent {...props} />
    </SidebarProvider>
  );
};


import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { SupplierForm, FormResponse, FormQuestion } from "@/types/supplierForms";
import { 
  <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer, Cell, PieChart, Pie 
} from "recharts";
import { Badge } from '@/components/ui/badge';

interface ResponseAnalyticsProps {
  form: SupplierForm;
  responses: FormResponse[];
}

export const ResponseAnalytics: React.FC<ResponseAnalyticsProps> = ({ form, responses }) => {
  const completedCount = responses.filter(r => r.status === 'completed').length;
  const pendingCount = responses.filter(r => r.status === 'pending').length;
  const overdueCount = responses.filter(r => r.status === 'overdue').length;
  
  const statusData = [
    { name: 'Completed', value: completedCount, color: '#10b981' },
    { name: 'Pending', value: pendingCount, color: '#f59e0b' },
    { name: 'Overdue', value: overdueCount, color: '#ef4444' },
  ];
  
  const getQuestionResponseData = (questionId: string) => {
    const completedResponses = responses.filter(r => r.status === 'completed');
    
    // For yes/no questions
    const findQuestion = (questionId: string): FormQuestion | undefined => {
      for (const section of form.sections) {
        const question = section.questions.find(q => q.id === questionId);
        if (question) return question;
      }
      return undefined;
    };
    
    const question = findQuestion(questionId);
    
    if (!question) return [];
    
    if (question.type === 'yesno') {
      const yesCount = completedResponses.filter(r => r.answers[questionId] === true).length;
      const noCount = completedResponses.filter(r => r.answers[questionId] === false).length;
      
      return [
        { name: 'Yes', value: yesCount, color: '#10b981' },
        { name: 'No', value: noCount, color: '#ef4444' },
      ];
    }
    
    if (question.type === 'select' || question.type === 'radio') {
      const optionCounts: Record<string, number> = {};
      
      if (question.options) {
        question.options.forEach(option => {
          optionCounts[option] = 0;
        });
      }
      
      completedResponses.forEach(response => {
        const answer = response.answers[questionId];
        if (answer && typeof answer === 'string') {
          optionCounts[answer] = (optionCounts[answer] || 0) + 1;
        }
      });
      
      return Object.entries(optionCounts).map(([name, value], index) => {
        const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981', '#0ea5e9'];
        return { name, value, color: colors[index % colors.length] };
      });
    }
    
    if (question.type === 'checkbox' && question.options) {
      const optionCounts: Record<string, number> = {};
      
      question.options.forEach(option => {
        optionCounts[option] = 0;
      });
      
      completedResponses.forEach(response => {
        const answer = response.answers[questionId];
        if (Array.isArray(answer)) {
          answer.forEach(option => {
            optionCounts[option] = (optionCounts[option] || 0) + 1;
          });
        }
      });
      
      return Object.entries(optionCounts).map(([name, value], index) => {
        const colors = ['#3b82f6', '#8b5cf6', '#ec4899', '#f97316', '#10b981', '#0ea5e9'];
        return { name, value, color: colors[index % colors.length] };
      });
    }
    
    return [];
  };
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Response Analytics</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Response Status</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="flex flex-wrap justify-center gap-2 mt-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                {completedCount} completed
              </Badge>
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                {pendingCount} pending
              </Badge>
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                {overdueCount} overdue
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        {form.sections.flatMap(section => 
          section.questions
            .filter(q => ['yesno', 'select', 'radio', 'checkbox'].includes(q.type))
            .filter(q => getQuestionResponseData(q.id).some(item => item.value > 0))
            .map(question => (
              <Card key={question.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">{question.text}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="h-[200px]">
                    <ResponsiveContainer width="100%" height="100%">
                      {['select', 'radio', 'checkbox'].includes(question.type) ? (
                        <BarChart
                          data={getQuestionResponseData(question.id)}
                          layout="vertical"
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="name" type="category" width={100} />
                          <Tooltip />
                          <Bar dataKey="value">
                            {getQuestionResponseData(question.id).map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Bar>
                        </BarChart>
                      ) : (
                        <PieChart>
                          <Pie
                            data={getQuestionResponseData(question.id)}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          >
                            {getQuestionResponseData(question.id).map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      )}
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            ))
        )}
      </div>
    </div>
  );
};

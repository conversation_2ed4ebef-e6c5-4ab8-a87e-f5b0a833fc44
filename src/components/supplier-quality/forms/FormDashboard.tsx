
import React, { useState } from 'react';
import { FormsList } from './FormsList';
import { FormDetail } from './FormDetail';
import { CreateFormDialog } from './CreateFormDialog';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useSupplierForms } from '@/hooks/useSupplierForms';
import { useToast } from '@/hooks/use-toast';
import { SupplierForm } from '@/types/supplierForms';
import { Link } from 'react-router-dom';

export const FormDashboard: React.FC = () => {
  const {
    forms,
    selectedForm,
    selectedFormId,
    formResponses,
    formRecipients,
    setSelectedFormId,
    createForm,
    updateForm,
    deleteForm,
    sendForm
  } = useSupplierForms();
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const { toast } = useToast();
  
  const handleEditForm = (id: string) => {
    setSelectedFormId(id);
  };
  
  const handleDeleteForm = (id: string) => {
    deleteForm(id);
    toast({
      title: 'Form Deleted',
      description: 'The form has been successfully deleted.'
    });
  };
  
  const handleSendForm = (id: string) => {
    // Fix: Pass an empty array as the second argument to match the function signature
    sendForm(id, []);
  };
  
  const handleCreateForm = (formData: Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'>) => {
    const formId = createForm(formData);
    setSelectedFormId(formId);
  };
  
  return (
    <div>
      {selectedForm ? (
        <FormDetail
          form={selectedForm}
          onBack={() => setSelectedFormId(null)}
        />
      ) : (
        <div className="space-y-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Forms Dashboard</h2>
            <Link to="/form-builder">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create New Form
              </Button>
            </Link>
          </div>
          
          <FormsList
            forms={forms}
            onSelectForm={setSelectedFormId}
            onEditForm={handleEditForm}
            onDeleteForm={handleDeleteForm}
            onSendForm={handleSendForm}
          />
        </div>
      )}
      
      <CreateFormDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onCreateForm={handleCreateForm}
      />
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Calendar,
  FileText,
  Download,
  Eye,
  Edit,
  Copy,
  Trash2,
  Mail,
  Link,
  BarChart3,
  Settings,
  ChevronLeft
} from 'lucide-react';
import { SupplierForm, FormResponse, FormRecipient } from '@/types/supplierForms';
import { mockFormResponses } from '@/data/mockSupplierForms';
import { ResponseDetailViewer } from './ResponseDetailViewer';

interface FormDetailProps {
  form: SupplierForm;
  onBack: () => void;
}

export const FormDetail: React.FC<FormDetailProps> = ({ form, onBack }) => {
  const [activeTab, setActiveTab] = useState('responses');
  const [selectedResponse, setSelectedResponse] = useState<FormResponse | null>(null);
  const [showResponseViewer, setShowResponseViewer] = useState(false);

  const mockResponses = mockFormResponses.filter(r => r.formId === form.id);

  const getStatusBadge = (status: string) => {
    const variants = {
      'completed': 'bg-green-100 text-green-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'overdue': 'bg-red-100 text-red-800'
    };
    return <Badge className={variants[status] || 'bg-gray-100 text-gray-800'}>{status}</Badge>;
  };

  const handleViewResponse = (response: FormResponse) => {
    setSelectedResponse(response);
    setShowResponseViewer(true);
  };

  const handleExportResponse = (format: 'pdf' | 'excel') => {
    console.log(`Exporting response in ${format} format`);
    // Implement export functionality
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack} className="gap-2">
            <ChevronLeft className="h-4 w-4" />
            Back to Forms
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{form.name}</h1>
            <p className="text-gray-600">{form.description}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Mail className="h-4 w-4" />
            Send Form
          </Button>
          <Button variant="outline" className="gap-2">
            <Link className="h-4 w-4" />
            Copy Link
          </Button>
          <Button variant="outline" className="gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Form Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium">Created At</p>
              <p className="text-sm text-gray-600">{new Date(form.createdAt).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Recipients</p>
              <p className="text-sm text-gray-600">{form.recipientCount}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Status</p>
              <div className="flex gap-1">
                {getStatusBadge(`Completed: ${form.status.completed}`)}
                {getStatusBadge(`Pending: ${form.status.pending}`)}
                {getStatusBadge(`Overdue: ${form.status.overdue}`)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="responses">Responses</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="edit">Edit Form</TabsTrigger>
        </TabsList>

        <TabsContent value="responses">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Responses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockResponses.map((response) => (
                  <div key={response.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium">{response.supplierName}</h4>
                        {getStatusBadge(response.status)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {response.submittedAt ? (
                          <p>Submitted: {new Date(response.submittedAt).toLocaleDateString()}</p>
                        ) : (
                          <p>Pending submission</p>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewResponse(response)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Form Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Analytics content here</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="edit">
          <Card>
            <CardHeader>
              <CardTitle>Edit Form</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Edit form content here</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Response Detail Viewer */}
      {selectedResponse && (
        <ResponseDetailViewer
          response={selectedResponse}
          formName={form.name}
          isOpen={showResponseViewer}
          onClose={() => setShowResponseViewer(false)}
          onExport={handleExportResponse}
        />
      )}
    </div>
  );
};

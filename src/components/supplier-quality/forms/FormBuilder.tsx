
import React, { useState, useRef } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  ChevronLeft, Plus, Trash, GripVertical, 
  AlignLeft, CheckSquare, FileText, ListChecks, 
  ToggleLeft, List, Save, ChevronDown, ChevronUp 
} from "lucide-react";
import { SupplierForm, FormSection, FormQuestion, QuestionType } from "@/types/supplierForms";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface FormBuilderProps {
  form: SupplierForm | null;
  onBack: () => void;
  onSave: (form: Partial<SupplierForm>) => void;
}

export const FormBuilder: React.FC<FormBuilderProps> = ({ form, onBack, onSave }) => {
  const [formData, setFormData] = useState<Partial<SupplierForm>>(form || {
    name: '',
    description: '',
    sections: [],
    recipientCount: 0,
    status: { completed: 0, pending: 0, overdue: 0 }
  });
  
  const [expandedSection, setExpandedSection] = useState<string | null>(
    formData.sections && formData.sections.length > 0 ? formData.sections[0].id : null
  );
  
  const [newQuestionType, setNewQuestionType] = useState<QuestionType>('text');
  const [addingSectionId, setAddingSectionId] = useState<string | null>(null);
  const [isAddingSection, setIsAddingSection] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState('');
  const [newSectionDescription, setNewSectionDescription] = useState('');
  
  const handleSave = () => {
    onSave(formData);
  };
  
  const addSection = () => {
    if (!newSectionTitle.trim()) return;
    
    const newSection: FormSection = {
      id: `section-${Date.now()}`,
      title: newSectionTitle,
      description: newSectionDescription || undefined,
      questions: []
    };
    
    setFormData(prev => ({
      ...prev,
      sections: [...(prev.sections || []), newSection]
    }));
    
    setExpandedSection(newSection.id);
    setIsAddingSection(false);
    setNewSectionTitle('');
    setNewSectionDescription('');
  };
  
  const deleteSection = (sectionId: string) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.filter(section => section.id !== sectionId) || []
    }));
  };
  
  const addQuestion = (sectionId: string) => {
    const newQuestion: FormQuestion = {
      id: `question-${Date.now()}`,
      type: newQuestionType,
      text: '',
      required: false,
      options: newQuestionType === 'select' || newQuestionType === 'radio' || newQuestionType === 'checkbox' 
        ? ['Option 1'] 
        : undefined
    };
    
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: [...section.questions, newQuestion]
          };
        }
        return section;
      }) || []
    }));
    
    setAddingSectionId(null);
  };
  
  const deleteQuestion = (sectionId: string, questionId: string) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.filter(q => q.id !== questionId)
          };
        }
        return section;
      }) || []
    }));
  };
  
  const updateQuestion = (sectionId: string, questionId: string, updates: Partial<FormQuestion>) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId) {
                return { ...q, ...updates };
              }
              return q;
            })
          };
        }
        return section;
      }) || []
    }));
  };
  
  const addOptionToQuestion = (sectionId: string, questionId: string) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId) {
                return { 
                  ...q, 
                  options: [...(q.options || []), `Option ${(q.options?.length || 0) + 1}`]
                };
              }
              return q;
            })
          };
        }
        return section;
      }) || []
    }));
  };
  
  const updateOption = (sectionId: string, questionId: string, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId && q.options) {
                const newOptions = [...q.options];
                newOptions[index] = value;
                return { ...q, options: newOptions };
              }
              return q;
            })
          };
        }
        return section;
      }) || []
    }));
  };
  
  const deleteOption = (sectionId: string, questionId: string, index: number) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections?.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            questions: section.questions.map(q => {
              if (q.id === questionId && q.options) {
                const newOptions = [...q.options];
                newOptions.splice(index, 1);
                return { ...q, options: newOptions };
              }
              return q;
            })
          };
        }
        return section;
      }) || []
    }));
  };
  
  const getQuestionTypeIcon = (type: QuestionType) => {
    switch (type) {
      case 'text': return <AlignLeft className="h-5 w-5 mr-2" />;
      case 'textarea': return <FileText className="h-5 w-5 mr-2" />;
      case 'select': return <List className="h-5 w-5 mr-2" />;
      case 'radio': return <ListChecks className="h-5 w-5 mr-2" />;
      case 'checkbox': return <CheckSquare className="h-5 w-5 mr-2" />;
      case 'file': return <FileText className="h-5 w-5 mr-2" />;
      case 'yesno': return <ToggleLeft className="h-5 w-5 mr-2" />;
      default: return <AlignLeft className="h-5 w-5 mr-2" />;
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack} className="flex items-center gap-1">
          <ChevronLeft className="h-4 w-4" />
          Back to Forms
        </Button>
        <Button onClick={handleSave} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          Save Form
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Form Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="form-name">Form Name</Label>
            <Input 
              id="form-name" 
              value={formData.name || ''} 
              onChange={e => setFormData({...formData, name: e.target.value})}
              placeholder="Enter form name..."
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="form-description">Description</Label>
            <Textarea 
              id="form-description" 
              value={formData.description || ''} 
              onChange={e => setFormData({...formData, description: e.target.value})}
              placeholder="Enter form description..."
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="form-due-date">Due Date</Label>
            <Input 
              id="form-due-date" 
              type="date"
              value={formData.dueDate ? new Date(formData.dueDate).toISOString().split('T')[0] : ''} 
              onChange={e => setFormData({...formData, dueDate: new Date(e.target.value).toISOString()})}
            />
          </div>
        </CardContent>
      </Card>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Form Sections</h3>
          <Button onClick={() => setIsAddingSection(true)} size="sm" className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            Add Section
          </Button>
        </div>
        
        {formData.sections?.map((section, sectionIndex) => (
          <Card key={section.id} className="border-2 border-dashed border-gray-200 hover:border-gray-300 transition-colors">
            <CardHeader className="cursor-pointer bg-gray-50" onClick={() => setExpandedSection(expandedSection === section.id ? null : section.id)}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GripVertical className="h-5 w-5 text-gray-400" />
                  <CardTitle className="text-lg">{section.title || `Section ${sectionIndex + 1}`}</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={(e) => { e.stopPropagation(); deleteSection(section.id); }}
                  >
                    <Trash className="h-4 w-4 text-red-500" />
                  </Button>
                  {expandedSection === section.id ? 
                    <ChevronUp className="h-5 w-5" /> : 
                    <ChevronDown className="h-5 w-5" />
                  }
                </div>
              </div>
              {section.description && (
                <CardDescription>{section.description}</CardDescription>
              )}
            </CardHeader>
            
            {expandedSection === section.id && (
              <CardContent className="pt-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor={`section-title-${section.id}`}>Section Title</Label>
                    <Input 
                      id={`section-title-${section.id}`}
                      value={section.title} 
                      onChange={e => {
                        setFormData({
                          ...formData,
                          sections: formData.sections?.map(s => 
                            s.id === section.id ? { ...s, title: e.target.value } : s
                          )
                        });
                      }}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor={`section-desc-${section.id}`}>Description (Optional)</Label>
                    <Textarea 
                      id={`section-desc-${section.id}`}
                      value={section.description || ''} 
                      onChange={e => {
                        setFormData({
                          ...formData,
                          sections: formData.sections?.map(s => 
                            s.id === section.id ? { ...s, description: e.target.value } : s
                          )
                        });
                      }}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Questions</Label>
                      <Button 
                        onClick={() => setAddingSectionId(addingSectionId === section.id ? null : section.id)} 
                        size="sm" 
                        variant={addingSectionId === section.id ? "default" : "outline"}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        Add Question
                      </Button>
                    </div>
                    
                    {addingSectionId === section.id && (
                      <Card className="p-4 border border-dashed">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          <Button 
                            variant="outline" 
                            className={`flex items-center justify-start ${newQuestionType === 'text' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('text')}
                          >
                            <AlignLeft className="h-4 w-4 mr-2" />
                            Text
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'textarea' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('textarea')}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            Paragraph
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'select' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('select')}
                          >
                            <List className="h-4 w-4 mr-2" />
                            Dropdown
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'radio' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('radio')}
                          >
                            <ListChecks className="h-4 w-4 mr-2" />
                            Radio
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'checkbox' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('checkbox')}
                          >
                            <CheckSquare className="h-4 w-4 mr-2" />
                            Checkbox
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'file' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('file')}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            File Upload
                          </Button>
                          <Button 
                            variant="outline"
                            className={`flex items-center justify-start ${newQuestionType === 'yesno' ? 'bg-blue-50 border-blue-300' : ''}`}
                            onClick={() => setNewQuestionType('yesno')}
                          >
                            <ToggleLeft className="h-4 w-4 mr-2" />
                            Yes/No
                          </Button>
                        </div>
                        
                        <div className="flex justify-end mt-4">
                          <Button 
                            onClick={() => addQuestion(section.id)}
                            size="sm"
                          >
                            Add
                          </Button>
                        </div>
                      </Card>
                    )}
                    
                    <div className="space-y-4 mt-4">
                      {section.questions.map((question, qIndex) => (
                        <Card key={question.id} className="p-4 border border-dashed">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              <GripVertical className="h-5 w-5 text-gray-400" />
                              {getQuestionTypeIcon(question.type)}
                              <span className="text-sm font-medium">
                                {question.text || `Question ${qIndex + 1}`}
                              </span>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => deleteQuestion(section.id, question.id)}
                            >
                              <Trash className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                          
                          <div className="mt-4 space-y-3">
                            <div className="space-y-1">
                              <Label htmlFor={`question-text-${question.id}`}>Question Text</Label>
                              <Input 
                                id={`question-text-${question.id}`}
                                value={question.text} 
                                onChange={(e) => updateQuestion(section.id, question.id, { text: e.target.value })}
                                placeholder="Enter your question..."
                              />
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <Checkbox 
                                id={`required-${question.id}`}
                                checked={question.required} 
                                onCheckedChange={(checked) => 
                                  updateQuestion(section.id, question.id, { required: !!checked })
                                }
                              />
                              <Label htmlFor={`required-${question.id}`}>Required</Label>
                            </div>
                            
                            {(question.type === 'select' || question.type === 'radio' || question.type === 'checkbox') && (
                              <div className="space-y-2">
                                <Label>Options</Label>
                                {question.options?.map((option, optIndex) => (
                                  <div key={optIndex} className="flex items-center gap-2">
                                    <Input 
                                      value={option}
                                      onChange={(e) => updateOption(section.id, question.id, optIndex, e.target.value)}
                                      placeholder={`Option ${optIndex + 1}`}
                                    />
                                    <Button 
                                      variant="ghost" 
                                      size="sm"
                                      onClick={() => deleteOption(section.id, question.id, optIndex)}
                                      disabled={question.options?.length === 1}
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                                
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => addOptionToQuestion(section.id, question.id)}
                                  className="mt-2"
                                >
                                  <Plus className="h-4 w-4 mr-1" />
                                  Add Option
                                </Button>
                              </div>
                            )}
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>
      
      <Dialog open={isAddingSection} onOpenChange={setIsAddingSection}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Section</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="section-title">Section Title</Label>
              <Input 
                id="section-title" 
                value={newSectionTitle}
                onChange={(e) => setNewSectionTitle(e.target.value)}
                placeholder="Enter section title..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="section-description">Description (Optional)</Label>
              <Textarea 
                id="section-description" 
                value={newSectionDescription}
                onChange={(e) => setNewSectionDescription(e.target.value)}
                placeholder="Enter section description..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingSection(false)}>Cancel</Button>
            <Button onClick={addSection} disabled={!newSectionTitle.trim()}>Add Section</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

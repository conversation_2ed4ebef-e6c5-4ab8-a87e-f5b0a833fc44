
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  User, 
  Calendar, 
  FileText, 
  Download, 
  Eye, 
  MessageSquare,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { FormResponse } from '@/types/supplierForms';

interface ResponseDetailViewerProps {
  response: FormResponse;
  formName: string;
  isOpen: boolean;
  onClose: () => void;
  onExport?: (format: 'pdf' | 'excel') => void;
}

interface ComparisonResponse {
  id: string;
  supplierName: string;
  submittedAt: string;
  answers: Record<string, any>;
}

export const ResponseDetailViewer: React.FC<ResponseDetailViewerProps> = ({
  response,
  formName,
  isOpen,
  onClose,
  onExport
}) => {
  const [activeTab, setActiveTab] = useState('details');
  const [showComparison, setShowComparison] = useState(false);
  
  // Mock comparison data - in real app, this would come from props or API
  const comparisonResponses: ComparisonResponse[] = [
    {
      id: 'resp-2',
      supplierName: 'TechCorp Manufacturing',
      submittedAt: '2024-01-18T14:30:00Z',
      answers: response.answers // Simplified for demo
    }
  ];

  const formatAnswer = (answer: any, questionType?: string) => {
    if (answer === null || answer === undefined) {
      return <span className="text-gray-400 italic">No response</span>;
    }

    if (typeof answer === 'boolean') {
      return answer ? (
        <Badge className="bg-green-100 text-green-800">Yes</Badge>
      ) : (
        <Badge className="bg-red-100 text-red-800">No</Badge>
      );
    }

    if (Array.isArray(answer)) {
      return (
        <div className="flex flex-wrap gap-1">
          {answer.map((item, index) => (
            <Badge key={index} variant="outline">{item}</Badge>
          ))}
        </div>
      );
    }

    if (typeof answer === 'string' && answer.startsWith('http')) {
      return (
        <Button variant="outline" size="sm" asChild>
          <a href={answer} target="_blank" rel="noopener noreferrer">
            <FileText className="h-3 w-3 mr-1" />
            View File
          </a>
        </Button>
      );
    }

    return <span>{String(answer)}</span>;
  };

  const getResponseScore = () => {
    const answers = Object.values(response.answers);
    const yesAnswers = answers.filter(answer => answer === true || answer === 'Yes').length;
    const totalAnswers = answers.length;
    return totalAnswers > 0 ? Math.round((yesAnswers / totalAnswers) * 100) : 0;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Response Details - {response.supplierName}
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details">Response Details</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>

          <div className="mt-4 overflow-y-auto max-h-[calc(90vh-200px)]">
            <TabsContent value="details" className="space-y-6">
              {/* Response Header */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <h3 className="text-lg font-semibold">{response.supplierName}</h3>
                        <p className="text-sm text-gray-600">{formName}</p>
                      </div>
                      <Badge className={getStatusColor(response.status)}>
                        {response.status}
                      </Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onExport?.('pdf')}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        PDF
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onExport?.('excel')}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Excel
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Submitted</p>
                        <p className="text-sm text-gray-600">
                          {response.submittedAt ? new Date(response.submittedAt).toLocaleDateString() : 'Pending'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Supplier</p>
                        <p className="text-sm text-gray-600">{response.supplierName}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Completion</p>
                        <p className="text-sm text-gray-600">{getResponseScore()}% Complete</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Response Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Responses</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {Object.entries(response.answers).map(([questionId, answer]) => (
                    <div key={questionId} className="border-b pb-4 last:border-b-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="font-medium mb-2">
                            Question {questionId.replace('q-', '')}
                          </p>
                          <div className="text-sm text-gray-600 mb-2">
                            {/* In real app, this would be the actual question text */}
                            Sample question text for {questionId}
                          </div>
                          <div>
                            {formatAnswer(answer)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analysis" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Response Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-4">Response Summary</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span>Total Questions:</span>
                          <span className="font-medium">{Object.keys(response.answers).length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Answered:</span>
                          <span className="font-medium text-green-600">
                            {Object.values(response.answers).filter(a => a !== null && a !== undefined).length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Positive Responses:</span>
                          <span className="font-medium text-blue-600">
                            {Object.values(response.answers).filter(a => a === true || a === 'Yes').length}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Overall Score:</span>
                          <span className="font-medium">{getResponseScore()}%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-4">Risk Indicators</h4>
                      <div className="space-y-2">
                        {Object.values(response.answers).filter(a => a === false || a === 'No').length > 0 && (
                          <div className="flex items-center gap-2 text-red-600">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm">Negative responses detected</span>
                          </div>
                        )}
                        {!response.submittedAt && (
                          <div className="flex items-center gap-2 text-yellow-600">
                            <Clock className="h-4 w-4" />
                            <span className="text-sm">Response pending</span>
                          </div>
                        )}
                        {getResponseScore() >= 80 && (
                          <div className="flex items-center gap-2 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">High compliance score</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comparison" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Response Comparison</CardTitle>
                  <p className="text-sm text-gray-600">
                    Compare this response with other suppliers
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">Question</th>
                          <th className="text-left p-2">{response.supplierName}</th>
                          {comparisonResponses.map(comp => (
                            <th key={comp.id} className="text-left p-2">{comp.supplierName}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(response.answers).slice(0, 5).map(([questionId, answer]) => (
                          <tr key={questionId} className="border-b">
                            <td className="p-2 font-medium">Q{questionId.replace('q-', '')}</td>
                            <td className="p-2">{formatAnswer(answer)}</td>
                            {comparisonResponses.map(comp => (
                              <td key={comp.id} className="p-2">
                                {formatAnswer(comp.answers[questionId])}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="timeline" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Response Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <MessageSquare className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">Form Sent</p>
                        <p className="text-sm text-gray-600">Form was sent to supplier</p>
                        <p className="text-xs text-gray-500">2 days ago</p>
                      </div>
                    </div>
                    
                    {response.submittedAt && (
                      <div className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">Response Submitted</p>
                          <p className="text-sm text-gray-600">Supplier completed the form</p>
                          <p className="text-xs text-gray-500">
                            {new Date(response.submittedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

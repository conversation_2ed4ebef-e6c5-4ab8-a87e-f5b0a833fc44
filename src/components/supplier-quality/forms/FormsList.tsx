
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Edit, Trash, Send, ChevronRight } from "lucide-react";
import { SupplierForm } from "@/types/supplierForms";
import { format } from "date-fns";

interface FormsListProps {
  forms: SupplierForm[];
  onSelectForm: (id: string) => void;
  onEditForm: (id: string) => void;
  onDeleteForm: (id: string) => void;
  onSendForm: (id: string) => void;
}

export const FormsList: React.FC<FormsListProps> = ({ 
  forms, 
  onSelectForm, 
  onEditForm, 
  onDeleteForm,
  onSendForm
}) => {
  return (
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Form Name</TableHead>
              <TableHead>Recipients</TableHead>
              <TableHead>Responses</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {forms.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <p className="text-gray-500">No forms created yet</p>
                </TableCell>
              </TableRow>
            ) : (
              forms.map(form => (
                <TableRow key={form.id} className="cursor-pointer hover:bg-gray-50" onClick={() => onSelectForm(form.id)}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      {form.name}
                      <ChevronRight className="ml-2 h-4 w-4 text-gray-400" />
                    </div>
                    <p className="text-sm text-gray-500 mt-1">{form.description}</p>
                  </TableCell>
                  <TableCell>{form.recipientCount}</TableCell>
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {form.status.completed} completed
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                          {form.status.pending} pending
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          {form.status.overdue} overdue
                        </Badge>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {form.dueDate ? format(new Date(form.dueDate), 'MMM d, yyyy') : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2" onClick={e => e.stopPropagation()}>
                      <Button variant="ghost" size="sm" onClick={() => onEditForm(form.id)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => onSendForm(form.id)}>
                        <Send className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => onDeleteForm(form.id)}>
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};


import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { FormRecipient } from "@/types/supplierForms";
import { Button } from '@/components/ui/button';
import { Mail, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface RecipientsListProps {
  recipients: FormRecipient[];
  onSendReminder: (recipientId: string) => void;
}

export const RecipientsList: React.FC<RecipientsListProps> = ({ 
  recipients,
  onSendReminder
}) => {
  const [showAddRecipient, setShowAddRecipient] = useState(false);
  const [newRecipient, setNewRecipient] = useState({
    name: '',
    email: '',
    company: ''
  });
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-amber-500" />;
      case 'overdue': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return null;
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      case 'pending':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pending</Badge>;
      case 'overdue':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Overdue</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <>
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Recipient</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recipients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    <p className="text-gray-500">No recipients added</p>
                  </TableCell>
                </TableRow>
              ) : (
                recipients.map(recipient => (
                  <TableRow key={recipient.id}>
                    <TableCell>
                      <div className="font-medium">{recipient.name}</div>
                      <div className="text-sm text-gray-500">{recipient.email}</div>
                    </TableCell>
                    <TableCell>{recipient.company}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(recipient.status)}
                        {getStatusBadge(recipient.status)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {recipient.status !== 'completed' && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => onSendReminder(recipient.id)}
                          className="flex items-center gap-1"
                        >
                          <Mail className="h-4 w-4" />
                          Send Reminder
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <Dialog open={showAddRecipient} onOpenChange={setShowAddRecipient}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Recipient</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="recipient-name">Name</Label>
              <Input 
                id="recipient-name" 
                value={newRecipient.name}
                onChange={(e) => setNewRecipient({...newRecipient, name: e.target.value})}
                placeholder="Enter recipient name..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="recipient-email">Email</Label>
              <Input 
                id="recipient-email" 
                type="email"
                value={newRecipient.email}
                onChange={(e) => setNewRecipient({...newRecipient, email: e.target.value})}
                placeholder="Enter recipient email..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="recipient-company">Company</Label>
              <Input 
                id="recipient-company" 
                value={newRecipient.company}
                onChange={(e) => setNewRecipient({...newRecipient, company: e.target.value})}
                placeholder="Enter company name..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddRecipient(false)}>Cancel</Button>
            <Button onClick={() => {
              // Add recipient logic would go here in a real app
              setShowAddRecipient(false);
            }}>Add Recipient</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Building, MapPin, Phone, Mail, Calendar, TrendingUp, MessageSquare, User } from "lucide-react";
import { SRMWorkspace } from "./srm/SRMWorkspace";
import { SupplierCommunicationView } from "./SupplierCommunicationView";
import { mockSupplierRelationships } from "@/data/mockSupplierRelationships";

type ViewMode = 'list' | 'details' | 'communication';

export const SupplierManagement: React.FC = () => {
  const [selectedSupplierId, setSelectedSupplierId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [suppliers] = useState(mockSupplierRelationships);

  const handleRowClick = (supplierId: string) => {
    setSelectedSupplierId(supplierId);
    setViewMode('details');
  };

  const handleViewCommunication = (supplierId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click when clicking the Messages button
    setSelectedSupplierId(supplierId);
    setViewMode('communication');
  };

  const handleBackToList = () => {
    setSelectedSupplierId(null);
    setViewMode('list');
  };

  // If a supplier is selected, show the appropriate view
  if (selectedSupplierId && viewMode !== 'list') {
    const selectedSupplier = suppliers.find(s => s.id === selectedSupplierId);
    if (selectedSupplier) {
      if (viewMode === 'communication') {
        return (
          <SupplierCommunicationView 
            supplier={selectedSupplier} 
            onBack={handleBackToList}
          />
        );
      } else {
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button 
                variant="outline" 
                onClick={handleBackToList}
              >
                ← Back to Suppliers
              </Button>
            </div>
            <SRMWorkspace supplier={selectedSupplier} />
          </div>
        );
      }
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      'Active': 'bg-green-100 text-green-800',
      'Inactive': 'bg-red-100 text-red-800',
      'Pending': 'bg-yellow-100 text-yellow-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getRiskBadge = (overallScore: number) => {
    if (overallScore >= 90) return <Badge className="bg-green-100 text-green-800">Low</Badge>;
    if (overallScore >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    return <Badge className="bg-red-100 text-red-800">High</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Supplier Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Risk Level</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Communications</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suppliers.map((supplier) => (
                <TableRow 
                  key={supplier.id}
                  className="cursor-pointer hover:bg-slate-50"
                  onClick={() => handleRowClick(supplier.id)}
                >
                  <TableCell>
                    <div>
                      <div className="font-medium">{supplier.name}</div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        {supplier.category}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {supplier.contact.name}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {supplier.contact.email}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {supplier.contact.phone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(supplier.status)}</TableCell>
                  <TableCell>{getRiskBadge(supplier.overallScore)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="font-medium">{supplier.overallScore}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">{supplier.communications.length} messages</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={(e) => handleViewCommunication(supplier.id, e)}
                        className="bg-blue-50 hover:bg-blue-100 text-blue-700"
                      >
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Messages
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

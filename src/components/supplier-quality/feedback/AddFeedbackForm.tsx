
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { SupplierFeedback, FeedbackTag, FeedbackSentiment } from "@/types/supplierFeedback";
import { feedbackFormSchema, FeedbackFormValues } from "./feedbackFormSchema";
import { RatingField } from "./RatingField";
import { TagsField } from "./TagsField";
import { SentimentField } from "./SentimentField";
import { UserInfoFields } from "./UserInfoFields";

interface AddFeedbackFormProps {
  onSubmit: (data: Omit<SupplierFeedback, "id">) => void;
  className?: string;
}

export const AddFeedbackForm: React.FC<AddFeedbackFormProps> = ({
  onSubmit,
  className = "",
}) => {
  const availableTags: FeedbackTag[] = [
    "Quality Issue",
    "Delivery Issue",
    "Communication Issue",
    "Packaging Issue",
    "Documentation Issue",
    "Other",
  ];

  const sentiments: FeedbackSentiment[] = ["Positive", "Neutral", "Negative"];

  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      supplierName: "",
      rating: 0,
      feedback: "",
      tags: [],
      submittedBy: "",
      submitterRole: "",
      sentiment: "Neutral",
    },
  });

  const handleFormSubmit = (data: FeedbackFormValues) => {
    const submissionData: Omit<SupplierFeedback, "id"> = {
      supplierName: data.supplierName,
      rating: data.rating as 1 | 2 | 3 | 4 | 5,
      feedback: data.feedback,
      tags: data.tags as FeedbackTag[],
      submittedBy: data.submittedBy,
      submitterRole: data.submitterRole,
      sentiment: data.sentiment as FeedbackSentiment,
      submittedDate: new Date().toISOString().split("T")[0], // YYYY-MM-DD format
    };
    onSubmit(submissionData);
    form.reset();
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-xl">Add Supplier Feedback</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="supplierName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Supplier Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter supplier name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <RatingField form={form} />

            <FormField
              control={form.control}
              name="feedback"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Feedback</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter your feedback about the supplier"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <TagsField form={form} availableTags={availableTags} />
            <SentimentField form={form} sentiments={sentiments} />
            <UserInfoFields form={form} />

            <Button type="submit" className="w-full md:w-auto">
              Submit Feedback
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

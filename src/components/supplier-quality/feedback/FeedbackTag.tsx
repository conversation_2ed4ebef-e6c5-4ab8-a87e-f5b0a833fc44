
import React from "react";
import { FeedbackTag as TagType } from "@/types/supplierFeedback";
import { Badge } from "@/components/ui/badge";

interface FeedbackTagProps {
  tag: TagType;
  onClick?: () => void;
  selected?: boolean;
  className?: string;
}

export const FeedbackTag: React.FC<FeedbackTagProps> = ({
  tag,
  onClick,
  selected = false,
  className = "",
}) => {
  const getTagColor = (tag: TagType): string => {
    switch (tag) {
      case "Quality Issue":
        return selected ? "bg-red-600 hover:bg-red-700" : "bg-red-100 text-red-800 hover:bg-red-200";
      case "Delivery Issue":
        return selected ? "bg-blue-600 hover:bg-blue-700" : "bg-blue-100 text-blue-800 hover:bg-blue-200";
      case "Communication Issue":
        return selected ? "bg-purple-600 hover:bg-purple-700" : "bg-purple-100 text-purple-800 hover:bg-purple-200";
      case "Packaging Issue":
        return selected ? "bg-amber-600 hover:bg-amber-700" : "bg-amber-100 text-amber-800 hover:bg-amber-200";
      case "Documentation Issue":
        return selected ? "bg-emerald-600 hover:bg-emerald-700" : "bg-emerald-100 text-emerald-800 hover:bg-emerald-200";
      case "Other":
        return selected ? "bg-gray-600 hover:bg-gray-700" : "bg-gray-100 text-gray-800 hover:bg-gray-200";
      default:
        return selected ? "bg-gray-600 hover:bg-gray-700" : "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  return (
    <Badge 
      variant="outline"
      className={`${getTagColor(tag)} ${onClick ? 'cursor-pointer' : ''} ${className} transition-colors`}
      onClick={onClick}
    >
      {tag}
    </Badge>
  );
};

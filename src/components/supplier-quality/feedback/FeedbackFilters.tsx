
import React from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { FeedbackTag } from "./FeedbackTag";
import { FeedbackTag as TagType, FeedbackSentiment } from "@/types/supplierFeedback";
import { SentimentIndicator } from "./SentimentIndicator";
import { Button } from "@/components/ui/button";

interface FeedbackFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedTags: TagType[];
  setSelectedTags: (tags: TagType[]) => void;
  selectedSentiment: FeedbackSentiment | "All";
  setSelectedSentiment: (sentiment: FeedbackSentiment | "All") => void;
}

export const FeedbackFilters: React.FC<FeedbackFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  selectedTags,
  setSelectedTags,
  selectedSentiment,
  setSelectedSentiment,
}) => {
  const availableTags: TagType[] = [
    "Quality Issue",
    "Delivery Issue",
    "Communication Issue",
    "Packaging Issue",
    "Documentation Issue",
    "Other",
  ];

  const sentiments: (FeedbackSentiment | "All")[] = ["All", "Positive", "Neutral", "Negative"];

  const toggleTag = (tag: TagType) => {
    if (selectedTags.includes(tag)) {
      setSelectedTags(selectedTags.filter((t) => t !== tag));
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedTags([]);
    setSelectedSentiment("All");
  };

  return (
    <div className="bg-gray-50 p-4 rounded-lg mb-6">
      <h3 className="text-lg font-medium mb-3">Filter Feedback</h3>
      
      <div className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            placeholder="Search by supplier, feedback, or submitter..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* Tags */}
        <div>
          <h4 className="text-sm font-medium mb-2">Filter by Tag</h4>
          <div className="flex flex-wrap gap-2">
            {availableTags.map((tag) => (
              <FeedbackTag
                key={tag}
                tag={tag}
                onClick={() => toggleTag(tag)}
                selected={selectedTags.includes(tag)}
              />
            ))}
          </div>
        </div>
        
        {/* Sentiment */}
        <div>
          <h4 className="text-sm font-medium mb-2">Filter by Sentiment</h4>
          <div className="flex gap-3 flex-wrap">
            {sentiments.map((sentiment) => (
              <Button
                key={sentiment}
                variant={selectedSentiment === sentiment ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedSentiment(sentiment)}
                className="flex items-center gap-2"
              >
                {sentiment !== "All" && (
                  <SentimentIndicator 
                    sentiment={sentiment}
                    size={16}
                    className={selectedSentiment === sentiment ? "text-white" : ""}
                  />
                )}
                {sentiment}
              </Button>
            ))}
          </div>
        </div>
        
        {/* Clear filters */}
        {(searchQuery || selectedTags.length > 0 || selectedSentiment !== "All") && (
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-sm"
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

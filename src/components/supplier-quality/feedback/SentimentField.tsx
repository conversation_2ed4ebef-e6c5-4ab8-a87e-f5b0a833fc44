
import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { FeedbackFormValues } from "./feedbackFormSchema";
import { FeedbackSentiment } from "@/types/supplierFeedback";

interface SentimentFieldProps {
  form: UseFormReturn<FeedbackFormValues>;
  sentiments: FeedbackSentiment[];
}

export const SentimentField: React.FC<SentimentFieldProps> = ({ form, sentiments }) => {
  return (
    <FormField
      control={form.control}
      name="sentiment"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Sentiment</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select sentiment" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {sentiments.map((sentiment) => (
                <SelectItem key={sentiment} value={sentiment}>
                  {sentiment}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

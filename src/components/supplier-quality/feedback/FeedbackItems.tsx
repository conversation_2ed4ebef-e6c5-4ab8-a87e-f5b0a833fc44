
import React from "react";
import { FeedbackCard } from "./FeedbackCard";
import { SupplierFeedback } from "@/types/supplierFeedback";

interface FeedbackItemsProps {
  feedbackItems: SupplierFeedback[];
}

export const FeedbackItems: React.FC<FeedbackItemsProps> = ({ feedbackItems }) => {
  return (
    <>
      <h2 className="text-xl font-semibold mb-4">
        Feedback List
        <span className="text-sm font-normal text-gray-500 ml-2">
          ({feedbackItems.length} entries)
        </span>
      </h2>

      {feedbackItems.length === 0 ? (
        <div className="bg-gray-50 p-8 rounded-lg flex items-center justify-center">
          <p className="text-gray-500">No feedback entries match your filters</p>
        </div>
      ) : (
        <div className="space-y-4">
          {feedbackItems.map((item) => (
            <FeedbackCard key={item.id} feedback={item} />
          ))}
        </div>
      )}
    </>
  );
};

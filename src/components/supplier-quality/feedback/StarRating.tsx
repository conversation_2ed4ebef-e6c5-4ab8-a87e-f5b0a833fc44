
import React from "react";
import { Star } from "lucide-react";

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: number;
  readOnly?: boolean;
  onChange?: (rating: number) => void;
  className?: string;
}

export const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 20,
  readOnly = true,
  onChange,
  className = "",
}) => {
  const handleClick = (selectedRating: number) => {
    if (readOnly) return;
    onChange?.(selectedRating);
  };

  return (
    <div className={`flex ${className}`}>
      {[...Array(maxRating)].map((_, i) => {
        const starValue = i + 1;
        const filled = starValue <= rating;
        
        return (
          <Star
            key={i}
            size={size}
            className={`${
              filled ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
            } ${!readOnly ? "cursor-pointer" : ""}`}
            onClick={() => handleClick(starValue)}
          />
        );
      })}
    </div>
  );
};

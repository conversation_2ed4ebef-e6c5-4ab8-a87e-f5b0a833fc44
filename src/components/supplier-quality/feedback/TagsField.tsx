
import React from "react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { FeedbackTag as TagComponent } from "./FeedbackTag";
import { UseFormReturn } from "react-hook-form";
import { FeedbackFormValues } from "./feedbackFormSchema";
import { FeedbackTag } from "@/types/supplierFeedback";

interface TagsFieldProps {
  form: UseFormReturn<FeedbackFormValues>;
  availableTags: FeedbackTag[];
}

export const TagsField: React.FC<TagsFieldProps> = ({ form, availableTags }) => {
  const toggleTag = (tag: FeedbackTag) => {
    const currentTags = form.getValues("tags");
    if (currentTags.includes(tag)) {
      form.setValue(
        "tags",
        currentTags.filter((t) => t !== tag)
      );
    } else {
      form.setValue("tags", [...currentTags, tag]);
    }
  };

  return (
    <FormField
      control={form.control}
      name="tags"
      render={() => (
        <FormItem>
          <FormLabel>Tags (select at least one)</FormLabel>
          <FormControl>
            <div className="flex flex-wrap gap-2 mt-1">
              {availableTags.map((tag) => (
                <TagComponent
                  key={tag}
                  tag={tag}
                  onClick={() => toggleTag(tag)}
                  selected={form.getValues("tags").includes(tag)}
                />
              ))}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

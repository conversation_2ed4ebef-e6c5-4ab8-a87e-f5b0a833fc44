
import React from "react";
import { SupplierFeedback } from "@/types/supplierFeedback";
import { Card, CardContent } from "@/components/ui/card";
import { StarRating } from "./StarRating";
import { SentimentIndicator } from "./SentimentIndicator";
import { FeedbackTag } from "./FeedbackTag";
import { format } from "date-fns";

interface FeedbackCardProps {
  feedback: SupplierFeedback;
  className?: string;
}

export const FeedbackCard: React.FC<FeedbackCardProps> = ({
  feedback,
  className = "",
}) => {
  const formattedDate = () => {
    try {
      const date = new Date(feedback.submittedDate);
      return format(date, "MMM d, yyyy");
    } catch (error) {
      return feedback.submittedDate;
    }
  };

  return (
    <Card className={`shadow-sm hover:shadow transition-shadow ${className}`}>
      <CardContent className="p-5">
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="font-medium text-lg">{feedback.supplierName}</h3>
            <div className="flex items-center gap-2 mt-1">
              <StarRating rating={feedback.rating} size={18} />
              <span className="text-sm text-gray-500">{feedback.rating}/5</span>
            </div>
          </div>
          <SentimentIndicator sentiment={feedback.sentiment} size={24} />
        </div>

        <p className="text-gray-700 mb-4">{feedback.feedback}</p>

        <div className="flex flex-wrap gap-2 mb-4">
          {feedback.tags.map((tag, index) => (
            <FeedbackTag key={index} tag={tag} />
          ))}
        </div>

        <div className="text-sm text-gray-500 flex justify-between items-center border-t pt-3 mt-2">
          <div>
            <span className="font-medium">{feedback.submittedBy}</span>
            {feedback.submitterRole && (
              <span>, {feedback.submitterRole}</span>
            )}
          </div>
          <div>{formattedDate()}</div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { FeedbackFilters } from "./FeedbackFilters";
import { AddFeedbackForm } from "./AddFeedbackForm";
import { FeedbackItems } from "./FeedbackItems";
import { SupplierTimelineContainer } from "../timeline/SupplierTimelineContainer";
import { useSupplierFeedback } from "@/hooks/useSupplierFeedback";

export const FeedbackListContainer: React.FC = () => {
  const {
    feedbackItems,
    searchQuery,
    setSearchQuery,
    selectedTags,
    setSelectedTags,
    selectedSentiment,
    setSelectedSentiment,
    handleAddFeedback,
  } = useSupplierFeedback();

  return (
    <div className="space-y-6">
      {/* Timeline section */}
      <div>
        <SupplierTimelineContainer />
      </div>
      
      {/* Feedback section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left sidebar with filters and form */}
        <div className="lg:col-span-1 space-y-6">
          <FeedbackFilters
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            selectedTags={selectedTags}
            setSelectedTags={setSelectedTags}
            selectedSentiment={selectedSentiment}
            setSelectedSentiment={setSelectedSentiment}
          />
          <AddFeedbackForm onSubmit={handleAddFeedback} />
        </div>

        {/* Right side with feedback cards */}
        <div className="lg:col-span-2">
          <FeedbackItems feedbackItems={feedbackItems} />
        </div>
      </div>
    </div>
  );
};

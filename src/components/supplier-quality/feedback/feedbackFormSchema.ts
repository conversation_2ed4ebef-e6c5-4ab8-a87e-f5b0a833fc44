
import { z } from "zod";

export const feedbackFormSchema = z.object({
  supplierName: z.string().min(1, "Supplier name is required"),
  rating: z.number().min(1).max(5),
  feedback: z.string().min(10, "Feedback must be at least 10 characters"),
  tags: z.array(z.string()).min(1, "Select at least one tag"),
  submittedBy: z.string().min(1, "Your name is required"),
  submitterRole: z.string().min(1, "Your role is required"),
  sentiment: z.string(),
});

export type FeedbackFormValues = z.infer<typeof feedbackFormSchema>;

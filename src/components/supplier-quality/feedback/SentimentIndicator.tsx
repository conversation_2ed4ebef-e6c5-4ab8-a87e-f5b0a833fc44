
import React from "react";
import { <PERSON>, <PERSON>h, Frown } from "lucide-react";
import { FeedbackSentiment } from "@/types/supplierFeedback";

interface SentimentIndicatorProps {
  sentiment: FeedbackSentiment;
  size?: number;
  className?: string;
  showLabel?: boolean;
}

export const SentimentIndicator: React.FC<SentimentIndicatorProps> = ({
  sentiment,
  size = 20,
  className = "",
  showLabel = false,
}) => {
  let Icon;
  let color;
  
  switch (sentiment) {
    case "Positive":
      Icon = Smile;
      color = "text-green-500";
      break;
    case "Neutral":
      Icon = Meh;
      color = "text-amber-500";
      break;
    case "Negative":
      Icon = Frown;
      color = "text-red-500";
      break;
  }
  
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Icon size={size} className={`${color} fill-current opacity-70`} />
      {showLabel && <span className={`text-sm ${color}`}>{sentiment}</span>}
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageSquare, Plus, Search, Filter, TrendingUp, ThumbsUp, ThumbsDown } from 'lucide-react';
import { FeedbackCard } from './FeedbackCard';
import { AddFeedbackForm } from './AddFeedbackForm';
import { useSupplierFeedback } from '@/hooks/useSupplierFeedback';

export const FeedbackView: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [sentimentFilter, setSentimentFilter] = useState<string>('all');
  const [showAddForm, setShowAddForm] = useState(false);

  const { feedbackItems, handleAddFeedback } = useSupplierFeedback();

  const filteredFeedback = feedbackItems.filter(item => {
    const matchesSearch = item.supplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.feedback.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.tags.includes(categoryFilter as any);
    const matchesSentiment = sentimentFilter === 'all' || item.sentiment === sentimentFilter;
    return matchesSearch && matchesCategory && matchesSentiment;
  });

  const positiveCount = feedbackItems.filter(f => f.sentiment === 'Positive').length;
  const neutralCount = feedbackItems.filter(f => f.sentiment === 'Neutral').length;
  const negativeCount = feedbackItems.filter(f => f.sentiment === 'Negative').length;
  const avgRating = feedbackItems.reduce((acc, f) => acc + f.rating, 0) / feedbackItems.length;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Feedback</p>
                <p className="text-2xl font-bold">{feedbackItems.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Rating</p>
                <p className="text-2xl font-bold">{avgRating.toFixed(1)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ThumbsUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Positive</p>
                <p className="text-2xl font-bold">{positiveCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ThumbsDown className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Negative</p>
                <p className="text-2xl font-bold">{negativeCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Supplier Feedback</CardTitle>
            <Button onClick={() => setShowAddForm(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Add Feedback
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search feedback..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Quality Issue">Quality Issue</SelectItem>
                <SelectItem value="Delivery Issue">Delivery Issue</SelectItem>
                <SelectItem value="Communication Issue">Communication Issue</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sentimentFilter} onValueChange={setSentimentFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by sentiment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sentiments</SelectItem>
                <SelectItem value="Positive">Positive</SelectItem>
                <SelectItem value="Neutral">Neutral</SelectItem>
                <SelectItem value="Negative">Negative</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            {filteredFeedback.map((item) => (
              <FeedbackCard key={item.id} feedback={item} />
            ))}
          </div>
        </CardContent>
      </Card>

      {showAddForm && (
        <AddFeedbackForm 
          onSubmit={(data) => {
            handleAddFeedback(data);
            setShowAddForm(false);
          }} 
        />
      )}
    </div>
  );
};

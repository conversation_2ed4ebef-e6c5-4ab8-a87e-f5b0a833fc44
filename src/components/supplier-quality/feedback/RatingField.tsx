
import React from "react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { StarRating } from "./StarRating";
import { UseFormReturn } from "react-hook-form";
import { FeedbackFormValues } from "./feedbackFormSchema";

interface RatingFieldProps {
  form: UseFormReturn<FeedbackFormValues>;
}

export const RatingField: React.FC<RatingFieldProps> = ({ form }) => {
  return (
    <FormField
      control={form.control}
      name="rating"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Rating</FormLabel>
          <FormControl>
            <div>
              <StarRating
                rating={field.value}
                readOnly={false}
                onChange={(rating) => field.onChange(rating)}
                size={24}
                className="mb-2"
              />
              {field.value > 0 && (
                <div className="text-sm">{field.value}/5</div>
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

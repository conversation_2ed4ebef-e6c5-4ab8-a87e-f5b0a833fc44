
import React from "react";
import { Filter } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface FilterBarProps {
  onRegionChange: (region: string) => void;
  onTypeChange: (type: string) => void;
  onRiskChange: (risk: string) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  onRegionChange,
  onTypeChange,
  onRiskChange
}) => {
  return (
    <Card className="shadow-sm p-4 mt-4">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        <div className="flex items-center">
          <Filter className="h-4 w-4 mr-2 text-gray-500" />
          <span className="font-medium text-gray-700">Filter By:</span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
          <Select defaultValue="all" onValueChange={onRegionChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select Region" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Regions</SelectItem>
              <SelectItem value="north-america">North America</SelectItem>
              <SelectItem value="europe">Europe</SelectItem>
              <SelectItem value="asia">Asia</SelectItem>
              <SelectItem value="latam">Latin America</SelectItem>
            </SelectContent>
          </Select>
          
          <Select defaultValue="all" onValueChange={onTypeChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Supplier Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="raw-materials">Raw Materials</SelectItem>
              <SelectItem value="components">Components</SelectItem>
              <SelectItem value="services">Services</SelectItem>
              <SelectItem value="packaging">Packaging</SelectItem>
            </SelectContent>
          </Select>
          
          <Select defaultValue="all" onValueChange={onRiskChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Risk Tier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Risk Tiers</SelectItem>
              <SelectItem value="low">Low Risk</SelectItem>
              <SelectItem value="medium">Medium Risk</SelectItem>
              <SelectItem value="high">High Risk</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </Card>
  );
};

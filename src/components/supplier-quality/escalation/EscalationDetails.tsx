
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SupplierEscalation } from "@/types/supplierEscalation";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Clock, User } from "lucide-react";

interface EscalationDetailsProps {
  escalation: SupplierEscalation;
}

export const EscalationDetails: React.FC<EscalationDetailsProps> = ({ escalation }) => {
  const { toast } = useToast();
  const [assignee, setAssignee] = useState<string>(escalation.assignedTo);

  const handleAssigneeChange = (value: string) => {
    setAssignee(value);
    toast({
      title: "Assignee updated",
      description: `This escalation has been assigned to ${value}`,
    });
  };

  const getDueDateColor = () => {
    const dueDate = new Date(escalation.dueDate);
    const today = new Date();
    const diffDays = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return "text-red-600";
    if (diffDays < 3) return "text-amber-600";
    return "text-gray-600";
  };

  const getStatusBadgeColor = () => {
    switch (escalation.status) {
      case "New": return "bg-blue-100 text-blue-800";
      case "In Review": return "bg-amber-100 text-amber-800";
      case "Escalated": return "bg-red-100 text-red-800";
      case "Resolved": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center">
              {escalation.id}
              <Badge className={`ml-3 ${getStatusBadgeColor()}`}>
                {escalation.status}
              </Badge>
            </CardTitle>
            <CardDescription className="text-base mt-1">
              {escalation.supplier}
            </CardDescription>
          </div>
          
          <Button variant="outline" size="sm">
            Update Status
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Issue</h3>
            <p className="mt-1">{escalation.issue}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Impact</h3>
            <p className="mt-1">{escalation.impact}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500 flex items-center">
              <User className="h-4 w-4 mr-1" />
              Raised By
            </h3>
            <p className="mt-1">{escalation.raisedBy}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500 flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              Raised Date
            </h3>
            <p className="mt-1">{escalation.raisedDate}</p>
          </div>
          
          <div>
            <h3 className={`text-sm font-medium flex items-center ${getDueDateColor()}`}>
              <Clock className="h-4 w-4 mr-1" />
              Due Date
            </h3>
            <p className={`mt-1 ${getDueDateColor()}`}>{escalation.dueDate}</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-500 mb-2">Assigned To</h3>
          <Select value={assignee} onValueChange={handleAssigneeChange}>
            <SelectTrigger className="w-full md:w-[220px]">
              <SelectValue placeholder="Select assignee" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Marcus Chen">Marcus Chen (Procurement)</SelectItem>
              <SelectItem value="Sarah Johnson">Sarah Johnson (Quality)</SelectItem>
              <SelectItem value="James Wilson">James Wilson (Supply Chain)</SelectItem>
              <SelectItem value="Rachel Kim">Rachel Kim (QA)</SelectItem>
              <SelectItem value="Michael Brown">Michael Brown (Operations)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end space-x-2 pt-2">
        <Button variant="outline">Documents</Button>
        <Button>Take Action</Button>
      </CardFooter>
    </Card>
  );
};

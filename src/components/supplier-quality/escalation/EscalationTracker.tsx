
import React from "react";
import { EscalationDetails } from "./EscalationDetails";
import { EscalationHistory } from "./EscalationHistory";
import { CommentThread } from "./CommentThread";
import { mockEscalationData } from "@/data/mockEscalationData";

export const EscalationTracker: React.FC = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left column: Issue details */}
      <div className="lg:col-span-2">
        <EscalationDetails escalation={mockEscalationData} />
      </div>
      
      {/* Right column: History and comments */}
      <div className="lg:col-span-1 space-y-6">
        <EscalationHistory history={mockEscalationData.history} />
        <CommentThread comments={mockEscalationData.comments} escalationId={mockEscalationData.id} />
      </div>
    </div>
  );
}

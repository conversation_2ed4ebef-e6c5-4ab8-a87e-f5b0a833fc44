
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { EscalationHistoryEvent } from "@/types/supplierEscalation";

interface EscalationHistoryProps {
  history: EscalationHistoryEvent[];
}

export const EscalationHistory: React.FC<EscalationHistoryProps> = ({ history }) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Escalation Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative pl-6 space-y-6 before:absolute before:left-2 before:top-2 before:bottom-2 before:w-0.5 before:bg-gray-200">
          {history.map((event, index) => (
            <div key={index} className="relative">
              <div className="absolute -left-6 mt-1 w-4 h-4 rounded-full bg-white border-2 border-blue-500"></div>
              <div className={`text-sm ${index === 0 ? 'font-medium' : ''}`}>
                <div className="text-gray-500 text-xs">{event.date} - {event.time}</div>
                <div className="mt-0.5">{event.action}</div>
                <div className="text-gray-600 text-xs mt-0.5">by {event.by}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

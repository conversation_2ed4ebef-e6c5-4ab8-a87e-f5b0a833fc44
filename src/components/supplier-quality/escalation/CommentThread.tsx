
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { EscalationComment } from "@/types/supplierEscalation";
import { User, Send } from "lucide-react";

interface CommentThreadProps {
  comments: EscalationComment[];
  escalationId: string;
}

export const CommentThread: React.FC<CommentThreadProps> = ({ comments, escalationId }) => {
  const [newComment, setNewComment] = useState("");
  const { toast } = useToast();

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim() === "") return;

    toast({
      title: "Comment added",
      description: "Your comment has been added to the thread",
    });

    // In a real app, this would add the comment to the thread
    setNewComment("");
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Comments & Updates</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4 max-h-72 overflow-y-auto">
          {comments.map((comment, index) => (
            <div key={index} className="pb-3 border-b border-gray-100 last:border-0">
              <div className="flex items-center mb-1">
                <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
                <div className="ml-2">
                  <div className="font-medium text-sm">{comment.author}</div>
                  <div className="text-xs text-gray-500">{comment.date} - {comment.time}</div>
                </div>
              </div>
              <p className="text-sm mt-1 pl-10">{comment.text}</p>
            </div>
          ))}
        </div>

        <form onSubmit={handleCommentSubmit} className="mt-3 space-y-2">
          <Textarea 
            placeholder="Add a comment or update..." 
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="min-h-[80px]"
          />
          <div className="flex justify-end">
            <Button type="submit" size="sm" className="mt-1" disabled={!newComment.trim()}>
              <Send className="h-4 w-4 mr-1" />
              Send
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

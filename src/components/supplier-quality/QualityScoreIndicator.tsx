
import React from "react";

interface QualityScoreIndicatorProps {
  score: number;
}

export const QualityScoreIndicator: React.FC<QualityScoreIndicatorProps> = ({ score }) => {
  const getColorClass = () => {
    if (score >= 90) return "text-green-600";
    if (score >= 75) return "text-blue-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };
  
  const getBgColorClass = () => {
    if (score >= 90) return "bg-green-200";
    if (score >= 75) return "bg-blue-200";
    if (score >= 60) return "bg-yellow-200";
    return "bg-red-200";
  };

  return (
    <div className="flex items-center">
      <span className={`font-medium ${getColorClass()}`}>{score}%</span>
      <div className="w-16 h-2 ml-2 bg-gray-200 rounded-full">
        <div 
          className={`h-full rounded-full ${getBgColorClass()}`} 
          style={{ width: `${score}%` }}
        ></div>
      </div>
    </div>
  );
};

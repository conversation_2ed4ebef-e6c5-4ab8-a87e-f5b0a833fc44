
import React from "react";
import { SupplierAudit } from "@/types/supplierAudits";
import { format } from "date-fns";
import { AuditStatusBadge } from "./AuditStatusBadge";
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { useIsMobile } from "@/hooks/use-mobile";
import { Card } from "@/components/ui/card";

interface AuditListProps {
  audits: SupplierAudit[];
}

export const AuditList: React.FC<AuditListProps> = ({ audits }) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className="space-y-4">
        {audits.map((audit) => (
          <Card key={audit.id} className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{audit.supplierName}</h4>
                <p className="text-sm">{audit.auditType}</p>
              </div>
              <AuditStatusBadge status={audit.status} />
            </div>
            
            <div className="mt-2">
              <p className="text-sm">
                <span className="font-medium text-gray-600">Date:</span>{" "}
                {format(new Date(audit.scheduledDate), "MMM d, yyyy")}
              </p>
              {audit.auditor && (
                <p className="text-sm">
                  <span className="font-medium text-gray-600">Auditor:</span>{" "}
                  {audit.auditor}
                </p>
              )}
              {audit.score !== undefined && (
                <p className="text-sm">
                  <span className="font-medium text-gray-600">Score:</span>{" "}
                  {audit.score}%
                </p>
              )}
            </div>
            
            <div className="mt-3 flex flex-wrap gap-2">
              <button className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                Assign Auditor
              </button>
              <button className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                Upload Checklist
              </button>
              {audit.status === "Completed" && (
                <button className="text-xs px-2 py-1 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded">
                  View Findings
                </button>
              )}
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Supplier</TableHead>
          <TableHead>Audit Type</TableHead>
          <TableHead>Scheduled Date</TableHead>
          <TableHead>Auditor</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Score</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {audits.map((audit) => (
          <TableRow key={audit.id}>
            <TableCell className="font-medium">{audit.supplierName}</TableCell>
            <TableCell>{audit.auditType}</TableCell>
            <TableCell>{format(new Date(audit.scheduledDate), "MMM d, yyyy")}</TableCell>
            <TableCell>{audit.auditor || "Not assigned"}</TableCell>
            <TableCell>
              <AuditStatusBadge status={audit.status} />
            </TableCell>
            <TableCell>{audit.score !== undefined ? `${audit.score}%` : "-"}</TableCell>
            <TableCell>
              <div className="flex gap-2">
                <button className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                  Assign
                </button>
                <button className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded">
                  Upload
                </button>
                {audit.status === "Completed" && (
                  <button className="text-xs px-2 py-1 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded">
                    Findings
                  </button>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

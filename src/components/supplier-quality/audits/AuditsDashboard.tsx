
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, ClipboardCheck, Alert<PERSON><PERSON>gle, Bar<PERSON>hart3, FileText } from "lucide-react";
import { AuditDashboard } from "../audit-dashboard/AuditDashboard";

export const AuditsDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Scheduled Audits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">12</div>
            <p className="text-xs text-blue-600">This month</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-blue-700 border-blue-300">
              View Calendar
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700 flex items-center gap-2">
              <ClipboardCheck className="h-4 w-4" />
              Active Audits
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">8</div>
            <p className="text-xs text-green-600">In progress</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-green-700 border-green-300">
              View Active
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-amber-700 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              CAPA Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-800">25</div>
            <p className="text-xs text-amber-600">Open CAPAs</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-amber-700 border-amber-300">
              View CAPAs
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Audit Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">92%</div>
            <p className="text-xs text-purple-600">Pass rate</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-purple-700 border-purple-300">
              View Analytics
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-indigo-700 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Audit Templates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-800">15</div>
            <p className="text-xs text-indigo-600">Available</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-indigo-700 border-indigo-300">
              Manage Templates
            </Button>
          </CardContent>
        </Card>
      </div>

      <AuditDashboard />
    </div>
  );
};

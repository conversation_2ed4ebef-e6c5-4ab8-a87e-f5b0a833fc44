
import React, { useState } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Upload, Plus, X } from "lucide-react";
import { AuditIntake } from "@/types/auditIntake";
import { useToast } from "@/hooks/use-toast";

interface AuditIntakeFormProps {
  onSubmit: (intake: Omit<AuditIntake, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  initialData?: Partial<AuditIntake>;
}

export const AuditIntakeForm: React.FC<AuditIntakeFormProps> = ({
  onSubmit,
  onCancel,
  initialData
}) => {
  const [formData, setFormData] = useState({
    vendorName: initialData?.vendorName || "PT Sat Nusapersada Tbk",
    productLevel: initialData?.productLevel || "ASUS NB",
    startDate: initialData?.auditDates?.startDate || "2022-03-09",
    endDate: initialData?.auditDates?.endDate || "2022-03-11",
    address: initialData?.address || "Jl. Pelita VI No. 99, Batam, Indonesia",
    auditors: initialData?.auditors || ["Sam Liu", "Yeos"]
  });
  
  const [newAuditor, setNewAuditor] = useState("");
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vendorName || !formData.productLevel || !formData.startDate || !formData.endDate) {
      toast({
        title: "Missing Required Fields",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    onSubmit({
      vendorName: formData.vendorName,
      productLevel: formData.productLevel,
      auditDates: {
        startDate: formData.startDate,
        endDate: formData.endDate
      },
      address: formData.address,
      auditors: formData.auditors,
      status: 'draft'
    });
  };

  const addAuditor = () => {
    if (newAuditor.trim() && !formData.auditors.includes(newAuditor.trim())) {
      setFormData(prev => ({
        ...prev,
        auditors: [...prev.auditors, newAuditor.trim()]
      }));
      setNewAuditor("");
    }
  };

  const removeAuditor = (auditor: string) => {
    setFormData(prev => ({
      ...prev,
      auditors: prev.auditors.filter(a => a !== auditor)
    }));
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Supplier Audit Intake Form
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="vendor-name">Vendor Name *</Label>
              <Input
                id="vendor-name"
                value={formData.vendorName}
                onChange={(e) => setFormData(prev => ({ ...prev, vendorName: e.target.value }))}
                placeholder="Enter vendor name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-level">Product Level *</Label>
              <Input
                id="product-level"
                value={formData.productLevel}
                onChange={(e) => setFormData(prev => ({ ...prev, productLevel: e.target.value }))}
                placeholder="Enter product level"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="start-date">Audit Start Date *</Label>
              <Input
                id="start-date"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="end-date">Audit End Date *</Label>
              <Input
                id="end-date"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              placeholder="Enter vendor address"
              rows={3}
            />
          </div>

          <div className="space-y-4">
            <Label>Auditors</Label>
            <div className="flex gap-2 mb-2">
              <Input
                value={newAuditor}
                onChange={(e) => setNewAuditor(e.target.value)}
                placeholder="Add auditor name"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAuditor())}
              />
              <Button type="button" onClick={addAuditor} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {formData.auditors.map((auditor) => (
                <div
                  key={auditor}
                  className="flex items-center gap-1 bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                >
                  {auditor}
                  <button
                    type="button"
                    onClick={() => removeAuditor(auditor)}
                    className="ml-1 hover:text-blue-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600 mb-2">
              Upload related audit checklists and forms
            </p>
            <Button type="button" variant="outline" size="sm">
              Choose Files
            </Button>
            <p className="text-xs text-gray-500 mt-2">
              Supported formats: PDF, DOC, XLSX (Max 10MB per file)
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">
              Create Audit
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, ClipboardList, TrendingUp, AlertTriangle, Settings, Link } from 'lucide-react';
import { AuditCalendarView } from './AuditCalendarView';
import { useSupplierAudits } from '@/hooks/useSupplierAudits';
import { AuditTemplateManager } from './AuditTemplateManager';
import { AuditAnalytics } from './AuditAnalytics';
import { CAPAManagement } from './CAPAManagement';
import { AuditExecutionPanel } from './AuditExecutionPanel';
import { ReminderSettingsPanel } from './ReminderSettingsPanel';
import { FollowUpAuditScheduler } from './FollowUpAuditScheduler';
import { <PERSON>hancedNCTracker } from './EnhancedNCTracker';

export const AuditsManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('calendar');
  const { audits } = useSupplierAudits();

  // Mock handlers for audit execution
  const handleSaveAuditQuestion = (sectionId: string, questionId: string, updates: any) => {
    console.log('Saving audit question:', { sectionId, questionId, updates });
  };

  const handleAddObservation = (sectionId: string, observation: string) => {
    console.log('Adding observation:', { sectionId, observation });
  };

  const handleAddNC = (sectionId: string, ncData: any) => {
    console.log('Adding NC:', { sectionId, ncData });
  };

  // Mock sections data for execution panel
  const mockSections = [
    {
      id: 'receiving-inspection',
      name: 'Receiving Inspection',
      questions: [
        {
          id: 'ri-q1',
          question: 'Are incoming materials properly inspected and documented?',
          score: 1 as 0 | 1,
          evidenceFiles: [],
          auditorComments: 'Process well documented and followed',
          required: true
        }
      ],
      completionStatus: 'in-progress' as const
    }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Scheduled Audits</p>
                <p className="text-2xl font-bold">{audits.filter(a => a.status === 'Planned').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ClipboardList className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Audits</p>
                <p className="text-2xl font-bold">{audits.filter(a => a.status === 'In Progress').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{audits.filter(a => a.status === 'Completed').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Open NCs</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          <TabsTrigger value="execution">Execution</TabsTrigger>
          <TabsTrigger value="nc-tracker">NC Tracker</TabsTrigger>
          <TabsTrigger value="followup">Follow-up</TabsTrigger>
          <TabsTrigger value="reminders">Reminders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-6">
          <AuditCalendarView audits={audits} />
        </TabsContent>

        <TabsContent value="execution" className="space-y-6">
          <AuditExecutionPanel
            auditId="audit-001"
            sections={mockSections}
            onSave={handleSaveAuditQuestion}
            onAddObservation={handleAddObservation}
            onAddNC={handleAddNC}
          />
        </TabsContent>

        <TabsContent value="nc-tracker" className="space-y-6">
          <EnhancedNCTracker />
        </TabsContent>

        <TabsContent value="followup" className="space-y-6">
          <FollowUpAuditScheduler />
        </TabsContent>

        <TabsContent value="reminders" className="space-y-6">
          <ReminderSettingsPanel />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AuditAnalytics />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <AuditTemplateManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { GenericTable, GenericTableColumn } from '@/components/shared/table/GenericTable';
import { 
  AlertTriangle,
  Clock,
  CheckCircle,
  User,
  MessageSquare,
  Upload,
  Calendar,
  Eye,
  Edit
} from 'lucide-react';
import { SupplierNonConformance, NCStatus, NCSeverity } from '@/types/supplierNonConformance';

interface NCComment {
  id: string;
  author: string;
  comment: string;
  timestamp: string;
  attachments?: string[];
}

interface NCTimeline {
  id: string;
  action: string;
  author: string;
  timestamp: string;
  details?: string;
}

export const EnhancedNCTracker: React.FC = () => {
  const [nonConformances, setNonConformances] = useState<SupplierNonConformance[]>([
    {
      id: 'nc-001',
      ncNumber: 'NC-2024-001',
      supplierName: 'TechCorp Manufacturing',
      issueDescription: 'Calibration certificates expired for measuring equipment',
      status: 'Open',
      severity: 'Major',
      capaLinked: false,
      dateReported: '2024-01-15',
      responsiblePerson: 'John Smith'
    },
    {
      id: 'nc-002',
      ncNumber: 'NC-2024-002',
      supplierName: 'Global Components Ltd',
      issueDescription: 'Document control procedure not followed',
      status: 'In Review',
      severity: 'Minor',
      capaLinked: true,
      dateReported: '2024-01-10',
      dateResolved: '2024-01-20',
      responsiblePerson: 'Sarah Johnson'
    }
  ]);

  const [selectedNC, setSelectedNC] = useState<SupplierNonConformance | null>(null);
  const [showTimelineDialog, setShowTimelineDialog] = useState(false);
  const [showCommentsDialog, setShowCommentsDialog] = useState(false);
  const [newComment, setNewComment] = useState('');

  const mockComments: NCComment[] = [
    {
      id: 'comment-1',
      author: 'John Smith',
      comment: 'Initial assessment completed. Supplier contacted for response.',
      timestamp: '2024-01-15 10:30:00'
    },
    {
      id: 'comment-2',
      author: 'TechCorp Quality Manager',
      comment: 'We have identified the root cause and are implementing corrective actions.',
      timestamp: '2024-01-16 14:20:00',
      attachments: ['corrective_action_plan.pdf']
    }
  ];

  const mockTimeline: NCTimeline[] = [
    {
      id: 'timeline-1',
      action: 'NC Created',
      author: 'John Smith',
      timestamp: '2024-01-15 09:00:00',
      details: 'Initial non-conformance identified during audit'
    },
    {
      id: 'timeline-2',
      action: 'Assigned to Supplier',
      author: 'John Smith',
      timestamp: '2024-01-15 10:30:00',
      details: 'Assigned to TechCorp Quality Manager'
    },
    {
      id: 'timeline-3',
      action: 'Status Updated',
      author: 'Sarah Johnson',
      timestamp: '2024-01-16 11:15:00',
      details: 'Changed from Open to In Review'
    }
  ];

  const handleStatusUpdate = (ncId: string, newStatus: NCStatus) => {
    setNonConformances(ncs => 
      ncs.map(nc => 
        nc.id === ncId ? { ...nc, status: newStatus } : nc
      )
    );
  };

  const handleAssignmentUpdate = (ncId: string, newAssignee: string) => {
    setNonConformances(ncs => 
      ncs.map(nc => 
        nc.id === ncId ? { ...nc, responsiblePerson: newAssignee } : nc
      )
    );
  };

  const getSeverityBadge = (severity: NCSeverity) => {
    const variants = {
      'Critical': 'bg-red-100 text-red-800 border-red-200',
      'Major': 'bg-orange-100 text-orange-800 border-orange-200',
      'Minor': 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    return <Badge className={variants[severity]}>{severity}</Badge>;
  };

  const getStatusBadge = (status: NCStatus) => {
    const variants = {
      'Open': 'bg-red-100 text-red-800',
      'In Review': 'bg-blue-100 text-blue-800',
      'Closed': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[status]}>{status}</Badge>;
  };

  const columns: GenericTableColumn<SupplierNonConformance>[] = [
    {
      field: 'ncNumber',
      header: 'NC Number',
      sortable: true,
      cellRenderer: (item) => (
        <span className="font-mono text-sm">{item.ncNumber}</span>
      )
    },
    {
      field: 'supplierName',
      header: 'Supplier',
      sortable: true
    },
    {
      field: 'issueDescription',
      header: 'Description',
      cellRenderer: (item) => (
        <div className="max-w-xs truncate" title={item.issueDescription}>
          {item.issueDescription}
        </div>
      )
    },
    {
      field: 'severity',
      header: 'Severity',
      sortable: true,
      cellRenderer: (item) => getSeverityBadge(item.severity)
    },
    {
      field: 'status',
      header: 'Status',
      sortable: true,
      cellRenderer: (item) => (
        <Select 
          value={item.status} 
          onValueChange={(value) => handleStatusUpdate(item.id, value as NCStatus)}
        >
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Open">Open</SelectItem>
            <SelectItem value="In Review">In Review</SelectItem>
            <SelectItem value="Closed">Closed</SelectItem>
          </SelectContent>
        </Select>
      )
    },
    {
      field: 'responsiblePerson',
      header: 'Assigned To',
      cellRenderer: (item) => (
        <Select 
          value={item.responsiblePerson || ''} 
          onValueChange={(value) => handleAssignmentUpdate(item.id, value)}
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Assign..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="John Smith">John Smith</SelectItem>
            <SelectItem value="Sarah Johnson">Sarah Johnson</SelectItem>
            <SelectItem value="Mike Wilson">Mike Wilson</SelectItem>
            <SelectItem value="Quality Manager">Quality Manager</SelectItem>
          </SelectContent>
        </Select>
      )
    },
    {
      field: 'dateReported',
      header: 'Date Reported',
      sortable: true,
      cellRenderer: (item) => new Date(item.dateReported).toLocaleDateString()
    }
  ];

  const renderActions = (item: SupplierNonConformance) => (
    <div className="flex gap-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          setSelectedNC(item);
          setShowCommentsDialog(true);
        }}
        className="gap-1"
      >
        <MessageSquare className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => {
          setSelectedNC(item);
          setShowTimelineDialog(true);
        }}
        className="gap-1"
      >
        <Clock className="h-3 w-3" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className="gap-1"
      >
        <Eye className="h-3 w-3" />
      </Button>
    </div>
  );

  const handleAddComment = () => {
    if (newComment.trim()) {
      console.log('Adding comment:', newComment);
      setNewComment('');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Non-Conformance Tracker
          </CardTitle>
        </CardHeader>
        <CardContent>
          <GenericTable
            columns={columns}
            data={nonConformances}
            totalCount={nonConformances.length}
            currentPage={1}
            pageSize={10}
            onPageChange={() => {}}
            onPageSizeChange={() => {}}
            renderActions={renderActions}
            emptyMessage="No non-conformances found"
          />
        </CardContent>
      </Card>

      {/* Comments Dialog */}
      <Dialog open={showCommentsDialog} onOpenChange={setShowCommentsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Comments - {selectedNC?.ncNumber}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {mockComments.map((comment) => (
              <Card key={comment.id} className="p-3">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{comment.author}</span>
                      <span className="text-xs text-gray-500">{comment.timestamp}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{comment.comment}</p>
                    {comment.attachments && (
                      <div className="flex gap-2">
                        {comment.attachments.map((attachment, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {attachment}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="space-y-3 border-t pt-4">
            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Add a comment..."
              rows={3}
            />
            <div className="flex justify-between">
              <Button variant="outline" size="sm" className="gap-2">
                <Upload className="h-4 w-4" />
                Attach File
              </Button>
              <Button onClick={handleAddComment} size="sm">
                Add Comment
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Timeline Dialog */}
      <Dialog open={showTimelineDialog} onOpenChange={setShowTimelineDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timeline - {selectedNC?.ncNumber}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {mockTimeline.map((event, index) => (
              <div key={event.id} className="flex items-start gap-4">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-blue-600" />
                  </div>
                  {index < mockTimeline.length - 1 && (
                    <div className="w-px h-8 bg-gray-200 mt-2" />
                  )}
                </div>
                <div className="flex-1 pb-4">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">{event.action}</span>
                    <span className="text-xs text-gray-500">{event.timestamp}</span>
                  </div>
                  <p className="text-sm text-gray-600">by {event.author}</p>
                  {event.details && (
                    <p className="text-sm text-gray-700 mt-1">{event.details}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};


import React from "react";
import { AuditStatus } from "@/types/supplierAudits";
import { Badge } from "@/components/ui/badge";

interface AuditStatusBadgeProps {
  status: AuditStatus;
}

export const AuditStatusBadge: React.FC<AuditStatusBadgeProps> = ({ status }) => {
  switch (status) {
    case "Planned":
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
          Planned
        </Badge>
      );
    case "In Progress":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100">
          In Progress
        </Badge>
      );
    case "Completed":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100">
          Completed
        </Badge>
      );
    default:
      return <Badge>{status}</Badge>;
  }
};

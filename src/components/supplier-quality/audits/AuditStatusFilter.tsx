
import React from "react";
import { AuditStatus } from "@/types/supplierAudits";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AuditStatusBadge } from "./AuditStatusBadge";

interface AuditStatusFilterProps {
  currentFilter: AuditStatus | "all";
  onFilterChange: (filter: AuditStatus | "all") => void;
}

export const AuditStatusFilter: React.FC<AuditStatusFilterProps> = ({ 
  currentFilter, 
  onFilterChange 
}) => {
  return (
    <div>
      <h3 className="font-medium mb-2">Filter by Status</h3>
      <Select 
        value={currentFilter} 
        onValueChange={(value) => onFilterChange(value as AuditStatus | "all")}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select a status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Statuses</SelectItem>
          <SelectItem value="Planned">
            <div className="flex items-center">
              <span className="mr-2">Planned</span>
              <AuditStatusBadge status="Planned" />
            </div>
          </SelectItem>
          <SelectItem value="In Progress">
            <div className="flex items-center">
              <span className="mr-2">In Progress</span>
              <AuditStatusBadge status="In Progress" />
            </div>
          </SelectItem>
          <SelectItem value="Completed">
            <div className="flex items-center">
              <span className="mr-2">Completed</span>
              <AuditStatusBadge status="Completed" />
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Bell, 
  Clock, 
  Mail, 
  MessageSquare, 
  Calendar,
  History,
  Settings,
  Plus,
  Trash2
} from 'lucide-react';

interface ReminderRule {
  id: string;
  name: string;
  enabled: boolean;
  triggerDays: number;
  triggerEvent: 'audit_due' | 'nc_due' | 'followup_due';
  notificationMethod: 'email' | 'in_app' | 'both';
  recipients: string[];
}

interface NotificationHistory {
  id: string;
  type: string;
  recipient: string;
  sentAt: string;
  status: 'sent' | 'delivered' | 'failed';
  relatedItem: string;
}

export const ReminderSettingsPanel: React.FC = () => {
  const [reminderRules, setReminderRules] = useState<ReminderRule[]>([
    {
      id: 'rule-1',
      name: 'Audit Due Reminder',
      enabled: true,
      triggerDays: 3,
      triggerEvent: 'audit_due',
      notificationMethod: 'both',
      recipients: ['<EMAIL>', '<EMAIL>']
    },
    {
      id: 'rule-2',
      name: 'NC Response Overdue',
      enabled: true,
      triggerDays: 1,
      triggerEvent: 'nc_due',
      notificationMethod: 'email',
      recipients: ['<EMAIL>']
    }
  ]);

  const [notificationHistory] = useState<NotificationHistory[]>([
    {
      id: 'notif-1',
      type: 'Audit Reminder',
      recipient: '<EMAIL>',
      sentAt: '2024-01-15 10:30:00',
      status: 'delivered',
      relatedItem: 'Quality System Audit - TechCorp'
    },
    {
      id: 'notif-2',
      type: 'NC Due Reminder',
      recipient: '<EMAIL>',
      sentAt: '2024-01-14 14:20:00',
      status: 'sent',
      relatedItem: 'NC-001: Calibration Issue'
    }
  ]);

  const [showAddRule, setShowAddRule] = useState(false);
  const [newRule, setNewRule] = useState<Partial<ReminderRule>>({
    name: '',
    enabled: true,
    triggerDays: 1,
    triggerEvent: 'audit_due',
    notificationMethod: 'email',
    recipients: []
  });

  const toggleRuleEnabled = (ruleId: string) => {
    setReminderRules(rules => 
      rules.map(rule => 
        rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
      )
    );
  };

  const deleteRule = (ruleId: string) => {
    setReminderRules(rules => rules.filter(rule => rule.id !== ruleId));
  };

  const addNewRule = () => {
    if (newRule.name && newRule.triggerDays) {
      const rule: ReminderRule = {
        id: `rule-${Date.now()}`,
        name: newRule.name,
        enabled: newRule.enabled || true,
        triggerDays: newRule.triggerDays,
        triggerEvent: newRule.triggerEvent || 'audit_due',
        notificationMethod: newRule.notificationMethod || 'email',
        recipients: newRule.recipients || []
      };
      setReminderRules(rules => [...rules, rule]);
      setNewRule({});
      setShowAddRule(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'sent': 'bg-blue-100 text-blue-800',
      'delivered': 'bg-green-100 text-green-800',
      'failed': 'bg-red-100 text-red-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getTriggerEventLabel = (event: string) => {
    const labels = {
      'audit_due': 'Audit Due Date',
      'nc_due': 'NC Response Due',
      'followup_due': 'Follow-up Audit Due'
    };
    return labels[event as keyof typeof labels] || event;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              <CardTitle>Reminder Rules</CardTitle>
            </div>
            <Button 
              onClick={() => setShowAddRule(true)}
              size="sm" 
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Rule
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reminderRules.map((rule) => (
              <Card key={rule.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{rule.name}</h4>
                      <Switch 
                        checked={rule.enabled}
                        onCheckedChange={() => toggleRuleEnabled(rule.id)}
                      />
                      {rule.enabled ? (
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      ) : (
                        <Badge variant="secondary">Disabled</Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>
                        <Clock className="h-3 w-3 inline mr-1" />
                        Trigger: {rule.triggerDays} days before {getTriggerEventLabel(rule.triggerEvent)}
                      </p>
                      <p>
                        <Bell className="h-3 w-3 inline mr-1" />
                        Method: {rule.notificationMethod}
                      </p>
                      <p>
                        <Mail className="h-3 w-3 inline mr-1" />
                        Recipients: {rule.recipients.length} configured
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteRule(rule.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            ))}

            {showAddRule && (
              <Card className="p-4 border-dashed">
                <div className="space-y-4">
                  <div>
                    <Label>Rule Name</Label>
                    <Input
                      value={newRule.name || ''}
                      onChange={(e) => setNewRule({...newRule, name: e.target.value})}
                      placeholder="Enter rule name..."
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Trigger Days Before</Label>
                      <Input
                        type="number"
                        value={newRule.triggerDays || 1}
                        onChange={(e) => setNewRule({...newRule, triggerDays: parseInt(e.target.value)})}
                        min="1"
                        max="30"
                      />
                    </div>
                    
                    <div>
                      <Label>Trigger Event</Label>
                      <Select 
                        value={newRule.triggerEvent} 
                        onValueChange={(value) => setNewRule({...newRule, triggerEvent: value as any})}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="audit_due">Audit Due Date</SelectItem>
                          <SelectItem value="nc_due">NC Response Due</SelectItem>
                          <SelectItem value="followup_due">Follow-up Audit Due</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label>Notification Method</Label>
                    <Select 
                      value={newRule.notificationMethod} 
                      onValueChange={(value) => setNewRule({...newRule, notificationMethod: value as any})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="email">Email Only</SelectItem>
                        <SelectItem value="in_app">In-App Only</SelectItem>
                        <SelectItem value="both">Both Email & In-App</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={addNewRule} size="sm">Save Rule</Button>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowAddRule(false)} 
                      size="sm"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <History className="h-5 w-5" />
            <CardTitle>Notification History</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {notificationHistory.map((notification) => (
              <div 
                key={notification.id} 
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">{notification.type}</span>
                    {getStatusBadge(notification.status)}
                  </div>
                  <p className="text-xs text-gray-600 mb-1">
                    To: {notification.recipient}
                  </p>
                  <p className="text-xs text-gray-500">
                    {notification.relatedItem} • {notification.sentAt}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

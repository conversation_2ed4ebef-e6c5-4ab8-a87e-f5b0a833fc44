
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { FileText, Plus, Download, Edit, Copy } from 'lucide-react';

const mockTemplates = [
  {
    id: 'template-001',
    name: 'ISO 9001 Audit Template',
    type: 'Quality System',
    version: '2.1',
    status: 'Active',
    lastModified: '2024-01-10',
    usageCount: 15
  },
  {
    id: 'template-002',
    name: 'Supplier Assessment Checklist',
    type: 'Supplier Evaluation',
    version: '1.5',
    status: 'Active',
    lastModified: '2024-01-08',
    usageCount: 8
  },
  {
    id: 'template-003',
    name: 'Environmental Audit Template',
    type: 'Environmental',
    version: '1.0',
    status: 'Draft',
    lastModified: '2024-01-05',
    usageCount: 0
  }
];

export const AuditTemplateManager: React.FC = () => {
  const [templates] = useState(mockTemplates);

  const getStatusBadge = (status: string) => {
    const variant = status === 'Active' ? 'default' : 'secondary';
    return <Badge variant={variant}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Audit Templates</h3>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Template Library</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Template Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Usage Count</TableHead>
                <TableHead>Last Modified</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {templates.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-600" />
                      {template.name}
                    </div>
                  </TableCell>
                  <TableCell>{template.type}</TableCell>
                  <TableCell>v{template.version}</TableCell>
                  <TableCell>{getStatusBadge(template.status)}</TableCell>
                  <TableCell>{template.usageCount}</TableCell>
                  <TableCell>{template.lastModified}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

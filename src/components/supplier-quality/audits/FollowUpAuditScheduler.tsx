
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Calendar } from 'lucide-react';
import { FollowUpAuditDetailModal } from './FollowUpAuditDetailModal';

export const FollowUpAuditScheduler: React.FC = () => {
  const [followUpAudits, setFollowUpAudits] = useState<any[]>([
    {
      id: 'fu-001',
      originalAuditId: 'audit-001',
      supplierName: 'TechCorp Manufacturing',
      scheduledDate: '2024-02-15',
      status: 'scheduled',
      priority: 'high',
      focusAreas: ['Calibration Management', 'Document Control'],
      previousNCs: [
        {
          id: 'nc-001',
          description: 'Calibration certificates expired',
          severity: 'Major',
          status: 'in_progress',
          dueDate: '2024-02-10',
          correctiveAction: 'Implementing new calibration tracking system'
        }
      ],
      expectedOutcomes: ['Verify calibration system implementation', 'Close outstanding NCs'],
      assignedAuditor: '<PERSON>',
      estimatedDuration: 4
    }
  ]);

  const [selectedAudit, setSelectedAudit] = useState<any>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const getPriorityColor = (priority: string) => {
    const variants = {
      'critical': 'bg-red-100 text-red-800',
      'high': 'bg-orange-100 text-orange-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'low': 'bg-green-100 text-green-800'
    };
    return variants[priority] || 'bg-gray-100 text-gray-800';
  };

  const handleViewDetails = (audit: any) => {
    setSelectedAudit(audit);
    setShowDetailModal(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Follow-up Audit Scheduler</h3>
        <div className="flex gap-2">
          <Input type="date" className="max-w-xs" />
          <Button>Apply Filters</Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Follow-up Audits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {followUpAudits.map((audit) => (
              <Card key={audit.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{audit.supplierName}</h4>
                      <Badge className={getPriorityColor(audit.priority)}>
                        {audit.priority} Priority
                      </Badge>
                      <Badge variant="outline">{audit.status}</Badge>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Scheduled: {new Date(audit.scheduledDate).toLocaleDateString()}</p>
                      <p>Focus Areas: {audit.focusAreas.join(', ')}</p>
                      <p>Open NCs: {audit.previousNCs.filter((nc: any) => nc.status !== 'closed').length}</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(audit)}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detail Modal */}
      {selectedAudit && (
        <FollowUpAuditDetailModal
          audit={selectedAudit}
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          onSchedule={(auditId) => console.log('Schedule audit:', auditId)}
          onEdit={(auditId) => console.log('Edit audit:', auditId)}
        />
      )}
    </div>
  );
};

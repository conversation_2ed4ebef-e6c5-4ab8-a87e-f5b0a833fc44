
import React, { useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SupplierAudit } from "@/types/supplierAudits";
import { AuditStatusBadge } from "./AuditStatusBadge";
import { format } from "date-fns";
import { Plus, Calendar as CalendarIcon, Clock, MapPin, User } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface AuditCalendarViewProps {
  audits: SupplierAudit[];
}

interface NewEvent {
  title: string;
  type: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  attendees: string;
  description: string;
}

export const AuditCalendarView: React.FC<AuditCalendarViewProps> = ({ audits }) => {
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isCreateEventOpen, setIsCreateEventOpen] = useState(false);
  const [newEvent, setNewEvent] = useState<NewEvent>({
    title: '',
    type: 'audit',
    date: '',
    time: '',
    duration: '2',
    location: '',
    attendees: '',
    description: ''
  });
  
  // Get audits for selected date
  const auditsForSelectedDate = selectedDate 
    ? audits.filter(
        audit => format(new Date(audit.scheduledDate), 'yyyy-MM-dd') === 
          format(selectedDate, 'yyyy-MM-dd')
      )
    : [];
  
  // Get all dates with audits for highlighting in calendar
  const auditDates = audits.map(audit => new Date(audit.scheduledDate));

  const handleCreateEvent = () => {
    if (!newEvent.title || !newEvent.date || !newEvent.time) {
      toast({
        title: "Missing Information",
        description: "Please fill in title, date, and time",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Event Created",
      description: `${newEvent.type} "${newEvent.title}" has been scheduled successfully`
    });

    setNewEvent({
      title: '',
      type: 'audit',
      date: '',
      time: '',
      duration: '2',
      location: '',
      attendees: '',
      description: ''
    });
    setIsCreateEventOpen(false);
  };

  const openCreateEventModal = () => {
    if (selectedDate) {
      setNewEvent({
        ...newEvent,
        date: format(selectedDate, 'yyyy-MM-dd')
      });
    }
    setIsCreateEventOpen(true);
  };

  const eventTypes = [
    { value: 'audit', label: 'Supplier Audit' },
    { value: 'meeting', label: 'Review Meeting' },
    { value: 'assessment', label: 'Assessment' },
    { value: 'training', label: 'Training Session' },
    { value: 'deadline', label: 'Compliance Deadline' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Audit & Event Calendar</h3>
        <Button onClick={openCreateEventModal} className="gap-2">
          <Plus className="h-4 w-4" />
          Schedule Event
        </Button>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        <div className="lg:w-1/2">
          <div className="bg-white rounded-md">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border shadow-sm"
              modifiers={{
                audit: auditDates,
              }}
              modifiersStyles={{
                audit: { 
                  fontWeight: 'bold',
                  backgroundColor: '#e2e8f0',
                  color: '#1e40af'
                }
              }}
            />
          </div>
        </div>
        
        <div className="lg:w-1/2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'No date selected'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {auditsForSelectedDate.length > 0 ? (
                <div className="space-y-3">
                  {auditsForSelectedDate.map(audit => (
                    <Card key={audit.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h4 className="font-medium">{audit.supplierName}</h4>
                            <p className="text-sm text-gray-600">{audit.auditType}</p>
                            {audit.auditor && (
                              <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                                <User className="h-3 w-3" />
                                {audit.auditor}
                              </div>
                            )}
                          </div>
                          <AuditStatusBadge status={audit.status} />
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            9:00 AM
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            On-site
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-2">
                          <Button size="sm" variant="outline" className="text-xs">
                            View Details
                          </Button>
                          <Button size="sm" variant="outline" className="text-xs">
                            Assign Auditor
                          </Button>
                          <Button size="sm" variant="outline" className="text-xs">
                            Upload Checklist
                          </Button>
                          {audit.status === "Completed" && (
                            <Button size="sm" variant="outline" className="text-xs text-blue-600">
                              View Findings
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-md">
                  <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500 mb-4">No events scheduled for this date</p>
                  <Button onClick={openCreateEventModal} variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Schedule Event
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Create Event Modal */}
      <Dialog open={isCreateEventOpen} onOpenChange={setIsCreateEventOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Schedule New Event
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Event Title</Label>
                <Input
                  id="title"
                  value={newEvent.title}
                  onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                  placeholder="Enter event title"
                />
              </div>
              <div>
                <Label htmlFor="type">Event Type</Label>
                <select
                  id="type"
                  value={newEvent.type}
                  onChange={(e) => setNewEvent({...newEvent, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {eventTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={newEvent.date}
                  onChange={(e) => setNewEvent({...newEvent, date: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={newEvent.time}
                  onChange={(e) => setNewEvent({...newEvent, time: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="duration">Duration (hours)</Label>
                <Input
                  id="duration"
                  value={newEvent.duration}
                  onChange={(e) => setNewEvent({...newEvent, duration: e.target.value})}
                  placeholder="2"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={newEvent.location}
                  onChange={(e) => setNewEvent({...newEvent, location: e.target.value})}
                  placeholder="Enter location"
                />
              </div>
              <div>
                <Label htmlFor="attendees">Attendees</Label>
                <Input
                  id="attendees"
                  value={newEvent.attendees}
                  onChange={(e) => setNewEvent({...newEvent, attendees: e.target.value})}
                  placeholder="Enter attendee emails"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <textarea
                id="description"
                value={newEvent.description}
                onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md h-24"
                placeholder="Enter event description"
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setIsCreateEventOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateEvent}>
              Schedule Event
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

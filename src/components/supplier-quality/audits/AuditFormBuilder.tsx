
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  Plus, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Camera,
  FileText,
  Warehouse,
  Wrench,
  ShieldCheck
} from "lucide-react";
import { AuditForm, AuditSection, AuditQuestion, AuditSectionType } from "@/types/auditIntake";
import { useToast } from "@/hooks/use-toast";

interface AuditFormBuilderProps {
  intakeId: string;
  onSave: (form: Omit<AuditForm, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialData?: AuditForm;
}

const sectionConfig = {
  'receiving-inspection': {
    name: 'Receiving Inspection',
    icon: <FileText className="h-4 w-4" />,
    color: 'bg-blue-50 text-blue-700'
  },
  'warehouse': {
    name: 'Warehouse',
    icon: <Warehouse className="h-4 w-4" />,
    color: 'bg-green-50 text-green-700'
  },
  'assembly': {
    name: 'Assembly',
    icon: <Wrench className="h-4 w-4" />,
    color: 'bg-orange-50 text-orange-700'
  },
  'clean-room': {
    name: 'Clean Room',
    icon: <ShieldCheck className="h-4 w-4" />,
    color: 'bg-purple-50 text-purple-700'
  }
};

export const AuditFormBuilder: React.FC<AuditFormBuilderProps> = ({
  intakeId,
  onSave,
  initialData
}) => {
  const [sections, setSections] = useState<AuditSection[]>(
    initialData?.sections || Object.keys(sectionConfig).map(key => ({
      id: key,
      name: sectionConfig[key as AuditSectionType].name,
      questions: [
        {
          id: `${key}-q1`,
          question: "Are materials stored per spec?",
          score: 1,
          evidenceFiles: [],
          auditorComments: "All racks labeled and dated",
          required: true
        }
      ],
      completionStatus: 'not-started' as const
    }))
  );
  
  const [activeTab, setActiveTab] = useState('receiving-inspection');
  const { toast } = useToast();

  const addQuestion = (sectionId: string) => {
    setSections(prev => prev.map(section => {
      if (section.id === sectionId) {
        const newQuestion: AuditQuestion = {
          id: `${sectionId}-q${section.questions.length + 1}`,
          question: "",
          score: 0,
          evidenceFiles: [],
          auditorComments: "",
          required: false
        };
        return {
          ...section,
          questions: [...section.questions, newQuestion]
        };
      }
      return section;
    }));
  };

  const updateQuestion = (sectionId: string, questionId: string, updates: Partial<AuditQuestion>) => {
    setSections(prev => prev.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          questions: section.questions.map(q => 
            q.id === questionId ? { ...q, ...updates } : q
          )
        };
      }
      return section;
    }));
  };

  const deleteQuestion = (sectionId: string, questionId: string) => {
    setSections(prev => prev.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          questions: section.questions.filter(q => q.id !== questionId)
        };
      }
      return section;
    }));
  };

  const calculateOverallScore = () => {
    const totalQuestions = sections.reduce((sum, section) => sum + section.questions.length, 0);
    const totalScore = sections.reduce((sum, section) => 
      sum + section.questions.reduce((sectionSum, q) => sectionSum + q.score, 0), 0
    );
    return totalQuestions > 0 ? Math.round((totalScore / totalQuestions) * 100) : 0;
  };

  const handleSave = () => {
    onSave({
      intakeId,
      sections,
      overallScore: calculateOverallScore()
    });
    
    toast({
      title: "Audit Form Saved",
      description: "Your audit form has been saved successfully."
    });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Area-Specific Audit Form Builder</CardTitle>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="text-lg px-3 py-1">
                Overall Score: {calculateOverallScore()}%
              </Badge>
              <Button onClick={handleSave}>
                Save Audit Form
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              {Object.entries(sectionConfig).map(([key, config]) => {
                const section = sections.find(s => s.id === key);
                const completedQuestions = section?.questions.filter(q => q.score === 1).length || 0;
                const totalQuestions = section?.questions.length || 0;
                
                return (
                  <TabsTrigger 
                    key={key} 
                    value={key}
                    className="flex flex-col items-center gap-1 p-3"
                  >
                    <div className="flex items-center gap-2">
                      {config.icon}
                      {config.name}
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {completedQuestions}/{totalQuestions}
                    </Badge>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {Object.keys(sectionConfig).map(sectionKey => {
              const section = sections.find(s => s.id === sectionKey);
              if (!section) return null;
              
              return (
                <TabsContent key={sectionKey} value={sectionKey} className="mt-6">
                  <Card>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        <CardTitle className="flex items-center gap-2">
                          {sectionConfig[sectionKey as AuditSectionType].icon}
                          {section.name}
                        </CardTitle>
                        <Button 
                          onClick={() => addQuestion(section.id)}
                          size="sm"
                          className="gap-2"
                        >
                          <Plus className="h-4 w-4" />
                          Add Question
                        </Button>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="space-y-6">
                      {section.questions.map((question, index) => (
                        <Card key={question.id} className="p-4 border-2 border-dashed">
                          <div className="space-y-4">
                            <div className="flex justify-between items-start">
                              <Label className="text-sm font-medium">
                                Question {index + 1}
                              </Label>
                              <Button
                                onClick={() => deleteQuestion(section.id, question.id)}
                                variant="ghost"
                                size="sm"
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Audit Question</Label>
                                <Textarea
                                  value={question.question}
                                  onChange={(e) => updateQuestion(section.id, question.id, { question: e.target.value })}
                                  placeholder="Enter audit question..."
                                  rows={2}
                                />
                              </div>
                              
                              <div className="space-y-2">
                                <Label>Score</Label>
                                <div className="flex gap-2">
                                  <Button
                                    type="button"
                                    variant={question.score === 0 ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => updateQuestion(section.id, question.id, { score: 0 })}
                                    className="flex items-center gap-1"
                                  >
                                    <XCircle className="h-4 w-4" />
                                    Fail (0)
                                  </Button>
                                  <Button
                                    type="button"
                                    variant={question.score === 1 ? "default" : "outline"}
                                    size="sm"
                                    onClick={() => updateQuestion(section.id, question.id, { score: 1 })}
                                    className="flex items-center gap-1"
                                  >
                                    <CheckCircle className="h-4 w-4" />
                                    Pass (1)
                                  </Button>
                                </div>
                              </div>
                            </div>
                            
                            <div className="space-y-2">
                              <Label>Auditor Comments</Label>
                              <Textarea
                                value={question.auditorComments}
                                onChange={(e) => updateQuestion(section.id, question.id, { auditorComments: e.target.value })}
                                placeholder="Add auditor comments and observations..."
                                rows={3}
                              />
                            </div>
                            
                            <div className="space-y-2">
                              <Label>Evidence Upload</Label>
                              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                <div className="flex items-center justify-center gap-4">
                                  <Button type="button" variant="outline" size="sm" className="gap-2">
                                    <Camera className="h-4 w-4" />
                                    Take Photo
                                  </Button>
                                  <Button type="button" variant="outline" size="sm" className="gap-2">
                                    <Upload className="h-4 w-4" />
                                    Upload File
                                  </Button>
                                </div>
                                <p className="text-xs text-gray-500 mt-2">
                                  Supported: JPG, PNG, PDF (Max 5MB)
                                </p>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                      
                      {section.questions.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          No questions added yet. Click "Add Question" to get started.
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Upload, 
  MessageSquare, 
  AlertTriangle,
  Save,
  FileText
} from 'lucide-react';
import { AuditSection, AuditQuestion } from '@/types/auditIntake';

interface AuditExecutionPanelProps {
  auditId: string;
  sections: AuditSection[];
  onSave: (sectionId: string, questionId: string, updates: Partial<AuditQuestion>) => void;
  onAddObservation: (sectionId: string, observation: string) => void;
  onAddNC: (sectionId: string, ncData: any) => void;
}

export const AuditExecutionPanel: React.FC<AuditExecutionPanelProps> = ({
  auditId,
  sections,
  onSave,
  onAddObservation,
  onAddNC
}) => {
  const [selectedSection, setSelectedSection] = useState(sections[0]?.id || '');
  const [selectedQuestion, setSelectedQuestion] = useState('');
  const [observationText, setObservationText] = useState('');
  const [ncDescription, setNcDescription] = useState('');

  const currentSection = sections.find(s => s.id === selectedSection);
  const currentQuestion = currentSection?.questions.find(q => q.id === selectedQuestion);

  const handleScoreUpdate = (questionId: string, score: 0 | 1) => {
    onSave(selectedSection, questionId, { score });
  };

  const handleCommentUpdate = (questionId: string, comments: string) => {
    onSave(selectedSection, questionId, { auditorComments: comments });
  };

  const handleAddObservation = () => {
    if (observationText.trim()) {
      onAddObservation(selectedSection, observationText);
      setObservationText('');
    }
  };

  const handleAddNC = () => {
    if (ncDescription.trim()) {
      onAddNC(selectedSection, {
        description: ncDescription,
        severity: 'Major',
        questionId: selectedQuestion
      });
      setNcDescription('');
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - Checklist Navigation */}
      <div className="w-1/3 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Audit Checklist</h2>
          <p className="text-sm text-gray-600">Select section and questions to evaluate</p>
        </div>
        
        <div className="p-4">
          {sections.map((section) => (
            <Card 
              key={section.id} 
              className={`mb-3 cursor-pointer transition-colors ${
                selectedSection === section.id ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedSection(section.id)}
            >
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">{section.name}</CardTitle>
                  <Badge variant={section.completionStatus === 'completed' ? 'default' : 'secondary'}>
                    {section.questions.filter(q => q.score === 1).length}/{section.questions.length}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-1">
                  {section.questions.map((question) => (
                    <div 
                      key={question.id}
                      className={`flex items-center gap-2 p-2 rounded text-xs cursor-pointer ${
                        selectedQuestion === question.id ? 'bg-blue-100' : 'hover:bg-gray-100'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedQuestion(question.id);
                      }}
                    >
                      {question.score === 1 ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : question.score === 0 ? (
                        <XCircle className="h-3 w-3 text-red-600" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border-2 border-gray-300" />
                      )}
                      <span className="truncate flex-1">{question.question}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Right Panel - Question Evaluation */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {currentQuestion ? (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Question Evaluation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Question</Label>
                    <p className="mt-1 text-gray-700">{currentQuestion.question}</p>
                    {currentQuestion.required && (
                      <Badge variant="destructive" className="mt-2">Required</Badge>
                    )}
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <Label className="text-base font-medium">Score</Label>
                    <div className="flex gap-3 mt-2">
                      <Button
                        variant={currentQuestion.score === 0 ? "destructive" : "outline"}
                        size="sm"
                        onClick={() => handleScoreUpdate(currentQuestion.id, 0)}
                        className="flex items-center gap-2"
                      >
                        <XCircle className="h-4 w-4" />
                        Fail (0)
                      </Button>
                      <Button
                        variant={currentQuestion.score === 1 ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleScoreUpdate(currentQuestion.id, 1)}
                        className="flex items-center gap-2"
                      >
                        <CheckCircle className="h-4 w-4" />
                        Pass (1)
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="comments" className="text-base font-medium">Auditor Comments</Label>
                    <Textarea
                      id="comments"
                      value={currentQuestion.auditorComments}
                      onChange={(e) => handleCommentUpdate(currentQuestion.id, e.target.value)}
                      placeholder="Add your observations and comments..."
                      rows={4}
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <Label className="text-base font-medium">Evidence Attachments</Label>
                    <div className="mt-2 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                      <Button variant="outline" size="sm" className="gap-2">
                        <Upload className="h-4 w-4" />
                        Upload Evidence
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">
                        Upload photos, documents, or other evidence
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Card className="flex-1">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <Label className="font-medium">Add Observation</Label>
                      <Textarea
                        value={observationText}
                        onChange={(e) => setObservationText(e.target.value)}
                        placeholder="Record additional observations..."
                        rows={2}
                      />
                      <Button 
                        onClick={handleAddObservation}
                        size="sm" 
                        className="gap-2"
                        disabled={!observationText.trim()}
                      >
                        <MessageSquare className="h-4 w-4" />
                        Add Observation
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="flex-1">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <Label className="font-medium">Add Non-Conformance</Label>
                      <Textarea
                        value={ncDescription}
                        onChange={(e) => setNcDescription(e.target.value)}
                        placeholder="Describe the non-conformance..."
                        rows={2}
                      />
                      <Button 
                        onClick={handleAddNC}
                        variant="destructive"
                        size="sm" 
                        className="gap-2"
                        disabled={!ncDescription.trim()}
                      >
                        <AlertTriangle className="h-4 w-4" />
                        Add NC
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end">
                <Button className="gap-2">
                  <Save className="h-4 w-4" />
                  Save & Continue
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Select a question from the left panel to begin evaluation</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangle, Clock, CheckCircle, FileText } from 'lucide-react';

const mockCAPAs = [
  {
    id: 'capa-001',
    supplier: 'TechCorp Industries',
    issue: 'Documentation gaps in quality procedures',
    severity: 'Medium',
    status: 'Open',
    dueDate: '2024-02-15',
    assignee: 'Quality Manager',
    progress: 65
  },
  {
    id: 'capa-002',
    supplier: 'Global Manufacturing',
    issue: 'Calibration records incomplete',
    severity: 'High',
    status: 'In Progress',
    dueDate: '2024-01-30',
    assignee: 'Technical Lead',
    progress: 80
  },
  {
    id: 'capa-003',
    supplier: 'Precision Parts Ltd',
    issue: 'Training records update required',
    severity: 'Low',
    status: 'Closed',
    dueDate: '2024-01-20',
    assignee: 'HR Manager',
    progress: 100
  }
];

export const CAPAManagement: React.FC = () => {
  const [capas] = useState(mockCAPAs);

  const getSeverityBadge = (severity: string) => {
    const variants = {
      'High': 'bg-red-100 text-red-800',
      'Medium': 'bg-yellow-100 text-yellow-800',
      'Low': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[severity as keyof typeof variants]}>{severity}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'Open': 'bg-blue-100 text-blue-800',
      'In Progress': 'bg-yellow-100 text-yellow-800',
      'Closed': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Open':
        return <AlertTriangle className="h-4 w-4 text-blue-600" />;
      case 'In Progress':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'Closed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">CAPA Management</h3>
        <Button className="gap-2">
          <FileText className="h-4 w-4" />
          Create CAPA
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Open CAPAs</p>
                <p className="text-2xl font-bold">{capas.filter(c => c.status === 'Open').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">In Progress</p>
                <p className="text-2xl font-bold">{capas.filter(c => c.status === 'In Progress').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Closed</p>
                <p className="text-2xl font-bold">{capas.filter(c => c.status === 'Closed').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active CAPAs</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>CAPA ID</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Issue</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {capas.map((capa) => (
                <TableRow key={capa.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(capa.status)}
                      {capa.id}
                    </div>
                  </TableCell>
                  <TableCell>{capa.supplier}</TableCell>
                  <TableCell className="max-w-xs truncate">{capa.issue}</TableCell>
                  <TableCell>{getSeverityBadge(capa.severity)}</TableCell>
                  <TableCell>{getStatusBadge(capa.status)}</TableCell>
                  <TableCell>{capa.dueDate}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${capa.progress}%` }}
                        />
                      </div>
                      <span className="text-sm">{capa.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">View</Button>
                      <Button variant="outline" size="sm">Update</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};


import React from 'react';
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  User, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText,
  TrendingUp,
  Target
} from 'lucide-react';

interface FollowUpAuditDetailModalProps {
  audit: any;
  isOpen: boolean;
  onClose: () => void;
  onSchedule: (auditId: string) => void;
  onEdit: (auditId: string) => void;
}

export const FollowUpAuditDetailModal: React.FC<FollowUpAuditDetailModalProps> = ({
  audit,
  isOpen,
  onClose,
  onSchedule,
  onEdit
}) => {
  if (!audit) return null;

  const getPrioritySeverity = (priority: string) => {
    switch (priority) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Follow-up Audit Details - {audit.supplierName}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">Audit History</TabsTrigger>
            <TabsTrigger value="ncs">Non-Conformances</TabsTrigger>
            <TabsTrigger value="planning">Planning</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Overview Cards */}
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Target className="h-8 w-8 text-blue-500" />
                    <div>
                      <p className="text-2xl font-bold">{audit.previousNCs.length}</p>
                      <p className="text-sm text-gray-600">Open NCs</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-8 w-8 text-green-500" />
                    <div>
                      <p className="text-2xl font-bold">{audit.estimatedDuration}h</p>
                      <p className="text-sm text-gray-600">Duration</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-8 w-8 text-orange-500" />
                    <div>
                      <p className="text-2xl font-bold">
                        <Badge variant={getPrioritySeverity(audit.priority)}>
                          {audit.priority}
                        </Badge>
                      </p>
                      <p className="text-sm text-gray-600">Priority</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Audit Details */}
            <Card>
              <CardHeader>
                <CardTitle>Audit Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium">Scheduled Date</p>
                    <p className="text-sm text-gray-600">{new Date(audit.scheduledDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Assigned Auditor</p>
                    <p className="text-sm text-gray-600">{audit.assignedAuditor}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Focus Areas</p>
                    <div className="flex gap-1 mt-1">
                      {audit.focusAreas.map((area: string, index: number) => (
                        <Badge key={index} variant="outline">{area}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Expected Outcomes</p>
                    <ul className="text-sm text-gray-600 mt-1">
                      {audit.expectedOutcomes.map((outcome: string, index: number) => (
                        <li key={index}>• {outcome}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Previous Audit History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Initial Audit - {audit.originalAuditId}</p>
                      <p className="text-sm text-gray-600">Completed 3 months ago</p>
                      <p className="text-sm text-gray-500">Found {audit.previousNCs.length} non-conformances</p>
                    </div>
                    <Badge variant="outline">Completed</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ncs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Non-Conformances to Follow-up</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {audit.previousNCs.map((nc: any) => (
                    <div key={nc.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                            <span className="font-medium">{nc.description}</span>
                            <Badge variant="destructive">{nc.severity}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{nc.correctiveAction}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Due: {new Date(nc.dueDate).toLocaleDateString()}</span>
                            <Badge variant={nc.status === 'in_progress' ? 'secondary' : 'outline'}>
                              {nc.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="planning" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Audit Planning</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Recommended Focus Areas</h4>
                  <div className="space-y-2">
                    {audit.focusAreas.map((area: string, index: number) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{area}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-2">Pre-Audit Checklist</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Review previous audit findings</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Verify corrective action status</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">Schedule supplier notification</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button variant="outline" onClick={() => onEdit(audit.id)}>
            Edit Audit
          </Button>
          <Button onClick={() => onSchedule(audit.id)}>
            Proceed with Audit
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

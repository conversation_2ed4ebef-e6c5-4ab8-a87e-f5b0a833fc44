
import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { PaperclipIcon, Send } from "lucide-react";
import { SupplierMessage } from "@/types/supplierMessages";

interface ReplyFormProps {
  threadId: string;
  onSubmit: (threadId: string, message: Omit<SupplierMessage, 'id' | 'threadId'>) => void;
}

export const ReplyForm: React.FC<ReplyFormProps> = ({ threadId, onSubmit }) => {
  const [replyText, setReplyText] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setAttachments(prev => [...prev, ...filesArray]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (replyText.trim() === "") return;

    const fileAttachments = attachments.map((file, index) => ({
      id: `att-new-${index}`,
      name: file.name,
      type: file.type,
      size: `${(file.size / 1024).toFixed(0)} KB`,
      url: "#"
    }));

    onSubmit(threadId, {
      from: "<EMAIL>",
      fromName: "BPR Hub QA Team",
      to: "<EMAIL>",
      toName: "Supplier Contact",
      body: replyText,
      timestamp: new Date().toISOString(),
      attachments: fileAttachments,
      isRead: true
    });

    setReplyText("");
    setAttachments([]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={handleSubmit}>
      <Textarea
        value={replyText}
        onChange={(e) => setReplyText(e.target.value)}
        placeholder="Type your reply here..."
        className="min-h-[120px] mb-3"
      />

      {attachments.length > 0 && (
        <div className="mb-3">
          <p className="text-xs font-medium text-gray-500 mb-2">ATTACHMENTS</p>
          <div className="flex flex-wrap gap-1">
            {attachments.map((file, index) => (
              <div 
                key={index} 
                className="bg-gray-100 rounded px-2 py-1 text-xs flex items-center"
              >
                <span className="truncate max-w-[200px]">{file.name}</span>
                <button 
                  type="button" 
                  className="ml-1 text-gray-500 hover:text-gray-700" 
                  onClick={() => removeAttachment(index)}
                >
                  &times;
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <div>
          <label htmlFor="file-upload" className="cursor-pointer">
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              className="gap-1"
            >
              <PaperclipIcon className="h-4 w-4" />
              Attach Files
            </Button>
            <input 
              id="file-upload" 
              type="file" 
              multiple 
              onChange={handleAttachmentChange}
              className="hidden" 
            />
          </label>
        </div>
        <Button type="submit" disabled={replyText.trim() === ""} className="gap-1">
          <Send className="h-4 w-4" />
          Send
        </Button>
      </div>
    </form>
  );
};


import React from "react";
import { MessageThread, MessageTag } from "@/types/supplierMessages";
import { format } from "date-fns";
import { Mail, PaperclipIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ThreadListProps {
  threads: MessageThread[];
  selectedThreadId: string | null;
  onSelectThread: (id: string) => void;
}

export const ThreadList: React.FC<ThreadListProps> = ({ 
  threads, 
  selectedThreadId, 
  onSelectThread 
}) => {
  const getTagColor = (tag: MessageTag) => {
    switch(tag) {
      case "Audit": return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "NC": return "bg-red-100 text-red-800 hover:bg-red-100";
      case "PO": return "bg-green-100 text-green-800 hover:bg-green-100";
      case "CAPA": return "bg-orange-100 text-orange-800 hover:bg-orange-100";
      case "Urgent": return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      default: return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const hasUnreadMessages = (thread: MessageThread) => {
    return thread.messages.some(message => !message.isRead);
  };

  return (
    <div className="divide-y overflow-y-auto max-h-[calc(100vh-280px)]">
      {threads.length === 0 ? (
        <div className="py-8 text-center text-gray-500">
          <Mail className="h-12 w-12 mx-auto mb-2 text-gray-400" />
          <p>No messages found</p>
        </div>
      ) : (
        threads.map(thread => {
          const isSelected = selectedThreadId === thread.id;
          const isUnread = hasUnreadMessages(thread);
          const lastMessage = thread.messages[thread.messages.length - 1];
          const hasAttachments = lastMessage.attachments.length > 0;
          
          return (
            <div 
              key={thread.id}
              className={`p-4 cursor-pointer ${isSelected ? 'bg-blue-50' : ''} ${isUnread ? 'font-medium' : ''} hover:bg-gray-50`}
              onClick={() => onSelectThread(thread.id)}
            >
              <div className="flex justify-between mb-1">
                <h3 className={`text-sm ${isUnread ? 'font-semibold' : ''}`}>{thread.subject}</h3>
                <span className="text-xs text-gray-500">
                  {format(new Date(thread.lastMessageDate), "MMM d")}
                </span>
              </div>
              
              <div className="flex items-center text-xs text-gray-600 mb-1.5">
                <span className="truncate">
                  {lastMessage.fromName} ({lastMessage.from})
                </span>
              </div>
              
              <p className="text-xs text-gray-500 truncate mb-2">
                {lastMessage.body.substring(0, 80)}...
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex gap-1 flex-wrap">
                  {thread.tags.map(tag => (
                    <Badge 
                      key={tag} 
                      variant="outline" 
                      className={`text-xs py-0.5 px-1.5 ${getTagColor(tag)}`}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
                {hasAttachments && (
                  <PaperclipIcon className="h-3.5 w-3.5 text-gray-500" />
                )}
              </div>
            </div>
          );
        })
      )}
    </div>
  );
};

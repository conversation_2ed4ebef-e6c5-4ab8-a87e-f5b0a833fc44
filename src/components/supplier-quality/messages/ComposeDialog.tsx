
import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EmailTemplate, MessageTag } from "@/types/supplierMessages";
import { Badge } from "@/components/ui/badge";
import { PaperclipIcon } from "lucide-react";

interface ComposeDialogProps {
  open: boolean;
  onClose: () => void;
  onSend: (message: { to: string, subject: string, body: string, attachments: File[] }) => void;
  emailTemplates: EmailTemplate[];
}

export const ComposeDialog: React.FC<ComposeDialogProps> = ({ 
  open, 
  onClose, 
  onSend,
  emailTemplates 
}) => {
  const [to, setTo] = useState("");
  const [subject, setSubject] = useState("");
  const [body, setBody] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = emailTemplates.find(t => t.id === templateId);
    if (template) {
      setSubject(template.subject);
      setBody(template.body);
    }
  };

  const handleAttachmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const filesArray = Array.from(e.target.files);
      setAttachments(prev => [...prev, ...filesArray]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSend({
      to,
      subject,
      body,
      attachments
    });
    resetForm();
    onClose();
  };

  const resetForm = () => {
    setTo("");
    setSubject("");
    setBody("");
    setAttachments([]);
    setSelectedTemplate("");
  };

  const getTagColor = (tag: MessageTag) => {
    switch(tag) {
      case "Audit": return "bg-blue-100 text-blue-800";
      case "NC": return "bg-red-100 text-red-800";
      case "PO": return "bg-green-100 text-green-800";
      case "CAPA": return "bg-orange-100 text-orange-800";
      case "Urgent": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>New Message</DialogTitle>
          <DialogDescription>
            Compose a new message to send to suppliers
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label htmlFor="template">Email Template</Label>
              <Select value={selectedTemplate} onValueChange={handleTemplateChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a template or start from scratch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No template (blank)</SelectItem>
                  {emailTemplates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      <div className="flex items-center justify-between w-full">
                        <span>{template.name}</span>
                        <div className="flex gap-1">
                          {template.tags.map(tag => (
                            <Badge 
                              key={tag} 
                              variant="outline" 
                              className={`text-xs py-0 ${getTagColor(tag)}`}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="to">To</Label>
              <Input
                id="to"
                value={to}
                onChange={(e) => setTo(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Message subject"
                required
              />
            </div>

            <div>
              <Label htmlFor="body">Message</Label>
              <Textarea
                id="body"
                value={body}
                onChange={(e) => setBody(e.target.value)}
                placeholder="Type your message here..."
                className="min-h-[200px]"
                required
              />
            </div>

            {attachments.length > 0 && (
              <div>
                <Label>Attachments</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {attachments.map((file, index) => (
                    <div 
                      key={index} 
                      className="bg-gray-100 rounded px-2 py-1 text-xs flex items-center"
                    >
                      <span className="truncate max-w-[200px]">{file.name}</span>
                      <button 
                        type="button" 
                        className="ml-1 text-gray-500 hover:text-gray-700" 
                        onClick={() => removeAttachment(index)}
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div>
              <label htmlFor="dialog-file-upload" className="cursor-pointer">
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  className="gap-1"
                >
                  <PaperclipIcon className="h-4 w-4" />
                  Attach Files
                </Button>
                <input 
                  id="dialog-file-upload" 
                  type="file" 
                  multiple 
                  onChange={handleAttachmentChange}
                  className="hidden" 
                />
              </label>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Send Message</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

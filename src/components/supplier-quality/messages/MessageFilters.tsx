
import React from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageTag } from "@/types/supplierMessages";

interface MessageFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedTags: MessageTag[];
  onTagToggle: (tag: MessageTag) => void;
}

export const MessageFilters: React.FC<MessageFiltersProps> = ({
  searchQuery,
  onSearchChange,
  selectedTags,
  onTagToggle
}) => {
  const availableTags: MessageTag[] = ["Audit", "NC", "PO", "CAPA", "General", "Urgent"];

  const getTagColor = (tag: MessageTag, isSelected: boolean) => {
    if (isSelected) {
      switch(tag) {
        case "Audit": return "bg-blue-500 text-white hover:bg-blue-600";
        case "NC": return "bg-red-500 text-white hover:bg-red-600";
        case "PO": return "bg-green-500 text-white hover:bg-green-600";
        case "CAPA": return "bg-orange-500 text-white hover:bg-orange-600";
        case "Urgent": return "bg-purple-500 text-white hover:bg-purple-600";
        default: return "bg-gray-500 text-white hover:bg-gray-600";
      }
    } else {
      switch(tag) {
        case "Audit": return "bg-blue-100 text-blue-800 hover:bg-blue-200";
        case "NC": return "bg-red-100 text-red-800 hover:bg-red-200";
        case "PO": return "bg-green-100 text-green-800 hover:bg-green-200";
        case "CAPA": return "bg-orange-100 text-orange-800 hover:bg-orange-200";
        case "Urgent": return "bg-purple-100 text-purple-800 hover:bg-purple-200";
        default: return "bg-gray-100 text-gray-800 hover:bg-gray-200";
      }
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Input
          type="search"
          placeholder="Search messages..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full"
        />
      </div>
      
      <div>
        <p className="text-sm font-medium mb-2">Filter by tags:</p>
        <div className="flex flex-wrap gap-2">
          {availableTags.map(tag => {
            const isSelected = selectedTags.includes(tag);
            return (
              <Badge
                key={tag}
                variant="outline"
                className={`cursor-pointer px-2 py-1 ${getTagColor(tag, isSelected)}`}
                onClick={() => onTagToggle(tag)}
              >
                {tag}
              </Badge>
            );
          })}
        </div>
      </div>
      
      {selectedTags.length > 0 && (
        <div className="text-right">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => selectedTags.forEach(tag => onTagToggle(tag))}
            className="text-xs"
          >
            Clear filters
          </Button>
        </div>
      )}
    </div>
  );
};

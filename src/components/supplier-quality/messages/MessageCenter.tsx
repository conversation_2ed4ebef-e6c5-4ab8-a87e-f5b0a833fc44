
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ThreadList } from "./ThreadList";
import { ThreadDetail } from "./ThreadDetail";
import { MessageFilters } from "./MessageFilters";
import { ComposeDialog } from "./ComposeDialog";
import { useSupplierMessages } from "@/hooks/useSupplierMessages";
import { SupplierMessage, MessageTag } from "@/types/supplierMessages";
import { Mail, PlusCircle } from "lucide-react";

export const MessageCenter: React.FC = () => {
  const {
    threads,
    selectedThread,
    selectedThreadId,
    emailTemplates,
    setSelectedThreadId,
    searchQuery,
    setSearchQuery,
    selectedTags,
    setSelectedTags,
    markThreadAsRead,
    sendReply
  } = useSupplierMessages();
  
  const [composeOpen, setComposeOpen] = useState(false);
  
  const handleSelectThread = (id: string) => {
    setSelectedThreadId(id);
    markThreadAsRead(id);
  };
  
  const handleTagToggle = (tag: MessageTag) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };
  
  const handleSendMessage = (message: { to: string, subject: string, body: string, attachments: File[] }) => {
    // In a real application, this would send a new message
    console.log("Sending new message:", message);
  };
  
  const handleReply = (threadId: string, message: Omit<SupplierMessage, 'id' | 'threadId'>) => {
    sendReply(threadId, message);
  };

  return (
    <>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-2xl font-bold">Supplier Message Center</h2>
        <Button onClick={() => setComposeOpen(true)} className="gap-2">
          <PlusCircle className="h-4 w-4" />
          New Message
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Card className="h-full flex flex-col">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Messages</CardTitle>
            </CardHeader>
            <CardContent className="pb-6 flex-1 flex flex-col">
              <div className="mb-4">
                <MessageFilters
                  searchQuery={searchQuery}
                  onSearchChange={setSearchQuery}
                  selectedTags={selectedTags}
                  onTagToggle={handleTagToggle}
                />
              </div>
              <div className="flex-1 overflow-hidden">
                <ThreadList
                  threads={threads}
                  selectedThreadId={selectedThreadId}
                  onSelectThread={handleSelectThread}
                />
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="lg:col-span-2">
          <Card className="h-full">
            <CardContent className="p-6">
              {selectedThread ? (
                <ThreadDetail 
                  thread={selectedThread} 
                  onReply={handleReply} 
                />
              ) : (
                <div className="h-full flex items-center justify-center text-center p-8">
                  <div>
                    <Mail className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-700">Select a message</h3>
                    <p className="text-gray-500 mt-2 max-w-md">
                      Choose a conversation from the list to view the message thread
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      <ComposeDialog
        open={composeOpen}
        onClose={() => setComposeOpen(false)}
        onSend={handleSendMessage}
        emailTemplates={emailTemplates}
      />
    </>
  );
};

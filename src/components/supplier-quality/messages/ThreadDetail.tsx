
import React from "react";
import { format } from "date-fns";
import { MessageThread, SupplierMessage } from "@/types/supplierMessages";
import { ReplyForm } from "./ReplyForm";
import { PaperclipIcon, FileText, FileImage, FileArchive } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

interface ThreadDetailProps {
  thread: MessageThread;
  onReply: (threadId: string, message: Omit<SupplierMessage, 'id' | 'threadId'>) => void;
}

export const ThreadDetail: React.FC<ThreadDetailProps> = ({ thread, onReply }) => {
  const getTagColor = (tag: string) => {
    switch(tag) {
      case "Audit": return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "NC": return "bg-red-100 text-red-800 hover:bg-red-100";
      case "PO": return "bg-green-100 text-green-800 hover:bg-green-100";
      case "CAPA": return "bg-orange-100 text-orange-800 hover:bg-orange-100";
      case "Urgent": return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      default: return "bg-gray-100 text-gray-800 hover:bg-gray-100";
    }
  };

  const getFileIcon = (type: string) => {
    if (type.includes("image")) return FileImage;
    if (type.includes("zip") || type.includes("archive")) return FileArchive;
    return FileText;
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4 border-b pb-3">
        <div>
          <h2 className="text-lg font-semibold">{thread.subject}</h2>
          <div className="flex gap-1 mt-1">
            {thread.tags.map(tag => (
              <Badge 
                key={tag} 
                variant="outline" 
                className={`text-xs py-0.5 px-2 ${getTagColor(tag)}`}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto space-y-6 mb-4">
        {thread.messages.map((message, index) => {
          const Icon = index % 2 === 0 ? FileText : PaperclipIcon;
          return (
            <Card key={message.id} className={`border ${index % 2 === 0 ? 'border-l-blue-500 border-l-4' : 'border-l-gray-500 border-l-4'}`}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <p className="font-medium">{message.fromName}</p>
                    <p className="text-xs text-gray-500">{message.from}</p>
                  </div>
                  <p className="text-xs text-gray-500">
                    {format(new Date(message.timestamp), "MMM d, yyyy 'at' h:mm a")}
                  </p>
                </div>
                <div className="text-sm my-3 whitespace-pre-line">
                  <p>{message.body}</p>
                </div>
                {message.attachments.length > 0 && (
                  <div className="mt-4">
                    <div className="text-xs font-medium text-gray-500 mb-2">
                      ATTACHMENTS ({message.attachments.length})
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {message.attachments.map(attachment => {
                        const IconComponent = getFileIcon(attachment.type);
                        return (
                          <Button
                            key={attachment.id}
                            variant="outline"
                            size="sm"
                            className="text-xs flex items-center gap-1"
                          >
                            <IconComponent className="h-3.5 w-3.5" />
                            <span className="max-w-[180px] truncate">{attachment.name}</span>
                            <span className="text-gray-500 ml-1">({attachment.size})</span>
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="border-t pt-4">
        <ReplyForm threadId={thread.id} onSubmit={onReply} />
      </div>
    </div>
  );
};

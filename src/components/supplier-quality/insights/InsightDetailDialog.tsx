
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { SupplierInsight } from "@/types/supplierInsights";
import { 
  AlertTriangle, 
  Info, 
  Lightbulb, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  User,
  Target,
  Plus
} from "lucide-react";
import { format } from "date-fns";

interface InsightDetailDialogProps {
  insight: SupplierInsight | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdateStatus: (id: string, status: any) => void;
  onAddFollowUp: (id: string, action: string) => void;
}

export const InsightDetailDialog: React.FC<InsightDetailDialogProps> = ({
  insight,
  open,
  onOpenChange,
  onUpdateStatus,
  onAddFollowUp
}) => {
  const [newAction, setNewAction] = useState("");

  if (!insight) return null;

  const getTypeIcon = () => {
    switch (insight.type) {
      case "Alert":
        return <AlertTriangle className="h-6 w-6 text-red-500" />;
      case "Insight":
        return <Info className="h-6 w-6 text-blue-500" />;
      case "Recommendation":
        return <Lightbulb className="h-6 w-6 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusBadgeColor = () => {
    switch (insight.status) {
      case "New":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "In Progress":
        return "bg-yellow-50 text-yellow-800 border-yellow-200";
      case "Resolved":
        return "bg-green-50 text-green-800 border-green-200";
      case "Dismissed":
        return "bg-gray-50 text-gray-800 border-gray-200";
      default:
        return "";
    }
  };

  const handleAddAction = () => {
    if (newAction.trim()) {
      onAddFollowUp(insight.id, newAction.trim());
      setNewAction("");
    }
  };

  const handleStatusChange = (status: any) => {
    onUpdateStatus(insight.id, status);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getTypeIcon()}
            <span>{insight.title}</span>
            <Badge variant="outline" className={getStatusBadgeColor()}>
              {insight.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Info className="h-5 w-5" />
                Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Supplier</label>
                  <p className="font-semibold">{insight.supplierName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Category</label>
                  <p className="font-semibold">{insight.category}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Detected Date</label>
                  <p className="flex items-center gap-1">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    {format(new Date(insight.detectedDate), 'MMM d, yyyy')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Urgency Level</label>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 rounded-full" style={{
                      backgroundColor: insight.urgency >= 8 ? '#EF4444' : 
                                     insight.urgency >= 6 ? '#F59E0B' : 
                                     insight.urgency >= 4 ? '#EAB308' : '#10B981'
                    }} />
                    <span className="font-semibold">{insight.urgency}/10</span>
                  </div>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Description</label>
                <p className="mt-1 text-gray-800">{insight.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Related Data */}
          {insight.relatedData && insight.relatedData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Key Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {insight.relatedData.map((data, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="text-sm text-gray-600 mb-1">{data.type}</div>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold">{data.value}</span>
                        {data.trend && (
                          data.trend === 'up' ? (
                            <TrendingUp className="h-5 w-5 text-red-500" />
                          ) : data.trend === 'down' ? (
                            <TrendingDown className="h-5 w-5 text-green-500" />
                          ) : null
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Follow-up Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                Follow-up Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {insight.followUpActions && insight.followUpActions.length > 0 && (
                <div className="space-y-2">
                  {insight.followUpActions.map((action, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span>{action}</span>
                    </div>
                  ))}
                </div>
              )}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add new follow-up action..."
                  value={newAction}
                  onChange={(e) => setNewAction(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddAction()}
                />
                <Button onClick={handleAddAction} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={() => handleStatusChange('In Progress')}
              disabled={insight.status === 'In Progress'}
            >
              Mark In Progress
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleStatusChange('Resolved')}
              disabled={insight.status === 'Resolved'}
            >
              Mark Resolved
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleStatusChange('Dismissed')}
              disabled={insight.status === 'Dismissed'}
            >
              Dismiss
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

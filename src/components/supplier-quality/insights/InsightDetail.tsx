
import React, { useState } from "react";
import { SupplierInsight, InsightStatus } from "@/types/supplierInsights";
import { 
  <PERSON>, 
  Card<PERSON>ead<PERSON>, 
  Card<PERSON>ontent, 
  CardFooter, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  AlertTriangle, 
  Info, 
  Lightbulb,
  TrendingUp,
  TrendingDown, 
  ChevronLeft,
  Check,
  X,
  PlayCircle,
  Plus
} from "lucide-react";
import { format } from "date-fns";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";

interface InsightDetailProps {
  insight: SupplierInsight;
  onBack: () => void;
  onStatusChange: (id: string, status: InsightStatus) => void;
  onAddAction: (id: string, action: string) => void;
}

export const InsightDetail: React.FC<InsightDetailProps> = ({ 
  insight, 
  onBack,
  onStatusChange,
  onAddAction
}) => {
  const [newAction, setNewAction] = useState("");
  
  const handleAddAction = () => {
    if (newAction.trim()) {
      onAddAction(insight.id, newAction.trim());
      setNewAction("");
    }
  };
  
  const getTypeIcon = () => {
    switch (insight.type) {
      case "Alert":
        return <AlertTriangle className="h-6 w-6 text-red-500" />;
      case "Insight":
        return <Info className="h-6 w-6 text-blue-500" />;
      case "Recommendation":
        return <Lightbulb className="h-6 w-6 text-yellow-500" />;
      default:
        return null;
    }
  };
  
  const getTypeBadgeColor = () => {
    switch (insight.type) {
      case "Alert":
        return "bg-red-50 text-red-800 border-red-200";
      case "Insight":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "Recommendation":
        return "bg-yellow-50 text-yellow-800 border-yellow-200";
      default:
        return "";
    }
  };
  
  const getCategoryBadgeColor = () => {
    switch (insight.category) {
      case "Quality":
        return "bg-purple-50 text-purple-800 border-purple-200";
      case "Delivery":
        return "bg-orange-50 text-orange-800 border-orange-200";
      case "Compliance":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "Risk":
        return "bg-red-50 text-red-800 border-red-200";
      case "Audit":
        return "bg-green-50 text-green-800 border-green-200";
      case "Financial":
        return "bg-cyan-50 text-cyan-800 border-cyan-200";
      default:
        return "";
    }
  };
  
  const getUrgencyColor = () => {
    if (insight.urgency >= 8) return "text-red-600";
    if (insight.urgency >= 5) return "text-yellow-600";
    return "text-green-600";
  };
  
  const renderMetricValue = (type: string, value: string | number, trend?: 'up' | 'down' | 'stable') => {
    return (
      <div className="flex items-center">
        <span className="font-medium mr-1">{value}</span>
        {trend && (
          trend === 'up' ? (
            <TrendingUp className="h-4 w-4 text-red-500" />
          ) : trend === 'down' ? (
            <TrendingDown className="h-4 w-4 text-green-500" />
          ) : null
        )}
      </div>
    );
  };
  
  return (
    <Card className="shadow-sm">
      <CardHeader>
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="sm" onClick={onBack} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
        </div>
        
        <div className="flex items-start gap-3">
          {getTypeIcon()}
          <div>
            <CardTitle className="text-xl">{insight.title}</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className={getTypeBadgeColor()}>
                {insight.type}
              </Badge>
              <Badge variant="outline" className={getCategoryBadgeColor()}>
                {insight.category}
              </Badge>
              <div className="text-sm text-gray-500">
                <span>Detected:</span> {format(new Date(insight.detectedDate), 'MMM d, yyyy')}
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div>
          <h3 className="font-medium mb-2">Description</h3>
          <p className="text-gray-700">{insight.description}</p>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">Supplier Details</h3>
            <div className="space-y-2">
              <div className="flex justify-between border-b pb-1">
                <span className="text-gray-600">Name:</span>
                <span className="font-medium">{insight.supplierName}</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span className="text-gray-600">ID:</span>
                <span>{insight.supplierId}</span>
              </div>
              <div className="flex justify-between border-b pb-1">
                <span className="text-gray-600">Urgency Level:</span>
                <span className={`font-bold ${getUrgencyColor()}`}>{insight.urgency}/10</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">Status</h3>
            <Select
              value={insight.status}
              onValueChange={(value) => onStatusChange(insight.id, value as InsightStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Update Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="New">New</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Resolved">Resolved</SelectItem>
                <SelectItem value="Dismissed">Dismissed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {insight.relatedData && insight.relatedData.length > 0 && (
          <div>
            <h3 className="font-medium mb-2">Related Data Points</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {insight.relatedData.map((data, index) => (
                <Card key={index} className="p-3">
                  <div className="text-sm text-gray-600">{data.type}</div>
                  <div className="text-lg font-medium flex items-center mt-1">
                    {renderMetricValue(data.type, data.value, data.trend)}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
        
        <div>
          <h3 className="font-medium mb-2">Follow-up Actions</h3>
          {insight.followUpActions && insight.followUpActions.length > 0 ? (
            <ul className="space-y-2">
              {insight.followUpActions.map((action, index) => (
                <li key={index} className="flex items-start p-2 bg-gray-50 rounded-md">
                  <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                  <span>{action}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500 italic">No follow-up actions defined yet.</p>
          )}
          
          <div className="flex mt-3 gap-2">
            <Input 
              value={newAction}
              onChange={(e) => setNewAction(e.target.value)}
              placeholder="Add a new follow-up action..."
              className="flex-1"
            />
            <Button onClick={handleAddAction}>
              <Plus className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="border-t pt-4 flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Cancel
        </Button>
        
        {insight.status !== 'Resolved' && (
          <Button 
            variant="default" 
            onClick={() => onStatusChange(insight.id, "Resolved")}
          >
            <Check className="h-4 w-4 mr-1" />
            Mark as Resolved
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

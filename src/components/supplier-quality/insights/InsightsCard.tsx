
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  AlertTriangle, 
  Info, 
  TrendingUp, 
  TrendingDown,
  BarChart2,
  Lightbulb
} from "lucide-react";
import { SupplierInsight } from "@/types/supplierInsights";
import { format } from "date-fns";

interface InsightsCardProps {
  insight: SupplierInsight;
  onClick: () => void;
}

export const InsightsCard: React.FC<InsightsCardProps> = ({ insight, onClick }) => {
  const getTypeIcon = () => {
    switch (insight.type) {
      case "Alert":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "Insight":
        return <Info className="h-5 w-5 text-blue-500" />;
      case "Recommendation":
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      default:
        return null;
    }
  };
  
  const getTypeBadgeColor = () => {
    switch (insight.type) {
      case "Alert":
        return "bg-red-50 text-red-800 border-red-200";
      case "Insight":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "Recommendation":
        return "bg-yellow-50 text-yellow-800 border-yellow-200";
      default:
        return "";
    }
  };
  
  const getCategoryBadgeColor = () => {
    switch (insight.category) {
      case "Quality":
        return "bg-purple-50 text-purple-800 border-purple-200";
      case "Delivery":
        return "bg-orange-50 text-orange-800 border-orange-200";
      case "Compliance":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "Risk":
        return "bg-red-50 text-red-800 border-red-200";
      case "Audit":
        return "bg-green-50 text-green-800 border-green-200";
      case "Financial":
        return "bg-cyan-50 text-cyan-800 border-cyan-200";
      default:
        return "";
    }
  };
  
  const getStatusBadgeColor = () => {
    switch (insight.status) {
      case "New":
        return "bg-blue-50 text-blue-800 border-blue-200";
      case "In Progress":
        return "bg-yellow-50 text-yellow-800 border-yellow-200";
      case "Resolved":
        return "bg-green-50 text-green-800 border-green-200";
      case "Dismissed":
        return "bg-gray-50 text-gray-800 border-gray-200";
      default:
        return "";
    }
  };
  
  const renderMetricValue = (type: string, value: string | number, trend?: 'up' | 'down' | 'stable') => {
    return (
      <div className="flex items-center text-sm">
        <span className="font-medium mr-1">{value}</span>
        {trend && (
          trend === 'up' ? (
            <TrendingUp className="h-3 w-3 text-red-500" />
          ) : trend === 'down' ? (
            <TrendingDown className="h-3 w-3 text-green-500" />
          ) : null
        )}
      </div>
    );
  };
  
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
      <CardHeader className="pb-2 pt-4 px-4">
        <div className="flex items-start justify-between mb-1">
          <div className="flex items-center">
            {getTypeIcon()}
            <CardTitle className="text-base ml-1">{insight.title}</CardTitle>
          </div>
          <Badge variant="outline" className={getStatusBadgeColor()}>
            {insight.status}
          </Badge>
        </div>
        <div className="flex items-center gap-1 text-sm text-gray-500">
          <span>{insight.supplierName}</span>
          <span>•</span>
          <span>{format(new Date(insight.detectedDate), 'MMM d, yyyy')}</span>
        </div>
      </CardHeader>
      <CardContent className="px-4 py-2">
        <p className="text-sm text-gray-700">{insight.description}</p>
        
        {insight.relatedData && insight.relatedData.length > 0 && (
          <div className="mt-3 grid grid-cols-2 gap-2">
            {insight.relatedData.map((data, index) => (
              <div key={index} className="bg-gray-50 p-2 rounded-md">
                <div className="text-xs text-gray-500">{data.type}</div>
                {renderMetricValue(data.type, data.value, data.trend)}
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="px-4 py-2 border-t flex justify-between">
        <div className="flex gap-1">
          <Badge variant="outline" className={getTypeBadgeColor()}>
            {insight.type}
          </Badge>
          <Badge variant="outline" className={getCategoryBadgeColor()}>
            {insight.category}
          </Badge>
        </div>
        
        {insight.type === 'Recommendation' && (
          <div className="flex items-center">
            <BarChart2 className="h-4 w-4 text-blue-500 mr-1" />
            <span className="text-xs text-gray-500">Score Impact</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

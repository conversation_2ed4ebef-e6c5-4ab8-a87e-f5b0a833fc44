
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { TrendingUp, Info, Pie<PERSON><PERSON> } from "lucide-react";
import { InsightsCenter } from "./InsightsCenter";

export const InsightsDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-700 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Risk Heatmap
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800">7</div>
            <p className="text-xs text-red-600">Risk hotspots</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-red-700 border-red-300">
              View Heatmap
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
              <Info className="h-4 w-4" />
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">12</div>
            <p className="text-xs text-blue-600">New insights</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-blue-700 border-blue-300">
              View Insights
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 flex items-center gap-2">
              <PieChart className="h-4 w-4" />
              Performance Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">89%</div>
            <p className="text-xs text-purple-600">Overall performance</p>
            <Button size="sm" variant="outline" className="w-full mt-2 text-purple-700 border-purple-300">
              View Analysis
            </Button>
          </CardContent>
        </Card>
      </div>

      <InsightsCenter />
    </div>
  );
};

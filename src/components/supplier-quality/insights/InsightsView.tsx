
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Brain, TrendingUp, Shield, BarChart3 } from 'lucide-react';
import { RiskHeatmapView } from './RiskHeatmapView';
import { AIInsightsView } from './AIInsightsView';
import { PerformanceAnalysisView } from './PerformanceAnalysisView';

export const InsightsView: React.FC = () => {
  const [activeModal, setActiveModal] = useState<string | null>(null);

  const openModal = (modalType: string) => {
    setActiveModal(modalType);
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openModal('heatmap')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-600" />
              Risk Heatmap
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Visualize supplier risk distribution and identify high-risk areas</p>
            <Button className="w-full" onClick={(e) => { e.stopPropagation(); openModal('heatmap'); }}>
              View Risk Heatmap
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openModal('ai-insights')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-600" />
              AI Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Get AI-powered recommendations and predictions</p>
            <Button className="w-full" onClick={(e) => { e.stopPropagation(); openModal('ai-insights'); }}>
              View AI Insights
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => openModal('performance')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              Performance Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">Analyze supplier performance trends and metrics</p>
            <Button className="w-full" onClick={(e) => { e.stopPropagation(); openModal('performance'); }}>
              View Performance Analysis
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Insights Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">15</div>
              <div className="text-sm text-red-700">High Risk Suppliers</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">23</div>
              <div className="text-sm text-yellow-700">AI Recommendations</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">87%</div>
              <div className="text-sm text-green-700">Performance Score</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">142</div>
              <div className="text-sm text-blue-700">Total Suppliers</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Risk Heatmap Modal */}
      <Dialog open={activeModal === 'heatmap'} onOpenChange={closeModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Supplier Risk Heatmap</DialogTitle>
          </DialogHeader>
          <RiskHeatmapView />
        </DialogContent>
      </Dialog>

      {/* AI Insights Modal */}
      <Dialog open={activeModal === 'ai-insights'} onOpenChange={closeModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>AI-Powered Insights</DialogTitle>
          </DialogHeader>
          <AIInsightsView />
        </DialogContent>
      </Dialog>

      {/* Performance Analysis Modal */}
      <Dialog open={activeModal === 'performance'} onOpenChange={closeModal}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Performance Analysis</DialogTitle>
          </DialogHeader>
          <PerformanceAnalysisView />
        </DialogContent>
      </Dialog>
    </div>
  );
};

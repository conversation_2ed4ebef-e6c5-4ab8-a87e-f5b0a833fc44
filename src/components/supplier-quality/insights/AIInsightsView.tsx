
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Brain, TrendingUp, AlertTriangle, CheckCircle, Clock } from 'lucide-react';
import { SupplierInsight } from '@/types/supplierInsights';

const mockInsights: SupplierInsight[] = [
  {
    id: 'insight-001',
    supplierId: 'sup-002',
    supplierName: 'Global Manufacturing',
    type: 'Alert',
    category: 'Quality',
    title: 'Declining Quality Trend Detected',
    description: 'Quality scores have decreased by 15% over the last 3 months. Immediate action recommended.',
    detectedDate: '2024-01-18',
    status: 'New',
    urgency: 8,
    relatedData: [
      { type: 'Quality Score', value: 78, trend: 'down' },
      { type: 'Defect Rate', value: 3.2, trend: 'up' }
    ],
    followUpActions: ['Schedule immediate audit', 'Review quality procedures', 'Implement corrective measures']
  },
  {
    id: 'insight-002',
    supplierId: 'sup-005',
    supplierName: 'Metro Supplies',
    type: 'Recommendation',
    category: 'Risk',
    title: 'High Risk Supplier Assessment',
    description: 'Based on recent performance data, this supplier shows elevated risk factors requiring attention.',
    detectedDate: '2024-01-15',
    status: 'In Progress',
    urgency: 9,
    relatedData: [
      { type: 'Risk Score', value: 70, trend: 'up' },
      { type: 'Delivery Performance', value: 82, trend: 'down' }
    ],
    followUpActions: ['Conduct risk assessment', 'Develop mitigation plan', 'Consider alternative suppliers']
  },
  {
    id: 'insight-003',
    supplierId: 'sup-004',
    supplierName: 'Quality Systems Inc',
    type: 'Insight',
    category: 'Delivery',
    title: 'Exceptional Performance Recognition',
    description: 'This supplier has consistently exceeded performance targets and could be a strategic partner.',
    detectedDate: '2024-01-12',
    status: 'Resolved',
    urgency: 3,
    relatedData: [
      { type: 'On-Time Delivery', value: 98, trend: 'up' },
      { type: 'Quality Score', value: 95, trend: 'stable' }
    ],
    followUpActions: ['Consider expanded partnership', 'Negotiate better terms', 'Benchmark best practices']
  }
];

export const AIInsightsView: React.FC = () => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Alert':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'Recommendation':
        return <Brain className="h-5 w-5 text-purple-600" />;
      case 'Insight':
        return <TrendingUp className="h-5 w-5 text-blue-600" />;
      default:
        return <Brain className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      'Alert': 'bg-red-100 text-red-800',
      'Recommendation': 'bg-purple-100 text-purple-800',
      'Insight': 'bg-blue-100 text-blue-800'
    };
    return <Badge className={variants[type as keyof typeof variants]}>{type}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'New': 'bg-blue-100 text-blue-800',
      'In Progress': 'bg-yellow-100 text-yellow-800',
      'Resolved': 'bg-green-100 text-green-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getUrgencyColor = (urgency: number) => {
    if (urgency >= 8) return 'text-red-600';
    if (urgency >= 6) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingUp className="h-3 w-3 text-red-600 rotate-180" />;
      default:
        return <div className="h-3 w-3 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Active Alerts</p>
                <p className="text-2xl font-bold text-red-600">
                  {mockInsights.filter(i => i.type === 'Alert' && i.status !== 'Resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Brain className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Recommendations</p>
                <p className="text-2xl font-bold text-purple-600">
                  {mockInsights.filter(i => i.type === 'Recommendation').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Insights</p>
                <p className="text-2xl font-bold text-blue-600">
                  {mockInsights.filter(i => i.type === 'Insight').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-green-600">
                  {mockInsights.filter(i => i.status === 'Resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        {mockInsights.map((insight) => (
          <Card key={insight.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4 flex-1">
                  {getTypeIcon(insight.type)}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold">{insight.title}</h4>
                      {getTypeBadge(insight.type)}
                      {getStatusBadge(insight.status)}
                      <div className={`text-sm font-medium ${getUrgencyColor(insight.urgency)}`}>
                        Urgency: {insight.urgency}/10
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <span>Supplier: {insight.supplierName}</span>
                      <span>Category: {insight.category}</span>
                      <span>Detected: {insight.detectedDate}</span>
                    </div>

                    {insight.relatedData && (
                      <div className="mb-3">
                        <h5 className="text-sm font-medium mb-2">Related Data:</h5>
                        <div className="flex gap-4">
                          {insight.relatedData.map((data, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm">
                              <span>{data.type}: {data.value}</span>
                              {data.trend && getTrendIcon(data.trend)}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {insight.followUpActions && (
                      <div>
                        <h5 className="text-sm font-medium mb-2">Recommended Actions:</h5>
                        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                          {insight.followUpActions.map((action, index) => (
                            <li key={index}>{action}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    View Details
                  </Button>
                  {insight.status !== 'Resolved' && (
                    <Button size="sm">
                      Take Action
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Brain, TrendingUp, Alert<PERSON>riangle, Lightbulb, CheckCircle } from "lucide-react";
import { mockSupplierInsights } from "@/data/mockSupplierInsights";

export const SupplierInsightsDashboard: React.FC = () => {
  const getInsightIcon = (type: string) => {
    switch (type) {
      case "Alert":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "Insight":
        return <Lightbulb className="h-4 w-4 text-blue-600" />;
      case "Recommendation":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      default:
        return <Brain className="h-4 w-4 text-purple-600" />;
    }
  };

  const getUrgencyBadge = (urgency: number) => {
    if (urgency >= 8) return <Badge className="bg-red-100 text-red-800">High</Badge>;
    if (urgency >= 5) return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    return <Badge className="bg-green-100 text-green-800">Low</Badge>;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "New":
        return <Badge className="bg-blue-100 text-blue-800">New</Badge>;
      case "In Progress":
        return <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>;
      case "Dismissed":
        return <Badge className="bg-gray-100 text-gray-800">Dismissed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Critical Alerts</p>
                <p className="text-2xl font-bold">
                  {mockSupplierInsights.filter(i => i.type === 'Alert' && i.urgency >= 8).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">New Insights</p>
                <p className="text-2xl font-bold">
                  {mockSupplierInsights.filter(i => i.status === 'New').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Recommendations</p>
                <p className="text-2xl font-bold">
                  {mockSupplierInsights.filter(i => i.type === 'Recommendation').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">
                  {mockSupplierInsights.filter(i => i.status === 'Dismissed').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI-Generated Supplier Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockSupplierInsights.map((insight) => (
              <div key={insight.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getInsightIcon(insight.type)}
                    <h4 className="font-medium">{insight.title}</h4>
                  </div>
                  <div className="flex items-center gap-2">
                    {getUrgencyBadge(insight.urgency)}
                    {getStatusBadge(insight.status)}
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-3">{insight.description}</p>
                
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    {insight.supplierName} • {insight.detectedDate}
                  </div>
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

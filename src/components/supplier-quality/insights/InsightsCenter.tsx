
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, TrendingUp, AlertTriangle, Lightbulb, Info } from "lucide-react";
import { useSupplierInsights } from "@/hooks/useSupplierInsights";
import { InsightsCard } from "./InsightsCard";
import { InsightDetailDialog } from "./InsightDetailDialog";

export const InsightsCenter: React.FC = () => {
  const {
    insights,
    selectedInsight,
    typeFilter,
    categoryFilter,
    statusFilter,
    searchQuery,
    setTypeFilter,
    setCategoryFilter,
    setStatusFilter,
    setSearchQuery,
    setSelectedInsightId,
    updateInsightStatus,
    addFollowUpAction
  } = useSupplierInsights();

  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  const handleInsightClick = (insightId: string) => {
    setSelectedInsightId(insightId);
    setDetailDialogOpen(true);
  };

  const handleCloseDetail = () => {
    setDetailDialogOpen(false);
    setSelectedInsightId(null);
  };

  const handleTypeChange = (value: string) => {
    setTypeFilter(value as any);
  };

  const handleCategoryChange = (value: string) => {
    setCategoryFilter(value as any);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value as any);
  };

  const insightTypeCounts = {
    Alert: insights.filter(i => i.type === 'Alert').length,
    Insight: insights.filter(i => i.type === 'Insight').length,
    Recommendation: insights.filter(i => i.type === 'Recommendation').length
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Insights</p>
                <p className="text-3xl font-bold">{insights.length}</p>
              </div>
              <Info className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Alerts</p>
                <p className="text-3xl font-bold text-red-600">{insightTypeCounts.Alert}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Recommendations</p>
                <p className="text-3xl font-bold text-yellow-600">{insightTypeCounts.Recommendation}</p>
              </div>
              <Lightbulb className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Insights</p>
                <p className="text-3xl font-bold text-green-600">{insightTypeCounts.Insight}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2 flex-1 min-w-[200px]">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search insights..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Select value={typeFilter} onValueChange={handleTypeChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Types</SelectItem>
                <SelectItem value="Alert">Alerts</SelectItem>
                <SelectItem value="Insight">Insights</SelectItem>
                <SelectItem value="Recommendation">Recommendations</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={categoryFilter} onValueChange={handleCategoryChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Categories</SelectItem>
                <SelectItem value="Quality">Quality</SelectItem>
                <SelectItem value="Delivery">Delivery</SelectItem>
                <SelectItem value="Compliance">Compliance</SelectItem>
                <SelectItem value="Risk">Risk</SelectItem>
                <SelectItem value="Audit">Audit</SelectItem>
                <SelectItem value="Financial">Financial</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Status</SelectItem>
                <SelectItem value="New">New</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Resolved">Resolved</SelectItem>
                <SelectItem value="Dismissed">Dismissed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {insights.map((insight) => (
          <InsightsCard
            key={insight.id}
            insight={insight}
            onClick={() => handleInsightClick(insight.id)}
          />
        ))}
      </div>

      {insights.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Info className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No insights found</h3>
            <p className="text-gray-600">Try adjusting your filters to see more insights.</p>
          </CardContent>
        </Card>
      )}

      {/* Detail Dialog */}
      <InsightDetailDialog
        insight={selectedInsight}
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        onUpdateStatus={updateInsightStatus}
        onAddFollowUp={addFollowUpAction}
      />
    </div>
  );
};

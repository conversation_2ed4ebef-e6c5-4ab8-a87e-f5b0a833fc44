
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart, Pie, Cell } from 'recharts';

const performanceTrendData = [
  { month: 'Jan', quality: 88, delivery: 92, cost: 85, overall: 88 },
  { month: 'Feb', quality: 90, delivery: 89, cost: 87, overall: 89 },
  { month: 'Mar', quality: 87, delivery: 95, cost: 84, overall: 89 },
  { month: 'Apr', quality: 92, delivery: 88, cost: 89, overall: 90 },
  { month: 'May', quality: 89, delivery: 94, cost: 91, overall: 91 },
  { month: 'Jun', quality: 91, delivery: 90, cost: 88, overall: 90 }
];

const categoryPerformance = [
  { category: 'Electronics', score: 92, suppliers: 25 },
  { category: 'Manufacturing', score: 87, suppliers: 18 },
  { category: 'Components', score: 89, suppliers: 32 },
  { category: 'Materials', score: 85, suppliers: 15 },
  { category: 'Services', score: 91, suppliers: 12 }
];

const riskDistribution = [
  { name: 'Low Risk', value: 45, color: '#10B981' },
  { name: 'Medium Risk', value: 35, color: '#F59E0B' },
  { name: 'High Risk', value: 15, color: '#EF4444' },
  { name: 'Critical Risk', value: 5, color: '#DC2626' }
];

export const PerformanceAnalysisView: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">90%</div>
              <div className="text-sm text-gray-600">Overall Performance</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">91%</div>
              <div className="text-sm text-gray-600">Quality Score</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">90%</div>
              <div className="text-sm text-gray-600">Delivery Performance</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">88%</div>
              <div className="text-sm text-gray-600">Cost Efficiency</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[80, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="overall" stroke="#3B82F6" strokeWidth={2} name="Overall" />
                <Line type="monotone" dataKey="quality" stroke="#10B981" strokeWidth={2} name="Quality" />
                <Line type="monotone" dataKey="delivery" stroke="#8B5CF6" strokeWidth={2} name="Delivery" />
                <Line type="monotone" dataKey="cost" stroke="#F59E0B" strokeWidth={2} name="Cost" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Category Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={categoryPerformance} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[80, 95]} />
                <YAxis dataKey="category" type="category" />
                <Tooltip />
                <Bar dataKey="score" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Risk Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={riskDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {riskDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Suppliers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: 'Quality Systems Inc', score: 95, category: 'Systems' },
                { name: 'TechCorp Industries', score: 92, category: 'Electronics' },
                { name: 'Elite Components', score: 90, category: 'Components' },
                { name: 'Precision Parts Ltd', score: 88, category: 'Components' },
                { name: 'Advanced Materials', score: 85, category: 'Materials' }
              ].map((supplier, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{supplier.name}</p>
                    <p className="text-sm text-gray-600">{supplier.category}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">{supplier.score}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

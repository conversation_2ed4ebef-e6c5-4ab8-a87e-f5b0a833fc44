
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SupplierRiskData } from '@/types/supplierRiskHeatmap';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>atter, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

const mockSupplierRiskData: SupplierRiskData[] = [
  { id: 'sup-001', name: 'TechCorp Industries', auditScore: 92, riskScore: 25, spend: 850000, category: 'Electronics' },
  { id: 'sup-002', name: 'Global Manufacturing', auditScore: 78, riskScore: 45, spend: 1200000, category: 'Manufacturing' },
  { id: 'sup-003', name: 'Precision Parts Ltd', auditScore: 88, riskScore: 30, spend: 650000, category: 'Components' },
  { id: 'sup-004', name: 'Quality Systems Inc', auditScore: 95, riskScore: 15, spend: 950000, category: 'Systems' },
  { id: 'sup-005', name: 'Metro Supplies', auditScore: 65, riskScore: 70, spend: 450000, category: 'Supplies' },
  { id: 'sup-006', name: 'Advanced Materials', auditScore: 85, riskScore: 35, spend: 750000, category: 'Materials' },
  { id: 'sup-007', name: 'Rapid Solutions', auditScore: 72, riskScore: 55, spend: 320000, category: 'Services' },
  { id: 'sup-008', name: 'Elite Components', auditScore: 90, riskScore: 20, spend: 1100000, category: 'Components' }
];

export const RiskHeatmapView: React.FC = () => {
  const [selectedSupplier, setSelectedSupplier] = useState<SupplierRiskData | null>(null);

  const getRiskColor = (riskScore: number) => {
    if (riskScore <= 25) return '#10B981'; // Green - Low Risk
    if (riskScore <= 50) return '#F59E0B'; // Yellow - Medium Risk
    if (riskScore <= 75) return '#EF4444'; // Red - High Risk
    return '#DC2626'; // Dark Red - Critical Risk
  };

  const getRiskLevel = (riskScore: number) => {
    if (riskScore <= 25) return 'Low';
    if (riskScore <= 50) return 'Medium';
    if (riskScore <= 75) return 'High';
    return 'Critical';
  };

  const getRiskBadge = (riskScore: number) => {
    const level = getRiskLevel(riskScore);
    const variants = {
      'Low': 'bg-green-100 text-green-800',
      'Medium': 'bg-yellow-100 text-yellow-800',
      'High': 'bg-red-100 text-red-800',
      'Critical': 'bg-red-200 text-red-900'
    };
    return <Badge className={variants[level as keyof typeof variants]}>{level} Risk</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Risk vs Audit Score Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ScatterChart data={mockSupplierRiskData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="auditScore" 
                    type="number" 
                    domain={[60, 100]} 
                    name="Audit Score"
                    label={{ value: 'Audit Score', position: 'bottom' }}
                  />
                  <YAxis 
                    dataKey="riskScore" 
                    type="number" 
                    domain={[0, 100]} 
                    name="Risk Score"
                    label={{ value: 'Risk Score', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip 
                    cursor={{ strokeDasharray: '3 3' }}
                    content={({ active, payload }) => {
                      if (active && payload && payload[0]) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-white p-3 border rounded-lg shadow-md">
                            <p className="font-medium">{data.name}</p>
                            <p>Audit Score: {data.auditScore}%</p>
                            <p>Risk Score: {data.riskScore}</p>
                            <p>Spend: ${data.spend.toLocaleString()}</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Scatter name="Suppliers" dataKey="riskScore">
                    {mockSupplierRiskData.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={getRiskColor(entry.riskScore)}
                        style={{ cursor: 'pointer' }}
                        onClick={() => setSelectedSupplier(entry)}
                      />
                    ))}
                  </Scatter>
                </ScatterChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Risk Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium">Low Risk (0-25)</span>
                  <Badge className="bg-green-100 text-green-800">
                    {mockSupplierRiskData.filter(s => s.riskScore <= 25).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <span className="text-sm font-medium">Medium Risk (26-50)</span>
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {mockSupplierRiskData.filter(s => s.riskScore > 25 && s.riskScore <= 50).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <span className="text-sm font-medium">High Risk (51-75)</span>
                  <Badge className="bg-red-100 text-red-800">
                    {mockSupplierRiskData.filter(s => s.riskScore > 50 && s.riskScore <= 75).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-red-100 rounded-lg">
                  <span className="text-sm font-medium">Critical Risk (76-100)</span>
                  <Badge className="bg-red-200 text-red-900">
                    {mockSupplierRiskData.filter(s => s.riskScore > 75).length}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {selectedSupplier && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Supplier Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <h4 className="font-medium">{selectedSupplier.name}</h4>
                  {getRiskBadge(selectedSupplier.riskScore)}
                  <div className="text-sm space-y-1">
                    <p>Audit Score: {selectedSupplier.auditScore}%</p>
                    <p>Risk Score: {selectedSupplier.riskScore}</p>
                    <p>Annual Spend: ${selectedSupplier.spend.toLocaleString()}</p>
                    <p>Category: {selectedSupplier.category}</p>
                  </div>
                  <Button size="sm" className="w-full">
                    View Full Profile
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

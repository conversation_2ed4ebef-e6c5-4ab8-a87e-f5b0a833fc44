
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { SupplierQualification } from "@/types/supplierQualification";
import { QualityScoreIndicator } from "../QualityScoreIndicator";
import { RiskScoreBadge } from "../RiskScoreBadge";
import { StatusBadge } from "@/components/vendors/StatusBadge";

interface MobileSupplierRowProps {
  supplier: SupplierQualification;
}

export const MobileSupplierRow: React.FC<MobileSupplierRowProps> = ({ supplier }) => {
  const getRiskLevel = (riskScore: number): "Low" | "Medium" | "High" => {
    if (riskScore >= 80) return "Low";
    if (riskScore >= 60) return "Medium";
    return "High";
  };

  return (
    <Card className="mb-4 overflow-hidden">
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <h3 className="text-lg font-medium text-blue-600">{supplier.name}</h3>
          <StatusBadge status={supplier.status} />
        </div>
        <p className="text-sm text-gray-500 mb-3">ID: {supplier.id}</p>
        
        <div className="space-y-2 mt-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-500">Audit Score:</span>
            <QualityScoreIndicator score={supplier.auditScore} />
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-500">NCR Count:</span>
            <span className={`text-sm font-medium ${supplier.ncrCount > 5 ? 'text-red-600' : 'text-gray-700'}`}>
              {supplier.ncrCount}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-500">On-time Delivery:</span>
            <span className={`text-sm font-medium ${supplier.onTimeDelivery < 80 ? 'text-red-600' : 'text-green-600'}`}>
              {supplier.onTimeDelivery}%
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-500">Risk Score:</span>
            <RiskScoreBadge risk={getRiskLevel(supplier.riskScore)} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

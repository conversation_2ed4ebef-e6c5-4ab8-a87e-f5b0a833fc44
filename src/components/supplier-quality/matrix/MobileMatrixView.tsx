
import React from "react";
import { SupplierQualification } from "@/types/supplierQualification";
import { MobileSupplierRow } from "./MobileSupplierRow";

interface MobileMatrixViewProps {
  suppliers: SupplierQualification[];
}

export const MobileMatrixView: React.FC<MobileMatrixViewProps> = ({ suppliers }) => {
  return (
    <div className="space-y-4">
      {suppliers.length > 0 ? (
        suppliers.map((supplier) => (
          <MobileSupplierRow key={supplier.id} supplier={supplier} />
        ))
      ) : (
        <div className="text-center py-8 text-gray-500">
          No suppliers match the current filters.
        </div>
      )}
    </div>
  );
};

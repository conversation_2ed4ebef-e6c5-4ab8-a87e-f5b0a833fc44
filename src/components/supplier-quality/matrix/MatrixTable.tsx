
import React from "react";
import { TableHeader, TableBody, TableRow, TableHead, TableCell } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { SupplierQualification } from "@/types/supplierQualification"; 
import { QualityScoreIndicator } from "../QualityScoreIndicator";
import { RiskScoreBadge } from "../RiskScoreBadge";
import { StatusBadge } from "@/components/vendors/StatusBadge";
import { ChevronDown, ChevronUp, ArrowUpDown } from "lucide-react";

interface MatrixTableProps {
  suppliers: SupplierQualification[];
  sortField: keyof SupplierQualification | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (field: keyof SupplierQualification) => void;
}

export const MatrixTable: React.FC<MatrixTableProps> = ({
  suppliers,
  sortField,
  sortDirection,
  handleSort
}) => {
  const getSortIcon = (field: keyof SupplierQualification) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === 'asc' 
      ? <ChevronUp className="ml-1 h-4 w-4" />
      : <ChevronDown className="ml-1 h-4 w-4" />;
  };

  const getRiskLevel = (riskScore: number): "Low" | "Medium" | "High" => {
    if (riskScore >= 80) return "Low";
    if (riskScore >= 60) return "Medium";
    return "High";
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <ResizableTable className="w-full" minColumnWidth={120}>
        <TableHeader>
          <TableRow>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('name')}
            >
              <div className="flex items-center">
                Supplier Name
                {getSortIcon('name')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('auditScore')}
            >
              <div className="flex items-center">
                Audit Score
                {getSortIcon('auditScore')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('ncrCount')}
            >
              <div className="flex items-center">
                NCR Count
                {getSortIcon('ncrCount')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('onTimeDelivery')}
            >
              <div className="flex items-center">
                On-time Delivery
                {getSortIcon('onTimeDelivery')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('riskScore')}
            >
              <div className="flex items-center">
                Risk Score
                {getSortIcon('riskScore')}
              </div>
            </TableHead>
            <TableHead 
              className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
              onClick={() => handleSort('status')}
            >
              <div className="flex items-center">
                Status
                {getSortIcon('status')}
              </div>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {suppliers.length > 0 ? (
            suppliers.map((supplier, index) => (
              <TableRow key={supplier.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <TableCell>
                  <div className="font-medium text-blue-600">{supplier.name}</div>
                  <div className="text-xs text-gray-500">ID: {supplier.id}</div>
                </TableCell>
                <TableCell>
                  <QualityScoreIndicator score={supplier.auditScore} />
                </TableCell>
                <TableCell>
                  <span className={`font-medium ${supplier.ncrCount > 5 ? 'text-red-600' : 'text-gray-700'}`}>
                    {supplier.ncrCount}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`font-medium ${supplier.onTimeDelivery < 80 ? 'text-red-600' : 'text-green-600'}`}>
                    {supplier.onTimeDelivery}%
                  </span>
                </TableCell>
                <TableCell>
                  <RiskScoreBadge risk={getRiskLevel(supplier.riskScore)} />
                </TableCell>
                <TableCell>
                  <StatusBadge status={supplier.status} />
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No suppliers match the current filters.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </ResizableTable>
    </div>
  );
};


import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { format } from "date-fns";

interface RecentActivityProps {
  className?: string;
}

export const RecentActivity: React.FC<RecentActivityProps> = ({ className }) => {
  // Mock data for recent activities
  const activities = [
    { date: "May 20, 2025", event: "Supplier Audit Completed", supplier: "TechSupply Inc.", status: "Passed" },
    { date: "May 18, 2025", event: "CAPA Issued", supplier: "ElectroParts Ltd.", status: "Open" },
    { date: "May 17, 2025", event: "New Supplier Onboarded", supplier: "GlobalComponents Co.", status: "Approved" },
    { date: "May 15, 2025", event: "Certification Expired", supplier: "MicroAssembly Inc.", status: "Action Required" },
    { date: "May 14, 2025", event: "Risk Assessment Updated", supplier: "PrecisionParts GmbH", status: "Medium Risk" },
  ];

  return (
    <Card className={`shadow-sm hover:shadow-md transition-shadow ${className || ''}`}>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Recent Supplier Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.map((activity, index) => (
            <div key={index} className="flex flex-col lg:flex-row lg:items-center justify-between py-3 border-b last:border-b-0">
              <div className="flex flex-col">
                <div className="font-medium">{activity.event}</div>
                <div className="text-sm text-gray-600">{activity.supplier}</div>
              </div>
              
              <div className="flex mt-2 lg:mt-0 justify-between items-center">
                <span className={`text-xs px-2 py-1 rounded-full mr-3 ${
                  activity.status === "Passed" || activity.status === "Approved" ? "bg-green-100 text-green-800" : 
                  activity.status === "Open" || activity.status === "Action Required" ? "bg-amber-100 text-amber-800" : 
                  activity.status === "Medium Risk" ? "bg-blue-100 text-blue-800" :
                  "bg-gray-100 text-gray-800"
                }`}>
                  {activity.status}
                </span>
                <span className="text-xs text-gray-500">{activity.date}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

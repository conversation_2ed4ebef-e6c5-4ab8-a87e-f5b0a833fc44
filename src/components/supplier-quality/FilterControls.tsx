
import React, { useState } from "react";
import { FilterSection } from "./filter-components/FilterSection";
import { Filter, MapPin, Shield } from "lucide-react";

interface FilterControlsProps {
  className?: string;
}

export const FilterControls: React.FC<FilterControlsProps> = ({ className = "" }) => {
  const [regionFilter, setRegionFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [riskFilter, setRiskFilter] = useState("");

  return (
    <div className={`flex flex-wrap gap-4 mt-6 mb-2 ${className}`}>
      <FilterSection
        title="Region"
        icon={<MapPin className="h-4 w-4" />}
        options={["North America", "Europe", "Asia Pacific", "South America"]}
        selectedValue={regionFilter}
        onValueChange={setRegionFilter}
      />
      
      <FilterSection
        title="Type"
        icon={<Filter className="h-4 w-4" />}
        options={["Raw Materials", "Components", "Finished Goods", "Services"]}
        selectedValue={typeFilter}
        onValueChange={setTypeFilter}
      />
      
      <FilterSection
        title="Risk Tier"
        icon={<Shield className="h-4 w-4" />}
        options={["Low", "Medium", "High", "Critical"]}
        selectedValue={riskFilter}
        onValueChange={setRiskFilter}
      />
    </div>
  );
};

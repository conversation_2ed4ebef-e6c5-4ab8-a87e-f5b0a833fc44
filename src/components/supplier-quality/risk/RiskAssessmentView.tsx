
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { Shield, AlertTriangle, TrendingUp, Users } from "lucide-react";

export const RiskAssessmentView: React.FC = () => {
  const riskData = [
    { name: "Low Risk", value: 45, color: "#10B981" },
    { name: "Medium Risk", value: 35, color: "#F59E0B" },
    { name: "High Risk", value: 15, color: "#EF4444" },
    { name: "Critical Risk", value: 5, color: "#7C2D12" }
  ];

  const supplierRiskTrends = [
    { month: "Jan", lowRisk: 48, mediumRisk: 32, highRisk: 15, criticalRisk: 5 },
    { month: "Feb", lowRisk: 47, mediumRisk: 33, highRisk: 15, criticalRisk: 5 },
    { month: "Mar", lowRisk: 46, mediumRisk: 34, highRisk: 15, criticalRisk: 5 },
    { month: "Apr", lowRisk: 45, mediumRisk: 35, highRisk: 15, criticalRisk: 5 },
    { month: "May", lowRisk: 45, mediumRisk: 35, highRisk: 15, criticalRisk: 5 },
    { month: "Jun", lowRisk: 45, mediumRisk: 35, highRisk: 15, criticalRisk: 5 }
  ];

  const highRiskSuppliers = [
    { name: "Alpha Manufacturing", riskScore: 8.5, category: "Financial", lastReview: "2024-01-15" },
    { name: "Beta Components", riskScore: 7.8, category: "Quality", lastReview: "2024-01-20" },
    { name: "Gamma Industries", riskScore: 7.2, category: "Delivery", lastReview: "2024-01-25" },
    { name: "Delta Systems", riskScore: 6.9, category: "Compliance", lastReview: "2024-01-30" }
  ];

  const getRiskBadge = (score: number) => {
    if (score >= 8) return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
    if (score >= 6) return <Badge className="bg-orange-100 text-orange-800">High</Badge>;
    if (score >= 4) return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
    return <Badge className="bg-green-100 text-green-800">Low</Badge>;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Suppliers</p>
                <p className="text-2xl font-bold">234</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">High Risk</p>
                <p className="text-2xl font-bold text-red-600">15</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Average Risk Score</p>
                <p className="text-2xl font-bold">4.2</p>
              </div>
              <Shield className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Risk Trend</p>
                <p className="text-2xl font-bold text-green-600">↓ 2.1%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Risk Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={riskData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {riskData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Risk Trend Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={supplierRiskTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="criticalRisk" stackId="a" fill="#7C2D12" />
                <Bar dataKey="highRisk" stackId="a" fill="#EF4444" />
                <Bar dataKey="mediumRisk" stackId="a" fill="#F59E0B" />
                <Bar dataKey="lowRisk" stackId="a" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>High Risk Suppliers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {highRiskSuppliers.map((supplier, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-semibold">{supplier.name}</h4>
                  <p className="text-sm text-gray-600">Category: {supplier.category} • Last Review: {supplier.lastReview}</p>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm text-gray-600">Risk Score</div>
                    <div className="font-bold">{supplier.riskScore}</div>
                  </div>
                  {getRiskBadge(supplier.riskScore)}
                  <Button size="sm" variant="outline">
                    Review
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

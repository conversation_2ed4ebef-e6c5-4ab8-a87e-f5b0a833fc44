
import React from "react";

interface RiskScoreBadgeProps {
  risk: 'Low' | 'Medium' | 'High';
}

export const RiskScoreBadge: React.FC<RiskScoreBadgeProps> = ({ risk }) => {
  let bgColor = "bg-green-100";
  let textColor = "text-green-800";
  
  if (risk === "Medium") {
    bgColor = "bg-yellow-100";
    textColor = "text-yellow-800";
  } else if (risk === "High") {
    bgColor = "bg-red-100";
    textColor = "text-red-800";
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
      {risk}
    </span>
  );
};

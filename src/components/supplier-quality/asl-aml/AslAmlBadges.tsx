
import React from "react";
import { Badge } from "@/components/ui/badge";
import { ApprovalType } from "@/types/supplierApprovedList";

interface ApprovalTypeBadgeProps {
  approvalType: ApprovalType;
}

export const ApprovalTypeBadge: React.FC<ApprovalTypeBadgeProps> = ({ approvalType }) => {
  switch (approvalType) {
    case "ASL":
      return <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">ASL</Badge>;
    case "AML":
      return <Badge variant="outline" className="bg-purple-50 text-purple-800 border-purple-200">AML</Badge>;
    case "Both":
      return (
        <div className="flex gap-1">
          <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">ASL</Badge>
          <Badge variant="outline" className="bg-purple-50 text-purple-800 border-purple-200">AML</Badge>
        </div>
      );
    default:
      return <Badge variant="outline">None</Badge>;
  }
};

interface RiskBadgeProps {
  risk: string;
}

export const RiskBadge: React.FC<RiskBadgeProps> = ({ risk }) => {
  const badgeColor = getRiskBadgeColor(risk);
  return (
    <Badge variant="outline" className={badgeColor}>
      {risk}
    </Badge>
  );
};

interface StatusBadgeProps {
  status: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const badgeColor = getStatusBadgeColor(status);
  return (
    <Badge variant="outline" className={badgeColor}>
      {status}
    </Badge>
  );
};

// Utility functions
export const getRiskBadgeColor = (risk: string): string => {
  switch (risk) {
    case "Low":
      return "bg-green-100 text-green-800 border-green-200";
    case "Medium":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "High":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

export const getStatusBadgeColor = (status: string): string => {
  switch (status) {
    case "Approved":
      return "bg-green-100 text-green-800 border-green-200";
    case "Restricted":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "Suspended":
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

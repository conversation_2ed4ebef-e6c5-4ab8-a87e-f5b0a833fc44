
import React from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { FileX } from "lucide-react";

export const AslAmlEmptyState: React.FC = () => {
  return (
    <TableRow>
      <TableCell colSpan={7} className="text-center py-12">
        <div className="flex flex-col items-center justify-center">
          <FileX className="h-12 w-12 text-gray-400 mb-3" />
          <p className="text-gray-500 font-medium">No suppliers found</p>
          <p className="text-gray-400 text-sm">Try adjusting your filters or search criteria</p>
        </div>
      </TableCell>
    </TableRow>
  );
};

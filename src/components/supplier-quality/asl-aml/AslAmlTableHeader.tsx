
import React from "react";
import { 
  TableHeader, 
  TableRow, 
  TableHead 
} from "@/components/ui/table";
import { <PERSON>Down, ArrowU<PERSON>, ArrowUpDown } from "lucide-react";

interface AslAmlTableHeaderProps {
  sortField: string | null;
  handleSort: (field: string) => void;
  sortDirection: string | null;
}

export const AslAmlTableHeader: React.FC<AslAmlTableHeaderProps> = ({
  sortField,
  handleSort,
  sortDirection
}) => {
  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
    return sortDirection === "asc" ? (
      <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
    ) : (
      <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />
    );
  };
  
  return (
    <TableHeader>
      <TableRow>
        <TableHead 
          className="cursor-pointer text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4 hover:bg-gray-100 group"
          onClick={() => handleSort('name')}
        >
          <div className="flex items-center">
            Supplier
            {getSortIcon('name')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4 hover:bg-gray-100 group"
          onClick={() => handleSort('approvalType')}
        >
          <div className="flex items-center">
            Approval Type
            {getSortIcon('approvalType')}
          </div>
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4">Category & Status</TableHead>
        <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4">Validity</TableHead>
        <TableHead 
          className="cursor-pointer text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4 hover:bg-gray-100 group"
          onClick={() => handleSort('riskTier')}
        >
          <div className="flex items-center">
            Risk Tier
            {getSortIcon('riskTier')}
          </div>
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4">Qualification Basis</TableHead>
        <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider py-3 px-4">Actions</TableHead>
      </TableRow>
    </TableHeader>
  );
};

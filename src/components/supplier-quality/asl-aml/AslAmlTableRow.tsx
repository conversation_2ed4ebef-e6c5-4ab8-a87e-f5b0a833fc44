
import React from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Eye, RefreshCw, X } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { SupplierAslAml } from "@/types/supplierAslAml";

interface AslAmlTableRowProps {
  supplier: SupplierAslAml;
  requestRequalification: (supplierId: string) => void;
  revokeApproval: (supplierId: string) => void;
}

export const AslAmlTableRow: React.FC<AslAmlTableRowProps> = ({
  supplier,
  requestRequalification,
  revokeApproval
}) => {
  const getApprovalTypeBadge = (type: string) => {
    switch (type) {
      case 'ASL':
        return <Badge className="bg-blue-100 text-blue-800">ASL</Badge>;
      case 'AML':
        return <Badge className="bg-purple-100 text-purple-800">AML</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{type}</Badge>;
    }
  };

  const getRiskTierBadge = (tier: string) => {
    switch (tier) {
      case 'Low':
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      case 'Medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case 'High':
        return <Badge className="bg-red-100 text-red-800">High</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{tier}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <TableRow>
      <TableCell className="font-medium">{supplier.name}</TableCell>
      <TableCell>{getApprovalTypeBadge(supplier.approvalType)}</TableCell>
      <TableCell>{getRiskTierBadge(supplier.riskTier)}</TableCell>
      <TableCell>{getStatusBadge(supplier.status)}</TableCell>
      <TableCell>{new Date(supplier.lastAudit).toLocaleDateString()}</TableCell>
      <TableCell>{new Date(supplier.nextReview).toLocaleDateString()}</TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1">
          {supplier.categories?.map((category, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {category}
            </Badge>
          ))}
        </div>
      </TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1">
          {supplier.certifications?.map((cert, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {cert}
            </Badge>
          ))}
        </div>
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => requestRequalification(supplier.id)}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Request Requalification
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => revokeApproval(supplier.id)}
              className="text-red-600"
            >
              <X className="mr-2 h-4 w-4" />
              Revoke Approval
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};

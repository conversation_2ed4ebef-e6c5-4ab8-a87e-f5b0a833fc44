
import React from "react";
import { useSupplierAslAml } from "@/hooks/useSupplierAslAml";
import { Card } from "@/components/ui/card";
import { 
  Table, 
  TableBody
} from "@/components/ui/table";
import { AslAmlToolbar } from "./AslAmlToolbar";
import { AslAmlTableHeader } from "./AslAmlTableHeader";
import { AslAmlTableRow } from "./AslAmlTableRow";
import { AslAmlEmptyState } from "./AslAmlEmptyState";

export const AslAmlManagement: React.FC = () => {
  const {
    suppliers,
    sortField,
    sortDirection,
    approvalTypeFilter,
    riskTierFilter,
    searchQuery,
    handleSort,
    setApprovalTypeFilter,
    setRiskTierFilter,
    setSearchQuery,
    requestRequalification,
    revokeApproval,
    exportSupplierList
  } = useSupplierAslAml();
  
  return (
    <div className="space-y-4">
      <Card className="p-4 shadow-md">
        <AslAmlToolbar 
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          approvalTypeFilter={approvalTypeFilter}
          setApprovalTypeFilter={setApprovalTypeFilter}
          riskTierFilter={riskTierFilter}
          setRiskTierFilter={setRiskTierFilter}
          exportSupplierList={exportSupplierList}
        />
        
        <div className="overflow-x-auto">
          <Table className="min-w-full border-collapse">
            <AslAmlTableHeader 
              sortField={sortField}
              sortDirection={sortDirection}
              handleSort={handleSort}
            />
            <TableBody>
              {suppliers.length === 0 ? (
                <AslAmlEmptyState />
              ) : (
                suppliers.map(supplier => (
                  <AslAmlTableRow
                    key={supplier.id}
                    supplier={supplier}
                    requestRequalification={requestRequalification}
                    revokeApproval={revokeApproval}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  );
};

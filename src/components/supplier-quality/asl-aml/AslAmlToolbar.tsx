
import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Search, FileText } from "lucide-react";
import { ApprovalType } from "@/types/supplierApprovedList";

interface AslAmlToolbarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  approvalTypeFilter: ApprovalType | 'All';
  setApprovalTypeFilter: (type: ApprovalType | 'All') => void;
  riskTierFilter: 'Low' | 'Medium' | 'High' | 'All';
  setRiskTierFilter: (risk: 'Low' | 'Medium' | 'High' | 'All') => void;
  exportSupplierList: () => void;
}

export const AslAmlToolbar: React.FC<AslAmlToolbarProps> = ({
  searchQuery,
  setSearchQuery,
  approvalTypeFilter,
  setApprovalTypeFilter,
  riskTierFilter,
  setRiskTierFilter,
  exportSupplierList
}) => {
  return (
    <div className="flex flex-col md:flex-row gap-4 justify-between mb-4">
      <div className="flex flex-col md:flex-row gap-4 flex-1">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
          <Input
            placeholder="Search suppliers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        
        <Select
          value={approvalTypeFilter}
          onValueChange={(value) => setApprovalTypeFilter(value as ApprovalType | 'All')}
        >
          <SelectTrigger className="w-[150px] bg-white">
            <SelectValue placeholder="Approval Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Types</SelectItem>
            <SelectItem value="ASL">ASL Only</SelectItem>
            <SelectItem value="AML">AML Only</SelectItem>
            <SelectItem value="Both">ASL & AML</SelectItem>
          </SelectContent>
        </Select>
        
        <Select
          value={riskTierFilter}
          onValueChange={(value) => setRiskTierFilter(value as 'Low' | 'Medium' | 'High' | 'All')}
        >
          <SelectTrigger className="w-[150px] bg-white">
            <SelectValue placeholder="Risk Tier" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Risk Levels</SelectItem>
            <SelectItem value="Low">Low Risk</SelectItem>
            <SelectItem value="Medium">Medium Risk</SelectItem>
            <SelectItem value="High">High Risk</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Button variant="outline" className="flex gap-1 items-center bg-white hover:bg-gray-50" onClick={exportSupplierList}>
        <FileText className="h-4 w-4 mr-1" />
        Export List
      </Button>
    </div>
  );
};

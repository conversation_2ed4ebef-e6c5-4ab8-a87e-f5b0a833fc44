
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Shield, CheckCircle } from "lucide-react";

interface ComplianceRiskIndicatorProps {
  department: string;
}

export const ComplianceRiskIndicator: React.FC<ComplianceRiskIndicatorProps> = ({ department }) => {
  const riskData = [
    {
      category: "Process Compliance",
      level: "Low",
      score: 92,
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: "bg-green-50 border-green-200"
    },
    {
      category: "Documentation",
      level: "Medium",
      score: 78,
      icon: <Shield className="h-5 w-5 text-amber-600" />,
      color: "bg-amber-50 border-amber-200"
    },
    {
      category: "Supplier Risk",
      level: "High",
      score: 65,
      icon: <AlertTriangle className="h-5 w-5 text-red-600" />,
      color: "bg-red-50 border-red-200"
    },
    {
      category: "Training Compliance",
      level: "Low",
      score: 89,
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: "bg-green-50 border-green-200"
    }
  ];

  const getRiskBadge = (level: string) => {
    switch (level) {
      case "High":
        return <Badge className="bg-red-100 text-red-800">High Risk</Badge>;
      case "Medium":
        return <Badge className="bg-amber-100 text-amber-800">Medium Risk</Badge>;
      case "Low":
        return <Badge className="bg-green-100 text-green-800">Low Risk</Badge>;
      default:
        return <Badge variant="outline">{level}</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Compliance Risk Indicators</CardTitle>
        <p className="text-sm text-slate-600">Real-time risk assessment across key compliance areas</p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {riskData.map((item, index) => (
            <Card key={index} className={`${item.color} shadow-sm`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  {item.icon}
                  {getRiskBadge(item.level)}
                </div>
                <h4 className="font-medium text-slate-900 mb-1">{item.category}</h4>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-slate-900">{item.score}%</span>
                  <span className="text-sm text-slate-600">Compliance</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

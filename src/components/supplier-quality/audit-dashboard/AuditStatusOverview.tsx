
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Calendar, Clock, CheckCircle, AlertTriangle } from "lucide-react";

export const AuditStatusOverview: React.FC = () => {
  const statusData = [
    {
      title: "Upcoming",
      count: 12,
      icon: <Calendar className="h-6 w-6 text-blue-600" />,
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      title: "In Progress",
      count: 8,
      icon: <Clock className="h-6 w-6 text-amber-600" />,
      bgColor: "bg-amber-50",
      borderColor: "border-amber-200"
    },
    {
      title: "Completed",
      count: 45,
      icon: <CheckCircle className="h-6 w-6 text-green-600" />,
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      title: "Overdue",
      count: 3,
      icon: <AlertTriangle className="h-6 w-6 text-red-600" />,
      bgColor: "bg-red-50",
      borderColor: "border-red-200"
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
      {statusData.map((item, index) => (
        <Card key={index} className={`${item.bgColor} ${item.borderColor} shadow-sm hover:shadow-md transition-shadow`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">{item.title}</p>
                <p className="text-3xl font-bold text-slate-900">{item.count}</p>
              </div>
              <div className="flex-shrink-0">
                {item.icon}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

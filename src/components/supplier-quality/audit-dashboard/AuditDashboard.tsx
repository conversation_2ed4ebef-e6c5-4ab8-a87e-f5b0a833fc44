
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clock, CheckCircle, AlertTriangle, TrendingUp, Filter } from "lucide-react";
import { AuditStatusOverview } from "./AuditStatusOverview";
import { AuditTrendsChart } from "./AuditTrendsChart";
import { CapaStatusChart } from "./CapaStatusChart";
import { ComplianceRiskIndicator } from "./ComplianceRiskIndicator";
import { AuditFilters } from "./AuditFilters";

interface AuditDashboardProps {
  className?: string;
}

export const AuditDashboard: React.FC<AuditDashboardProps> = ({ className }) => {
  const [timeRange, setTimeRange] = useState("30days");
  const [auditType, setAuditType] = useState("all");
  const [department, setDepartment] = useState("all");
  const [showFilters, setShowFilters] = useState(false);

  return (
    <div className={`space-y-6 w-full max-w-[1400px] mx-auto ${className}`}>
      {/* Header with Filters */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Real-Time Audit Dashboard</h2>
          <p className="text-slate-600">Monitor compliance audit activities and performance</p>
        </div>
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="gap-2"
        >
          <Filter className="h-4 w-4" />
          Filters
        </Button>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <AuditFilters
          timeRange={timeRange}
          auditType={auditType}
          department={department}
          onTimeRangeChange={setTimeRange}
          onAuditTypeChange={setAuditType}
          onDepartmentChange={setDepartment}
        />
      )}

      {/* Status Overview */}
      <AuditStatusOverview />

      {/* Charts Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 w-full">
        <AuditTrendsChart timeRange={timeRange} auditType={auditType} />
        <CapaStatusChart />
      </div>

      {/* Compliance Risk Indicator */}
      <ComplianceRiskIndicator department={department} />
    </div>
  );
};

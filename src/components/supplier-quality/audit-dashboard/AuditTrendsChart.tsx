
import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from "recharts";

interface AuditTrendsChartProps {
  timeRange: string;
  auditType: string;
}

export const AuditTrendsChart: React.FC<AuditTrendsChartProps> = ({ timeRange, auditType }) => {
  const passFail = [
    { month: "Jan", pass: 85, fail: 15 },
    { month: "Feb", pass: 88, fail: 12 },
    { month: "Mar", pass: 82, fail: 18 },
    { month: "Apr", pass: 90, fail: 10 },
    { month: "May", pass: 87, fail: 13 },
    { month: "Jun", pass: 92, fail: 8 }
  ];

  const supplierTrends = [
    { supplier: "Omega Polymers", score: 88 },
    { supplier: "Falcon Alloys", score: 92 },
    { supplier: "Zenith Steel", score: 85 },
    { supplier: "MetalTech Inc.", score: 90 },
    { supplier: "Precision Circuits", score: 94 }
  ];

  return (
    <div className="space-y-6">
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="h-2 w-2 rounded-full bg-green-500"></div>
            Audit Pass/Fail Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={passFail}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="pass" stroke="#10b981" strokeWidth={2} name="Pass %" />
                <Line type="monotone" dataKey="fail" stroke="#ef4444" strokeWidth={2} name="Fail %" />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>Supplier Performance Scores</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={supplierTrends} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="supplier" type="category" width={120} />
                <Tooltip />
                <Bar dataKey="score" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

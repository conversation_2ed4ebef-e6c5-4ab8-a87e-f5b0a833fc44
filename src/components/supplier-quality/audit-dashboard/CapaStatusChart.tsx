
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";
import { Badge } from "@/components/ui/badge";

export const CapaStatusChart: React.FC = () => {
  const capaData = [
    { name: "Open", value: 15, color: "#ef4444" },
    { name: "In Review", value: 8, color: "#f59e0b" },
    { name: "Closed", value: 32, color: "#10b981" }
  ];

  const totalCapas = capaData.reduce((sum, item) => sum + item.value, 0);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>CAPAs by Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={capaData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {capaData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-3xl font-bold text-slate-900">{totalCapas}</p>
              <p className="text-sm text-slate-600">Total CAPAs</p>
            </div>
            
            <div className="space-y-3">
              {capaData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  <Badge variant="outline">{item.value}</Badge>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { SupplierQualityDashboard } from "../SupplierQualityDashboard";
import { AuditsManagement } from "../audits/AuditsManagement";
import { TraceabilityView } from "../traceability/TraceabilityView";
import { MatrixView } from "../matrix/MatrixView";
import { FormDashboard } from "../forms/FormDashboard";
import { ESourcingDashboard } from "../esourcing/ESourcingDashboard";
import { TimelineView } from "../timeline/TimelineView";
import { InsightsView } from "../insights/InsightsView";
import { FeedbackView } from "../feedback/FeedbackView";

interface TabContentProps {
  activeTab: string;
}

export const TabContent: React.FC<TabContentProps> = ({ activeTab }) => {
  switch (activeTab) {
    case "management":
      return <SupplierQualityDashboard />;
    case "audits":
      return <AuditsManagement />;
    case "traceability":
      return <TraceabilityView />;
    case "matrix":
      return <MatrixView />;
    case "forms":
      return <FormDashboard />;
    case "esourcing":
      return <ESourcingDashboard />;
    case "timeline":
      return <TimelineView />;
    case "insights":
      return <InsightsView />;
    case "feedback":
      return <FeedbackView />;
    default:
      return <SupplierQualityDashboard />;
  }
};

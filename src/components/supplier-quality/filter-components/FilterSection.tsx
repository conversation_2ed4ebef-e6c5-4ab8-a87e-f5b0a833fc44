
import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface FilterSectionProps {
  title: string;
  icon: React.ReactNode;
  options: string[];
  selectedValue: string;
  onValueChange: (value: string) => void;
}

export const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  icon,
  options,
  selectedValue,
  onValueChange
}) => {
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        {icon}
        {title}
      </div>
      <div className="flex flex-wrap gap-1">
        {options.map((option) => (
          <Button
            key={option}
            size="sm"
            variant={selectedValue === option ? "default" : "outline"}
            className="h-7 text-xs"
            onClick={() => onValueChange(selectedValue === option ? '' : option)}
          >
            {option}
          </Button>
        ))}
      </div>
    </div>
  );
};


import React from "react";
import { Button } from "@/components/ui/button";

interface FilterButtonProps {
  label: string;
  onClick?: () => void;
}

export const FilterButton: React.FC<FilterButtonProps> = ({ label, onClick }) => {
  return (
    <Button 
      variant="outline" 
      className="flex justify-between items-center w-full"
      onClick={onClick}
    >
      <span>{label}</span>
      <span>▼</span>
    </Button>
  );
};

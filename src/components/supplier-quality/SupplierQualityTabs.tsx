
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  LayoutDashboard, 
  FormInput,
  Users, 
  ClipboardCheck, 
  GitBranch, 
  Grid3X3,
  ShoppingCart,
  Calendar,
  Info,
  MessageSquare
} from "lucide-react";

interface SupplierQualityTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const SupplierQualityTabs: React.FC<SupplierQualityTabsProps> = ({
  activeTab,
  onTabChange
}) => {
  return (
    <div className="w-full">
      <TabsList className="grid w-full grid-cols-5 lg:grid-cols-10 bg-white border border-gray-200">
        <TabsTrigger value="dashboard" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <LayoutDashboard className="h-4 w-4" />
          <span className="hidden sm:inline">Dashboard</span>
        </TabsTrigger>
        <TabsTrigger value="forms" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <FormInput className="h-4 w-4" />
          <span className="hidden sm:inline">Forms</span>
        </TabsTrigger>
        <TabsTrigger value="management" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <Users className="h-4 w-4" />
          <span className="hidden sm:inline">Management</span>
        </TabsTrigger>
        <TabsTrigger value="audits" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <ClipboardCheck className="h-4 w-4" />
          <span className="hidden sm:inline">Audits</span>
        </TabsTrigger>
        <TabsTrigger value="traceability" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <GitBranch className="h-4 w-4" />
          <span className="hidden sm:inline">Traceability</span>
        </TabsTrigger>
        <TabsTrigger value="matrix" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <Grid3X3 className="h-4 w-4" />
          <span className="hidden sm:inline">Matrix</span>
        </TabsTrigger>
        <TabsTrigger value="esourcing" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <ShoppingCart className="h-4 w-4" />
          <span className="hidden sm:inline">E-Sourcing</span>
        </TabsTrigger>
        <TabsTrigger value="timeline" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <Calendar className="h-4 w-4" />
          <span className="hidden sm:inline">Timeline</span>
        </TabsTrigger>
        <TabsTrigger value="insights" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <Info className="h-4 w-4" />
          <span className="hidden sm:inline">AI Insights</span>
        </TabsTrigger>
        <TabsTrigger value="feedback" className="flex items-center gap-2 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700">
          <MessageSquare className="h-4 w-4" />
          <span className="hidden sm:inline">Feedback</span>
        </TabsTrigger>
      </TabsList>
    </div>
  );
};

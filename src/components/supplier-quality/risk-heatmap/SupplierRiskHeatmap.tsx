
import React, { useState } from "react";
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SupplierRiskData } from "@/types/supplierRiskHeatmap";
import { useSupplierRiskAnalysis } from "@/hooks/useSupplierRiskAnalysis";
import { SupplierRiskTooltip } from "./SupplierRiskTooltip";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { ZoomIn, ZoomOut, RefreshCw } from "lucide-react";

interface SupplierRiskHeatmapProps {
  data: SupplierRiskData[];
}

export const SupplierRiskHeatmap: React.FC<SupplierRiskHeatmapProps> = ({ data }) => {
  const { getQuadrantColor } = useSupplierRiskAnalysis();
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDomainReset, setIsDomainReset] = useState(true);
  
  // Calculate domain ranges based on data and zoom level
  const riskMin = isDomainReset ? 0 : Math.max(0, Math.min(...data.map(d => d.riskScore)) - 10);
  const riskMax = isDomainReset ? 100 : Math.min(100, Math.max(...data.map(d => d.riskScore)) + 10);
  const auditMin = isDomainReset ? 50 : Math.max(50, Math.min(...data.map(d => d.auditScore)) - 5);
  const auditMax = isDomainReset ? 100 : Math.min(100, Math.max(...data.map(d => d.auditScore)) + 5);
  
  // Adjust bubble size based on spend
  const getSize = (spend: number) => {
    const baseSize = Math.max(1000, Math.min(2500, spend / 1000));
    return baseSize * zoomLevel;
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.25, 2));
    setIsDomainReset(false);
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.5));
  };

  const handleReset = () => {
    setZoomLevel(1);
    setIsDomainReset(true);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Supplier Risk Heatmap</CardTitle>
        <CardDescription>
          Visualization of suppliers based on audit scores, risk levels, and spend
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="heatmap">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
            <TabsList>
              <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
              <TabsTrigger value="grid">Grid View</TabsTrigger>
            </TabsList>
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleZoomIn}
                className="h-8 w-8 p-0"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleZoomOut}
                className="h-8 w-8 p-0"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleReset}
                className="h-8 flex items-center"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Reset
              </Button>
            </div>
          </div>
          
          <TabsContent value="heatmap" className="mt-0">
            <div className="w-full h-[500px]">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    type="number" 
                    dataKey="riskScore" 
                    domain={[riskMin, riskMax]} 
                    name="Risk Score" 
                    label={{ value: 'Risk Score (lower is better)', position: 'bottom', offset: 0 }}
                    reversed
                  />
                  <YAxis 
                    type="number" 
                    dataKey="auditScore" 
                    domain={[auditMin, auditMax]} 
                    name="Audit Score" 
                    label={{ value: 'Audit Score (%)', angle: -90, position: 'left' }}
                  />
                  <ZAxis 
                    type="number" 
                    dataKey="spend" 
                    range={[400, 1000]} 
                    name="Annual Spend" 
                  />
                  <Tooltip content={<SupplierRiskTooltip />} />
                  <Legend />
                  
                  {/* Render each supplier as a scatter point */}
                  {data.map(supplier => (
                    <Scatter 
                      key={supplier.id}
                      name={supplier.name}
                      data={[supplier]}
                      fill={getQuadrantColor(supplier.riskScore, supplier.auditScore)}
                    />
                  ))}
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>
          
          <TabsContent value="grid" className="mt-0">
            <SupplierRiskGrid data={data} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Grid view component for tabular representation
const SupplierRiskGrid: React.FC<{data: SupplierRiskData[]}> = ({ data }) => {
  const { getRiskLevel, getAuditScoreLevel, formatCurrency, getQuadrantLabel } = useSupplierRiskAnalysis();
  
  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-muted">
            <th className="text-left px-4 py-2">Supplier</th>
            <th className="text-left px-4 py-2">Audit Score</th>
            <th className="text-left px-4 py-2">Risk Score</th>
            <th className="text-left px-4 py-2">Annual Spend</th>
            <th className="text-left px-4 py-2">Risk Level</th>
          </tr>
        </thead>
        <tbody>
          {data.map(supplier => (
            <tr key={supplier.id} className="border-b hover:bg-muted/50">
              <td className="px-4 py-3">{supplier.name}</td>
              <td className="px-4 py-3">
                <span className={`px-2 py-0.5 rounded-md text-xs font-medium ${
                  supplier.auditScore >= 85 ? 'bg-green-100 text-green-800' :
                  supplier.auditScore >= 70 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>{supplier.auditScore}%</span>
              </td>
              <td className="px-4 py-3">
                <span className={`px-2 py-0.5 rounded-md text-xs font-medium ${
                  supplier.riskScore < 30 ? 'bg-green-100 text-green-800' :
                  supplier.riskScore < 70 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>{supplier.riskScore}</span>
              </td>
              <td className="px-4 py-3">{formatCurrency(supplier.spend)}</td>
              <td className="px-4 py-3">
                <span className="inline-block px-2 py-1 rounded-md text-xs font-medium bg-gray-100">
                  {getQuadrantLabel(supplier.riskScore, supplier.auditScore)}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

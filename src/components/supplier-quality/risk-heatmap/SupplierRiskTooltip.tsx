
import React from "react";
import { useSupplierRiskAnalysis } from "@/hooks/useSupplierRiskAnalysis";

export const SupplierRiskTooltip = ({ active, payload }: any) => {
  const { getRiskLevel, formatCurrency, getQuadrantLabel } = useSupplierRiskAnalysis();
  
  if (!active || !payload || payload.length === 0) {
    return null;
  }

  const data = payload[0].payload;
  
  return (
    <div className="bg-white p-4 border rounded-md shadow-md">
      <p className="font-bold text-lg mb-1">{data.name}</p>
      {data.category && <p className="text-sm text-gray-500 mb-2">{data.category}</p>}
      
      <div className="grid grid-cols-2 gap-x-6 gap-y-1 text-sm">
        <div>Audit Score:</div>
        <div className="font-medium">{data.auditScore}%</div>
        
        <div>Risk Score:</div>
        <div className="font-medium">{data.riskScore}</div>
        
        <div>Annual Spend:</div>
        <div className="font-medium">{formatCurrency(data.spend)}</div>
        
        <div>Risk Level:</div>
        <div className="font-medium">{getRiskLevel(data.riskScore)}</div>
      </div>
      
      <div className="mt-2 pt-2 border-t text-sm font-medium">
        Status: {getQuadrantLabel(data.riskScore, data.auditScore)}
      </div>
    </div>
  );
};

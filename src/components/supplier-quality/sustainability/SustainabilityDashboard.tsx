
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Search, Upload, FileText, Leaf, Users, Shield, AlertTriangle } from "lucide-react";
import { SustainabilityStatusBadge } from "./SustainabilityStatusBadge";
import { SustainabilitySupplierCard } from "./SustainabilitySupplierCard";
import { SustainabilityAssessmentDetail } from "./SustainabilityAssessmentDetail";
import { DeclarationUpload } from "./DeclarationUpload";
import { mockSustainabilitySuppliers } from "@/data/mockSustainabilityData";

export const SustainabilityDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  const filteredSuppliers = mockSustainabilitySuppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const statusCounts = {
    compliant: mockSustainabilitySuppliers.filter(s => s.status === 'Compliant').length,
    watchlist: mockSustainabilitySuppliers.filter(s => s.status === 'Watchlist').length,
    highRisk: mockSustainabilitySuppliers.filter(s => s.status === 'High Risk').length,
  };

  const averageCarbonFootprint = (
    mockSustainabilitySuppliers.reduce((sum, s) => sum + s.carbonFootprint, 0) / 
    mockSustainabilitySuppliers.length
  ).toFixed(1);

  if (selectedSupplier) {
    const supplier = mockSustainabilitySuppliers.find(s => s.id === selectedSupplier);
    if (supplier) {
      return (
        <SustainabilityAssessmentDetail 
          supplier={supplier} 
          onBack={() => setSelectedSupplier(null)} 
        />
      );
    }
  }

  return (
    <div className="space-y-6">
      {/* Header Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Compliant</p>
                <p className="text-2xl font-bold text-green-600">{statusCounts.compliant}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-amber-600" />
              <div>
                <p className="text-sm text-gray-600">Watchlist</p>
                <p className="text-2xl font-bold text-amber-600">{statusCounts.watchlist}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">High Risk</p>
                <p className="text-2xl font-bold text-red-600">{statusCounts.highRisk}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Leaf className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Avg. Carbon Footprint</p>
                <p className="text-2xl font-bold text-blue-600">{averageCarbonFootprint}</p>
                <p className="text-xs text-gray-500">kg CO₂/unit</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="assessments">Assessments</TabsTrigger>
          <TabsTrigger value="declarations">Declarations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search suppliers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Bulk Upload
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Suppliers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredSuppliers.map(supplier => (
              <SustainabilitySupplierCard
                key={supplier.id}
                supplier={supplier}
                onSelect={() => setSelectedSupplier(supplier.id)}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="assessments">
          <Card>
            <CardHeader>
              <CardTitle>Assessment Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Select a supplier from the Overview tab to view detailed assessments.
              </p>
              <Button onClick={() => setActiveTab("overview")}>
                View Suppliers
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="declarations">
          <DeclarationUpload />
        </TabsContent>
      </Tabs>
    </div>
  );
};

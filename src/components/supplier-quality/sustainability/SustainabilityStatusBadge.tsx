
import React from "react";
import { Badge } from "@/components/ui/badge";

interface SustainabilityStatusBadgeProps {
  status: 'Compliant' | 'Watchlist' | 'High Risk';
}

export const SustainabilityStatusBadge: React.FC<SustainabilityStatusBadgeProps> = ({ status }) => {
  switch (status) {
    case "Compliant":
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100">
          Compliant
        </Badge>
      );
    case "Watchlist":
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100">
          Watchlist
        </Badge>
      );
    case "High Risk":
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100">
          High Risk
        </Badge>
      );
    default:
      return <Badge>{status}</Badge>;
  }
};

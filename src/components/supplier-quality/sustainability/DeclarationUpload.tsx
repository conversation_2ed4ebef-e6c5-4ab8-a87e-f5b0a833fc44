
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, FileText, Calendar, User, CheckCircle, XCircle, Clock } from "lucide-react";
import { mockSustainabilityDeclarations } from "@/data/mockSustainabilityData";

export const DeclarationUpload: React.FC = () => {
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    // <PERSON>le file upload logic here
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Approved": return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Rejected": return <XCircle className="h-4 w-4 text-red-600" />;
      case "Pending Review": return <Clock className="h-4 w-4 text-amber-600" />;
      default: return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Approved":
        return <Badge className="bg-green-50 text-green-700 border-green-200">Approved</Badge>;
      case "Rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "Pending Review":
        return <Badge className="bg-amber-50 text-amber-700 border-amber-200">Pending Review</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Sustainability Declaration</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? "border-blue-500 bg-blue-50" 
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Drop your sustainability declaration here
            </h3>
            <p className="text-gray-600 mb-4">
              or click to browse files (PDF, DOC, DOCX up to 10MB)
            </p>
            <Button>
              Choose File
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Declaration Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Declaration Requirements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium">Environmental Commitments</p>
                <p className="text-sm text-gray-600">Carbon reduction targets, waste management, and resource conservation</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium">Labor Standards</p>
                <p className="text-sm text-gray-600">Fair wages, working conditions, and employee rights</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium">Ethical Business Practices</p>
                <p className="text-sm text-gray-600">Anti-corruption policies, transparency, and governance</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium">Authorized Signature</p>
                <p className="text-sm text-gray-600">Must be signed by an authorized company representative</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Declarations */}
      <Card>
        <CardHeader>
          <CardTitle>Uploaded Declarations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockSustainabilityDeclarations.map((declaration) => (
              <div key={declaration.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="font-medium">{declaration.fileName}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>Uploaded: {new Date(declaration.uploadDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>Signed by: {declaration.signedBy}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>Valid until: {new Date(declaration.validUntil).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {getStatusBadge(declaration.status)}
                  <Button variant="outline" size="sm">
                    View
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

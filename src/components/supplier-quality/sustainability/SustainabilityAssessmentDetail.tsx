
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, Leaf, Users, Shield, FileText, Upload, Calendar } from "lucide-react";
import { SustainabilityStatusBadge } from "./SustainabilityStatusBadge";
import { SustainabilitySupplier } from "@/types/supplierSustainability";

interface SustainabilityAssessmentDetailProps {
  supplier: SustainabilitySupplier;
  onBack: () => void;
}

export const SustainabilityAssessmentDetail: React.FC<SustainabilityAssessmentDetailProps> = ({
  supplier,
  onBack
}) => {
  const [activeSection, setActiveSection] = useState("environmental");

  const getSectionIcon = (section: string) => {
    switch (section) {
      case "environmental": return <Leaf className="h-5 w-5" />;
      case "labor": return <Users className="h-5 w-5" />;
      case "ethical": return <Shield className="h-5 w-5" />;
      default: return null;
    }
  };

  const getSectionColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-amber-600";
    return "text-red-600";
  };

  const getProgressColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-amber-500";
    return "bg-red-500";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{supplier.name}</h1>
              <p className="text-gray-600">Sustainability Assessment</p>
            </div>
            <SustainabilityStatusBadge status={supplier.status} />
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Overall Score</p>
              <p className={`text-2xl font-bold ${getSectionColor(supplier.overallScore)}`}>
                {supplier.overallScore}%
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Carbon Footprint</p>
              <p className="text-2xl font-bold text-blue-600">{supplier.carbonFootprint}</p>
              <p className="text-xs text-gray-500">kg CO₂/unit</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Last Audit</p>
              <p className="text-lg font-semibold">{supplier.lastAudit}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Declaration</p>
              <div className="flex items-center justify-center gap-2 mt-1">
                <FileText className={`h-4 w-4 ${supplier.declarationUploaded ? 'text-green-600' : 'text-gray-400'}`} />
                <span className="text-sm font-medium">
                  {supplier.declarationUploaded ? 'Uploaded' : 'Pending'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assessment Sections */}
      <Tabs value={activeSection} onValueChange={setActiveSection}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="environmental" className="flex items-center gap-2">
            <Leaf className="h-4 w-4" />
            Environmental
          </TabsTrigger>
          <TabsTrigger value="labor" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Labor
          </TabsTrigger>
          <TabsTrigger value="ethical" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Ethical
          </TabsTrigger>
          <TabsTrigger value="declaration" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Declaration
          </TabsTrigger>
        </TabsList>

        {Object.entries(supplier.assessments).map(([key, assessment]) => (
          <TabsContent key={key} value={key}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getSectionIcon(key)}
                  {key.charAt(0).toUpperCase() + key.slice(1)} Assessment
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Score Comparison */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Self-Assessment Score</span>
                      <span className={`font-bold ${getSectionColor(assessment.selfScore)}`}>
                        {assessment.selfScore}%
                      </span>
                    </div>
                    <Progress value={assessment.selfScore} className="h-3" />
                  </div>

                  {assessment.auditScore && (
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">External Audit Score</span>
                        <span className={`font-bold ${getSectionColor(assessment.auditScore)}`}>
                          {assessment.auditScore}%
                        </span>
                      </div>
                      <Progress value={assessment.auditScore} className="h-3" />
                    </div>
                  )}
                </div>

                {/* Assessment Items */}
                <div className="space-y-4">
                  <h4 className="font-medium">Assessment Items</h4>
                  {assessment.items.map((item, index) => (
                    <Card key={item.id} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <h5 className="font-medium">{item.question}</h5>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <span className="text-sm text-gray-600">Self Rating: </span>
                              <Badge variant="outline">{item.selfRating}/5</Badge>
                            </div>
                            {item.auditRating && (
                              <div>
                                <span className="text-sm text-gray-600">Audit Rating: </span>
                                <Badge variant="outline">{item.auditRating}/5</Badge>
                              </div>
                            )}
                          </div>

                          {item.notes && (
                            <div>
                              <span className="text-sm text-gray-600">Notes: </span>
                              <p className="text-sm">{item.notes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Last Updated */}
                <div className="flex items-center gap-2 text-sm text-gray-600 pt-4 border-t">
                  <Calendar className="h-4 w-4" />
                  <span>Last updated: {new Date(assessment.lastUpdated).toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}

        <TabsContent value="declaration">
          <Card>
            <CardHeader>
              <CardTitle>Sustainability Declaration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {supplier.declarationUploaded ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-blue-600" />
                      <div>
                        <p className="font-medium">Sustainability Declaration</p>
                        <p className="text-sm text-gray-600">
                          Signed on: {supplier.declarationDate && new Date(supplier.declarationDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Approved
                    </Badge>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 mr-2" />
                      View Document
                    </Button>
                    <Button variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload New Version
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Declaration Uploaded</h3>
                  <p className="text-gray-600 mb-4">
                    This supplier has not yet uploaded their sustainability declaration.
                  </p>
                  <Button>
                    <Upload className="h-4 w-4 mr-2" />
                    Request Declaration
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

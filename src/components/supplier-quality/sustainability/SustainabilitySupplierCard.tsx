
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Leaf, FileCheck, Phone, Mail } from "lucide-react";
import { SustainabilityStatusBadge } from "./SustainabilityStatusBadge";
import { SustainabilitySupplier } from "@/types/supplierSustainability";

interface SustainabilitySupplierCardProps {
  supplier: SustainabilitySupplier;
  onSelect: () => void;
}

export const SustainabilitySupplierCard: React.FC<SustainabilitySupplierCardProps> = ({
  supplier,
  onSelect
}) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-semibold text-lg">{supplier.name}</h3>
              <p className="text-sm text-gray-600">{supplier.contact.name}</p>
            </div>
            <SustainabilityStatusBadge status={supplier.status} />
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <span>Last Audit: {supplier.lastAudit}</span>
            </div>
            <div className="flex items-center gap-2">
              <Leaf className="h-4 w-4 text-green-600" />
              <span>{supplier.carbonFootprint} kg CO₂/unit</span>
            </div>
          </div>

          {/* Overall Score */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Score:</span>
            <div className="flex items-center gap-2">
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div 
                  className={`h-full rounded-full ${
                    supplier.overallScore >= 80 ? 'bg-green-500' :
                    supplier.overallScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${supplier.overallScore}%` }}
                ></div>
              </div>
              <span className="font-medium">{supplier.overallScore}%</span>
            </div>
          </div>

          {/* Declaration Status */}
          <div className="flex items-center gap-2">
            <FileCheck className={`h-4 w-4 ${supplier.declarationUploaded ? 'text-green-600' : 'text-gray-400'}`} />
            <span className="text-sm">
              Declaration: {supplier.declarationUploaded ? 'Uploaded' : 'Pending'}
            </span>
            {supplier.declarationDate && (
              <Badge variant="outline" className="text-xs">
                {new Date(supplier.declarationDate).toLocaleDateString()}
              </Badge>
            )}
          </div>

          {/* Contact Info */}
          <div className="pt-2 border-t space-y-1">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Mail className="h-3 w-3" />
              <span>{supplier.contact.email}</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Phone className="h-3 w-3" />
              <span>{supplier.contact.phone}</span>
            </div>
          </div>

          {/* Action Button */}
          <Button onClick={onSelect} className="w-full mt-3" size="sm">
            View Assessment
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

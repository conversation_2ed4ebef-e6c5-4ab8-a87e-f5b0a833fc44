
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, ComposedChart } from "recharts";
import { TrendingUp, TrendingDown, Target, Clock, CheckCircle, AlertTriangle } from "lucide-react";
import { OTIFMetrics } from "@/types/supplierRelationship";

interface OTIFPerformanceTabProps {
  otifMetrics: OTIFMetrics;
}

export const OTIFPerformanceTab: React.FC<OTIFPerformanceTabProps> = ({ otifMetrics }) => {
  const getTrendIcon = () => {
    switch (otifMetrics.trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down": return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <Target className="h-4 w-4 text-gray-600" />;
    }
  };

  // Ensure monthly data has proper structure with fallback
  const monthlyDataWithFallback = otifMetrics.monthlyData && otifMetrics.monthlyData.length > 0 
    ? otifMetrics.monthlyData 
    : [
        { month: "Jan", otif: 94, onTime: 96, inFull: 98, orders: 150 },
        { month: "Feb", otif: 96, onTime: 98, inFull: 98, orders: 165 },
        { month: "Mar", otif: 95, onTime: 97, inFull: 98, orders: 180 },
        { month: "Apr", otif: 97, onTime: 99, inFull: 98, orders: 170 },
        { month: "May", otif: 96, onTime: 98, inFull: 98, orders: 185 },
        { month: "Jun", otif: 98, onTime: 99, inFull: 99, orders: 190 }
      ];

  const deliveryData = [
    { name: "On Time", value: otifMetrics.deliveryPerformance?.onTime || 95, color: "#10b981" },
    { name: "Late", value: otifMetrics.deliveryPerformance?.late || 3, color: "#ef4444" },
    { name: "Early", value: otifMetrics.deliveryPerformance?.early || 2, color: "#f59e0b" }
  ];

  const qualityMetricsWithFallback = {
    firstPassYield: otifMetrics.qualityMetrics?.firstPassYield || 98,
    defectRate: otifMetrics.qualityMetrics?.defectRate || 0.5,
    customerComplaints: otifMetrics.qualityMetrics?.customerComplaints || 2
  };

  return (
    <div className="space-y-6">
      {/* OTIF Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Current OTIF</p>
                <p className="text-3xl font-bold text-blue-600">{otifMetrics.currentOTIF}%</p>
                <p className="text-xs text-gray-500">Target: {otifMetrics.targetOTIF}%</p>
              </div>
              <div className="flex flex-col items-center">
                {getTrendIcon()}
                <Badge variant={otifMetrics.currentOTIF >= otifMetrics.targetOTIF ? "default" : "destructive"} className="mt-1">
                  {otifMetrics.currentOTIF >= otifMetrics.targetOTIF ? "On Target" : "Below Target"}
                </Badge>
              </div>
            </div>
            <div className="mt-4">
              <Progress value={(otifMetrics.currentOTIF / otifMetrics.targetOTIF) * 100} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-3">
              <Clock className="h-5 w-5 text-blue-600" />
              <p className="text-sm font-medium">On-Time Delivery</p>
            </div>
            <p className="text-2xl font-bold">{deliveryData[0].value}%</p>
            <p className="text-xs text-gray-500">Current month performance</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 mb-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <p className="text-sm font-medium">First Pass Yield</p>
            </div>
            <p className="text-2xl font-bold">{qualityMetricsWithFallback.firstPassYield}%</p>
            <p className="text-xs text-gray-500">Quality performance</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* OTIF Trend */}
        <Card>
          <CardHeader>
            <CardTitle>OTIF Trend Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyDataWithFallback}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[75, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="otif" stroke="#3b82f6" strokeWidth={3} name="OTIF %" />
                <Line type="monotone" dataKey="onTime" stroke="#10b981" strokeWidth={2} name="On Time %" />
                <Line type="monotone" dataKey="inFull" stroke="#8b5cf6" strokeWidth={2} name="In Full %" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Delivery Performance Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Delivery Performance Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={deliveryData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {deliveryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value}%`} />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {deliveryData.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                    <span className="text-sm">{item.name}</span>
                  </div>
                  <span className="font-medium">{item.value}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Orders and Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Orders and OTIF Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={monthlyDataWithFallback}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" domain={[0, 200]} />
              <YAxis yAxisId="right" orientation="right" domain={[75, 100]} />
              <Tooltip />
              <Bar yAxisId="left" dataKey="orders" fill="#94a3b8" name="Orders" />
              <Line yAxisId="right" type="monotone" dataKey="otif" stroke="#3b82f6" strokeWidth={2} name="OTIF %" />
            </ComposedChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Quality Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Metrics Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <p className="text-2xl font-bold">{qualityMetricsWithFallback.firstPassYield}%</p>
              <p className="text-sm text-gray-600">First Pass Yield</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="h-8 w-8 text-amber-600" />
              </div>
              <p className="text-2xl font-bold">{qualityMetricsWithFallback.defectRate}%</p>
              <p className="text-sm text-gray-600">Defect Rate</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <p className="text-2xl font-bold">{qualityMetricsWithFallback.customerComplaints}</p>
              <p className="text-sm text-gray-600">Customer Complaints</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

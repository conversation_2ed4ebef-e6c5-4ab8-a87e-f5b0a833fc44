
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from "recharts";
import { TrendingUp, Target, Award, AlertCircle } from "lucide-react";
import { mockPerformanceAnalytics } from "@/data/mockSRMData";

export const PerformanceAnalyticsTab: React.FC = () => {
  const radarData = [
    { subject: 'Quality', A: 95, B: 90, fullMark: 100 },
    { subject: 'Delivery', A: 88, B: 85, fullMark: 100 },
    { subject: 'Cost', A: 91, B: 88, fullMark: 100 },
    { subject: 'Innovation', A: 85, B: 82, fullMark: 100 },
    { subject: 'Compliance', A: 97, B: 94, fullMark: 100 },
    { subject: 'Communication', A: 92, B: 89, fullMark: 100 }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Performance Trend</p>
                <p className="text-2xl font-bold text-green-600">+5.2%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Target Achievement</p>
                <p className="text-2xl font-bold">87%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Best Category</p>
                <p className="text-lg font-bold">Quality</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Improvement Areas</p>
                <p className="text-lg font-bold">Delivery</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Radar</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" />
                <PolarRadiusAxis domain={[0, 100]} />
                <Radar name="Current" dataKey="A" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                <Radar name="Target" dataKey="B" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
              </RadarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockPerformanceAnalytics.trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[75, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="performance" stroke="#3b82f6" strokeWidth={3} name="Performance" />
                <Line type="monotone" dataKey="quality" stroke="#10b981" strokeWidth={2} name="Quality" />
                <Line type="monotone" dataKey="delivery" stroke="#f59e0b" strokeWidth={2} name="Delivery" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Category Benchmarks</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={mockPerformanceAnalytics.benchmarkData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis domain={[70, 100]} />
              <Tooltip />
              <Bar dataKey="avgScore" fill="#3b82f6" name="Average Score" />
              <Bar dataKey="topPerformer" fill="#10b981" name="Top Performer" />
              <Bar dataKey="industryAvg" fill="#6b7280" name="Industry Average" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { SupplierRelationship } from "@/types/supplierRelationship";
import { Building, Phone, Mail, User, TrendingUp, AlertTriangle } from "lucide-react";

interface SRMWorkspaceProps {
  supplier: SupplierRelationship;
}

export const SRMWorkspace: React.FC<SRMWorkspaceProps> = ({ supplier }) => {
  return (
    <div className="space-y-6">
      {/* Supplier Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                {supplier.name}
              </CardTitle>
              <p className="text-gray-600">{supplier.category}</p>
            </div>
            <Badge variant={supplier.status === 'Active' ? 'default' : 'secondary'}>
              {supplier.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-500" />
              <div>
                <p className="font-medium">{supplier.contact.name}</p>
                <p className="text-sm text-gray-600">{supplier.contact.role}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-500" />
              <p className="text-sm">{supplier.contact.email}</p>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <p className="text-sm">{supplier.contact.phone}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="communications">Communications</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Overall Score</span>
                  <span className="text-2xl font-bold">{supplier.overallScore}%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">OTIF Score</span>
                  <span className="text-2xl font-bold">{supplier.otifMetrics.currentOTIF}%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Risk Events</span>
                  <span className="text-2xl font-bold">{supplier.riskEvents.length}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle>Performance History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supplier.performanceHistory.map((record, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded">
                    <div>
                      <p className="font-medium">{record.period}</p>
                      <p className="text-sm text-gray-600">{record.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold">{record.score}%</p>
                      <div className="flex gap-2 text-sm">
                        <span>Quality: {record.qualityScore}%</span>
                        <span>Delivery: {record.deliveryScore}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contracts">
          <Card>
            <CardHeader>
              <CardTitle>Active Contracts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supplier.contracts.map((contract) => (
                  <div key={contract.id} className="p-4 border rounded">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-medium">{contract.title}</h3>
                        <p className="text-sm text-gray-600">{contract.type}</p>
                        <p className="text-sm">Value: ${contract.value.toLocaleString()}</p>
                      </div>
                      <Badge>{contract.status}</Badge>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      <p>Start: {contract.startDate}</p>
                      <p>End: {contract.endDate}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="communications">
          <Card>
            <CardHeader>
              <CardTitle>Recent Communications</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supplier.communications.map((comm) => (
                  <div key={comm.id} className="p-4 border rounded">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-medium">{comm.subject}</h3>
                        <p className="text-sm text-gray-600">{comm.type} - {comm.date}</p>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant={comm.priority === 'High' ? 'destructive' : 'secondary'}>
                          {comm.priority}
                        </Badge>
                        <Badge>{comm.status}</Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { AlertTriangle, Shield, CheckCircle, Clock } from "lucide-react";
import { RiskEvent } from "@/types/supplierRelationship";

interface RiskComplianceTabProps {
  riskEvents: RiskEvent[];
}

export const RiskComplianceTab: React.FC<RiskComplianceTabProps> = ({ riskEvents }) => {
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "High":
        return <Badge className="bg-red-100 text-red-800">High</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{severity}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Resolved":
        return <Badge className="bg-green-100 text-green-800">Resolved</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Open":
        return <Badge className="bg-orange-100 text-orange-800">Open</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const complianceMetrics = [
    { area: "ISO 9001", score: 95, status: "Compliant" },
    { area: "ISO 14001", score: 88, status: "Compliant" },
    { area: "OHSAS 18001", score: 92, status: "Compliant" },
    { area: "IATF 16949", score: 85, status: "Minor Issues" }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Open Risks</p>
                <p className="text-2xl font-bold text-red-600">
                  {riskEvents.filter(r => r.status !== 'Resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Compliance Score</p>
                <p className="text-2xl font-bold text-green-600">90%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Resolved Risks</p>
                <p className="text-2xl font-bold">
                  {riskEvents.filter(r => r.status === 'Resolved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Resolution Time</p>
                <p className="text-2xl font-bold">12 days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Risk Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {riskEvents.length > 0 ? (
                riskEvents.map((risk) => (
                  <div key={risk.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{risk.type} Risk</h3>
                          {getSeverityBadge(risk.severity)}
                        </div>
                        <p className="text-sm text-gray-600">{risk.description}</p>
                        <p className="text-xs text-gray-500">Date: {risk.date}</p>
                        <p className="text-sm font-medium">Mitigation: {risk.mitigationPlan}</p>
                      </div>
                      <div className="text-right">
                        {getStatusBadge(risk.status)}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <p className="text-gray-600">No active risk events</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Compliance Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {complianceMetrics.map((metric, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{metric.area}</span>
                    <Badge variant={metric.status === 'Compliant' ? 'default' : 'secondary'}>
                      {metric.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={metric.score} className="flex-1 h-2" />
                    <span className="text-sm font-medium">{metric.score}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Risk Mitigation Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Quality Control Enhancement</p>
                <p className="text-sm text-gray-600">Implement additional quality checks</p>
              </div>
              <Button size="sm" variant="outline">View Details</Button>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Supplier Audit Schedule</p>
                <p className="text-sm text-gray-600">Quarterly compliance audits</p>
              </div>
              <Button size="sm" variant="outline">View Details</Button>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <p className="font-medium">Training Program</p>
                <p className="text-sm text-gray-600">Staff compliance training</p>
              </div>
              <Button size="sm" variant="outline">View Details</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

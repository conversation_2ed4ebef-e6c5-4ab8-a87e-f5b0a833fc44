
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { Calendar, DollarSign, FileText, AlertTriangle, TrendingUp } from "lucide-react";
import { mockContractData } from "@/data/mockSRMData";

export const EnhancedContractsTab: React.FC = () => {
  const contractData = [
    { month: "Jan", value: 2100000, count: 3 },
    { month: "Feb", value: 2300000, count: 4 },
    { month: "Mar", value: 2500000, count: 3 },
    { month: "Apr", value: 2200000, count: 3 },
    { month: "May", value: 2400000, count: 4 },
    { month: "Jun", value: 2600000, count: 3 }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Active Contracts</p>
                <p className="text-2xl font-bold">{mockContractData.activeContracts.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Value</p>
                <p className="text-2xl font-bold">${(mockContractData.contractValue.total / 1000000).toFixed(1)}M</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Expiring Soon</p>
                <p className="text-2xl font-bold">{mockContractData.upcomingRenewals.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Performance</p>
                <p className="text-2xl font-bold">89%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Contract Value Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={contractData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${(value as number / 1000000).toFixed(1)}M`, 'Contract Value']} />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Renewals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockContractData.upcomingRenewals.map((renewal, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{renewal.supplier}</p>
                    <p className="text-sm text-gray-600">{renewal.renewalDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">${(renewal.value / 1000000).toFixed(1)}M</p>
                    <Badge variant="outline">Renewal Due</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Contracts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Supplier</th>
                  <th className="text-left p-2">Contract Title</th>
                  <th className="text-left p-2">Value</th>
                  <th className="text-left p-2">End Date</th>
                  <th className="text-left p-2">Performance</th>
                  <th className="text-left p-2">Status</th>
                </tr>
              </thead>
              <tbody>
                {mockContractData.activeContracts.map((contract) => (
                  <tr key={contract.id} className="border-b">
                    <td className="p-2 font-medium">{contract.supplier}</td>
                    <td className="p-2">{contract.title}</td>
                    <td className="p-2">${(contract.value / 1000000).toFixed(1)}M</td>
                    <td className="p-2">{contract.endDate}</td>
                    <td className="p-2">
                      <div className="flex items-center gap-2">
                        <Progress value={contract.performance} className="w-16 h-2" />
                        <span className="text-sm">{contract.performance}%</span>
                      </div>
                    </td>
                    <td className="p-2">
                      <Badge variant={contract.status === 'Active' ? 'default' : 'secondary'}>
                        {contract.status}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

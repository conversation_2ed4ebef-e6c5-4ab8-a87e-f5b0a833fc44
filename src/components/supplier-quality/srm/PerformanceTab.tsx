
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON>ontainer, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { TrendingUp, TrendingDown, Minus, Target } from "lucide-react";
import { PerformanceRecord } from "@/types/supplierRelationship";
import { mockPerformanceAnalytics } from "@/data/mockSRMData";

interface PerformanceTabProps {
  performanceHistory: PerformanceRecord[];
}

export const PerformanceTab: React.FC<PerformanceTabProps> = ({ performanceHistory }) => {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down": return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  // Extended KPI metrics to include OTIF
  const extendedKpiMetrics = [
    ...mockPerformanceAnalytics.kpiMetrics,
    { name: "OTIF Performance", current: 89.2, target: 95, trend: "up" }
  ];

  return (
    <div className="space-y-6">
      {/* KPI Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {extendedKpiMetrics.map((kpi, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{kpi.name}</p>
                  <p className="text-2xl font-bold">{kpi.current}%</p>
                  <p className="text-xs text-gray-500">Target: {kpi.target}%</p>
                </div>
                <div className="flex flex-col items-center">
                  {getTrendIcon(kpi.trend)}
                  <Badge variant={kpi.current >= kpi.target ? "default" : "destructive"} className="mt-1">
                    {kpi.current >= kpi.target ? "On Track" : "Below Target"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={mockPerformanceAnalytics.trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[75, 100]} />
                <Tooltip />
                <Line type="monotone" dataKey="performance" stroke="#3b82f6" strokeWidth={3} />
                <Line type="monotone" dataKey="quality" stroke="#10b981" strokeWidth={2} />
                <Line type="monotone" dataKey="delivery" stroke="#f59e0b" strokeWidth={2} />
                <Line type="monotone" dataKey="cost" stroke="#8b5cf6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Benchmark Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Category Benchmarks</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={mockPerformanceAnalytics.benchmarkData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis domain={[70, 100]} />
                <Tooltip />
                <Bar dataKey="avgScore" fill="#3b82f6" />
                <Bar dataKey="topPerformer" fill="#10b981" />
                <Bar dataKey="industryAvg" fill="#6b7280" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Historical Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Historical Performance Scores</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Period</th>
                  <th className="text-left p-2">Overall Score</th>
                  <th className="text-left p-2">Quality</th>
                  <th className="text-left p-2">Delivery</th>
                  <th className="text-left p-2">Cost</th>
                  <th className="text-left p-2">OTIF</th>
                  <th className="text-left p-2">Status</th>
                </tr>
              </thead>
              <tbody>
                {performanceHistory.map((record, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2 font-medium">{record.period}</td>
                    <td className="p-2">
                      <Badge variant={record.score >= 90 ? "default" : record.score >= 80 ? "secondary" : "destructive"}>
                        {record.score}%
                      </Badge>
                    </td>
                    <td className="p-2">{record.qualityScore}%</td>
                    <td className="p-2">{record.deliveryScore}%</td>
                    <td className="p-2">{record.costScore}%</td>
                    <td className="p-2">
                      <Badge variant={record.otifScore && record.otifScore >= 90 ? "default" : "secondary"}>
                        {record.otifScore || "N/A"}%
                      </Badge>
                    </td>
                    <td className="p-2">
                      <Badge variant={record.score >= 85 ? "default" : "secondary"}>
                        {record.score >= 85 ? "Excellent" : "Good"}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

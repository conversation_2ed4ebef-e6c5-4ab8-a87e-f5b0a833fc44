
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { SupplierRelationship } from "@/types/supplierRelationship";
import { MessageSquare, Mail, Calendar, ArrowLeft } from "lucide-react";

interface SupplierCommunicationViewProps {
  supplier: SupplierRelationship;
  onBack: () => void;
}

export const SupplierCommunicationView: React.FC<SupplierCommunicationViewProps> = ({ supplier, onBack }) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Suppliers
        </Button>
        <div>
          <h2 className="text-2xl font-bold">{supplier.name}</h2>
          <p className="text-gray-600">Communication History</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Recent Communications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {supplier.communications.length > 0 ? (
              supplier.communications.map((comm) => (
                <div key={comm.id} className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium">{comm.subject}</h3>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {comm.date}
                        </div>
                        <div className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          {comm.type}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant={comm.priority === 'High' ? 'destructive' : 'secondary'}>
                        {comm.priority}
                      </Badge>
                      <Badge variant={comm.status === 'completed' ? 'default' : 'outline'}>
                        {comm.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No communications found for this supplier.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

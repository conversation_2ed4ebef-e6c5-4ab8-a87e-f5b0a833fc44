
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Settings, Save, RefreshCw } from "lucide-react";

export const SupplierQualitySettings: React.FC = () => {
  const [settings, setSettings] = useState({
    autoAuditReminders: true,
    emailNotifications: true,
    riskAssessmentFrequency: "quarterly",
    defaultAuditType: "remote",
    capaTimeoutDays: 30,
    certificateExpiryWarningDays: 60,
    customEmailTemplate: "Dear supplier,\n\nThis is a reminder about your upcoming audit.\n\nBest regards,\nQuality Team"
  });

  const handleSave = () => {
    console.log("Saving settings:", settings);
    // Implement save logic
  };

  const handleReset = () => {
    // Reset to default values
    setSettings({
      autoAuditReminders: true,
      emailNotifications: true,
      riskAssessmentFrequency: "quarterly",
      defaultAuditType: "remote",
      capaTimeoutDays: 30,
      certificateExpiryWarningDays: 60,
      customEmailTemplate: "Dear supplier,\n\nThis is a reminder about your upcoming audit.\n\nBest regards,\nQuality Team"
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Supplier Quality Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Notification Settings */}
          <div className="space-y-4">
            <h3 className="font-semibold">Notification Settings</h3>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-reminders">Automatic Audit Reminders</Label>
                <p className="text-sm text-gray-600">Send automatic reminders for upcoming audits</p>
              </div>
              <Switch
                id="auto-reminders"
                checked={settings.autoAuditReminders}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, autoAuditReminders: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-gray-600">Receive email notifications for important events</p>
              </div>
              <Switch
                id="email-notifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => 
                  setSettings(prev => ({ ...prev, emailNotifications: checked }))
                }
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="capa-timeout">CAPA Timeout (Days)</Label>
                <Input
                  id="capa-timeout"
                  type="number"
                  value={settings.capaTimeoutDays}
                  onChange={(e) => 
                    setSettings(prev => ({ ...prev, capaTimeoutDays: parseInt(e.target.value) }))
                  }
                />
              </div>
              
              <div>
                <Label htmlFor="cert-warning">Certificate Expiry Warning (Days)</Label>
                <Input
                  id="cert-warning"
                  type="number"
                  value={settings.certificateExpiryWarningDays}
                  onChange={(e) => 
                    setSettings(prev => ({ ...prev, certificateExpiryWarningDays: parseInt(e.target.value) }))
                  }
                />
              </div>
            </div>
          </div>

          {/* Audit Settings */}
          <div className="space-y-4">
            <h3 className="font-semibold">Audit Settings</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="risk-frequency">Risk Assessment Frequency</Label>
                <Select 
                  value={settings.riskAssessmentFrequency}
                  onValueChange={(value) => 
                    setSettings(prev => ({ ...prev, riskAssessmentFrequency: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="biannual">Bi-Annual</SelectItem>
                    <SelectItem value="annual">Annual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="default-audit">Default Audit Type</Label>
                <Select 
                  value={settings.defaultAuditType}
                  onValueChange={(value) => 
                    setSettings(prev => ({ ...prev, defaultAuditType: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="onsite">On-site</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Email Template */}
          <div className="space-y-4">
            <h3 className="font-semibold">Email Template</h3>
            <div>
              <Label htmlFor="email-template">Custom Email Template</Label>
              <Textarea
                id="email-template"
                rows={6}
                value={settings.customEmailTemplate}
                onChange={(e) => 
                  setSettings(prev => ({ ...prev, customEmailTemplate: e.target.value }))
                }
                placeholder="Enter custom email template..."
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4">
            <Button onClick={handleSave} className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Save Settings
            </Button>
            <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { mockDocumentVersions } from "@/data/mockSupplierDocuments";
import { FileText, Download, History } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface DocumentVersionsProps {
  documentId: string;
}

export const DocumentVersions: React.FC<DocumentVersionsProps> = ({ documentId }) => {
  const versions = mockDocumentVersions.filter(
    (version) => version.documentId === documentId
  ).sort((a, b) => b.version - a.version);

  if (versions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Version History</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-sm">No version history available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between py-4">
        <CardTitle className="text-lg flex items-center">
          <History className="mr-2 h-5 w-5 text-gray-500" />
          Version History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {versions.map((version) => (
            <div key={version.id} className="border rounded-md p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <h4 className="font-medium">Version {version.version}</h4>
                    <p className="text-sm text-gray-500">
                      Uploaded on {new Date(version.uploadDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
              <div className="text-sm">
                <p><span className="font-medium">Uploaded by:</span> {version.uploadedBy}</p>
                {version.notes && (
                  <p className="mt-1"><span className="font-medium">Notes:</span> {version.notes}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

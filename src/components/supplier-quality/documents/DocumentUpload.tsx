
import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mockReviewers } from "@/data/mockSupplierDocuments";
import { Upload } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useFormValidation, commonValidationPatterns } from "@/hooks/useFormValidation";
import { useToast } from "@/hooks/use-toast";
import { z } from "zod";

const documentUploadSchema = z.object({
  documentName: commonValidationPatterns.requiredString("Document Name"),
  expiryDate: commonValidationPatterns.requiredDate,
  reviewer: commonValidationPatterns.requiredString("Reviewer"),
  notes: commonValidationPatterns.optionalString
});

type DocumentUploadFormData = z.infer<typeof documentUploadSchema>;

export const DocumentUpload: React.FC = () => {
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const { toast } = useToast();

  const form = useFormValidation({
    schema: documentUploadSchema,
    defaultValues: {
      documentName: "",
      expiryDate: "",
      reviewer: "",
      notes: ""
    }
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleSubmit = (data: DocumentUploadFormData) => {
    if (!selectedFile) {
      toast({
        title: "Missing file",
        description: "Please select a file to upload",
        variant: "destructive",
      });
      return;
    }
    
    console.log("Uploading document:", data, selectedFile);
    toast({
      title: "Document uploaded",
      description: `${data.documentName} has been uploaded successfully`,
    });
    
    form.reset();
    setSelectedFile(null);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Upload New Document</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="documentName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Document Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. ISO 9001 Certificate" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="expiryDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="space-y-2">
              <FormLabel htmlFor="documentFile">Upload File</FormLabel>
              <div className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center bg-gray-50">
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 mb-2">
                  {selectedFile ? selectedFile.name : "Drag & drop or click to upload"}
                </p>
                <Input 
                  id="documentFile" 
                  type="file" 
                  className="hidden" 
                  onChange={handleFileChange}
                />
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm"
                  onClick={() => document.getElementById("documentFile")?.click()}
                >
                  Select File
                </Button>
              </div>
            </div>
            
            <FormField
              control={form.control}
              name="reviewer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Assign Reviewer</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select reviewer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {mockReviewers.map((reviewer) => (
                        <SelectItem key={reviewer.id} value={reviewer.id}>
                          {reviewer.name} - {reviewer.role}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Add any relevant notes about this document"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <Button type="submit" className="w-full">Upload Document</Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

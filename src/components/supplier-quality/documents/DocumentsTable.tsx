
import React, { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DocumentStatusBadge } from "./DocumentStatusBadge";
import { SupplierDocument } from "@/types/supplierDocuments";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DocumentVersions } from "./DocumentVersions";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mockReviewers } from "@/data/mockSupplierDocuments";
import { FileText, Download, History, UserPlus } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface DocumentsTableProps {
  documents: SupplierDocument[];
}

export const DocumentsTable: React.FC<DocumentsTableProps> = ({ documents }) => {
  const [selectedDocument, setSelectedDocument] = useState<SupplierDocument | null>(null);
  const isMobile = useIsMobile();

  if (documents.length === 0) {
    return (
      <div className="text-center p-8 border border-dashed rounded-md bg-gray-50">
        <p className="text-gray-500">No documents found</p>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="space-y-4">
        {documents.map((doc) => (
          <div key={doc.id} className="border rounded-md p-4 bg-white">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">{doc.name}</h3>
              <DocumentStatusBadge status={doc.status} />
            </div>
            <div className="text-sm space-y-2">
              <p className="text-gray-600">
                <span className="font-medium">File:</span> {doc.fileName}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Supplier:</span> {doc.supplierName}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Expiry:</span> {new Date(doc.expiryDate).toLocaleDateString()}
              </p>
              <p className="text-gray-600">
                <span className="font-medium">Reviewer:</span> {doc.reviewer || "None"}
              </p>
            </div>
            <div className="mt-4 flex flex-wrap gap-2">
              <Button variant="outline" size="sm" className="flex-1" onClick={() => setSelectedDocument(doc)}>
                <History className="h-4 w-4 mr-1" />
                History
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Document Name</TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Supplier</TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Upload Date</TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Expiry Date</TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Status</TableHead>
            <TableHead className="font-medium text-xs uppercase tracking-wider">Reviewer</TableHead>
            <TableHead className="text-right font-medium text-xs uppercase tracking-wider">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((doc) => (
            <TableRow key={doc.id}>
              <TableCell className="text-sm">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-gray-500" />
                  <div>
                    <div className="font-medium">{doc.name}</div>
                    <div className="text-sm text-gray-500">{doc.fileName}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-sm">{doc.supplierName}</TableCell>
              <TableCell className="text-sm">{new Date(doc.uploadDate).toLocaleDateString()}</TableCell>
              <TableCell className="text-sm">{new Date(doc.expiryDate).toLocaleDateString()}</TableCell>
              <TableCell className="text-sm">
                <DocumentStatusBadge status={doc.status} />
              </TableCell>
              <TableCell className="text-sm">
                <Select defaultValue={doc.reviewer}>
                  <SelectTrigger className="w-full max-w-[200px]">
                    <SelectValue placeholder="Assign reviewer" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockReviewers.map((reviewer) => (
                      <SelectItem key={reviewer.id} value={reviewer.name}>
                        {reviewer.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TableCell>
              <TableCell className="text-right text-sm">
                <div className="flex justify-end gap-2">
                  <Button variant="outline" size="sm" onClick={() => setSelectedDocument(doc)}>
                    <History className="h-4 w-4 mr-1" />
                    History
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Dialog open={!!selectedDocument} onOpenChange={(open) => !open && setSelectedDocument(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Document History - {selectedDocument?.name}</DialogTitle>
          </DialogHeader>
          {selectedDocument && <DocumentVersions documentId={selectedDocument.id} />}
        </DialogContent>
      </Dialog>
    </>
  );
};

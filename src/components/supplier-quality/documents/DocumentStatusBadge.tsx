
import React from "react";
import { DocumentStatus } from "@/types/supplierDocuments";

interface DocumentStatusBadgeProps {
  status: DocumentStatus;
}

export const DocumentStatusBadge: React.FC<DocumentStatusBadgeProps> = ({ status }) => {
  let bgColor = "bg-green-100";
  let textColor = "text-green-800";
  
  if (status === "Expiring Soon") {
    bgColor = "bg-yellow-100";
    textColor = "text-yellow-800";
  } else if (status === "Expired") {
    bgColor = "bg-red-100";
    textColor = "text-red-800";
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
      {status}
    </span>
  );
};

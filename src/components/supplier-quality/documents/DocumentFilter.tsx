
import React from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mockSupplierQualifications } from "@/data/mockSupplierQualifications";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";

interface DocumentFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  supplierFilter: string;
  onSupplierChange: (value: string) => void;
  typeFilter: string;
  onTypeChange: (value: string) => void;
  currentStatus?: string;
  searchQuery?: string;
  onStatusChange?: (value: string) => void;
}

export const DocumentFilter: React.FC<DocumentFilterProps> = ({
  searchTerm,
  onSearchChange,
  supplierFilter,
  onSupplierChange,
  typeFilter,
  onTypeChange,
  currentStatus = "all",
  searchQuery = "",
  onStatusChange = () => {},
}) => {
  const suppliers = mockSupplierQualifications.map(s => s.name);
  const documentTypes = ["ISO Certificate", "Quality Manual", "Compliance Certificate", "Audit Report", "All Types"];
  
  return (
    <div className="bg-white p-4 rounded-md shadow-sm border mb-6 w-full">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
        <div className="space-y-2 w-full">
          <Label htmlFor="search">Search Documents</Label>
          <Input
            id="search"
            placeholder="Search by document name or content..."
            value={searchTerm || searchQuery}
            onChange={(e) => {
              onSearchChange(e.target.value);
            }}
            className="w-full"
          />
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="supplier-filter">Filter by Supplier</Label>
          <Select value={supplierFilter} onValueChange={onSupplierChange}>
            <SelectTrigger id="supplier-filter" className="w-full">
              <SelectValue placeholder="All Suppliers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers.map((supplier) => (
                <SelectItem key={supplier} value={supplier}>
                  {supplier}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="type-filter">Filter by Type</Label>
          <Select value={typeFilter} onValueChange={onTypeChange}>
            <SelectTrigger id="type-filter" className="w-full">
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              {documentTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {(supplierFilter !== "all" || typeFilter !== "all" || searchTerm) && (
        <div className="flex flex-wrap gap-2 mt-4 items-center w-full">
          <span className="text-sm text-gray-500">Active filters:</span>
          {supplierFilter !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Supplier: {supplierFilter}
              <button 
                className="ml-1 text-gray-500 hover:text-gray-700"
                onClick={() => onSupplierChange("all")}
              >
                ×
              </button>
            </Badge>
          )}
          {typeFilter !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Type: {typeFilter}
              <button 
                className="ml-1 text-gray-500 hover:text-gray-700"
                onClick={() => onTypeChange("all")}
              >
                ×
              </button>
            </Badge>
          )}
          {searchTerm && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{searchTerm}"
              <button 
                className="ml-1 text-gray-500 hover:text-gray-700"
                onClick={() => onSearchChange("")}
              >
                ×
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

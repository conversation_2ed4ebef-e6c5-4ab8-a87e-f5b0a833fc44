
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Check<PERSON>ircle, AlertTriangle, XCircle } from "lucide-react";

interface StatusBreakdownProps {
  approvedSuppliers: number;
  pendingApproval: number;
  requiresFollowUp: number;
  disapproved: number;
}

export const StatusBreakdown: React.FC<StatusBreakdownProps> = ({
  approvedSuppliers,
  pendingApproval,
  requiresFollowUp,
  disapproved
}) => {
  const compliantPercentage = Math.round((approvedSuppliers / (approvedSuppliers + pendingApproval + requiresFollowUp)) * 100);
  const atRiskPercentage = Math.round((pendingApproval / (approvedSuppliers + pendingApproval + requiresFollowUp)) * 100);
  const unqualifiedPercentage = Math.round((requiresFollowUp / (approvedSuppliers + pendingApproval + requiresFollowUp)) * 100);
  
  return (
    <>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Supplier Status Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="bg-green-50 border border-green-100 p-3 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="font-medium">Compliant</span>
            </div>
            <div className="flex items-center">
              <span className="text-xl font-bold text-green-700 mr-2">{approvedSuppliers}</span>
              <span className="text-sm text-green-600">{compliantPercentage}%</span>
            </div>
          </div>
          
          <div className="bg-amber-50 border border-amber-100 p-3 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-amber-600 mr-2" />
              <span className="font-medium">At Risk</span>
            </div>
            <div className="flex items-center">
              <span className="text-xl font-bold text-amber-700 mr-2">{pendingApproval}</span>
              <span className="text-sm text-amber-600">{atRiskPercentage}%</span>
            </div>
          </div>
          
          <div className="bg-red-50 border border-red-100 p-3 rounded-lg flex items-center justify-between">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 text-red-600 mr-2" />
              <span className="font-medium">Unqualified</span>
            </div>
            <div className="flex items-center">
              <span className="text-xl font-bold text-red-700 mr-2">{requiresFollowUp}</span>
              <span className="text-sm text-red-600">{unqualifiedPercentage}%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </>
  );
};

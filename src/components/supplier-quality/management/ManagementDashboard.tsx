import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, MessageSquare, BarChart3, Building, Clock, Target } from "lucide-react";
import { SupplierManagement } from "../SupplierManagement";
import { mockSupplierRelationships } from "@/data/mockSupplierRelationships";

export const ManagementDashboard: React.FC = () => {
  // Calculate aggregate OTIF metrics
  const avgOTIF = mockSupplierRelationships.reduce((acc, supplier) => acc + supplier.otifMetrics.currentOTIF, 0) / mockSupplierRelationships.length;
  const suppliersOnTarget = mockSupplierRelationships.filter(supplier => supplier.otifMetrics.currentOTIF >= supplier.otifMetrics.targetOTIF).length;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
              <Users className="h-4 w-4" />
              SRM Dashboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">142</div>
            <p className="text-xs text-blue-600">Active suppliers</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Average OTIF
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">{avgOTIF.toFixed(1)}%</div>
            <p className="text-xs text-green-600">Across all suppliers</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 flex items-center gap-2">
              <Target className="h-4 w-4" />
              On Target
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">{suppliersOnTarget}</div>
            <p className="text-xs text-purple-600">Suppliers meeting OTIF target</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Feedback Center
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">18</div>
            <p className="text-xs text-orange-600">Pending feedback</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-teal-700 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Supplier Scorecard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-teal-800">87%</div>
            <p className="text-xs text-teal-600">Average score</p>
          </CardContent>
        </Card>
      </div>

      <SupplierManagement />
    </div>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface InitiateRecallDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: RecallFormData) => void;
}

export interface RecallFormData {
  partNumber: string;
  description: string;
  severity: string;
  supplier: string;
}

export const InitiateRecallDialog: React.FC<InitiateRecallDialogProps> = ({ open, onOpenChange, onSubmit }) => {
  const [form, setForm] = useState<RecallFormData>({
    partNumber: "",
    description: "",
    severity: "Medium",
    supplier: ""
  });

  const [submitting, setSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({...form, [e.target.name]: e.target.value});
  };

  const handleSeverityChange = (value: string) => {
    setForm({...form, severity: value});
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setTimeout(() => {
      onSubmit(form);
      setSubmitting(false);
      setForm({
        partNumber: "",
        description: "",
        severity: "Medium",
        supplier: "",
      });
      onOpenChange(false);
    }, 500);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Initiate Product Recall</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="partNumber">Part Number</Label>
            <Input id="partNumber" name="partNumber" value={form.partNumber} onChange={handleChange} required />
          </div>
          <div>
            <Label htmlFor="supplier">Supplier</Label>
            <Input id="supplier" name="supplier" value={form.supplier} onChange={handleChange} required />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Input id="description" name="description" value={form.description} onChange={handleChange} required />
          </div>
          <div>
            <Label htmlFor="severity">Severity</Label>
            <Select value={form.severity} onValueChange={handleSeverityChange}>
              <SelectTrigger id="severity">
                <SelectValue placeholder="Select severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Critical">Critical</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter className="flex flex-row gap-2 mt-4">
            <Button variant="outline" type="button" onClick={() => onOpenChange(false)} disabled={submitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={submitting || !form.partNumber || !form.supplier || !form.description}>
              {submitting ? "Submitting..." : "Initiate Recall"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

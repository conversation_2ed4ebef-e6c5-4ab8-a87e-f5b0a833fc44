
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { CheckCircle, AlertTriangle, FileText } from "lucide-react";
import { PPAPFormData } from "../PPAPWizard";
import { DocumentUpload } from "@/components/document-hub/document-form/DocumentUpload";

interface UploadElementsStepProps {
  formData: PPAPFormData;
  updateFormData: (data: Partial<PPAPFormData>) => void;
}

const requiredElements = [
  { key: "designRecords", name: "Design Records", format: "PDF", required: true },
  { key: "processFlow", name: "Process Flow Diagram", format: "SVG", required: true },
  { key: "pfmea", name: "PFMEA", format: "XLSX", required: true },
  { key: "controlPlan", name: "Control Plan", format: "XLSX", required: true },
  { key: "dimensionalReport", name: "Dimensional Report", format: "CSV", required: true },
  { key: "materialCerts", name: "Material Certifications", format: "PDF", required: true },
  { key: "aar", name: "AAR", format: "PDF", required: false },
  { key: "psw", name: "PSW", format: "DOCX", required: true }
];

export const UploadElementsStep: React.FC<UploadElementsStepProps> = ({ formData, updateFormData }) => {
  const handleFileUpload = (key: string, file: File | null) => {
    updateFormData({
      uploads: {
        ...formData.uploads,
        [key]: file
      }
    });
  };

  const getUploadStatus = (key: string) => {
    const file = formData.uploads[key];
    const element = requiredElements.find(el => el.key === key);
    
    if (file) {
      return { status: "uploaded", icon: CheckCircle, color: "text-green-600" };
    } else if (element?.required) {
      return { status: "missing", icon: AlertTriangle, color: "text-red-600" };
    } else {
      return { status: "optional", icon: FileText, color: "text-slate-400" };
    }
  };

  const getAcceptedTypes = (format: string) => {
    switch (format.toLowerCase()) {
      case 'pdf':
        return '.pdf';
      case 'xlsx':
        return '.xlsx,.xls';
      case 'docx':
        return '.docx,.doc';
      case 'csv':
        return '.csv';
      case 'svg':
        return '.svg';
      default:
        return '*';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium text-slate-900 mb-2">Upload Required PPAP Elements</h3>
        <p className="text-slate-600">Upload all required documents to proceed with your PPAP submission</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {requiredElements.map((element) => {
          const uploadStatus = getUploadStatus(element.key);
          const StatusIcon = uploadStatus.icon;
          const acceptedTypes = getAcceptedTypes(element.format);
          
          return (
            <Card key={element.key} className="border border-slate-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <StatusIcon className={`h-5 w-5 ${uploadStatus.color}`} />
                    <span className="font-medium text-slate-900">{element.name}</span>
                    {element.required && (
                      <span className="text-xs text-red-500">*Required</span>
                    )}
                  </div>
                  <span className="text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded">
                    {element.format}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`upload-${element.key}`} className="text-sm font-medium">
                    Upload {element.name}
                  </Label>
                  <DocumentUpload
                    value={formData.uploads[element.key]}
                    onChange={(file) => handleFileUpload(element.key, file)}
                  />
                  <p className="text-xs text-slate-500">
                    Accepted formats: {element.format} (max 10MB)
                  </p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Upload Summary */}
      <Card className="bg-slate-50 border-slate-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-slate-900 mb-2">Upload Summary</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Required files uploaded:</span>
              <span className="font-medium">
                {Object.entries(formData.uploads).filter(([key, file]) => 
                  file && requiredElements.find(el => el.key === key)?.required
                ).length} / {requiredElements.filter(el => el.required).length}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Optional files uploaded:</span>
              <span className="font-medium">
                {Object.entries(formData.uploads).filter(([key, file]) => 
                  file && !requiredElements.find(el => el.key === key)?.required
                ).length} / {requiredElements.filter(el => !el.required).length}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

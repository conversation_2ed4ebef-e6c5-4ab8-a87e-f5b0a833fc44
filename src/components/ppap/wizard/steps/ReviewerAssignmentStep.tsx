
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Calendar } from "lucide-react";
import { PPAPFormData } from "../PPAPWizard";

interface ReviewerAssignmentStepProps {
  formData: PPAPFormData;
  updateFormData: (data: Partial<PPAPFormData>) => void;
}

export const ReviewerAssignmentStep: React.FC<ReviewerAssignmentStepProps> = ({ formData, updateFormData }) => {
  const qaTeamMembers = [
    "<PERSON><PERSON> Nair",
    "<PERSON>", 
    "<PERSON>",
    "<PERSON>"
  ];

  const mfgEngineers = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>"
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium text-slate-900 mb-2">Assign Reviewers</h3>
        <p className="text-slate-600">Assign team members to review this PPAP package</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Internal QA Assignment */}
        <Card className="border border-slate-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
              <User className="h-4 w-4 mr-2 text-blue-600" />
              Quality Assurance Lead *
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="qaAssignee">Assignee</Label>
              <Select value={formData.qaAssignee} onValueChange={(value) => updateFormData({ qaAssignee: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select QA lead" />
                </SelectTrigger>
                <SelectContent>
                  {qaTeamMembers.map((member) => (
                    <SelectItem key={member} value={member}>
                      {member}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Manufacturing Engineer Assignment */}
        <Card className="border border-slate-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
              <User className="h-4 w-4 mr-2 text-green-600" />
              Manufacturing Engineer *
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mfgEngineer">Assignee</Label>
              <Select value={formData.mfgEngineer} onValueChange={(value) => updateFormData({ mfgEngineer: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select engineer" />
                </SelectTrigger>
                <SelectContent>
                  {mfgEngineers.map((member) => (
                    <SelectItem key={member} value={member}>
                      {member}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Customer Representative */}
      <Card className="border border-slate-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
            <User className="h-4 w-4 mr-2 text-purple-600" />
            Customer Representative (Optional)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customerRep">Email Address</Label>
            <Input 
              id="customerRep" 
              type="email"
              placeholder="<EMAIL>"
              value={formData.customerRep}
              onChange={(e) => updateFormData({ customerRep: e.target.value })}
            />
            <p className="text-xs text-slate-500">
              Customer representative will receive notifications and can track progress
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Due Date and Comments */}
      <Card className="border border-slate-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
            <Calendar className="h-4 w-4 mr-2 text-amber-600" />
            Review Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="dueDate">Target Review Date</Label>
            <Input 
              id="dueDate" 
              type="date"
              value={formData.dueDate}
              onChange={(e) => updateFormData({ dueDate: e.target.value })}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="comments">Review Instructions/Comments</Label>
            <Textarea 
              id="comments" 
              placeholder="Any specific instructions for the reviewers..."
              className="min-h-[80px]"
              value={formData.comments}
              onChange={(e) => updateFormData({ comments: e.target.value })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

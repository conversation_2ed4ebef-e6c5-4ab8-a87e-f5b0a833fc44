
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { PPAPFormData } from "../PPAPWizard";

interface BasicInfoStepProps {
  formData: PPAPFormData;
  updateFormData: (data: Partial<PPAPFormData>) => void;
}

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ formData, updateFormData }) => {
  const customers = [
    "Flexonics Ltd.",
    "TechCorp Industries", 
    "Advanced Electronics",
    "Precision Manufacturing"
  ];

  const submissionReasons = [
    "Engineering Change (new PCB layout)",
    "New Part Introduction",
    "Material Change",
    "Process Change",
    "Supplier Change",
    "Customer Request"
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="customer">Customer *</Label>
          <Select value={formData.customer} onValueChange={(value) => updateFormData({ customer: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select customer" />
            </SelectTrigger>
            <SelectContent>
              {customers.map((customer) => (
                <SelectItem key={customer} value={customer}>
                  {customer}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="partNumber">Part Number *</Label>
          <Input 
            id="partNumber" 
            placeholder="e.g., X200-PCBA"
            value={formData.partNumber}
            onChange={(e) => updateFormData({ partNumber: e.target.value })}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="partName">Part Name/Description</Label>
        <Input 
          id="partName" 
          placeholder="e.g., PCB Assembly - Model X200"
          value={formData.partName}
          onChange={(e) => updateFormData({ partName: e.target.value })}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="revision">Revision *</Label>
          <Input 
            id="revision" 
            placeholder="e.g., Rev B"
            value={formData.revision}
            onChange={(e) => updateFormData({ revision: e.target.value })}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="submissionReason">Submission Reason</Label>
          <Select value={formData.submissionReason} onValueChange={(value) => updateFormData({ submissionReason: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select reason" />
            </SelectTrigger>
            <SelectContent>
              {submissionReasons.map((reason) => (
                <SelectItem key={reason} value={reason}>
                  {reason}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertTriangle, User, FileText, Calendar, Upload } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PPAPFormData } from "../PPAPWizard";

interface FinalReviewStepProps {
  formData: PPAPFormData;
  updateFormData: (data: Partial<PPAPFormData>) => void;
}

export const FinalReviewStep: React.FC<FinalReviewStepProps> = ({ formData, updateFormData }) => {
  const requiredElements = [
    { key: "designRecords", name: "Design Records", required: true },
    { key: "processFlow", name: "Process Flow Diagram", required: true },
    { key: "pfmea", name: "PFMEA", required: true },
    { key: "controlPlan", name: "Control Plan", required: true },
    { key: "dimensionalReport", name: "Dimensional Report", required: true },
    { key: "materialCerts", name: "Material Certifications", required: true },
    { key: "aar", name: "AAR", required: false },
    { key: "psw", name: "PSW", required: true }
  ];

  const uploadedFiles = Object.entries(formData.uploads).filter(([key, file]) => file !== null);
  const requiredFiles = requiredElements.filter(el => el.required);
  const uploadedRequiredFiles = uploadedFiles.filter(([key]) => 
    requiredElements.find(el => el.key === key)?.required
  );

  const allRequiredUploaded = uploadedRequiredFiles.length === requiredFiles.length;

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium text-slate-900 mb-2">Final Review</h3>
        <p className="text-slate-600">Review all information before submitting your PPAP package</p>
      </div>

      {/* Basic Information */}
      <Card className="border border-slate-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
            <FileText className="h-4 w-4 mr-2 text-blue-600" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-600">Customer:</span>
              <p className="font-medium">{formData.customer || "Not specified"}</p>
            </div>
            <div>
              <span className="text-slate-600">Part Number:</span>
              <p className="font-medium">{formData.partNumber || "Not specified"}</p>
            </div>
            <div>
              <span className="text-slate-600">Part Name:</span>
              <p className="font-medium">{formData.partName || "Not specified"}</p>
            </div>
            <div>
              <span className="text-slate-600">Revision:</span>
              <p className="font-medium">{formData.revision || "Not specified"}</p>
            </div>
          </div>
          {formData.submissionReason && (
            <div>
              <span className="text-slate-600 text-sm">Submission Reason:</span>
              <p className="font-medium">{formData.submissionReason}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Status */}
      <Card className="border border-slate-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
            <Upload className="h-4 w-4 mr-2 text-green-600" />
            Document Upload Status
            {allRequiredUploaded ? (
              <CheckCircle className="h-4 w-4 ml-2 text-green-600" />
            ) : (
              <AlertTriangle className="h-4 w-4 ml-2 text-red-600" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-2">
            {requiredElements.map((element) => {
              const isUploaded = formData.uploads[element.key] !== null;
              return (
                <div key={element.key} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                  <span className="text-sm">{element.name}</span>
                  <div className="flex items-center space-x-2">
                    {element.required && (
                      <Badge variant="outline" className="text-xs">Required</Badge>
                    )}
                    {isUploaded ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertTriangle className={`h-4 w-4 ${element.required ? 'text-red-600' : 'text-slate-400'}`} />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="bg-blue-50 p-3 rounded border border-blue-200">
            <p className="text-sm text-blue-800">
              <strong>Upload Summary:</strong> {uploadedRequiredFiles.length} of {requiredFiles.length} required files uploaded
              {uploadedFiles.length - uploadedRequiredFiles.length > 0 && 
                `, plus ${uploadedFiles.length - uploadedRequiredFiles.length} optional file(s)`
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Reviewer Assignment */}
      <Card className="border border-slate-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-slate-900 flex items-center">
            <User className="h-4 w-4 mr-2 text-purple-600" />
            Review Assignment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3 bg-slate-50 rounded">
              <span className="text-sm text-slate-600">QA Lead:</span>
              <p className="font-medium">{formData.qaAssignee || "Not assigned"}</p>
            </div>
            <div className="p-3 bg-slate-50 rounded">
              <span className="text-sm text-slate-600">Manufacturing Engineer:</span>
              <p className="font-medium">{formData.mfgEngineer || "Not assigned"}</p>
            </div>
          </div>
          
          {formData.customerRep && (
            <div className="p-3 bg-slate-50 rounded">
              <span className="text-sm text-slate-600">Customer Representative:</span>
              <p className="font-medium">{formData.customerRep}</p>
            </div>
          )}
          
          {formData.dueDate && (
            <div className="p-3 bg-slate-50 rounded flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-slate-600" />
              <div>
                <span className="text-sm text-slate-600">Target Review Date:</span>
                <p className="font-medium">{formData.dueDate}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Final Actions */}
      <Card className="border border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-green-900">Ready to Submit</h4>
              <p className="text-sm text-green-700">
                {allRequiredUploaded 
                  ? "All required documents have been uploaded. Click submit to create your PPAP package."
                  : "Please upload all required documents before submitting."
                }
              </p>
            </div>
            {allRequiredUploaded && (
              <CheckCircle className="h-8 w-8 text-green-600" />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { BasicInfoStep } from "./steps/BasicInfoStep";
import { UploadElementsStep } from "./steps/UploadElementsStep";
import { ReviewerAssignmentStep } from "./steps/ReviewerAssignmentStep";
import { FinalReviewStep } from "./steps/FinalReviewStep";

interface PPAPWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface PPAPFormData {
  customer: string;
  partNumber: string;
  partName: string;
  revision: string;
  submissionReason: string;
  uploads: Record<string, File | null>;
  qaAssignee: string;
  mfgEngineer: string;
  customerRep: string;
  dueDate: string;
  comments: string;
}

const steps = [
  { id: 1, title: "Basic Info", description: "Part and customer details" },
  { id: 2, title: "Upload Elements", description: "Required PPAP documents" },
  { id: 3, title: "Reviewer Assignment", description: "Assign team members" },
  { id: 4, title: "Final Review", description: "Review and submit" }
];

export const PPAPWizard: React.FC<PPAPWizardProps> = ({ open, onOpenChange }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<PPAPFormData>({
    customer: "",
    partNumber: "",
    partName: "",
    revision: "",
    submissionReason: "",
    uploads: {
      designRecords: null,
      processFlow: null,
      pfmea: null,
      controlPlan: null,
      dimensionalReport: null,
      materialCerts: null,
      aar: null,
      psw: null
    },
    qaAssignee: "",
    mfgEngineer: "",
    customerRep: "",
    dueDate: "",
    comments: ""
  });

  const updateFormData = (data: Partial<PPAPFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    console.log("Submitting PPAP package:", formData);
    onOpenChange(false);
    setCurrentStep(1);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep formData={formData} updateFormData={updateFormData} />;
      case 2:
        return <UploadElementsStep formData={formData} updateFormData={updateFormData} />;
      case 3:
        return <ReviewerAssignmentStep formData={formData} updateFormData={updateFormData} />;
      case 4:
        return <FinalReviewStep formData={formData} updateFormData={updateFormData} />;
      default:
        return null;
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.customer && formData.partNumber && formData.revision;
      case 2:
        return Object.values(formData.uploads).some(file => file !== null);
      case 3:
        return formData.qaAssignee && formData.mfgEngineer;
      case 4:
        return true;
      default:
        return false;
    }
  };

  const progress = (currentStep / steps.length) * 100;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden bg-white border-0 shadow-xl">
        <DialogHeader className="px-8 py-6 border-b border-gray-200 bg-gray-50">
          <DialogTitle className="text-xl font-semibold text-slate-900">
            Create PPAP Package
          </DialogTitle>
        </DialogHeader>

        <div className="px-8 py-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.id 
                      ? 'bg-teal-600 text-white' 
                      : 'bg-slate-200 text-slate-600'
                  }`}>
                    {step.id}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-1 mx-2 ${
                      currentStep > step.id ? 'bg-teal-600' : 'bg-slate-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <Progress value={progress} className="h-2" />
            <div className="mt-3">
              <h3 className="font-medium text-slate-900">{steps[currentStep - 1].title}</h3>
              <p className="text-sm text-slate-600">{steps[currentStep - 1].description}</p>
            </div>
          </div>

          {/* Step Content */}
          <div className="min-h-[400px]">
            {renderStepContent()}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center px-8 py-6 border-t border-gray-200 bg-gray-50">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="flex items-center"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>

          <div className="flex space-x-3">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            
            {currentStep < steps.length ? (
              <Button
                onClick={nextStep}
                disabled={!isStepValid()}
                className="bg-teal-600 hover:bg-teal-700 text-white flex items-center"
              >
                Next
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={!isStepValid()}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Submit PPAP Package
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BarChart3, Download, Calendar, TrendingUp, FileText, PieChart } from "lucide-react";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";

export const PPAPReports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("last-30-days");
  const [selectedReport, setSelectedReport] = useState("overview");

  const submissionData = [
    { month: 'Jan', submissions: 15, approved: 12, rejected: 3 },
    { month: 'Feb', submissions: 18, approved: 16, rejected: 2 },
    { month: 'Mar', submissions: 22, approved: 19, rejected: 3 },
    { month: 'Apr', submissions: 19, approved: 17, rejected: 2 },
    { month: 'May', submissions: 25, approved: 22, rejected: 3 },
    { month: 'Jun', submissions: 21, approved: 20, rejected: 1 }
  ];

  const statusData = [
    { name: 'Approved', value: 106, color: '#10B981' },
    { name: 'Under Review', value: 8, color: '#F59E0B' },
    { name: 'Rejected', value: 14, color: '#EF4444' },
    { name: 'Pending', value: 5, color: '#6B7280' }
  ];

  const supplierData = [
    { supplier: 'Acme Manufacturing', submissions: 35, approvalRate: 94 },
    { supplier: 'Quality Parts Inc', submissions: 28, approvalRate: 89 },
    { supplier: 'Precision Components', submissions: 22, approvalRate: 91 },
    { supplier: 'ElectroTech Solutions', submissions: 18, approvalRate: 83 },
    { supplier: 'Industrial Systems', submissions: 15, approvalRate: 87 }
  ];

  const handleExportReport = () => {
    const reportData = {
      period: selectedPeriod,
      reportType: selectedReport,
      generatedAt: new Date().toISOString(),
      summary: {
        totalSubmissions: 133,
        approvedSubmissions: 106,
        rejectedSubmissions: 14,
        approvalRate: '79.7%'
      },
      submissionTrends: submissionData,
      statusDistribution: statusData,
      supplierPerformance: supplierData
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `PPAP_Report_${selectedReport}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Reports</h2>
          <p className="text-sm text-gray-600">Generate and view Production Part Approval Process reports</p>
        </div>
        <Button onClick={handleExportReport} className="gap-2">
          <Download className="h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Report Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Report Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-2">Report Type</label>
              <Select value={selectedReport} onValueChange={setSelectedReport}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview Report</SelectItem>
                  <SelectItem value="supplier">Supplier Performance</SelectItem>
                  <SelectItem value="trends">Submission Trends</SelectItem>
                  <SelectItem value="quality">Quality Metrics</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-2">Time Period</label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-7-days">Last 7 Days</SelectItem>
                  <SelectItem value="last-30-days">Last 30 Days</SelectItem>
                  <SelectItem value="last-90-days">Last 90 Days</SelectItem>
                  <SelectItem value="this-year">This Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <FileText className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">133</div>
            <div className="text-sm text-gray-600">Total Submissions</div>
            <div className="text-xs text-green-600 mt-1">+12% vs last period</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">79.7%</div>
            <div className="text-sm text-gray-600">Approval Rate</div>
            <div className="text-xs text-green-600 mt-1">+2.3% vs last period</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-600">5.2</div>
            <div className="text-sm text-gray-600">Avg Review Days</div>
            <div className="text-xs text-red-600 mt-1">+0.5 vs last period</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <PieChart className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">18</div>
            <div className="text-sm text-gray-600">Active Suppliers</div>
            <div className="text-xs text-gray-600 mt-1">No change</div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>PPAP Submissions Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={submissionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="approved" fill="#10B981" name="Approved" />
                <Bar dataKey="rejected" fill="#EF4444" name="Rejected" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Supplier Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Top Supplier Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {supplierData.map((supplier, index) => (
              <div key={supplier.supplier} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{supplier.supplier}</div>
                    <div className="text-sm text-gray-500">{supplier.submissions} submissions</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-green-600">{supplier.approvalRate}%</div>
                  <div className="text-sm text-gray-500">Approval Rate</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

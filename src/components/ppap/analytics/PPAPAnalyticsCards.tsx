
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Users, AlertTriangle, CheckCircle, Clock, Target, BarChart3, FileText } from "lucide-react";
import { useState } from "react";

export const PPAPAnalyticsCards: React.FC = () => {
  const [activeDialog, setActiveDialog] = useState<{open: boolean, type: string, title: string}>({
    open: false,
    type: '',
    title: ''
  });

  const handleNavigateToAnalytics = (type: string, title: string) => {
    console.log(`Opening ${type} analytics`);
    setActiveDialog({ open: true, type, title });
  };

  const renderDialogContent = () => {
    switch (activeDialog.type) {
      case 'Performance':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">94.2%</div>
                  <p className="text-sm text-gray-600">Approval Rate</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">5.2 days</div>
                  <p className="text-sm text-gray-600">Avg Approval Time</p>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold">Recent Performance Trends</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">Q4 2024 Improvement</span>
                  <Badge className="bg-green-100 text-green-800">+12%</Badge>
                </div>
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">Supplier Compliance</span>
                  <Badge className="bg-blue-100 text-blue-800">98.5%</Badge>
                </div>
              </div>
            </div>
          </div>
        );
      case 'Supplier':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">156</div>
                  <p className="text-sm text-gray-600">Active Suppliers</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">23</div>
                  <p className="text-sm text-gray-600">Under Review</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">8</div>
                  <p className="text-sm text-gray-600">Non-Compliant</p>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold">Top Performing Suppliers</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">ABC Manufacturing</span>
                  <Badge className="bg-green-100 text-green-800">95.8%</Badge>
                </div>
                <div className="flex justify-between items-center p-2 border rounded">
                  <span className="text-sm">XYZ Components</span>
                  <Badge className="bg-green-100 text-green-800">93.2%</Badge>
                </div>
              </div>
            </div>
          </div>
        );
      case 'Risk':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card className="border-red-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    <span className="font-semibold text-red-800">High Risk Items</span>
                  </div>
                  <div className="text-2xl font-bold text-red-600">3</div>
                </CardContent>
              </Card>
              <Card className="border-orange-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-5 w-5 text-orange-600" />
                    <span className="font-semibold text-orange-800">Medium Risk</span>
                  </div>
                  <div className="text-2xl font-bold text-orange-600">12</div>
                </CardContent>
              </Card>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold">Risk Items Requiring Attention</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 border rounded border-red-200">
                  <div>
                    <span className="text-sm font-medium">Component A - Supply Chain</span>
                    <p className="text-xs text-gray-600">Single source supplier risk</p>
                  </div>
                  <Badge className="bg-red-100 text-red-800">High</Badge>
                </div>
                <div className="flex justify-between items-center p-2 border rounded border-orange-200">
                  <div>
                    <span className="text-sm font-medium">Material B - Quality</span>
                    <p className="text-xs text-gray-600">Specification changes required</p>
                  </div>
                  <Badge className="bg-orange-100 text-orange-800">Medium</Badge>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <p>Analytics dashboard for {activeDialog.title}</p>
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">87.5%</div>
                  <p className="text-sm text-gray-600">Overall Score</p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">234</div>
                  <p className="text-sm text-gray-600">Total Items</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Performance', 'Performance Trends')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Performance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800 mb-1">+12%</div>
            <p className="text-xs text-blue-600 mb-2">Approval rate improvement</p>
            <Button size="sm" variant="outline" className="w-full text-blue-700 border-blue-300">
              View Trends
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Supplier', 'Supplier Analytics')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-green-700 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Supplier Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800 mb-1">89%</div>
            <p className="text-xs text-green-600 mb-2">Average supplier score</p>
            <Button size="sm" variant="outline" className="w-full text-green-700 border-green-300">
              View Suppliers
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Risk', 'Risk Analysis')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Risk Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800 mb-1">3</div>
            <p className="text-xs text-orange-600 mb-2">High risk items</p>
            <Button size="sm" variant="outline" className="w-full text-orange-700 border-orange-300">
              View Risks
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Compliance', 'Compliance Rate')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Compliance Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800 mb-1">94.2%</div>
            <p className="text-xs text-purple-600 mb-2">Overall compliance</p>
            <Button size="sm" variant="outline" className="w-full text-purple-700 border-purple-300">
              View Details
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Timeline', 'Timeline Analysis')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-red-700 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Timeline Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-800 mb-1">5.2</div>
            <p className="text-xs text-red-600 mb-2">Avg days to approval</p>
            <Button size="sm" variant="outline" className="w-full text-red-700 border-red-300">
              View Timeline
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleNavigateToAnalytics('Quality', 'Quality Metrics')}>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-indigo-700 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Quality Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-800 mb-1">97.8%</div>
            <p className="text-xs text-indigo-600 mb-2">Quality score</p>
            <Button size="sm" variant="outline" className="w-full text-indigo-700 border-indigo-300">
              View Metrics
            </Button>
          </CardContent>
        </Card>
      </div>

      <Dialog open={activeDialog.open} onOpenChange={(open) => setActiveDialog({ ...activeDialog, open })}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {activeDialog.title}
            </DialogTitle>
          </DialogHeader>
          {renderDialogContent()}
        </DialogContent>
      </Dialog>
    </>
  );
};

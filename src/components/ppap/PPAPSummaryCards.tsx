
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, AlertTriangle, XCircle } from "lucide-react";

interface PPAPSummaryCardsProps {
  data: {
    open: number;
    approved: number;
    rejected: number;
    underReview: number;
  };
}

export const PPAPSummaryCards: React.FC<PPAPSummaryCardsProps> = ({ data }) => {
  const cards = [
    {
      title: "Open",
      value: data.open,
      icon: <Clock className="h-6 w-6 text-blue-600" />,
      bgColor: "bg-blue-50",
      textColor: "text-blue-900"
    },
    {
      title: "Approved",
      value: data.approved,
      icon: <FileCheck className="h-6 w-6 text-green-600" />,
      bgColor: "bg-green-50",
      textColor: "text-green-900"
    },
    {
      title: "Rejected",
      value: data.rejected,
      icon: <XCircle className="h-6 w-6 text-red-600" />,
      bgColor: "bg-red-50",
      textColor: "text-red-900"
    },
    {
      title: "Under Review",
      value: data.underReview,
      icon: <AlertTriangle className="h-6 w-6 text-amber-600" />,
      bgColor: "bg-amber-50",
      textColor: "text-amber-900"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card) => (
        <Card key={card.title} className="shadow-sm border-slate-200 hover:shadow-md transition-all duration-200 bg-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 mb-1">{card.title}</p>
                <p className={`text-3xl font-bold ${card.textColor}`}>{card.value}</p>
              </div>
              <div className={`p-3 rounded-xl ${card.bgColor}`}>
                {card.icon}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

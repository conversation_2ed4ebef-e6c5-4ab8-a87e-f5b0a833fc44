
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FileText, Clock, CheckCircle, AlertCircle, Eye } from "lucide-react";

export const PPAPReview: React.FC = () => {
  const [submissions] = useState([
    {
      id: "PPAP-001",
      partNumber: "ENG-12345",
      supplier: "Acme Manufacturing",
      submissionDate: "2025-01-10",
      level: "Level 4",
      status: "Under Review",
      reviewer: "<PERSON>",
      priority: "High"
    },
    {
      id: "PPAP-002", 
      partNumber: "BRK-67890",
      supplier: "Quality Parts Inc",
      submissionDate: "2025-01-08",
      level: "Level 3",
      status: "Approved",
      reviewer: "<PERSON>",
      priority: "Medium"
    },
    {
      id: "PPAP-003",
      partNumber: "TRN-11111",
      supplier: "Precision Components",
      submissionDate: "2025-01-12",
      level: "Level 5",
      status: "Rejected",
      reviewer: "<PERSON>",
      priority: "Low"
    }
  ]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Under Review":
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Under Review</Badge>;
      case "Approved":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case "Rejected":
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge className="bg-red-50 text-red-700">High</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-50 text-yellow-700">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-50 text-green-700">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Review</h2>
          <p className="text-sm text-gray-600">Review and approve Production Part Approval Process submissions</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">3</div>
            <div className="text-sm text-gray-600">Under Review</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">12</div>
            <div className="text-sm text-gray-600">Approved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">2</div>
            <div className="text-sm text-gray-600">Rejected</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">5</div>
            <div className="text-sm text-gray-600">Awaiting Response</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            PPAP Submissions for Review
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Part Number</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Submission Date</TableHead>
                <TableHead>Level</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Reviewer</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {submissions.map((submission) => (
                <TableRow key={submission.id}>
                  <TableCell className="font-medium">{submission.id}</TableCell>
                  <TableCell>{submission.partNumber}</TableCell>
                  <TableCell>{submission.supplier}</TableCell>
                  <TableCell>{new Date(submission.submissionDate).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{submission.level}</Badge>
                  </TableCell>
                  <TableCell>{getStatusBadge(submission.status)}</TableCell>
                  <TableCell>{submission.reviewer}</TableCell>
                  <TableCell>{getPriorityBadge(submission.priority)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        Review
                      </Button>
                      {submission.status === "Under Review" && (
                        <>
                          <Button variant="default" size="sm" className="bg-green-600 hover:bg-green-700">
                            Approve
                          </Button>
                          <Button variant="destructive" size="sm">
                            Reject
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

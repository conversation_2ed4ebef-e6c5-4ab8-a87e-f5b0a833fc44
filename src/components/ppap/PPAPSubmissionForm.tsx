
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Plus, FileText } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useFormValidation } from "@/hooks/useFormValidation";
import { ppapSchema, PPAPFormData } from "@/schemas/ppapValidation";
import { useToast } from "@/hooks/use-toast";

export const PPAPSubmissionForm: React.FC = () => {
  const { toast } = useToast();
  
  const form = useFormValidation({
    schema: ppapSchema,
    defaultValues: {
      partNumber: "",
      supplier: "",
      submissionLevel: "",
      description: "",
      drawingRevision: "",
      specRevision: ""
    }
  });

  const handleSubmit = (data: PPAPFormData) => {
    console.log("Creating PPAP submission:", data);
    toast({
      title: "PPAP Submission Created",
      description: "Your PPAP submission has been successfully created and is pending review.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Submission</h2>
          <p className="text-sm text-gray-600">Create new Production Part Approval Process submission</p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Submission
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Submission Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="partNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Part Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter part number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="supplier"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter supplier name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="submissionLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Submission Level *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select submission level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="level1">Level 1 - Part Submission Warrant only</SelectItem>
                          <SelectItem value="level2">Level 2 - PSW with product samples</SelectItem>
                          <SelectItem value="level3">Level 3 - PSW with product samples and limited supporting data</SelectItem>
                          <SelectItem value="level4">Level 4 - PSW with product samples and complete supporting data</SelectItem>
                          <SelectItem value="level5">Level 5 - PSW with product samples, complete supporting data, and records</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="drawingRevision"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Drawing Revision</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter drawing revision" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter part description"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex gap-2">
                <Button type="submit">Submit PPAP</Button>
                <Button type="button" variant="outline">Save Draft</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Required Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Part Submission Warrant (PSW)</p>
              <Button variant="outline" size="sm" className="mt-2">Upload File</Button>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Dimensional Results</p>
              <Button variant="outline" size="sm" className="mt-2">Upload File</Button>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Material Test Results</p>
              <Button variant="outline" size="sm" className="mt-2">Upload File</Button>
            </div>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600">Initial Process Studies</p>
              <Button variant="outline" size="sm" className="mt-2">Upload File</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

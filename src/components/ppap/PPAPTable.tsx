
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, Edit, Trash2, MoreHorizontal } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { DataTable, TableColumn } from "@/components/shared/table/DataTable";
import { Link } from "react-router-dom";

interface PPAPTableProps {
  filters: {
    customer: string;
    status: string;
    dateRange: { from: Date | null; to: Date | null };
  };
}

export const PPAPTable: React.FC<PPAPTableProps> = ({ filters }) => {
  const mockData = [
    {
      id: "PPAP-ELX-2024-0021",
      partNumber: "X200-PCBA",
      partName: "PCB Assembly - Model X200",
      customer: "Flexonics Ltd.",
      status: "Submitted",
      lastUpdated: "2024-12-15",
    },
    {
      id: "PPAP-TCH-2024-0019",
      partNumber: "Y150-CONN",
      partName: "Connector Assembly - Y150",
      customer: "TechCorp Industries",
      status: "Approved",
      lastUpdated: "2024-12-14",
    },
    {
      id: "PPAP-ADV-2024-0018",
      partNumber: "Z300-HARN",
      partName: "Wire Harness - Z300",
      customer: "Advanced Electronics",
      status: "Revision Required",
      lastUpdated: "2024-12-13",
    },
    {
      id: "PPAP-PRC-2024-0017",
      partNumber: "A400-SENS",
      partName: "Sensor Module - A400",
      customer: "Precision Manufacturing",
      status: "Under Review",
      lastUpdated: "2024-12-12",
    },
    {
      id: "PPAP-ELX-2024-0016",
      partNumber: "B250-DISP",
      partName: "Display Unit - B250",
      customer: "Flexonics Ltd.",
      status: "Rejected",
      lastUpdated: "2024-12-11",
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Submitted": { variant: "outline" as const, className: "border-blue-200 text-blue-800 bg-blue-50" },
      "Approved": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "Revision Required": { variant: "outline" as const, className: "border-amber-200 text-amber-800 bg-amber-50" },
      "Rejected": { variant: "outline" as const, className: "border-red-200 text-red-800 bg-red-50" },
      "Under Review": { variant: "outline" as const, className: "border-purple-200 text-purple-800 bg-purple-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["Submitted"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  const filteredData = mockData.filter((item) => {
    if (filters.customer && item.customer !== filters.customer) return false;
    if (filters.status && item.status !== filters.status) return false;
    // Add date range filtering logic if needed
    return true;
  });

  const columns: TableColumn[] = [
    {
      field: "id",
      header: "PPAP ID",
      sortable: true,
      width: "w-[300px]",
      cellRenderer: (item) => (
        <div className="flex flex-col">
          <Link to={`/ppap/${item.id}`} className="text-blue-600 cursor-pointer hover:text-blue-700 font-medium">
            {item.id}
          </Link>
          <span className="text-xs text-gray-500 mt-1">{item.partName}</span>
        </div>
      )
    },
    {
      field: "partNumber",
      header: "Part Number",
      sortable: true,
      cellRenderer: (item) => (
        <span className="font-medium text-slate-900">{item.partNumber}</span>
      )
    },
    {
      field: "customer",
      header: "Customer",
      sortable: true,
      cellRenderer: (item) => (
        <span className="text-slate-700">{item.customer}</span>
      )
    },
    {
      field: "status",
      header: "Status",
      sortable: true,
      cellRenderer: (item) => getStatusBadge(item.status)
    },
    {
      field: "lastUpdated",
      header: "Last Updated",
      sortable: true,
      cellRenderer: (item) => (
        <span className="text-slate-600">{item.lastUpdated}</span>
      )
    }
  ];

  const renderActions = (item: any) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-white z-50">
        <DropdownMenuItem>
          <Eye className="mr-2 h-4 w-4" />
          View
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem className="text-red-600">
          <Trash2 className="mr-2 h-4 w-4" />
          Withdraw
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <DataTable
      data={mockData}
      columns={columns}
      filteredData={filteredData}
      currentPage={1}
      pageSize={10}
      totalCount={filteredData.length}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
      renderActions={renderActions}
      emptyMessage="No PPAP packages found"
      className="shadow-sm"
    />
  );
};

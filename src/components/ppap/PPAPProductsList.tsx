
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Package, Search, Filter } from "lucide-react";
import { PPAPProductsTable, PPAPProduct } from "./table/PPAPProductsTable";

export const PPAPProductsList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  const [products] = useState<PPAPProduct[]>([
    {
      id: "PROD-001",
      partNumber: "ENG-12345",
      description: "Engine Control Module",
      supplier: "Acme Manufacturing",
      ppapLevel: "Level 4",
      status: "Active",
      approvalDate: "2024-12-15",
      nextReview: "2025-06-15",
      category: "Electronics"
    },
    {
      id: "PROD-002",
      partNumber: "BRK-67890",
      description: "Brake Pad Assembly",
      supplier: "Quality Parts Inc",
      ppapLevel: "Level 3",
      status: "Active",
      approvalDate: "2024-11-20",
      nextReview: "2025-05-20",
      category: "Mechanical"
    },
    {
      id: "PROD-003",
      partNumber: "TRN-11111",
      description: "Transmission Sensor",
      supplier: "Precision Components",
      ppapLevel: "Level 5",
      status: "Under Review",
      approvalDate: "",
      nextReview: "2025-02-01",
      category: "Sensors"
    },
    {
      id: "PROD-004",
      partNumber: "CHG-22222",
      description: "Charging Port Assembly",
      supplier: "ElectroTech Solutions",
      ppapLevel: "Level 4",
      status: "Expired",
      approvalDate: "2023-08-10",
      nextReview: "2024-08-10",
      category: "Electronics"
    }
  ]);

  const filteredProducts = products.filter(product =>
    product.partNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.supplier.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  // Get current page data
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + pageSize);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Products</h2>
          <p className="text-sm text-gray-600">Manage approved products and their PPAP status</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">{products.length}</div>
            <div className="text-sm text-gray-600">Total Products</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{products.filter(p => p.status === "Active").length}</div>
            <div className="text-sm text-gray-600">Active</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{products.filter(p => p.status === "Under Review").length}</div>
            <div className="text-sm text-gray-600">Under Review</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{products.filter(p => p.status === "Expired").length}</div>
            <div className="text-sm text-gray-600">Expired</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search products by part number, description, or supplier..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Products Table */}
      <PPAPProductsTable
        products={paginatedProducts}
        currentPage={currentPage}
        totalCount={filteredProducts.length}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

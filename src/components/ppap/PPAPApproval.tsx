
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Clock, AlertCircle, FileText, User } from "lucide-react";

export const PPAPApproval: React.FC = () => {
  const [approvals] = useState([
    {
      id: "PPAP-001",
      partNumber: "ENG-12345",
      supplier: "Acme Manufacturing",
      submissionLevel: "Level 4",
      approvalStage: "Final Approval",
      progress: 85,
      approver: "<PERSON>",
      dueDate: "2025-01-20",
      status: "In Progress"
    },
    {
      id: "PPAP-002",
      partNumber: "BRK-67890", 
      supplier: "Quality Parts Inc",
      submissionLevel: "Level 3",
      approvalStage: "Management Review",
      progress: 100,
      approver: "<PERSON>",
      dueDate: "2025-01-15",
      status: "Approved"
    },
    {
      id: "PPAP-003",
      partNumber: "TRN-11111",
      supplier: "Precision Components", 
      submissionLevel: "Level 5",
      approvalStage: "Technical Review",
      progress: 45,
      approver: "Mike <PERSON>",
      dueDate: "2025-01-25",
      status: "Under Review"
    }
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "In Progress":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "Under Review":
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Approved":
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Under Review":
        return <Badge className="bg-yellow-100 text-yellow-800">Under Review</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Approval</h2>
          <p className="text-sm text-gray-600">Track and manage PPAP approval workflows</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">8</div>
            <div className="text-sm text-gray-600">Approved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">5</div>
            <div className="text-sm text-gray-600">In Progress</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <AlertCircle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-yellow-600">3</div>
            <div className="text-sm text-gray-600">Under Review</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <FileText className="h-8 w-8 text-gray-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-600">92%</div>
            <div className="text-sm text-gray-600">Approval Rate</div>
          </CardContent>
        </Card>
      </div>

      {/* Approval Items */}
      <div className="space-y-4">
        {approvals.map((approval) => (
          <Card key={approval.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-3">
                  {getStatusIcon(approval.status)}
                  <div>
                    <h4 className="font-semibold text-slate-900">{approval.partNumber}</h4>
                    <p className="text-sm text-slate-600">{approval.supplier}</p>
                    <p className="text-xs text-slate-500">{approval.submissionLevel} - {approval.approvalStage}</p>
                  </div>
                </div>
                {getStatusBadge(approval.status)}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <User className="h-4 w-4" />
                  <span>Approver: {approval.approver}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <Clock className="h-4 w-4" />
                  <span>Due: {new Date(approval.dueDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <FileText className="h-4 w-4" />
                  <span>ID: {approval.id}</span>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-slate-700">Approval Progress</span>
                  <span className="text-sm text-slate-600">{approval.progress}%</span>
                </div>
                <Progress value={approval.progress} className="h-2" />
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">View Details</Button>
                {approval.status === "Under Review" && (
                  <>
                    <Button size="sm" className="bg-green-600 hover:bg-green-700">Approve</Button>
                    <Button variant="destructive" size="sm">Request Changes</Button>
                  </>
                )}
                {approval.status === "In Progress" && (
                  <Button size="sm">Continue Review</Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

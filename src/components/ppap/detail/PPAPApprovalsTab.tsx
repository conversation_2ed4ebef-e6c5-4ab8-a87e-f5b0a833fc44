
import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Calendar, MessageSquare } from "lucide-react";

interface PPAPApprovalsTabProps {
  ppapId: string;
}

export const PPAPApprovalsTab: React.FC<PPAPApprovalsTabProps> = ({ ppapId }) => {
  const approvals = [
    {
      id: "1",
      reviewer: "<PERSON>riya Nair",
      role: "Quality Assurance Lead",
      status: "Approved",
      assignedDate: "2024-12-15",
      completedDate: "2024-12-17",
      comments: "All quality requirements met. Documents are comprehensive and well-prepared.",
      avatar: "PN"
    },
    {
      id: "2",
      reviewer: "<PERSON>",
      role: "Manufacturing Engineer",
      status: "Under Review",
      assignedDate: "2024-12-15",
      completedDate: null,
      comments: "Currently reviewing process flow and control plan. Will complete by EOD tomorrow.",
      avatar: "AC"
    },
    {
      id: "3",
      reviewer: "<EMAIL>",
      role: "Customer Representative",
      status: "Pending",
      assignedDate: "2024-12-15",
      completedDate: null,
      comments: null,
      avatar: "CR"
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Approved": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "Under Review": { variant: "outline" as const, className: "border-purple-200 text-purple-800 bg-purple-50" },
      "Pending": { variant: "outline" as const, className: "border-slate-200 text-slate-600 bg-slate-50" },
      "Rejected": { variant: "outline" as const, className: "border-red-200 text-red-800 bg-red-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["Pending"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {approvals.map((approval) => (
        <Card key={approval.id} className="border border-slate-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-sm">
                  {approval.avatar}
                </div>
                <div>
                  <CardTitle className="text-base font-medium text-slate-900">{approval.reviewer}</CardTitle>
                  <p className="text-sm text-slate-600">{approval.role}</p>
                </div>
              </div>
              {getStatusBadge(approval.status)}
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="flex items-center gap-2 text-sm text-slate-600">
                <Calendar className="h-4 w-4" />
                <span>Assigned: {approval.assignedDate}</span>
              </div>
              {approval.completedDate && (
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <Calendar className="h-4 w-4" />
                  <span>Completed: {approval.completedDate}</span>
                </div>
              )}
            </div>
            {approval.comments && (
              <div className="flex gap-2 text-sm">
                <MessageSquare className="h-4 w-4 text-slate-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-slate-700">{approval.comments}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

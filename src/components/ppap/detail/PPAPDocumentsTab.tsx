
import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { DataTable, TableColumn } from "@/components/shared/table/DataTable";

interface PPAPDocumentsTabProps {
  ppapId: string;
}

export const PPAPDocumentsTab: React.FC<PPAPDocumentsTabProps> = ({ ppapId }) => {
  const documents = [
    {
      id: "1",
      documentType: "Design Records",
      fileName: "X200_Design_Records_RevB.pdf",
      uploadDate: "2024-12-15",
      status: "Approved",
      size: "2.4 MB"
    },
    {
      id: "2",
      documentType: "Process Flow Diagram",
      fileName: "X200_Process_Flow_RevB.svg",
      uploadDate: "2024-12-15",
      status: "Approved",
      size: "856 KB"
    },
    {
      id: "3",
      documentType: "PFMEA",
      fileName: "X200_PFMEA_RevB.xlsx",
      uploadDate: "2024-12-15",
      status: "Needs Fix",
      size: "1.2 MB"
    },
    {
      id: "4",
      documentType: "Control Plan",
      fileName: "X200_ControlPlan_RevB.xlsx",
      uploadDate: "2024-12-15",
      status: "Under Review",
      size: "987 KB"
    },
    {
      id: "5",
      documentType: "Dimensional Report",
      fileName: "X200_Dimensional_Report_RevB.csv",
      uploadDate: "2024-12-15",
      status: "Approved",
      size: "145 KB"
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Approved": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "Needs Fix": { variant: "outline" as const, className: "border-red-200 text-red-800 bg-red-50" },
      "Under Review": { variant: "outline" as const, className: "border-purple-200 text-purple-800 bg-purple-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["Under Review"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  const columns: TableColumn[] = [
    {
      field: "fileName",
      header: "Document",
      sortable: true,
      width: "w-[300px]",
      cellRenderer: (item) => (
        <div className="flex flex-col">
          <span className="text-blue-600 cursor-pointer hover:text-blue-700 font-medium">{item.fileName}</span>
          <span className="text-xs text-slate-500 mt-1">{item.documentType}</span>
        </div>
      )
    },
    {
      field: "uploadDate",
      header: "Upload Date",
      sortable: true,
      cellRenderer: (item) => (
        <span className="text-slate-700">{item.uploadDate}</span>
      )
    },
    {
      field: "size",
      header: "Size",
      sortable: true,
      cellRenderer: (item) => (
        <span className="text-slate-600">{item.size}</span>
      )
    },
    {
      field: "status",
      header: "Status",
      sortable: true,
      cellRenderer: (item) => getStatusBadge(item.status)
    }
  ];

  const renderActions = (item: any) => (
    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
      <Download className="h-4 w-4" />
    </Button>
  );

  return (
    <DataTable
      data={documents}
      columns={columns}
      filteredData={documents}
      currentPage={1}
      pageSize={10}
      totalCount={documents.length}
      onPageChange={() => {}}
      onPageSizeChange={() => {}}
      renderActions={renderActions}
      emptyMessage="No documents found"
      className="shadow-sm"
    />
  );
};

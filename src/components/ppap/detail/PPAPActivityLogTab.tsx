
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, User, FileText, CheckCircle, MessageSquare, Upload } from "lucide-react";

interface PPAPActivityLogTabProps {
  ppapId: string;
}

export const PPAPActivityLogTab: React.FC<PPAPActivityLogTabProps> = ({ ppapId }) => {
  const activities = [
    {
      id: "1",
      type: "approval",
      title: "PPAP Package Approved by QA Lead",
      description: "<PERSON><PERSON> <PERSON> approved all quality documentation",
      timestamp: "2024-12-17 14:30",
      user: "<PERSON><PERSON> <PERSON>",
      icon: CheckCircle,
      iconColor: "text-green-600"
    },
    {
      id: "2",
      type: "comment",
      title: "Comment Added",
      description: "<PERSON>: Currently reviewing process flow and control plan. Will complete by EOD tomorrow.",
      timestamp: "2024-12-16 16:45",
      user: "<PERSON>",
      icon: MessageSquare,
      iconColor: "text-blue-600"
    },
    {
      id: "3",
      type: "assignment",
      title: "Reviewers Assigned",
      description: "PPAP package assigned to <PERSON><PERSON> (QA Lead) and <PERSON> (Manufacturing Engineer)",
      timestamp: "2024-12-15 11:20",
      user: "System",
      icon: User,
      iconColor: "text-purple-600"
    },
    {
      id: "4",
      type: "upload",
      title: "Documents Uploaded",
      description: "All required PPAP elements uploaded successfully",
      timestamp: "2024-12-15 10:45",
      user: "<PERSON>",
      icon: Upload,
      iconColor: "text-indigo-600"
    },
    {
      id: "5",
      type: "created",
      title: "PPAP Package Created",
      description: "New PPAP package created for PCB Assembly - Model X200",
      timestamp: "2024-12-15 09:30",
      user: "John Smith",
      icon: FileText,
      iconColor: "text-slate-600"
    }
  ];

  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <Card key={activity.id} className="border border-slate-200">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="flex-shrink-0">
                <div className={`w-10 h-10 rounded-full bg-slate-50 flex items-center justify-center ${activity.iconColor}`}>
                  <activity.icon className="h-5 w-5" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sm font-medium text-slate-900">{activity.title}</h3>
                  <span className="text-xs text-slate-500">{activity.timestamp}</span>
                </div>
                <p className="text-sm text-slate-600 mb-2">{activity.description}</p>
                <div className="flex items-center gap-2">
                  <User className="h-3 w-3 text-slate-400" />
                  <span className="text-xs text-slate-500">{activity.user}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PPAPOverviewTabProps {
  data: {
    id: string;
    partNumber: string;
    partName: string;
    customer: string;
    status: string;
    revision: string;
    submissionReason: string;
    submittedDate: string;
    dueDate: string;
  };
}

export const PPAPOverviewTab: React.FC<PPAPOverviewTabProps> = ({ data }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card className="border border-slate-200">
        <CardHeader>
          <CardTitle className="text-base font-medium text-slate-900">Part Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-slate-600">Part Number</label>
            <p className="text-slate-900 font-medium">{data.partNumber}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-slate-600">Part Name</label>
            <p className="text-slate-900">{data.partName}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-slate-600">Revision</label>
            <p className="text-slate-900">{data.revision}</p>
          </div>
        </CardContent>
      </Card>

      <Card className="border border-slate-200">
        <CardHeader>
          <CardTitle className="text-base font-medium text-slate-900">Customer & Submission</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-slate-600">Customer</label>
            <p className="text-slate-900 font-medium">{data.customer}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-slate-600">Submission Reason</label>
            <p className="text-slate-900">{data.submissionReason}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-slate-600">Submitted Date</label>
            <p className="text-slate-900">{data.submittedDate}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { TableBody } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { VendorTablePagination } from "../../vendors/VendorTablePagination";
import { PPAPProductsTableHeader } from "./PPAPProductsTableHeader";
import { PPAPProductsTableRow } from "./PPAPProductsTableRow";

export interface PPAPProduct {
  id: string;
  partNumber: string;
  description: string;
  supplier: string;
  ppapLevel: string;
  status: string;
  approvalDate: string;
  nextReview: string;
  category: string;
}

interface PPAPProductsTableProps {
  products: PPAPProduct[];
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
}

export const PPAPProductsTable: React.FC<PPAPProductsTableProps> = ({ 
  products, 
  currentPage, 
  totalCount, 
  pageSize, 
  onPageChange,
  onPageSizeChange
}) => {
  const [showExpandColumn, setShowExpandColumn] = useState<boolean>(false);
  const isMobile = useIsMobile();

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  // Check if we should show the expand column based on screen width
  useEffect(() => {
    const checkWidth = () => {
      setShowExpandColumn(window.innerWidth < 1200);
    };
    
    checkWidth(); // Initial check
    window.addEventListener('resize', checkWidth);
    
    return () => {
      window.removeEventListener('resize', checkWidth);
    };
  }, []);

  return (
    <div className="flex flex-col h-full w-full">
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="w-full overflow-x-auto">
          <div className="min-w-full">
            <ResizableTable className="w-full table-fixed">
              <PPAPProductsTableHeader 
                isMobile={isMobile}
                showExpandColumn={showExpandColumn}
              />
              <TableBody>
                {products.length > 0 ? (
                  products.map((product, index) => (
                    <PPAPProductsTableRow
                      key={product.id}
                      product={product}
                      index={index}
                      isMobile={isMobile}
                      showExpandColumn={showExpandColumn}
                    />
                  ))
                ) : (
                  <tr>
                    <td colSpan={isMobile ? 4 : 10} className="text-center py-8 text-gray-500">
                      No products found
                    </td>
                  </tr>
                )}
              </TableBody>
            </ResizableTable>
          </div>
        </div>
      </div>
      
      <VendorTablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange || (() => {})}
        total={totalCount}
        className="mt-4 bg-white rounded-lg border border-gray-200"
      />
    </div>
  );
};


import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface PPAPProductsTableHeaderProps {
  isMobile: boolean;
  showExpandColumn: boolean;
}

export const PPAPProductsTableHeader: React.FC<PPAPProductsTableHeaderProps> = ({
  isMobile,
  showExpandColumn
}) => {
  if (isMobile) {
    return (
      <TableHeader>
        <TableRow>
          <TableHead className="w-10 p-2 bg-gray-50">
            <span className="sr-only">Expand</span>
          </TableHead>
          <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 p-2 bg-gray-50">
            Part Number
          </TableHead>
          <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 p-2 bg-gray-50">
            Status
          </TableHead>
          <TableHead className="w-10 p-2 bg-gray-50">
            <span className="sr-only">Actions</span>
          </TableHead>
        </TableRow>
      </TableHeader>
    );
  }

  return (
    <TableHeader className="sticky top-0 z-20 bg-gray-50">
      <TableRow className="bg-gray-50">
        {showExpandColumn && (
          <TableHead className="w-12 p-3 bg-gray-50">
            <span className="sr-only">Expand</span>
          </TableHead>
        )}
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-32 min-w-[120px]">
          Part Number
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-1/4 min-w-[200px]">
          Description
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-32 min-w-[120px]">
          Supplier
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
          Category
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
          PPAP Level
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-24">
          Status
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-28">
          Approval Date
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 w-28">
          Next Review
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-700 uppercase tracking-wider py-3 px-4 bg-gray-50 text-right w-20">
          Actions
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};


import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Eye, Edit } from "lucide-react";
import { PPAPProduct } from "./PPAPProductsTable";

interface PPAPProductsTableRowProps {
  product: PPAPProduct;
  index: number;
  isMobile: boolean;
  showExpandColumn: boolean;
}

export const PPAPProductsTableRow: React.FC<PPAPProductsTableRowProps> = ({ 
  product, 
  index,
  isMobile,
  showExpandColumn
}) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Under Review":
        return <Badge className="bg-yellow-100 text-yellow-800">Under Review</Badge>;
      case "Expired":
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCategoryBadge = (category: string) => {
    const colors = {
      Electronics: "bg-blue-100 text-blue-800",
      Mechanical: "bg-purple-100 text-purple-800", 
      Sensors: "bg-orange-100 text-orange-800"
    };
    return <Badge className={colors[category as keyof typeof colors] || "bg-gray-100 text-gray-800"}>{category}</Badge>;
  };

  if (isMobile) {
    return (
      <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100 transition-colors`}>
        <TableCell className="w-10 p-2">
          <ChevronDown size={16} className="text-gray-400" />
        </TableCell>
        <TableCell className="py-4 p-2">
          <div>
            <div className="font-medium text-gray-900">{product.partNumber}</div>
            <div className="text-sm text-gray-600 truncate">{product.description}</div>
            <div className="text-xs text-gray-500">{product.supplier}</div>
          </div>
        </TableCell>
        <TableCell className="py-4 p-2">
          {getStatusBadge(product.status)}
        </TableCell>
        <TableCell className="w-10 p-2">
          <div className="flex gap-1">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Eye className="h-3 w-3" />
            </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Edit className="h-3 w-3" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
    );
  }

  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100 transition-colors`}>
      {showExpandColumn && (
        <TableCell className="w-12 p-3 text-center">
          <ChevronDown size={16} className="text-gray-400" />
        </TableCell>
      )}
      
      <TableCell className="py-4 px-4 w-32 min-w-[120px]">
        <span className="font-medium text-gray-900">{product.partNumber}</span>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-1/4 min-w-[200px]">
        <span className="text-gray-700">{product.description}</span>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-32 min-w-[120px]">
        <span className="text-gray-700">{product.supplier}</span>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-24">
        {getCategoryBadge(product.category)}
      </TableCell>
      
      <TableCell className="py-4 px-4 w-24">
        <Badge variant="outline">{product.ppapLevel}</Badge>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-24">
        {getStatusBadge(product.status)}
      </TableCell>
      
      <TableCell className="py-4 px-4 w-28">
        <span className="text-gray-700">
          {product.approvalDate ? new Date(product.approvalDate).toLocaleDateString() : "N/A"}
        </span>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-28">
        <span className="text-gray-700">{new Date(product.nextReview).toLocaleDateString()}</span>
      </TableCell>
      
      <TableCell className="py-4 px-4 w-20 text-right">
        <div className="flex gap-1 justify-end">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-200">
            <Eye className="h-4 w-4 text-blue-600" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-200">
            <Edit className="h-4 w-4 text-blue-600" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
};


import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { PPAPWizard } from "./wizard/PPAPWizard";

export const CreatePPAPDialog: React.FC = () => {
  const [isWizardOpen, setIsWizardOpen] = useState(false);

  return (
    <>
      <Button 
        onClick={() => setIsWizardOpen(true)}
        className="bg-teal-600 hover:bg-teal-700 text-white"
      >
        <Plus className="mr-2 h-4 w-4" />
        Create New PPAP Package
      </Button>

      <PPAPWizard 
        open={isWizardOpen} 
        onOpenChange={setIsWizardOpen} 
      />
    </>
  );
};

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Plus, Clock, CheckCircle, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { InitiateRecallDialog, RecallFormData } from "./recall/InitiateRecallDialog";

export const PPAPRecall: React.FC = () => {
  const [recalls] = useState([
    {
      id: "RCL-001",
      partNumber: "BRK-67890",
      description: "Brake Pad Material Issue",
      severity: "High",
      status: "Active",
      dateInitiated: "2024-12-20",
      affectedUnits: 1250,
      supplier: "Quality Parts Inc",
      reason: "Material composition variance detected"
    },
    {
      id: "RCL-002",
      partNumber: "ENG-12345",
      description: "Software Version Mismatch",
      severity: "Medium",
      status: "Investigating",
      dateInitiated: "2025-01-05",
      affectedUnits: 350,
      supplier: "Acme Manufacturing",
      reason: "Incorrect firmware version installed"
    },
    {
      id: "RCL-003",
      partNumber: "CHG-22222",
      description: "Charging Port Overheating",
      severity: "Critical",
      status: "Resolved",
      dateInitiated: "2024-11-15",
      affectedUnits: 2100,
      supplier: "ElectroTech Solutions",
      reason: "Design flaw causing thermal runaway"
    }
  ]);

  const { toast } = useToast();
  const [dialogOpen, setDialogOpen] = useState(false);

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "Critical":
        return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
      case "High":
        return <Badge className="bg-orange-100 text-orange-800">High</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      default:
        return <Badge variant="outline">{severity}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Active":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "Investigating":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "Resolved":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "Closed":
        return <XCircle className="h-4 w-4 text-gray-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-red-100 text-red-800">Active</Badge>;
      case "Investigating":
        return <Badge className="bg-yellow-100 text-yellow-800">Investigating</Badge>;
      case "Resolved":
        return <Badge className="bg-green-100 text-green-800">Resolved</Badge>;
      case "Closed":
        return <Badge className="bg-gray-100 text-gray-800">Closed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleRecallSubmit = (data: RecallFormData) => {
    toast({
      title: "Recall initiated",
      description: `Recall for part ${data.partNumber} (${data.severity}) was initiated for supplier ${data.supplier}.`,
      variant: "default",
    });
  };

  return (
    <div className="space-y-6">
      <InitiateRecallDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={handleRecallSubmit}
      />
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">PPAP Recall Management</h2>
          <p className="text-sm text-gray-600">Track and manage product recalls and quality issues</p>
        </div>
        <Button className="gap-2" onClick={() => setDialogOpen(true)}>
          <Plus className="h-4 w-4" />
          Initiate Recall
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-red-600">{recalls.filter(r => r.status === "Active").length}</div>
            <div className="text-sm text-gray-600">Active Recalls</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-yellow-600">{recalls.filter(r => r.status === "Investigating").length}</div>
            <div className="text-sm text-gray-600">Under Investigation</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">{recalls.filter(r => r.status === "Resolved").length}</div>
            <div className="text-sm text-gray-600">Resolved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{recalls.reduce((sum, r) => sum + r.affectedUnits, 0).toLocaleString()}</div>
            <div className="text-sm text-gray-600">Total Affected Units</div>
          </CardContent>
        </Card>
      </div>

      {/* Recall Items */}
      <div className="space-y-4">
        {recalls.map((recall) => (
          <Card key={recall.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-3">
                  {getStatusIcon(recall.status)}
                  <div>
                    <h4 className="font-semibold text-slate-900">{recall.description}</h4>
                    <p className="text-sm text-slate-600">{recall.partNumber} - {recall.supplier}</p>
                    <p className="text-xs text-slate-500 mt-1">{recall.reason}</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  {getSeverityBadge(recall.severity)}
                  {getStatusBadge(recall.status)}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-sm">
                  <span className="text-slate-600">Recall ID:</span>
                  <span className="font-medium ml-1">{recall.id}</span>
                </div>
                <div className="text-sm">
                  <span className="text-slate-600">Date Initiated:</span>
                  <span className="font-medium ml-1">{new Date(recall.dateInitiated).toLocaleDateString()}</span>
                </div>
                <div className="text-sm">
                  <span className="text-slate-600">Affected Units:</span>
                  <span className="font-medium ml-1">{recall.affectedUnits.toLocaleString()}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">View Details</Button>
                {recall.status === "Active" && (
                  <>
                    <Button size="sm">Update Status</Button>
                    <Button variant="destructive" size="sm">Escalate</Button>
                  </>
                )}
                {recall.status === "Investigating" && (
                  <Button size="sm">Continue Investigation</Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

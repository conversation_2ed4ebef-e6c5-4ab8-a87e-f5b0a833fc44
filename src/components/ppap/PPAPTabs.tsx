
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PPAPSubmissionForm } from "./PPAPSubmissionForm";
import { PPAPReview } from "./PPAPReview";
import { PPAPApproval } from "./PPAPApproval";
import { PPAPProductsList } from "./PPAPProductsList";
import { PPAPRecall } from "./PPAPRecall";
import { PPAPReports } from "./PPAPReports";
import { FileText, Upload, CheckCircle, Package, AlertTriangle, BarChart3 } from "lucide-react";

export const PPAPTabs: React.FC = () => {
  const [activeTab, setActiveTab] = useState("submission");

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-6">
        <TabsTrigger value="submission" className="flex items-center gap-2">
          <Upload className="h-4 w-4" />
          Submission
        </TabsTrigger>
        <TabsTrigger value="review" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Review
        </TabsTrigger>
        <TabsTrigger value="approval" className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Approval
        </TabsTrigger>
        <TabsTrigger value="products" className="flex items-center gap-2">
          <Package className="h-4 w-4" />
          Products
        </TabsTrigger>
        <TabsTrigger value="recall" className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4" />
          Recall
        </TabsTrigger>
        <TabsTrigger value="reports" className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          Reports
        </TabsTrigger>
      </TabsList>

      <TabsContent value="submission" className="mt-6">
        <PPAPSubmissionForm />
      </TabsContent>

      <TabsContent value="review" className="mt-6">
        <PPAPReview />
      </TabsContent>

      <TabsContent value="approval" className="mt-6">
        <PPAPApproval />
      </TabsContent>

      <TabsContent value="products" className="mt-6">
        <PPAPProductsList />
      </TabsContent>

      <TabsContent value="recall" className="mt-6">
        <PPAPRecall />
      </TabsContent>

      <TabsContent value="reports" className="mt-6">
        <PPAPReports />
      </TabsContent>
    </Tabs>
  );
};

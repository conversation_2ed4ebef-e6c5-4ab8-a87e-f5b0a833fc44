
import React from "react";
import { CustomerNavigation } from "@/components/customer-compliance/CustomerNavigation";

interface CustomerWorkspaceNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export function CustomerWorkspaceNavigation({ activeTab, setActiveTab }: CustomerWorkspaceNavigationProps) {
  return (
    <CustomerNavigation 
      activeTab={activeTab} 
      setActiveTab={setActiveTab} 
    />
  );
}

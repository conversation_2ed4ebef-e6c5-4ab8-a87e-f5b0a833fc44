
import React from "react";
import { CustomerContent } from "@/components/customer-compliance/CustomerContent";
import { CustomerDocument, AuditRecord, CapaItem, OtifDataPoint } from "@/components/customer-compliance/types";

interface CustomerWorkspaceContentProps {
  activeTab: string;
  recentDocuments: CustomerDocument[];
  auditHistory: AuditRecord[];
  otifData: OtifDataPoint[];
  sharedDocuments: CustomerDocument[];
  capas: CapaItem[];
}

export function CustomerWorkspaceContent({
  activeTab,
  recentDocuments,
  auditHistory,
  otifData,
  sharedDocuments,
  capas
}: CustomerWorkspaceContentProps) {
  return (
    <CustomerContent
      activeTab={activeTab}
      recentDocuments={recentDocuments}
      auditHistory={auditHistory}
      otifData={otifData}
      sharedDocuments={sharedDocuments}
      capas={capas}
    />
  );
}

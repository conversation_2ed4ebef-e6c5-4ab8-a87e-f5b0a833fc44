
import React from "react";
import { ManageAccessPanel } from "@/components/customer-compliance/ManageAccessPanel";
import { CustomerWorkspaceHeader } from "./CustomerWorkspaceHeader";
import { CustomerWorkspaceNavigation } from "./CustomerWorkspaceNavigation";
import { CustomerWorkspaceContent } from "./CustomerWorkspaceContent";
import { AccountProvider, useAccount } from "@/contexts/AccountContext";

interface CustomerWorkspaceLayoutProps {
  customerName: string;
  customer: any;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  isManageAccessOpen: boolean;
  setIsManageAccessOpen: (isOpen: boolean) => void;
  recentDocuments: any[];
  auditHistory: any[];
  sharedDocuments: any[];
  capas: any[];
  otifData: any[];
}

// This is a wrapper component that ensures the AccountProvider is present
function SafeCustomerWorkspaceLayout(props: CustomerWorkspaceLayoutProps) {
  try {
    // Try to use the account context - if it fails, we'll render with the provider
    useAccount();
    return <CustomerWorkspaceLayoutInner {...props} />;
  } catch (error) {
    // If useAccount fails, we wrap the component with AccountProvider
    return (
      <AccountProvider>
        <CustomerWorkspaceLayoutInner {...props} />
      </AccountProvider>
    );
  }
}

// The inner component that assumes AccountProvider is available
function CustomerWorkspaceLayoutInner({
  customerName,
  customer,
  activeTab,
  setActiveTab,
  isManageAccessOpen,
  setIsManageAccessOpen,
  recentDocuments,
  auditHistory,
  sharedDocuments,
  capas,
  otifData
}: CustomerWorkspaceLayoutProps) {
  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Header and Actions */}
      <CustomerWorkspaceHeader 
        customer={customer} 
        onManageAccess={() => setIsManageAccessOpen(true)}
      />
      
      {/* Main content with tabs */}
      <div className="flex flex-col lg:flex-row gap-6 w-full">
        {/* Left side tabs */}
        <div className="lg:w-64 flex-shrink-0">
          <CustomerWorkspaceNavigation 
            activeTab={activeTab} 
            setActiveTab={setActiveTab} 
          />
        </div>
        
        {/* Right side content */}
        <div className="flex-1 min-w-0">
          <CustomerWorkspaceContent
            activeTab={activeTab}
            recentDocuments={recentDocuments}
            auditHistory={auditHistory}
            otifData={otifData}
            sharedDocuments={sharedDocuments}
            capas={capas}
          />
        </div>
      </div>

      {/* Manage Access Panel */}
      <ManageAccessPanel 
        isOpen={isManageAccessOpen} 
        onClose={() => setIsManageAccessOpen(false)}
        customerName={customerName}
      />
    </div>
  );
}

// Export the safe wrapper as the default component
export { SafeCustomerWorkspaceLayout as CustomerWorkspaceLayout };


import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Upload, Package, Users, ArrowLeft, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";

interface CustomerWorkspaceHeaderProps {
  customer: any;
  onManageAccess: () => void;
}

export function CustomerWorkspaceHeader({ customer, onManageAccess }: CustomerWorkspaceHeaderProps) {
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleShareNewFile = () => {
    toast({
      title: "Share New File",
      description: "File sharing functionality would be implemented here",
    });
  };

  const handleGeneratePackage = () => {
    toast({
      title: "Generating Compliance Package",
      description: "Creating comprehensive compliance package for customer review...",
    });
    
    // Simulate package generation with proper progress
    setTimeout(() => {
      toast({
        title: "Package Generated Successfully",
        description: `Compliance package for ${customer.name} is ready for download`,
      });
      
      // Create a more realistic download simulation
      const packageData = {
        customerName: customer.name,
        generatedDate: new Date().toISOString(),
        documents: [
          'Quality_Certifications.pdf',
          'Audit_Reports.pdf',
          'CAPA_Records.pdf',
          'Performance_Metrics.pdf',
          'Compliance_Matrix.xlsx'
        ],
        packageSize: '15.7 MB'
      };
      
      // Simulate file download
      const blob = new Blob([JSON.stringify(packageData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${customer.name}_Compliance_Package_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, 2000);
  };

  const handleBackToWorkspaces = () => {
    navigate('/customer-compliance');
  };

  return (
    <div className="space-y-4">
      {/* Back button */}
      <div>
        <Button 
          variant="outline" 
          size="sm" 
          className="gap-2"
          onClick={handleBackToWorkspaces}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Workspaces
        </Button>
      </div>

      {/* Header content */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="h-16 w-16 bg-gray-100 rounded-md flex items-center justify-center">
            <img 
              src="/placeholder.svg" 
              alt={`${customer.name} logo`} 
              className="h-10 w-10"
            />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{customer.name}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge className="bg-green-500 text-white">{customer.status}</Badge>
              <span className="text-gray-500">Last audit: {customer.lastAudit}</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" className="gap-2" onClick={handleShareNewFile}>
            <Upload className="h-4 w-4" />
            Share New File
          </Button>
          <Button variant="outline" className="gap-2" onClick={handleGeneratePackage}>
            <Package className="h-4 w-4" />
            Generate Package
          </Button>
          <Button 
            className="gap-2"
            onClick={onManageAccess}
          >
            <Users className="h-4 w-4" />
            Manage Access
          </Button>
        </div>
      </div>
    </div>
  );
}

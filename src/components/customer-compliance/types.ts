
export interface CustomerDocument {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  additionalInfo?: string;
}

export interface AuditRecord {
  id: string;
  date: string;
  status: 'Passed' | 'Failed' | 'Minor Findings';
  notes?: string;
  capaStatus?: 'Open' | 'Closed' | 'Not Required';
}

export interface CapaItem {
  id: string;
  title: string;
  status: 'Open' | 'Closed';
  dueDate?: string;
  closedDate?: string;
}

export interface OtifDataPoint {
  month: string;
  value: number;
}

export interface CustomerData {
  id: string;
  name: string;
  logoUrl: string;
  status: string;
  lastAccessed: string;
  sharedDocuments: number;
  complianceScore: number;
  qaContact: string;
  industry: string;
  certifications: {
    name: string;
    status: string;
    expiry: string;
  }[];
  capas: CapaItem[];
  otifData: OtifDataPoint[];
}

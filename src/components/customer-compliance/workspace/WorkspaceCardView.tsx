
import React from "react";
import { Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, Users, FileText, Calendar, Star } from "lucide-react";

interface Customer {
  id: string;
  name: string;
  logoUrl?: string;
  status: 'Active' | 'Read-only' | 'Access Expired';
  lastAccessed: string;
  sharedDocuments: number;
  complianceScore: number;
  qaContact: string;
  industry: 'Aerospace' | 'Automotive' | 'Medical' | 'Defense' | 'Electronics' | 'Unknown';
  auditStatus: 'Passed' | 'Pending' | 'Failed' | 'Scheduled';
}

interface WorkspaceCardViewProps {
  customers: Customer[];
  formatDate: (dateString: string) => string;
  getStatusBadge: (status: string) => React.ReactNode;
  getAuditStatusBadge: (auditStatus: string) => React.ReactNode;
}

export const WorkspaceCardView: React.FC<WorkspaceCardViewProps> = ({
  customers,
  formatDate,
  getStatusBadge,
  getAuditStatusBadge
}) => {
  const getIndustryIcon = (industry: string) => {
    switch (industry) {
      case 'Aerospace': return '✈️';
      case 'Automotive': return '🚗';
      case 'Medical': return '⚕️';
      case 'Defense': return '🛡️';
      case 'Electronics': return '💻';
      default: return '🏢';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {customers.map((customer) => (
        <Card key={customer.id} className="hover:shadow-md transition-shadow cursor-pointer group">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                  {customer.logoUrl ? (
                    <img 
                      src={customer.logoUrl} 
                      alt={customer.name}
                      className="w-8 h-8 object-cover rounded"
                    />
                  ) : (
                    <span className="text-lg">{getIndustryIcon(customer.industry)}</span>
                  )}
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold">{customer.name}</CardTitle>
                  <p className="text-sm text-gray-600">{customer.industry}</p>
                </div>
              </div>
              {getStatusBadge(customer.status)}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">{customer.sharedDocuments} docs</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-gray-600">{customer.complianceScore}% score</span>
              </div>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">QA Contact:</span>
                <span className="font-medium">{customer.qaContact}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Accessed:</span>
                <span className="font-medium">{formatDate(customer.lastAccessed)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Audit Status:</span>
                {getAuditStatusBadge(customer.auditStatus)}
              </div>
            </div>
            
            <Link to={`/customer-workspace/${customer.id}`} className="block">
              <Button 
                variant="outline" 
                className="w-full group-hover:bg-blue-50 group-hover:border-blue-200 transition-colors"
              >
                Open Workspace
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

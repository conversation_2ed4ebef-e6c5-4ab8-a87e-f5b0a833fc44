
import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Filter, ChevronDown } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

interface WorkspaceSearchAndFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  view: string;
  setView: (view: string) => void;
  industryFilter: string[];
  auditFilter: string[];
  handleIndustryChange: (industry: string) => void;
  handleAuditStatusChange: (status: string) => void;
}

export const WorkspaceSearchAndFilters: React.FC<WorkspaceSearchAndFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  view,
  setView,
  industryFilter,
  auditFilter,
  handleIndustryChange,
  handleAuditStatusChange,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <Input 
            placeholder="Search by customer or contact..." 
            className="pl-9" 
            value={searchQuery} 
            onChange={(e) => setSearchQuery(e.target.value)} 
          />
        </div>
        
        {/* View Toggle */}
        <div>
          <Tabs value={view} onValueChange={setView} className="w-full">
            <TabsList className="grid grid-cols-2 w-[180px]">
              <TabsTrigger value="card" className="gap-1">
                <div className="grid grid-cols-2 gap-0.5">
                  <div className="w-2 h-2 rounded-sm bg-current"></div>
                  <div className="w-2 h-2 rounded-sm bg-current"></div>
                  <div className="w-2 h-2 rounded-sm bg-current"></div>
                  <div className="w-2 h-2 rounded-sm bg-current"></div>
                </div>
                Cards
              </TabsTrigger>
              <TabsTrigger value="list" className="gap-1">
                <div className="flex flex-col gap-0.5">
                  <div className="w-4 h-1 rounded-sm bg-current"></div>
                  <div className="w-4 h-1 rounded-sm bg-current"></div>
                  <div className="w-4 h-1 rounded-sm bg-current"></div>
                </div>
                List
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      {/* Filter Options */}
      <div className="mt-4 flex flex-wrap gap-2">
        {/* Industry Filter */}
        <div className="dropdown inline-block relative">
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-3.5 w-3.5" />
            Industry
            <ChevronDown className="h-3.5 w-3.5" />
          </Button>
          <div className="dropdown-menu absolute hidden bg-white mt-1 shadow-md rounded border z-10 p-2 w-48">
            <div className="flex flex-col gap-1">
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={industryFilter.includes("Aerospace")} 
                  onChange={() => handleIndustryChange("Aerospace")} 
                />
                Aerospace
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={industryFilter.includes("Electronics")} 
                  onChange={() => handleIndustryChange("Electronics")} 
                />
                Electronics
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={industryFilter.includes("Automotive")} 
                  onChange={() => handleIndustryChange("Automotive")} 
                />
                Automotive
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={industryFilter.includes("Medical")} 
                  onChange={() => handleIndustryChange("Medical")} 
                />
                Medical
              </label>
            </div>
          </div>
        </div>
        
        {/* Audit Status Filter */}
        <div className="dropdown inline-block relative">
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-3.5 w-3.5" />
            Audit Status
            <ChevronDown className="h-3.5 w-3.5" />
          </Button>
          <div className="dropdown-menu absolute hidden bg-white mt-1 shadow-md rounded border z-10 p-2 w-48">
            <div className="flex flex-col gap-1">
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={auditFilter.includes("Passed")} 
                  onChange={() => handleAuditStatusChange("Passed")} 
                />
                Passed
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={auditFilter.includes("Failed")} 
                  onChange={() => handleAuditStatusChange("Failed")} 
                />
                Failed
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={auditFilter.includes("Pending")} 
                  onChange={() => handleAuditStatusChange("Pending")} 
                />
                Pending
              </label>
              <label className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer">
                <input 
                  type="checkbox" 
                  checked={auditFilter.includes("Scheduled")} 
                  onChange={() => handleAuditStatusChange("Scheduled")} 
                />
                Scheduled
              </label>
            </div>
          </div>
        </div>
        
        {/* Active Filters Indicators */}
        {industryFilter.length > 0 && (
          <div className="flex items-center">
            <Badge variant="outline" className="bg-blue-50">
              Industries: {industryFilter.length}
            </Badge>
          </div>
        )}
        
        {auditFilter.length > 0 && (
          <div className="flex items-center">
            <Badge variant="outline" className="bg-blue-50">
              Audit Status: {auditFilter.length}
            </Badge>
          </div>
        )}
      </div>
    </div>
  );
};

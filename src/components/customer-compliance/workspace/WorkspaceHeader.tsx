
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface WorkspaceHeaderProps {
  onCreateWorkspace: () => void;
}

export const WorkspaceHeader: React.FC<WorkspaceHeaderProps> = ({ onCreateWorkspace }) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
      <div className="flex items-center gap-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild></TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <p className="text-sm">
                A secure portal where you share compliance documents, performance dashboards, and audit status with each customer.
                Tailored for customers like Boeing or GE to view only what you allow — no more spreadsheets or email threads.
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      
      <Button 
        className="gap-2 bg-teal-600 hover:bg-teal-700"
        onClick={onCreateWorkspace}
      >
        <Plus className="h-4 w-4" />
        New Workspace
      </Button>
    </div>
  );
};

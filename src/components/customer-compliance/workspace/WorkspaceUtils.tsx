
import React from "react";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, XCircle, Clock, AlertCircle } from "lucide-react";
import { CustomerStatus } from "@/data/mockCustomers";

// Helper function to format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Function to get status badge styling
export const getStatusBadge = (status: CustomerStatus) => {
  switch (status) {
    case "Active":
      return <Badge className="bg-green-500 text-white">Active</Badge>;
    case "Read-only":
      return <Badge className="bg-amber-500 text-white">Read-only</Badge>;
    case "Access Expired":
      return <Badge className="bg-red-500 text-white">Access Expired</Badge>;
    default:
      return <Badge>Unknown</Badge>;
  }
};

// Function to get audit status badge styling
export const getAuditStatusBadge = (auditStatus: string) => {
  switch (auditStatus) {
    case "Passed":
      return <Badge className="bg-green-500 text-white flex items-center gap-1"><CheckCircle2 className="h-3 w-3" /> Passed</Badge>;
    case "Failed":
      return <Badge className="bg-red-500 text-white flex items-center gap-1"><XCircle className="h-3 w-3" /> Failed</Badge>;
    case "Pending":
      return <Badge className="bg-amber-500 text-white flex items-center gap-1"><Clock className="h-3 w-3" /> Pending</Badge>;
    case "Scheduled":
      return <Badge className="bg-blue-500 text-white flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Scheduled</Badge>;
    default:
      return <Badge>Unknown</Badge>;
  }
};

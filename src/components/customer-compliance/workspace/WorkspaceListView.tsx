
import React from "react";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { FileText, BarChart4 } from "lucide-react";
import { CustomerStatus } from "@/data/mockCustomers";
import { ResizableTable } from "@/components/ui/resizable-table";
import { TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";

interface WorkspaceListViewProps {
  customers: Array<{
    id: string;
    name: string;
    status: CustomerStatus;
    logoUrl: string;
    lastAccessed: string;
    complianceScore: number;
    sharedDocuments: number;
    qaContact: string;
    industry: string;
    auditStatus: string;
  }>;
  formatDate: (dateString: string) => string;
  getStatusBadge: (status: CustomerStatus) => React.ReactNode;
  getAuditStatusBadge: (auditStatus: string) => React.ReactNode;
}

export const WorkspaceListView: React.FC<WorkspaceListViewProps> = ({
  customers,
  formatDate,
  getStatusBadge,
  getAuditStatusBadge
}) => {
  return (
    <div className="bg-white border rounded-lg shadow-sm overflow-hidden">
      <ResizableTable className="w-full" minColumnWidth={120}>
        <TableHeader>
          <TableRow className="bg-gray-50">
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Customer</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Status</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Last Accessed</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Score</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Documents</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Contact</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Industry</TableHead>
            <TableHead className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-700">Audit</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="divide-y divide-gray-200">
          {customers.map(customer => (
            <TableRow key={customer.id} className="hover:bg-gray-50">
              <TableCell className="px-6 py-4 text-sm">
                <Link to={`/customer-compliance/${customer.id}`} className="flex items-center gap-3">
                  <div className="h-8 w-8 bg-gray-100 rounded flex items-center justify-center">
                    <img src={customer.logoUrl} alt={`${customer.name} logo`} className="h-5 w-5" />
                  </div>
                  <span className="font-medium">{customer.name}</span>
                </Link>
              </TableCell>
              <TableCell className="px-6 py-4 text-sm">{getStatusBadge(customer.status)}</TableCell>
              <TableCell className="px-6 py-4 text-sm text-gray-500">{formatDate(customer.lastAccessed)}</TableCell>
              <TableCell className="px-6 py-4 text-sm">
                <div className="flex items-center gap-2">
                  <span className={`font-medium ${
                    customer.complianceScore >= 90 ? 'text-green-600' : 
                    customer.complianceScore >= 80 ? 'text-amber-600' : 
                    'text-red-600'
                  }`}>
                    {customer.complianceScore}%
                  </span>
                  <BarChart4 className="h-4 w-4 text-teal-600" />
                </div>
              </TableCell>
              <TableCell className="px-6 py-4 text-sm">
                <div className="flex items-center gap-1.5">
                  <FileText className="h-4 w-4 text-teal-600" /> 
                  <span>{customer.sharedDocuments}</span>
                </div>
              </TableCell>
              <TableCell className="px-6 py-4 text-sm">{customer.qaContact}</TableCell>
              <TableCell className="px-6 py-4 text-sm">
                <Badge className="bg-gray-100 text-gray-700">{customer.industry}</Badge>
              </TableCell>
              <TableCell className="px-6 py-4 text-sm">{getAuditStatusBadge(customer.auditStatus)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </ResizableTable>
    </div>
  );
};

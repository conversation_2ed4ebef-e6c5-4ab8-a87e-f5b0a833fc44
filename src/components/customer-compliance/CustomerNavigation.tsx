
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart4, 
  FileText, 
  CheckCircle2, 
  Calendar, 
  MessageSquare 
} from "lucide-react";

interface CustomerNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export function CustomerNavigation({ activeTab, setActiveTab }: CustomerNavigationProps) {
  return (
    <Card>
      <CardContent className="p-0">
        <Tabs 
          orientation="vertical" 
          value={activeTab} 
          onValueChange={setActiveTab} 
          className="w-full"
        >
          <TabsList className="flex flex-col items-stretch h-auto bg-transparent space-y-1 p-2">
            <TabsTrigger 
              value="overview" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <BarChart4 className="mr-2 h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger 
              value="documents" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <FileText className="mr-2 h-4 w-4" />
              Shared Documents
            </TabsTrigger>
            <TabsTrigger 
              value="capas" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <CheckCircle2 className="mr-2 h-4 w-4" />
              CAPAs
            </TabsTrigger>
            <TabsTrigger 
              value="audits" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <Calendar className="mr-2 h-4 w-4" />
              Audit History
            </TabsTrigger>
            <TabsTrigger 
              value="performance" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <BarChart4 className="mr-2 h-4 w-4" />
              Performance
            </TabsTrigger>
            <TabsTrigger 
              value="metrics" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <BarChart4 className="mr-2 h-4 w-4" />
              Metrics Dashboard
            </TabsTrigger>
            <TabsTrigger 
              value="messages" 
              className="justify-start text-left px-4 py-3 data-[state=active]:bg-teal-100 data-[state=active]:text-teal-800"
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Messages
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardContent>
    </Card>
  );
}

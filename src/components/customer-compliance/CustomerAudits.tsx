
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Calendar } from "lucide-react";
import { formatDate } from "./utils/dateFormatters";
import { getAuditStatusBadge, getCapaStatusBadge } from "./utils/badgeUtils";
import { AuditRecord } from "./types";

interface CustomerAuditsProps {
  auditHistory: AuditRecord[];
}

export function CustomerAudits({ auditHistory }: CustomerAuditsProps) {
  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle className="text-lg">Complete Audit History</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="divide-y">
          {auditHistory.map((audit) => (
            <div key={audit.id} className="py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <p className="font-medium">{formatDate(audit.date)}</p>
                </div>
                {getAuditStatusBadge(audit.status)}
              </div>
              {audit.notes && (
                <p className="text-sm text-gray-500 mt-2 pl-7">{audit.notes}</p>
              )}
              {audit.capaStatus && (
                <div className="mt-2 pl-7">
                  <span className="text-sm">CAPA Status: </span> 
                  {getCapaStatusBadge(audit.capaStatus)}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

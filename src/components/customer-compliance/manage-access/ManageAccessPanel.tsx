
import React, { useState } from "react";
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, Sheet<PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserPlus } from "lucide-react";
import { UserTable } from "./UserTable";
import { InviteUserDialog } from "./InviteUserDialog";
import { ManageAccessPanelProps, AccessUser } from "./types";

export function ManageAccessPanel({ isOpen, onClose, customerName }: ManageAccessPanelProps) {
  const [users, setUsers] = useState<AccessUser[]>([
    {
      id: "1",
      name: "<PERSON>",
      email: "Jane.<PERSON>@boeing.com",
      role: "Viewer",
      lastAccess: "2025-05-19T14:23:00",
      expires: true,
      expiryDate: "2025-06-20",
      status: "Active"
    },
    {
      id: "2",
      name: "<PERSON><PERSON>",
      email: "<PERSON><PERSON>.<PERSON>@boeing.com",
      role: "Audit Reviewer",
      lastAccess: "2025-05-10T09:15:00",
      expires: false,
      expiryDate: null,
      status: "Active"
    },
    {
      id: "3",
      name: "<PERSON> QA",
      email: "<EMAIL>",
      role: "Commenter",
      lastAccess: "2025-04-01T11:30:00",
      expires: true,
      expiryDate: "2025-04-30",
      status: "Revoked"
    }
  ]);

  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserRole, setNewUserRole] = useState<AccessUser["role"]>("Viewer");
  const [newUserExpires, setNewUserExpires] = useState(true);
  const [newUserExpiryDate, setNewUserExpiryDate] = useState("2025-06-20");

  const handleRevokeAccess = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId ? { 
        ...user, 
        status: user.status === "Active" ? "Revoked" : "Active" 
      } : user
    ));
  };

  const handleUpdateRole = (userId: string, newRole: AccessUser["role"]) => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, role: newRole } : user
    ));
  };

  const handleToggleExpiry = (userId: string, expires: boolean) => {
    setUsers(users.map(user => 
      user.id === userId ? { 
        ...user, 
        expires,
        expiryDate: expires ? "2025-06-20" : null
      } : user
    ));
  };

  const handleInviteUser = () => {
    if (!newUserEmail) return;
    
    const newUser: AccessUser = {
      id: `${users.length + 1}`,
      name: newUserEmail.split('@')[0].replace('.', ' '),
      email: newUserEmail,
      role: newUserRole,
      lastAccess: null,
      expires: newUserExpires,
      expiryDate: newUserExpires ? newUserExpiryDate : null,
      status: "Active"
    };

    setUsers([...users, newUser]);
    setInviteDialogOpen(false);
    setNewUserEmail("");
    setNewUserRole("Viewer");
    setNewUserExpires(true);
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl p-0 overflow-y-auto">
          <SheetHeader className="p-6 border-b">
            <SheetTitle className="text-xl">Manage Access for {customerName}</SheetTitle>
          </SheetHeader>
          
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <div className="text-sm text-gray-500">
                {users.filter(u => u.status === "Active").length} active users
              </div>
              <Button className="gap-2" onClick={() => setInviteDialogOpen(true)}>
                <UserPlus className="h-4 w-4" />
                Invite New User
              </Button>
            </div>
            
            <UserTable 
              users={users}
              onRevokeAccess={handleRevokeAccess}
              onUpdateRole={handleUpdateRole}
              onToggleExpiry={handleToggleExpiry}
            />
          </div>

          <SheetFooter className="p-6 border-t">
            <Button variant="outline" onClick={onClose}>Done</Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>

      <InviteUserDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        onInviteUser={handleInviteUser}
        newUserEmail={newUserEmail}
        setNewUserEmail={setNewUserEmail}
        newUserRole={newUserRole}
        setNewUserRole={setNewUserRole}
        newUserExpires={newUserExpires}
        setNewUserExpires={setNewUserExpires}
        newUserExpiryDate={newUserExpiryDate}
        setNewUserExpiryDate={setNewUserExpiryDate}
      />
    </>
  );
}

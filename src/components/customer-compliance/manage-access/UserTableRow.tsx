
import React from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Clock, User } from "lucide-react";
import { AccessUser } from "./types";
import { formatDate, formatLastAccess } from "../utils/dateFormatters";

interface UserTableRowProps {
  user: AccessUser;
  onRevokeAccess: (userId: string) => void;
  onUpdateRole: (userId: string, newRole: AccessUser["role"]) => void;
  onToggleExpiry: (userId: string, expires: boolean) => void;
}

export const UserTableRow: React.FC<UserTableRowProps> = ({
  user,
  onRevokeAccess,
  onUpdateRole,
  onToggleExpiry
}) => {
  const getRoleBadgeColor = (role: AccessUser["role"]) => {
    switch (role) {
      case "Admin":
        return "bg-red-100 text-red-800 border-red-200";
      case "Editor":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Audit Reviewer":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "Commenter":
        return "bg-amber-100 text-amber-800 border-amber-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusBadge = (status: AccessUser["status"]) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-500">Active</Badge>;
      case "Revoked":
        return <Badge className="bg-red-500">Revoked</Badge>;
      case "Expired":
        return <Badge className="bg-gray-500">Expired</Badge>;
      default:
        return null;
    }
  };

  return (
    <TableRow>
      <TableCell>
        <div className="flex flex-col">
          <span className="font-medium">{user.name}</span>
          <span className="text-gray-500 text-sm">{user.email}</span>
          <div className="mt-1">
            {getStatusBadge(user.status)}
          </div>
        </div>
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild disabled={user.status !== "Active"}>
            <Button variant="outline" className={`text-sm h-8 ${getRoleBadgeColor(user.role)} border`}>
              {user.role}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="bg-white">
            <DropdownMenuItem onClick={() => onUpdateRole(user.id, "Viewer")}>
              Viewer
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onUpdateRole(user.id, "Commenter")}>
              Commenter
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onUpdateRole(user.id, "Editor")}>
              Editor
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onUpdateRole(user.id, "Audit Reviewer")}>
              Audit Reviewer
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onUpdateRole(user.id, "Admin")}>
              Admin
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
      <TableCell className="text-sm">
        {formatLastAccess(user.lastAccess)}
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <Switch 
            checked={user.expires}
            disabled={user.status !== "Active"}
            onCheckedChange={(checked) => onToggleExpiry(user.id, checked)}
          />
          <div className="text-sm">
            {user.expires ? (
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" /> 
                {formatDate(user.expiryDate)}
              </span>
            ) : (
              "No expiry"
            )}
          </div>
        </div>
      </TableCell>
      <TableCell>
        {user.status === "Active" ? (
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-red-600 hover:text-red-800 hover:bg-red-50"
            onClick={() => onRevokeAccess(user.id)}
          >
            Revoke
          </Button>
        ) : (
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-green-600 hover:text-green-800 hover:bg-green-50"
            onClick={() => onRevokeAccess(user.id)}
          >
            Restore
          </Button>
        )}
      </TableCell>
    </TableRow>
  );
};


import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { UserTableRow } from "./UserTableRow";
import { AccessUser } from "./types";

interface UserTableProps {
  users: AccessUser[];
  onRevokeAccess: (userId: string) => void;
  onUpdateRole: (userId: string, newRole: AccessUser["role"]) => void;
  onToggleExpiry: (userId: string, expires: boolean) => void;
}

export const UserTable: React.FC<UserTableProps> = ({
  users,
  onRevokeAccess,
  onUpdateRole,
  onToggleExpiry
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[250px]">User</TableHead>
            <TableHead className="w-[150px]">Role</TableHead>
            <TableHead className="w-[100px]">Last Access</TableHead>
            <TableHead className="w-[200px]">Expiry</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="divide-y divide-gray-100">
          {users.map((user, index) => (
            <UserTableRow 
              key={user.id}
              user={user}
              onRevokeAccess={onRevokeAccess}
              onUpdateRole={onUpdateRole}
              onToggleExpiry={onToggleExpiry}
            />
          ))}
          {users.length === 0 && (
            <tr>
              <td colSpan={5} className="py-6 text-center text-gray-500">
                No users found
              </td>
            </tr>
          )}
        </TableBody>
      </Table>
    </div>
  );
};


import React from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, Di<PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { AccessUser } from "./types";

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInviteUser: () => void;
  newUserEmail: string;
  setNewUserEmail: (email: string) => void;
  newUserRole: AccessUser["role"];
  setNewUserRole: (role: AccessUser["role"]) => void;
  newUserExpires: boolean;
  setNewUserExpires: (expires: boolean) => void;
  newUserExpiryDate: string;
  setNewUserExpiryDate: (date: string) => void;
}

export const InviteUserDialog: React.FC<InviteUserDialogProps> = ({
  open,
  onOpenChange,
  onInviteUser,
  newUserEmail,
  setNewUserEmail,
  newUserRole,
  setNewUserRole,
  newUserExpires,
  setNewUserExpires,
  newUserExpiryDate,
  setNewUserExpiryDate
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Invite New User</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">Email Address</label>
            <Input 
              id="email" 
              placeholder="<EMAIL>" 
              value={newUserEmail} 
              onChange={(e) => setNewUserEmail(e.target.value)} 
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="role" className="text-sm font-medium">Role</label>
            <Select value={newUserRole} onValueChange={(val) => setNewUserRole(val as AccessUser["role"])}>
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Viewer">Viewer</SelectItem>
                <SelectItem value="Commenter">Commenter</SelectItem>
                <SelectItem value="Editor">Editor</SelectItem>
                <SelectItem value="Audit Reviewer">Audit Reviewer</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label htmlFor="expiry" className="text-sm font-medium">Access Expires</label>
              <Switch id="expiry" checked={newUserExpires} onCheckedChange={setNewUserExpires} />
            </div>
            
            {newUserExpires && (
              <div className="pt-2">
                <Input 
                  type="date" 
                  value={newUserExpiryDate} 
                  onChange={(e) => setNewUserExpiryDate(e.target.value)}
                />
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={onInviteUser}>Invite User</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

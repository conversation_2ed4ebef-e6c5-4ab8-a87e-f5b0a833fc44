
import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { OtifDataPoint } from "./types";

interface CustomerPerformanceProps {
  otifData: OtifDataPoint[];
}

export function CustomerPerformance({ otifData }: CustomerPerformanceProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">OTIF Performance</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-2xl font-bold text-green-600">96%</div>
          <p className="text-sm text-gray-500">Average for last 6 months</p>
          
          <div className="mt-8 h-64 flex items-end gap-2">
            {otifData.map((item, i) => (
              <div key={i} className="flex flex-col items-center flex-1">
                <div className="text-sm mb-2">{item.value}%</div>
                <div 
                  className="w-full bg-blue-500 rounded-t"
                  style={{ height: `${item.value * 2}px` }}
                ></div>
                <div className="text-xs mt-2 text-gray-600">{item.month}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">Quality Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-500">Defect Rate</div>
              <div className="text-2xl font-bold text-green-600">0.2%</div>
              <p className="text-xs text-gray-500">Last 6 months</p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-500">First Pass Yield</div>
              <div className="text-2xl font-bold text-green-600">98.5%</div>
              <p className="text-xs text-gray-500">Last 6 months</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

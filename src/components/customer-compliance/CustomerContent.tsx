
import React from 'react';
import { CustomerOverview } from './CustomerOverview';
import { CustomerDocuments } from './CustomerDocuments';
import { CustomerCapas } from './CustomerCapas';
import { CustomerAudits } from './CustomerAudits';
import { CustomerPerformance } from './CustomerPerformance';
import { CustomerMessages } from './CustomerMessages';
import { CustomerPerformanceDashboard } from './CustomerPerformanceDashboard';
import { CustomerDocument, AuditRecord, CapaItem, OtifDataPoint } from './types';

interface CustomerContentProps {
  activeTab: string;
  recentDocuments: CustomerDocument[];
  auditHistory: AuditRecord[];
  otifData: OtifDataPoint[];
  sharedDocuments: CustomerDocument[];
  capas: CapaItem[];
}

export function CustomerContent({
  activeTab,
  recentDocuments,
  auditHistory,
  otifData,
  sharedDocuments,
  capas
}: CustomerContentProps) {
  // Mock data for the performance dashboard
  const ncrRateData = [
    { month: 'Jan', value: 2.0 },
    { month: 'Feb', value: 1.5 },
    { month: 'Mar', value: 3.0 },
    { month: 'Apr', value: 2.2 },
    { month: 'May', value: 1.8 },
    { month: 'Jun', value: 1.3 }
  ];
  
  const capaAgingData = [
    { name: 'Closed', value: 60, color: '#22c55e' },
    { name: 'In Progress', value: 30, color: '#f59e0b' },
    { name: 'Overdue', value: 10, color: '#ef4444' }
  ];
  
  const auditOutcomeData = [
    { date: 'Jan 15', status: 'Pass', color: '#22c55e' },
    { date: 'Mar 22', status: 'Minor', color: '#f59e0b' },
    { date: 'May 10', status: 'Pass', color: '#22c55e' },
    { date: 'Jul 12', status: 'Minor', color: '#f59e0b' },
    { date: 'Sep 05', status: 'Major', color: '#ef4444' },
    { date: 'Nov 30', status: 'Pass', color: '#22c55e' },
  ];

  return (
    <>
      {activeTab === "overview" && (
        <CustomerOverview 
          recentDocuments={recentDocuments} 
          auditHistory={auditHistory}
          otifData={otifData}
        />
      )}
      
      {activeTab === "documents" && (
        <CustomerDocuments sharedDocuments={sharedDocuments} />
      )}
      
      {activeTab === "capas" && (
        <CustomerCapas capas={capas} />
      )}
      
      {activeTab === "audits" && (
        <CustomerAudits auditHistory={auditHistory} />
      )}
      
      {activeTab === "performance" && (
        <CustomerPerformance otifData={otifData} />
      )}
      
      {activeTab === "metrics" && (
        <CustomerPerformanceDashboard 
          otifData={otifData}
          ncrRateData={ncrRateData}
          capaAgingData={capaAgingData}
          auditOutcomeData={auditOutcomeData}
        />
      )}
      
      {activeTab === "messages" && (
        <CustomerMessages />
      )}
    </>
  );
}

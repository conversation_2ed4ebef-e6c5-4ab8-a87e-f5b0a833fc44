
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatDate } from "./utils/dateFormatters";
import { getCapaStatusBadge } from "./utils/badgeUtils";
import { CapaItem } from "./types";

interface CustomerCapasProps {
  capas: CapaItem[];
}

export function CustomerCapas({ capas }: CustomerCapasProps) {
  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle className="text-lg">Corrective and Preventive Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="divide-y">
          {capas.map((capa) => (
            <div key={capa.id} className="py-4 flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm">{capa.id}</span>
                  {getCapaStatusBadge(capa.status)}
                </div>
                <p className="font-medium mt-1">{capa.title}</p>
              </div>
              <div className="text-sm text-gray-500">
                {capa.status === "Open" ? (
                  <>Due: {formatDate(capa.dueDate!)}</>
                ) : (
                  <>Closed: {formatDate(capa.closedDate!)}</>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

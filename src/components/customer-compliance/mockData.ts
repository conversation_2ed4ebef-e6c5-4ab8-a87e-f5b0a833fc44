
import { CustomerData, CustomerDocument, AuditRecord } from './types';

// Mock customer data for Boeing
export const boeingData: CustomerData = {
  id: "boeing",
  name: "Boeing",
  logoUrl: "/placeholder.svg",
  status: "Active",
  lastAccessed: "2025-05-19",
  sharedDocuments: 42,
  complianceScore: 92,
  qaContact: "<PERSON> - QA Lead",
  industry: "Aerospace",
  certifications: [
    { name: "ISO 9001", status: "Valid", expiry: "2026-04-15" },
    { name: "AS9100", status: "Valid", expiry: "2025-12-30" },
    { name: "NADCAP", status: "Valid", expiry: "2025-09-18" }
  ],
  capas: [
    { id: "CAPA-2025-003", title: "Process Control Deviation", status: "Open", dueDate: "2025-06-30" },
    { id: "CAPA-2025-001", title: "Document Control Finding", status: "Closed", closedDate: "2025-04-01" }
  ],
  otifData: [
    { month: "Dec", value: 94 },
    { month: "Jan", value: 96 },
    { month: "Feb", value: 95 },
    { month: "Mar", value: 97 },
    { month: "Apr", value: 98 },
    { month: "May", value: 96 }
  ]
};

// Mock recent documents
export const recentDocuments: CustomerDocument[] = [
  {
    id: "doc-001",
    name: "ISO 9001 Certificate",
    type: "PDF",
    uploadDate: "2025-03-01",
    additionalInfo: "Valid until 2026-04-15"
  },
  {
    id: "doc-002",
    name: "Supplier Quality Manual",
    type: "PDF",
    uploadDate: "2025-02-15",
    additionalInfo: "12 pages"
  },
  {
    id: "doc-003",
    name: "CAPA Summary Q1 2025",
    type: "XLSX",
    uploadDate: "2025-04-05"
  }
];

// Mock audit history
export const auditHistory: AuditRecord[] = [
  {
    id: "audit-002",
    date: "2025-01-15",
    status: "Passed",
    notes: "No findings"
  },
  {
    id: "audit-001",
    date: "2024-07-22",
    status: "Minor Findings",
    notes: "Documentation issues found in process control area",
    capaStatus: "Open"
  }
];

// Mock shared documents
export const sharedDocuments: CustomerDocument[] = [
  ...recentDocuments,
  {
    id: "doc-004",
    name: "First Article Inspection Report",
    type: "PDF",
    uploadDate: "2025-01-10"
  },
  {
    id: "doc-005",
    name: "Material Certification",
    type: "PDF",
    uploadDate: "2025-02-25"
  },
  {
    id: "doc-006",
    name: "Process Control Data",
    type: "XLSX",
    uploadDate: "2025-03-15"
  }
];

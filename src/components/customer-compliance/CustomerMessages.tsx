
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { MessageSquare, Send, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock messages data
const mockMessages = [
  {
    id: "1",
    subject: "AS9100 Certificate Expiry",
    from: "Jane <PERSON>",
    fromCompany: "Boeing QA",
    date: "2024-01-20",
    status: "unread",
    body: "Please provide an updated AS9100 certificate as the current one expires on March 30, 2024. We need this for our supplier qualification review.",
    priority: "high"
  },
  {
    id: "2",
    subject: "CAPA-2024-032 Update Request",
    from: "<PERSON>",
    fromCompany: "Boeing Sourcing",
    date: "2024-01-18",
    status: "read",
    body: "Can you provide a status update on the corrective actions for the calibration records issue identified in our last audit?",
    priority: "medium"
  },
  {
    id: "3",
    subject: "Q1 2024 Performance Review",
    from: "<PERSON>",
    fromCompany: "Boeing QA",
    date: "2024-01-15",
    status: "read",
    body: "Your Q1 performance metrics show excellent improvement. We'd like to schedule a review meeting to discuss potential expanded opportunities.",
    priority: "low"
  }
];

export function CustomerMessages() {
  const [messages, setMessages] = useState(mockMessages);
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [newMessage, setNewMessage] = useState({ subject: "", body: "" });
  const { toast } = useToast();

  const handleMessageClick = (message: any) => {
    setSelectedMessage(message);
    if (message.status === "unread") {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === message.id ? { ...msg, status: "read" } : msg
        )
      );
    }
  };

  const handleSendMessage = () => {
    if (!newMessage.subject.trim() || !newMessage.body.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both subject and message body",
        variant: "destructive"
      });
      return;
    }

    const message = {
      id: `msg-${Date.now()}`,
      subject: newMessage.subject,
      from: "You",
      fromCompany: "Your Company",
      date: new Date().toISOString().split('T')[0],
      status: "sent",
      body: newMessage.body,
      priority: "medium"
    };

    setMessages(prev => [message, ...prev]);
    setNewMessage({ subject: "", body: "" });
    setIsComposing(false);
    
    toast({
      title: "Message Sent",
      description: "Your message has been sent successfully",
    });
  };

  const unreadCount = messages.filter(msg => msg.status === "unread").length;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Messages List */}
      <Card>
        <CardHeader className="border-b flex flex-row items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Messages {unreadCount > 0 && <Badge variant="destructive">{unreadCount}</Badge>}
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsComposing(true)}
          >
            <Plus className="h-4 w-4 mr-1" />
            New Message
          </Button>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2">No messages to display</p>
                <p className="text-sm">Start a conversation with your customer's team</p>
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`p-4 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
                    message.status === "unread" ? "bg-blue-50 border-l-4 border-l-blue-500" : ""
                  } ${selectedMessage?.id === message.id ? "bg-gray-100" : ""}`}
                  onClick={() => handleMessageClick(message)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-medium text-sm ${message.status === "unread" ? "font-semibold" : ""}`}>
                      {message.subject}
                    </h4>
                    <div className="flex items-center gap-2">
                      {message.priority === "high" && (
                        <Badge variant="destructive" className="text-xs">High</Badge>
                      )}
                      {message.status === "unread" && (
                        <Badge variant="default" className="text-xs">New</Badge>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{message.body}</p>
                  <div className="flex justify-between items-center text-xs text-gray-500 mt-2">
                    <span>From: {message.from} ({message.fromCompany})</span>
                    <span>{message.date}</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Message Detail or Compose */}
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">
            {isComposing ? "Compose New Message" : selectedMessage ? "Message Details" : "Select a Message"}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          {isComposing ? (
            <div className="space-y-4">
              <Input
                placeholder="Subject"
                value={newMessage.subject}
                onChange={(e) => setNewMessage(prev => ({ ...prev, subject: e.target.value }))}
              />
              <Textarea
                placeholder="Type your message here..."
                className="min-h-[200px]"
                value={newMessage.body}
                onChange={(e) => setNewMessage(prev => ({ ...prev, body: e.target.value }))}
              />
              <div className="flex gap-2">
                <Button onClick={handleSendMessage} className="gap-2">
                  <Send className="h-4 w-4" />
                  Send Message
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsComposing(false);
                    setNewMessage({ subject: "", body: "" });
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : selectedMessage ? (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{selectedMessage.subject}</h3>
                <div className="text-sm text-gray-600 mt-1">
                  From: {selectedMessage.from} ({selectedMessage.fromCompany})
                </div>
                <div className="text-sm text-gray-500">
                  Date: {selectedMessage.date}
                </div>
              </div>
              <div className="border-t pt-4">
                <p className="text-gray-800 whitespace-pre-wrap">{selectedMessage.body}</p>
              </div>
              <div className="border-t pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsComposing(true);
                    setNewMessage({
                      subject: `Re: ${selectedMessage.subject}`,
                      body: `\n\n--- Original Message ---\nFrom: ${selectedMessage.from}\nDate: ${selectedMessage.date}\n\n${selectedMessage.body}`
                    });
                  }}
                  className="gap-2"
                >
                  <MessageSquare className="h-4 w-4" />
                  Reply
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2">Select a message to view details</p>
              <p className="text-sm">or compose a new message</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

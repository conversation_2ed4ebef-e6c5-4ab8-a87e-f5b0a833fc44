
import React from "react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { CalendarIcon, FileText } from "lucide-react";

interface SharingData {
  documents: Array<{
    id: string;
    name: string;
    type: string;
    selected: boolean;
  }>;
  accessLevel: "viewer" | "commenter" | "reviewer";
  expirationDate: Date | null;
}

interface InitialSharingStepProps {
  data: SharingData;
  onChange: (data: SharingData) => void;
}

export const InitialSharingStep: React.FC<InitialSharingStepProps> = ({
  data,
  onChange,
}) => {
  const handleAccessLevelChange = (value: string) => {
    onChange({
      ...data,
      accessLevel: value as "viewer" | "commenter" | "reviewer",
    });
  };

  const handleDocumentToggle = (docId: string) => {
    const updatedDocuments = data.documents.map((doc) =>
      doc.id === docId ? { ...doc, selected: !doc.selected } : doc
    );
    onChange({
      ...data,
      documents: updatedDocuments,
    });
  };

  const handleExpirationDateChange = (date: Date | undefined) => {
    onChange({
      ...data,
      expirationDate: date || null,
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium">Initial Sharing Setup</h3>
        <p className="text-gray-500 text-sm">
          Select initial documents to share and set access permissions
        </p>
      </div>

      <div className="space-y-6">
        {/* Document Selection */}
        <div className="space-y-3">
          <Label>Select Documents to Share</Label>
          {data.documents.map((doc) => (
            <div
              key={doc.id}
              className={`flex items-center justify-between p-3 border rounded-md ${
                doc.selected ? "border-blue-500 bg-blue-50" : ""
              }`}
              onClick={() => handleDocumentToggle(doc.id)}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gray-100 rounded">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">{doc.name}</p>
                  <p className="text-xs text-gray-500">{doc.type}</p>
                </div>
              </div>
              <Checkbox
                checked={doc.selected}
                onCheckedChange={() => handleDocumentToggle(doc.id)}
              />
            </div>
          ))}
        </div>

        {/* Access Permissions */}
        <div className="space-y-2">
          <Label htmlFor="accessLevel">Access Permissions</Label>
          <Select
            value={data.accessLevel}
            onValueChange={handleAccessLevelChange}
          >
            <SelectTrigger id="accessLevel">
              <SelectValue placeholder="Select access level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="viewer">Viewer</SelectItem>
              <SelectItem value="commenter">Commenter</SelectItem>
              <SelectItem value="reviewer">Audit Reviewer</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500 mt-1">
            {data.accessLevel === "viewer"
              ? "Viewers can only view shared content"
              : data.accessLevel === "commenter"
              ? "Commenters can view content and add comments"
              : "Reviewers can view content, add comments, and approve audit findings"}
          </p>
        </div>

        {/* Expiration Date */}
        <div className="space-y-2">
          <Label htmlFor="expirationDate">Optional Expiration Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={`w-full justify-start text-left font-normal ${
                  !data.expirationDate ? "text-gray-500" : ""
                }`}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {data.expirationDate ? (
                  format(data.expirationDate, "PPP")
                ) : (
                  <span>No expiration</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={data.expirationDate || undefined}
                onSelect={handleExpirationDateChange}
                initialFocus
                disabled={(date) => date < new Date()}
              />
            </PopoverContent>
          </Popover>
          <p className="text-xs text-gray-500 mt-1">
            Set a date when access will expire, or leave blank for no expiration
          </p>
        </div>
      </div>
    </div>
  );
};


import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>ext, Clipboard<PERSON>ist, FileCheck, BarChart4, MessageSquare } from "lucide-react";

interface ComplianceModulesData {
  documents: boolean;
  capas: boolean;
  audits: boolean;
  performance: boolean;
  messages: boolean;
}

interface ComplianceModulesStepProps {
  data: ComplianceModulesData;
  onChange: (data: ComplianceModulesData) => void;
}

export const ComplianceModulesStep: React.FC<ComplianceModulesStepProps> = ({
  data,
  onChange,
}) => {
  const handleCheckboxChange = (key: keyof ComplianceModulesData) => {
    onChange({
      ...data,
      [key]: !data[key],
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium">Select Compliance Modules to Share</h3>
        <p className="text-gray-500 text-sm">
          Choose which compliance modules you want to share with this customer
        </p>
      </div>

      <div className="space-y-5">
        <ModuleCard
          icon={<FileText className="h-6 w-6 text-blue-600" />}
          title="Certifications & Documents"
          description="Share certificates, policies, and other compliance documents"
          checked={data.documents}
          onChange={() => handleCheckboxChange("documents")}
        />

        <ModuleCard
          icon={<ClipboardList className="h-6 w-6 text-green-600" />}
          title="CAPAs"
          description="Share corrective and preventive action reports"
          checked={data.capas}
          onChange={() => handleCheckboxChange("capas")}
        />

        <ModuleCard
          icon={<FileCheck className="h-6 w-6 text-amber-600" />}
          title="Audit History"
          description="Share audit reports, findings and resolutions"
          checked={data.audits}
          onChange={() => handleCheckboxChange("audits")}
        />

        <ModuleCard
          icon={<BarChart4 className="h-6 w-6 text-purple-600" />}
          title="Performance Metrics"
          description="Share OTIF, NCR trends and other KPIs"
          checked={data.performance}
          onChange={() => handleCheckboxChange("performance")}
        />

        <ModuleCard
          icon={<MessageSquare className="h-6 w-6 text-gray-600" />}
          title="Messages (optional)"
          description="Allow direct messaging with customer contacts"
          checked={data.messages}
          onChange={() => handleCheckboxChange("messages")}
        />
      </div>
    </div>
  );
};

interface ModuleCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  checked: boolean;
  onChange: () => void;
}

const ModuleCard: React.FC<ModuleCardProps> = ({
  icon,
  title,
  description,
  checked,
  onChange,
}) => {
  return (
    <div
      className={`flex items-start gap-4 p-4 border rounded-md cursor-pointer transition-colors ${
        checked ? "border-blue-500 bg-blue-50" : "border-gray-200"
      }`}
      onClick={onChange}
    >
      <div className="flex-shrink-0 pt-0.5">{icon}</div>
      <div className="flex-grow">
        <div className="flex justify-between">
          <Label className="font-medium cursor-pointer">{title}</Label>
          <Checkbox checked={checked} onCheckedChange={() => onChange()} />
        </div>
        <p className="text-gray-500 text-sm mt-1">{description}</p>
      </div>
    </div>
  );
};

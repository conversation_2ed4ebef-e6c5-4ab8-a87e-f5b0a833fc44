
import React from "react";
import { cn } from "@/lib/utils";

interface StepIndicatorProps {
  steps: string[];
  currentStep: number;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
}) => {
  return (
    <div className="flex items-center justify-center space-x-2 mt-4">
      {steps.map((step, index) => (
        <React.Fragment key={step}>
          {/* Step circle */}
          <div
            className={cn(
              "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors",
              currentStep > index + 1
                ? "bg-blue-600 text-white" // completed step
                : currentStep === index + 1
                ? "bg-blue-600 text-white" // current step
                : "bg-gray-200 text-gray-500" // future step
            )}
          >
            {currentStep > index + 1 ? "✓" : index + 1}
          </div>

          {/* Step label */}
          <span
            className={cn(
              "text-sm hidden sm:inline transition-colors",
              currentStep === index + 1
                ? "text-gray-900 font-medium"
                : "text-gray-500"
            )}
          >
            {step}
          </span>

          {/* Connector line */}
          {index < steps.length - 1 && (
            <div
              className={cn(
                "flex-grow h-0.5 max-w-[40px] transition-colors",
                currentStep > index + 1 ? "bg-blue-600" : "bg-gray-200"
              )}
            ></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};


import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload } from "lucide-react";

interface CustomerDetailsData {
  companyName: string;
  industry: string;
  contactName: string;
  contactEmail: string;
  logo?: File | null;
  logoPreview?: string;
}

interface CustomerDetailsStepProps {
  data: CustomerDetailsData;
  onChange: (data: CustomerDetailsData) => void;
}

export const CustomerDetailsStep: React.FC<CustomerDetailsStepProps> = ({
  data,
  onChange,
}) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        onChange({
          ...data,
          logo: file,
          logoPreview: reader.result as string,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium">Customer Details</h3>
        <p className="text-gray-500 text-sm">
          Enter basic information about the customer you're creating a workspace for
        </p>
      </div>

      <div className="space-y-4">
        {/* Company Name */}
        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name *</Label>
          <Input
            id="companyName"
            placeholder="e.g., Boeing"
            value={data.companyName}
            onChange={(e) =>
              onChange({ ...data, companyName: e.target.value })
            }
            required
          />
        </div>

        {/* Industry */}
        <div className="space-y-2">
          <Label htmlFor="industry">Industry *</Label>
          <Select
            value={data.industry}
            onValueChange={(value) => onChange({ ...data, industry: value })}
          >
            <SelectTrigger id="industry">
              <SelectValue placeholder="Select industry" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Aerospace">Aerospace</SelectItem>
              <SelectItem value="Electronics">Electronics</SelectItem>
              <SelectItem value="Automotive">Automotive</SelectItem>
              <SelectItem value="Medical">Medical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Contact Name */}
        <div className="space-y-2">
          <Label htmlFor="contactName">Primary Contact Name *</Label>
          <Input
            id="contactName"
            placeholder="e.g., Jane Doe"
            value={data.contactName}
            onChange={(e) => onChange({ ...data, contactName: e.target.value })}
            required
          />
        </div>

        {/* Contact Email */}
        <div className="space-y-2">
          <Label htmlFor="contactEmail">Primary Contact Email *</Label>
          <Input
            id="contactEmail"
            type="email"
            placeholder="e.g., <EMAIL>"
            value={data.contactEmail}
            onChange={(e) =>
              onChange({ ...data, contactEmail: e.target.value })
            }
            required
          />
        </div>

        {/* Logo Upload */}
        <div className="space-y-2">
          <Label htmlFor="logo">Customer Logo (optional)</Label>
          <div className="flex flex-col items-center justify-center gap-4">
            {data.logoPreview ? (
              <div className="relative">
                <img
                  src={data.logoPreview}
                  alt="Logo preview"
                  className="w-32 h-32 object-contain border rounded-md"
                />
                <button
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center text-xs"
                  onClick={() => onChange({ ...data, logo: null, logoPreview: "" })}
                  type="button"
                >
                  ×
                </button>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center gap-2 w-full">
                <Upload className="h-12 w-12 text-gray-400" />
                <div className="text-center">
                  <p className="text-sm text-gray-600">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">
                    PNG, JPG or SVG (max. 2MB)
                  </p>
                </div>
                <input
                  id="logo"
                  type="file"
                  className="hidden"
                  accept="image/png,image/jpeg,image/svg+xml"
                  onChange={handleFileChange}
                />
                <label
                  htmlFor="logo"
                  className="mt-2 cursor-pointer bg-blue-50 text-blue-700 text-sm font-medium py-1 px-3 rounded-md hover:bg-blue-100"
                >
                  Choose file
                </label>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

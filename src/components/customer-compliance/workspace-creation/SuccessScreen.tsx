
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle2, FileText, User, Calendar, Settings } from "lucide-react";
import { WorkspaceFormData } from "./CreateWorkspaceModal";

interface SuccessScreenProps {
  data: WorkspaceFormData;
  onGoToWorkspace: () => void;
  onCreateAnother: () => void;
}

export const SuccessScreen: React.FC<SuccessScreenProps> = ({
  data,
  onGoToWorkspace,
  onCreateAnother,
}) => {
  const selectedDocuments = data.sharing.documents.filter(
    (doc) => doc.selected
  );

  return (
    <div className="space-y-8 py-4">
      <div className="flex flex-col items-center justify-center text-center">
        <div className="bg-green-100 p-3 rounded-full">
          <CheckCircle2 className="h-12 w-12 text-green-600" />
        </div>
        <h3 className="mt-4 text-xl font-semibold">
          Workspace Created Successfully
        </h3>
        <p className="mt-2 text-gray-600">
          Your new compliance workspace for {data.customerDetails.companyName} has been created and is ready to use
        </p>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b">
          <h4 className="font-medium">Workspace Summary</h4>
        </div>

        <div className="p-6 space-y-4">
          {/* Customer Information */}
          <div className="flex gap-3">
            <div className="bg-blue-50 p-2 rounded">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Customer</p>
              <p className="text-sm">{data.customerDetails.companyName}</p>
              <p className="text-xs text-gray-500">
                {data.customerDetails.contactName} ({data.customerDetails.contactEmail})
              </p>
            </div>
          </div>

          {/* Modules Included */}
          <div className="flex gap-3">
            <div className="bg-purple-50 p-2 rounded">
              <Settings className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Modules Included</p>
              <div className="flex flex-wrap mt-1 gap-2">
                {data.modules.documents && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    Documents
                  </span>
                )}
                {data.modules.capas && (
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                    CAPAs
                  </span>
                )}
                {data.modules.audits && (
                  <span className="px-2 py-1 bg-amber-100 text-amber-800 text-xs rounded">
                    Audits
                  </span>
                )}
                {data.modules.performance && (
                  <span className="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs rounded">
                    Performance
                  </span>
                )}
                {data.modules.messages && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                    Messages
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Shared Documents */}
          <div className="flex gap-3">
            <div className="bg-amber-50 p-2 rounded">
              <FileText className="h-5 w-5 text-amber-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Shared Documents</p>
              <ul className="mt-1 space-y-1">
                {selectedDocuments.map((doc) => (
                  <li key={doc.id} className="text-sm">
                    {doc.name} <span className="text-xs text-gray-500">({doc.type})</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Access Settings */}
          <div className="flex gap-3">
            <div className="bg-green-50 p-2 rounded">
              <Calendar className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Access Settings</p>
              <p className="text-sm capitalize">
                Access Level: {data.sharing.accessLevel}
              </p>
              {data.sharing.expirationDate && (
                <p className="text-sm">
                  Expires: {format(data.sharing.expirationDate, "MMMM d, yyyy")}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
        <Button onClick={onGoToWorkspace} className="bg-blue-600 hover:bg-blue-700">
          Go to {data.customerDetails.companyName} Workspace
        </Button>
        <Button variant="outline" onClick={onCreateAnother}>
          Create Another Workspace
        </Button>
      </div>
    </div>
  );
};

function format(date: Date, formatString: string): string {
  // Simple formatting function for the example
  const months = [
    "January", "February", "March", "April", "May", "June", 
    "July", "August", "September", "October", "November", "December"
  ];
  return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
}

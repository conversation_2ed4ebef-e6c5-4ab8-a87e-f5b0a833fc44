
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CustomerDetailsStep } from "./CustomerDetailsStep";
import { ComplianceModulesStep } from "./ComplianceModulesStep";
import { InitialSharingStep } from "./InitialSharingStep";
import { SuccessScreen } from "./SuccessScreen";
import { StepIndicator } from "./StepIndicator";
import { X } from "lucide-react";

interface CreateWorkspaceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export type WorkspaceFormData = {
  customerDetails: {
    companyName: string;
    industry: string;
    contactName: string;
    contactEmail: string;
    logo?: File | null;
    logoPreview?: string;
  };
  modules: {
    documents: boolean;
    capas: boolean;
    audits: boolean;
    performance: boolean;
    messages: boolean;
  };
  sharing: {
    documents: <PERSON><PERSON>y<{
      id: string;
      name: string;
      type: string;
      selected: boolean;
    }>;
    accessLevel: "viewer" | "commenter" | "reviewer";
    expirationDate: Date | null;
  };
};

export const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<WorkspaceFormData>({
    customerDetails: {
      companyName: "",
      industry: "Aerospace",
      contactName: "",
      contactEmail: "",
      logo: null,
      logoPreview: "",
    },
    modules: {
      documents: true,
      capas: true,
      audits: true,
      performance: true,
      messages: false,
    },
    sharing: {
      documents: [
        {
          id: "iso9001",
          name: "ISO 9001 Certificate",
          type: "PDF",
          selected: true,
        },
        {
          id: "capa-march",
          name: "CAPA Tracker March 2025",
          type: "XLSX",
          selected: true,
        },
        {
          id: "audit-q1",
          name: "Internal Audit Summary Q1",
          type: "PDF",
          selected: true,
        },
      ],
      accessLevel: "viewer",
      expirationDate: null,
    },
  });

  const handleNext = () => {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handleCloseModal = () => {
    setCurrentStep(1);
    onClose();
  };

  const handleCreateWorkspace = () => {
    // In a real application, this would make an API call to create the workspace
    console.log("Creating workspace with data:", formData);
    handleNext(); // Move to success screen
  };

  const handleCreateAnother = () => {
    setCurrentStep(1);
    setFormData({
      customerDetails: {
        companyName: "",
        industry: "Aerospace",
        contactName: "",
        contactEmail: "",
        logo: null,
        logoPreview: "",
      },
      modules: {
        documents: true,
        capas: true,
        audits: true,
        performance: true,
        messages: false,
      },
      sharing: {
        documents: [
          {
            id: "iso9001",
            name: "ISO 9001 Certificate",
            type: "PDF",
            selected: true,
          },
          {
            id: "capa-march",
            name: "CAPA Tracker March 2025",
            type: "XLSX",
            selected: true,
          },
          {
            id: "audit-q1",
            name: "Internal Audit Summary Q1",
            type: "PDF",
            selected: true,
          },
        ],
        accessLevel: "viewer",
        expirationDate: null,
      },
    });
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <CustomerDetailsStep
            data={formData.customerDetails}
            onChange={(updatedData) =>
              setFormData({
                ...formData,
                customerDetails: updatedData,
              })
            }
          />
        );
      case 2:
        return (
          <ComplianceModulesStep
            data={formData.modules}
            onChange={(updatedData) =>
              setFormData({
                ...formData,
                modules: updatedData,
              })
            }
          />
        );
      case 3:
        return (
          <InitialSharingStep
            data={formData.sharing}
            onChange={(updatedData) =>
              setFormData({
                ...formData,
                sharing: updatedData,
              })
            }
          />
        );
      case 4:
        return (
          <SuccessScreen
            data={formData}
            onGoToWorkspace={handleCloseModal}
            onCreateAnother={handleCreateAnother}
          />
        );
      default:
        return null;
    }
  };

  const getDialogTitle = () => {
    if (currentStep === 4) return "Workspace Created Successfully";
    return "Create New Customer Compliance Workspace";
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseModal}>
      <DialogContent className="max-w-3xl p-0">
        <DialogHeader className="p-6 border-b">
          <div className="absolute right-4 top-4">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-full"
              onClick={handleCloseModal}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <DialogTitle className="text-xl font-semibold">
            {getDialogTitle()}
          </DialogTitle>
          {currentStep < 4 && (
            <DialogDescription>
              Create a secure workspace to share compliance data with your customer
            </DialogDescription>
          )}

          {currentStep < 4 && (
            <StepIndicator
              currentStep={currentStep}
              steps={["Customer Details", "Compliance Modules", "Initial Sharing"]}
            />
          )}
        </DialogHeader>

        <div className="p-6">{renderStepContent()}</div>

        {currentStep < 4 && (
          <DialogFooter className="p-6 border-t">
            {currentStep > 1 && (
              <Button variant="outline" onClick={handlePrevious}>
                Previous
              </Button>
            )}
            {currentStep < 3 ? (
              <Button onClick={handleNext}>Next</Button>
            ) : (
              <Button onClick={handleCreateWorkspace}>Create Workspace</Button>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Shield, Calendar, FileText, AlertTriangle, CheckCircle } from "lucide-react";

interface ComplianceItem {
  id: string;
  requirement: string;
  standard: string;
  status: 'Compliant' | 'Non-Compliant' | 'In Progress' | 'Pending Review';
  dueDate: string;
  lastReview: string;
  assignee: string;
  progress: number;
}

const mockComplianceItems: ComplianceItem[] = [
  {
    id: "COMP-001",
    requirement: "Quality Management System",
    standard: "ISO 9001:2015",
    status: "Compliant",
    dueDate: "2024-12-31",
    lastReview: "2024-01-15",
    assignee: "Quality Team",
    progress: 100
  },
  {
    id: "COMP-002",
    requirement: "Aerospace Quality Management",
    standard: "AS9100D",
    status: "In Progress",
    dueDate: "2024-06-30",
    lastReview: "2024-01-10",
    assignee: "Aerospace Division",
    progress: 75
  },
  {
    id: "COMP-003",
    requirement: "Environmental Management",
    standard: "ISO 14001",
    status: "Pending Review",
    dueDate: "2024-08-15",
    lastReview: "2023-12-20",
    assignee: "Environmental Team",
    progress: 60
  }
];

export const ComplianceTracking: React.FC = () => {
  const [complianceItems] = useState<ComplianceItem[]>(mockComplianceItems);

  const getStatusBadge = (status: string) => {
    const variants = {
      'Compliant': 'bg-green-100 text-green-800',
      'Non-Compliant': 'bg-red-100 text-red-800',
      'In Progress': 'bg-blue-100 text-blue-800',
      'Pending Review': 'bg-yellow-100 text-yellow-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Compliant':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'Non-Compliant':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'In Progress':
        return <Calendar className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-yellow-600" />;
    }
  };

  const overallCompliance = Math.round(
    complianceItems.reduce((sum, item) => sum + item.progress, 0) / complianceItems.length
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overall Compliance</p>
                <p className="text-3xl font-bold text-green-600">{overallCompliance}%</p>
              </div>
              <Shield className="h-10 w-10 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Compliant Items</p>
                <p className="text-3xl font-bold text-blue-600">
                  {complianceItems.filter(item => item.status === 'Compliant').length}
                </p>
              </div>
              <CheckCircle className="h-10 w-10 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Progress</p>
                <p className="text-3xl font-bold text-orange-600">
                  {complianceItems.filter(item => item.status === 'In Progress').length}
                </p>
              </div>
              <Calendar className="h-10 w-10 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Review</p>
                <p className="text-3xl font-bold text-red-600">
                  {complianceItems.filter(item => item.status === 'Pending Review').length}
                </p>
              </div>
              <AlertTriangle className="h-10 w-10 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Compliance Requirements Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Requirement</TableHead>
                <TableHead>Standard</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {complianceItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(item.status)}
                      <div>
                        <div className="font-medium">{item.requirement}</div>
                        <div className="text-sm text-gray-500">Last review: {item.lastReview}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.standard}</Badge>
                  </TableCell>
                  <TableCell>{getStatusBadge(item.status)}</TableCell>
                  <TableCell>
                    <div className="w-20">
                      <Progress value={item.progress} className="h-2" />
                      <span className="text-xs text-gray-500">{item.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>{item.assignee}</TableCell>
                  <TableCell>{item.dueDate}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">Review</Button>
                      <Button size="sm">Update</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

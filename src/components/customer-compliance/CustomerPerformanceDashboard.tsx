
import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { 
  ChartContainer, 
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";

interface PerformanceDashboardProps {
  otifData: { month: string; value: number }[];
  ncrRateData: { month: string; value: number }[];
  capaAgingData: { name: string; value: number; color: string }[];
  auditOutcomeData: { date: string; status: string; color: string }[];
}

export function CustomerPerformanceDashboard({ 
  otifData,
  ncrRateData,
  capaAgingData,
  auditOutcomeData
}: PerformanceDashboardProps) {
  const handleExportPDF = () => {
    toast.success("KPI Report downloaded successfully");
    // In a real implementation, this would generate and download a PDF
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Performance Dashboard for Boeing</h2>
        <Button onClick={handleExportPDF} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export KPI Report
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* OTIF Line Chart */}
        <Card>
          <CardHeader className="border-b">
            <CardTitle className="text-lg">OTIF (On-Time In-Full)</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              <ChartContainer config={{ otif: { color: '#3b82f6' } }}>
                <LineChart data={otifData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[90, 100]} tickCount={5} />
                  <Tooltip content={<ChartTooltipContent />} />
                  <Legend content={<ChartLegendContent />} />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    name="OTIF %" 
                    stroke="#3b82f6" 
                    strokeWidth={2} 
                    dot={{ r: 4 }} 
                  />
                </LineChart>
              </ChartContainer>
            </div>
            <div className="mt-2 text-sm text-muted-foreground text-center">
              6-Month Average: 96%
            </div>
          </CardContent>
        </Card>

        {/* NCR Rate Bar Chart */}
        <Card>
          <CardHeader className="border-b">
            <CardTitle className="text-lg">NCR Rate (%)</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              <ChartContainer config={{ ncr: { color: '#ef4444' } }}>
                <BarChart data={ncrRateData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 5]} />
                  <Tooltip content={<ChartTooltipContent />} />
                  <Legend content={<ChartLegendContent />} />
                  <Bar 
                    dataKey="value" 
                    name="NCR Rate" 
                    fill="#ef4444" 
                    barSize={40} 
                    radius={[4, 4, 0, 0]} 
                  />
                </BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* CAPA Aging Pie Chart */}
        <Card>
          <CardHeader className="border-b">
            <CardTitle className="text-lg">CAPA Aging Status</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="h-[300px]">
              <ChartContainer config={{
                closed: { color: '#22c55e' },
                inProgress: { color: '#f59e0b' },
                overdue: { color: '#ef4444' },
              }}>
                <PieChart>
                  <Pie
                    data={capaAgingData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {capaAgingData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        {/* Recent Audit Outcome Timeline */}
        <Card>
          <CardHeader className="border-b">
            <CardTitle className="text-lg">Recent Audit Outcome Timeline</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="relative mt-4 h-[300px] flex items-center">
              <div className="absolute left-0 right-0 h-2 bg-gray-200 rounded-full">
                {auditOutcomeData.map((audit, index) => (
                  <div
                    key={index}
                    className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 rounded-full"
                    style={{ 
                      left: `${(index / (auditOutcomeData.length - 1)) * 100}%`,
                      backgroundColor: audit.color 
                    }}
                    title={`${audit.date}: ${audit.status}`}
                  />
                ))}
              </div>
              
              <div className="absolute left-0 right-0 top-4 flex justify-between">
                {auditOutcomeData.map((audit, index) => (
                  <div 
                    key={index} 
                    className="flex flex-col items-center"
                    style={{ 
                      left: `${(index / (auditOutcomeData.length - 1)) * 100}%`,
                      position: 'absolute',
                      transform: 'translateX(-50%)'
                    }}
                  >
                    <div 
                      className="p-2 rounded text-xs font-medium mb-1"
                      style={{ backgroundColor: audit.color, color: 'white' }}
                    >
                      {audit.status}
                    </div>
                    <div className="text-xs text-gray-500">{audit.date}</div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

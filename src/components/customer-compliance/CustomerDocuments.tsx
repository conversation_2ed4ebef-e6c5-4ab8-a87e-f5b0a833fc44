
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { formatDate } from "./utils/dateFormatters";
import { getDocumentIcon } from "./utils/documentUtils";
import { CustomerDocument } from "./types";

interface CustomerDocumentsProps {
  sharedDocuments: CustomerDocument[];
}

export function CustomerDocuments({ sharedDocuments }: CustomerDocumentsProps) {
  return (
    <Card>
      <CardHeader className="border-b">
        <CardTitle className="text-lg">All Shared Documents</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="divide-y">
          {sharedDocuments.map((doc) => (
            <div key={doc.id} className="py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getDocumentIcon(doc.type)}
                <div>
                  <p className="font-medium">{doc.name}</p>
                  <p className="text-xs text-gray-500">
                    {doc.type} {doc.additionalInfo && `• ${doc.additionalInfo}`}
                  </p>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                Uploaded {formatDate(doc.uploadDate)}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

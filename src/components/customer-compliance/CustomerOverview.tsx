
import React from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  CheckCircle2, 
  XCircle, 
  Calendar 
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatDate, formatLastAccess } from "./utils/dateFormatters";
import { getDocumentIcon } from "./utils/documentUtils";
import { getAuditStatusBadge, getCapaStatusBadge } from "./utils/badgeUtils";
import { CustomerDocument, AuditRecord, OtifDataPoint } from "./types";

interface CustomerOverviewProps {
  recentDocuments: CustomerDocument[];
  auditHistory: AuditRecord[];
  otifData: OtifDataPoint[];
}

export function CustomerOverview({ recentDocuments, auditHistory, otifData }: CustomerOverviewProps) {
  return (
    <div className="space-y-6">
      {/* Compliance Scorecard */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Compliance Scorecard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-8 w-8 text-green-500" />
              <div>
                <p className="font-medium">Certifications</p>
                <p className="text-sm text-gray-500">All valid and current</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <XCircle className="h-8 w-8 text-red-500" />
              <div>
                <p className="font-medium">CAPAs Open</p>
                <p className="text-sm text-gray-500">1 pending resolution</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-8 w-8 text-green-500" />
              <div>
                <p className="font-medium">Last Audit</p>
                <p className="text-sm text-gray-500">Passed - Jan 2025</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Recently Shared Documents */}
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">Recently Shared Documents</CardTitle>
        </CardHeader>
        <CardContent className="divide-y">
          {recentDocuments.map((doc) => (
            <div key={doc.id} className="py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getDocumentIcon(doc.type)}
                <div>
                  <p className="font-medium">{doc.name}</p>
                  <p className="text-xs text-gray-500">
                    {doc.type} {doc.additionalInfo && `• ${doc.additionalInfo}`}
                  </p>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                Uploaded {formatDate(doc.uploadDate)}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
      
      {/* Audit History */}
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">Audit History</CardTitle>
        </CardHeader>
        <CardContent className="divide-y">
          {auditHistory.map((audit) => (
            <div key={audit.id} className="py-3 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{formatDate(audit.date)}</p>
                    {getAuditStatusBadge(audit.status)}
                  </div>
                  {audit.notes && (
                    <p className="text-sm text-gray-500">{audit.notes}</p>
                  )}
                </div>
              </div>
              {audit.capaStatus && (
                <div>
                  CAPA: {getCapaStatusBadge(audit.capaStatus)}
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>
      
      {/* OTIF Performance */}
      <Card>
        <CardHeader className="border-b">
          <CardTitle className="text-lg">On-Time In-Full (OTIF) Performance</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="text-2xl font-bold text-green-600">96%</div>
          <p className="text-sm text-gray-500">Average for last 6 months</p>
          
          <div className="mt-4 h-36 flex items-end gap-2">
            {otifData.map((item, i) => (
              <div key={i} className="flex flex-col items-center flex-1">
                <div 
                  className="w-full bg-blue-500 rounded-t"
                  style={{ height: `${item.value}px` }}
                ></div>
                <div className="text-xs mt-2 text-gray-600">{item.month}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

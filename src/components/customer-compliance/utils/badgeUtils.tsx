
import React from "react";
import { Badge } from "@/components/ui/badge";

export const getAuditStatusBadge = (status: string) => {
  switch (status) {
    case 'Passed':
      return <Badge className="bg-green-500 text-white">Passed</Badge>;
    case 'Failed':
      return <Badge className="bg-red-500 text-white">Failed</Badge>;
    case 'Minor Findings':
      return <Badge className="bg-amber-500 text-white">Minor Findings</Badge>;
    default:
      return <Badge>Unknown</Badge>;
  }
};

export const getCapaStatusBadge = (status: string) => {
  switch (status) {
    case 'Open':
      return <Badge variant="outline" className="border-red-500 text-red-500">Open</Badge>;
    case 'Closed':
      return <Badge variant="outline" className="border-green-500 text-green-500">Closed</Badge>;
    case 'Not Required':
      return <Badge variant="outline" className="border-gray-500 text-gray-500">Not Required</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};

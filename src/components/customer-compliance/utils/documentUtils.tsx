
import React from "react";
import { FileText, FileSearch, FileImage } from "lucide-react";

export const getDocumentIcon = (type: string) => {
  switch (type) {
    case 'PDF':
      return <FileText className="h-5 w-5 text-blue-600" />;
    case 'XLSX':
      return <FileSearch className="h-5 w-5 text-green-600" />;
    case 'JPG':
    case 'PNG':
      return <FileImage className="h-5 w-5 text-amber-600" />;
    default:
      return <FileText className="h-5 w-5 text-gray-600" />;
  }
};

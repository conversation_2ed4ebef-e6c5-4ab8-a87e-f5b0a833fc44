
export const formatDate = (dateString: string | null) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }
  
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

export const formatLastAccess = (dateString: string | null) => {
  if (!dateString) return "Never";
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }
  
  const formattedDate = formatDate(dateString);
  const formattedTime = date.toLocaleTimeString('en-GB', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
  
  return `${formattedDate} at ${formattedTime}`;
};

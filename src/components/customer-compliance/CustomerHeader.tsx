
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Upload, Package, Users } from "lucide-react";
import { formatDate } from "./utils/dateFormatters";

interface CustomerHeaderProps {
  customer: {
    name: string;
    logoUrl: string;
    status: string;
    lastAccessed: string;
  };
  onManageAccess: () => void;
}

export function CustomerHeader({ customer, onManageAccess }: CustomerHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
      <div className="flex items-center gap-4">
        <div className="h-16 w-16 bg-gray-100 rounded-md flex items-center justify-center">
          <img 
            src={customer.logoUrl} 
            alt={`${customer.name} logo`} 
            className="h-10 w-10"
          />
        </div>
        <div>
          <h1 className="text-2xl font-bold">{customer.name}</h1>
          <div className="flex items-center gap-2 mt-1">
            <Badge className="bg-green-500 text-white">Active</Badge>
            <span className="text-gray-500">Last accessed: {formatDate(customer.lastAccessed)}</span>
          </div>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <Button variant="outline" className="gap-2">
          <Upload className="h-4 w-4" />
          Share New File
        </Button>
        <Button variant="outline" className="gap-2">
          <Package className="h-4 w-4" />
          Generate Package
        </Button>
        <Button 
          className="gap-2"
          onClick={onManageAccess}
        >
          <Users className="h-4 w-4" />
          Manage Access
        </Button>
      </div>
    </div>
  );
}

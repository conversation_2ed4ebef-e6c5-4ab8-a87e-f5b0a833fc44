import { Routes, Route } from "react-router-dom";
import { ErrorBoundary } from "@/components/error-handling";
import Index from "@/pages/Index";
import Dashboard from "@/pages/Dashboard";
import DocumentHub from "@/pages/DocumentHub";
import DocumentHubWithFolders from "@/pages/DocumentHubWithFolders";
import DocumentDetails from "@/pages/DocumentDetails";
import DocumentAdmin from "@/pages/DocumentAdmin";
import DocumentTablePage from "@/pages/DocumentTablePage";
import People from "@/pages/People";
import EmployeeDetail from "@/pages/EmployeeDetail";
import TrainingLibrary from "@/pages/TrainingLibrary";
import Assets from "@/pages/Assets";
import SupplierQuality from "@/pages/SupplierQuality";
import InventoryManagement from "@/pages/InventoryManagement";
import ProductHub from "@/pages/ProductHub";
import ProductionHub from "@/pages/ProductionHub";
import BPRHubPortal from "@/pages/BPRHubPortal";
import CustomerCompliance from "@/pages/CustomerCompliance";
import CustomerWorkspace from "@/pages/CustomerWorkspace";
import CustomerWorkspaceDetail from "@/pages/CustomerWorkspaceDetail";
import CustomerPortal from "@/pages/CustomerPortal";
import Login from "@/pages/Login";
import AuditTemplates from "@/pages/AuditTemplates";
import PPAP from "@/pages/PPAP";
import APQP from "@/pages/APQP";
import CMMS from "@/pages/CMMS";
import Traceability from "@/pages/Traceability";
import Vendors from "@/pages/Vendors";
import VendorDetails from "@/pages/VendorDetails";
import FormBuilder from "@/pages/FormBuilder";
import StandardsHub from "@/pages/StandardsHub";
import TrainingManagement from "@/pages/TrainingManagement";
import MaterialJourneyTimeline from "@/pages/MaterialJourneyTimeline";
import NotFound from "@/pages/NotFound";
import ServerError from "@/pages/ServerError";
import AccessDenied from "@/pages/AccessDenied";
import NetworkError from "@/pages/NetworkError";
import MaintenanceMode from "@/pages/MaintenanceMode";
import Profile from "@/pages/ProfilePage";

export const AppRoutes = () => {
  return (
    <ErrorBoundary>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/login" element={<Login />} />
        <Route path="/document-hub" element={<DocumentHub />} />
        <Route path="/document-admin" element={<DocumentAdmin />} />
        <Route path="/documents-folders" element={<DocumentHubWithFolders />} />
        <Route path="/document-table" element={<DocumentTablePage />} />
        <Route path="/document/:id" element={<DocumentDetails />} />
        <Route path="/people" element={<People />} />
        <Route path="/people/:employeeId" element={<EmployeeDetail />} />
        <Route path="/training-library" element={<TrainingLibrary />} />
        <Route path="/training-management" element={<TrainingManagement />} />
        <Route path="/assets" element={<Assets />} />
        <Route path="/supplier-quality" element={<SupplierQuality />} />
        <Route path="/inventory-management" element={<InventoryManagement />} />
        <Route path="/product-hub" element={<ProductHub />} />
        <Route path="/production-hub" element={<ProductionHub />} />
        <Route path="/vendors" element={<Vendors />} />
        <Route path="/vendors/:id" element={<VendorDetails />} />
        <Route path="/bpr-hub-portal" element={<BPRHubPortal />} />
        <Route path="/customer-compliance" element={<CustomerCompliance />} />
        <Route
          path="/customer-compliance/:id"
          element={<CustomerWorkspaceDetail />}
        />
        <Route
          path="/customer-workspace/:id"
          element={<CustomerWorkspaceDetail />}
        />
        <Route path="/customer-portal" element={<CustomerPortal />} />
        <Route path="/audit-templates" element={<AuditTemplates />} />
        <Route path="/ppap" element={<PPAP />} />
        <Route path="/apqp" element={<APQP />} />
        <Route path="/cmms" element={<CMMS />} />
        <Route path="/traceability" element={<Traceability />} />
        <Route
          path="/material-journey-timeline"
          element={<MaterialJourneyTimeline />}
        />
        <Route path="/standards-hub" element={<StandardsHub />} />
        <Route path="/form-builder" element={<FormBuilder />} />
        <Route path="/profile" element={<Profile />} />

        {/* Error pages */}
        <Route path="/error/500" element={<ServerError />} />
        <Route path="/error/403" element={<AccessDenied />} />
        <Route path="/error/network" element={<NetworkError />} />
        <Route path="/maintenance" element={<MaintenanceMode />} />

        {/* Catch-all route for 404s */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </ErrorBoundary>
  );
};

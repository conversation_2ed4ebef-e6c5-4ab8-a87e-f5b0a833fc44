
import React from "react";
import { B<PERSON>erRouter } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AccountProvider } from "@/contexts/AccountContext";
import { TrainingProvider } from "@/contexts/TrainingContext";

const queryClient = new QueryClient();

interface AppProvidersProps {
  children: React.ReactNode;
}

export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <AccountProvider>
            <TrainingProvider>
              {children}
              <Toaster />
              <Sonner />
            </TrainingProvider>
          </AccountProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

import React, {
  useState,
  useMemo,
  useEffect,
  useRef,
  useCallback,
} from "react";
import {
  Search,
  Filter,
  Eye,
  Edit,
  Download,
  ChevronDown,
  ChevronRight,
  Plus,
  MoreVertical,
  RotateCcw,
  X,
  ArrowUp,
  ArrowDown,
  ArrowUpDown,
  FileText,
  Folder,
  Table as TableIcon,
  MessageSquare,
  Cog,
  Calendar,
  User,
  Building,
  Tag,
  Settings,
  ChevronLeft,
  ChevronUp,
  MoreHorizontal,
} from "lucide-react";

// Types
interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId?: string | null;
}

interface DocumentTableProps {
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  documents?: Document[];
}

interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
  processes: string[];
}

interface FilterOption {
  id: string;
  label: string;
  count: number;
}

interface FormValues {
  title: string;
  department: string;
  category: string;
  processes: string;
  assignee: string;
  approver: string;
  description: string;
  status: string;
}

type SortField =
  | "title"
  | "department"
  | "category"
  | "status"
  | "assignee"
  | "publishedDate"
  | "reviewDate"
  | "approver"
  | null;
type SortDirection = "asc" | "desc" | null;

// Utility function for className merging
const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(" ");
};

// Utility functions
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return "-";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return dateString;
  }
};

const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Custom hooks
const useIsMobile = (): boolean => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => {
      window.removeEventListener("resize", checkIsMobile);
    };
  }, []);

  return isMobile;
};

// Sample data
const mockDocuments: Document[] = [
  {
    id: "doc-001",
    title: "Quality Management System Manual",
    version: "v2.1",
    department: "Quality Assurance",
    category: "Quality Manual",
    processes: "Document Control, Management Review",
    status: "Published",
    assignee: "Sarah Johnson",
    approver: "Michael Brown",
    date: "2024-03-15",
    publishedDate: "2024-03-15",
    reviewDate: "2025-03-15",
    description:
      "Comprehensive quality management system documentation following ISO 9001 standards.",
    parentFolderId: "folder-quality",
  },
  {
    id: "doc-002",
    title: "Standard Operating Procedure - Manufacturing",
    version: "v1.3",
    department: "Manufacturing",
    category: "SOP",
    processes: "Production Control, Quality Control",
    status: "Under Review",
    assignee: "John Smith",
    approver: "Lisa Davis",
    date: "2024-02-28",
    publishedDate: "2024-02-28",
    reviewDate: "2024-12-28",
    description:
      "Detailed manufacturing procedures and quality control measures.",
    parentFolderId: "folder-manufacturing",
  },
  {
    id: "doc-003",
    title: "Employee Safety Guidelines",
    version: "v3.0",
    department: "Human Resources",
    category: "Policy",
    processes: "Safety Management, Training",
    status: "Draft",
    assignee: "Emma Wilson",
    approver: "Robert Taylor",
    date: "2024-01-20",
    publishedDate: "2024-01-20",
    reviewDate: "2024-07-20",
    description:
      "Comprehensive safety guidelines and emergency procedures for all employees.",
    parentFolderId: "folder-hr",
  },
  {
    id: "doc-004",
    title: "Financial Reporting Standards",
    version: "v2.0",
    department: "Finance",
    category: "Standard",
    processes: "Financial Reporting, Compliance",
    status: "Published",
    assignee: "David Chen",
    approver: "Jennifer Lee",
    date: "2024-03-01",
    publishedDate: "2024-03-01",
    reviewDate: "2025-03-01",
    description:
      "Standards and procedures for financial reporting and compliance requirements.",
    parentFolderId: "folder-finance",
  },
  {
    id: "doc-005",
    title: "IT Security Policy",
    version: "v1.5",
    department: "Information Technology",
    category: "Policy",
    processes: "Security Management, Access Control",
    status: "Expired",
    assignee: "Alex Rodriguez",
    approver: "Maria Garcia",
    date: "2023-06-15",
    publishedDate: "2023-06-15",
    reviewDate: "2024-06-15",
    description:
      "Information technology security policies and access control procedures.",
    parentFolderId: "folder-it",
  },
];

// Filter options
const statusOptions: FilterOption[] = [
  { id: "draft", label: "Draft", count: 1 },
  { id: "under-review", label: "Under Review", count: 1 },
  { id: "published", label: "Published", count: 2 },
  { id: "expired", label: "Expired", count: 1 },
  { id: "archived", label: "Archived", count: 0 },
];

const departmentOptions: FilterOption[] = [
  { id: "quality-assurance", label: "Quality Assurance", count: 1 },
  { id: "manufacturing", label: "Manufacturing", count: 1 },
  { id: "human-resources", label: "Human Resources", count: 1 },
  { id: "finance", label: "Finance", count: 1 },
  { id: "information-technology", label: "Information Technology", count: 1 },
];

const categoryOptions: FilterOption[] = [
  { id: "quality-manual", label: "Quality Manual", count: 1 },
  { id: "sop", label: "SOP", count: 1 },
  { id: "policy", label: "Policy", count: 2 },
  { id: "standard", label: "Standard", count: 1 },
];

const assigneeOptions: FilterOption[] = [
  { id: "sarah-johnson", label: "Sarah Johnson", count: 1 },
  { id: "john-smith", label: "John Smith", count: 1 },
  { id: "emma-wilson", label: "Emma Wilson", count: 1 },
  { id: "david-chen", label: "David Chen", count: 1 },
  { id: "alex-rodriguez", label: "Alex Rodriguez", count: 1 },
];

const processOptions: FilterOption[] = [
  { id: "document-control", label: "Document Control", count: 1 },
  { id: "management-review", label: "Management Review", count: 1 },
  { id: "production-control", label: "Production Control", count: 1 },
  { id: "quality-control", label: "Quality Control", count: 1 },
  { id: "safety-management", label: "Safety Management", count: 1 },
  { id: "training", label: "Training", count: 1 },
  { id: "financial-reporting", label: "Financial Reporting", count: 1 },
  { id: "compliance", label: "Compliance", count: 1 },
  { id: "security-management", label: "Security Management", count: 1 },
  { id: "access-control", label: "Access Control", count: 1 },
];

// UI Components
const Button: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg" | "icon";
  className?: string;
  disabled?: boolean;
}> = ({
  children,
  onClick,
  variant = "default",
  size = "md",
  className = "",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none";

  const variantClasses = {
    default: "bg-teal-600 text-white hover:bg-teal-700",
    outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",
    ghost: "hover:bg-gray-100 text-gray-700",
  };

  const sizeClasses = {
    sm: "h-8 px-3 text-sm",
    md: "h-10 px-4 py-2",
    lg: "h-11 px-8",
    icon: "h-10 w-10",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
    >
      {children}
    </button>
  );
};

const Input: React.FC<{
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
}> = ({ type = "text", placeholder, value, onChange, className = "" }) => {
  return (
    <input
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      className={cn(
        "flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
    />
  );
};

const Badge: React.FC<{
  children: React.ReactNode;
  variant?: "default" | "outline";
  className?: string;
}> = ({ children, variant = "default", className = "" }) => {
  const baseClasses =
    "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";

  const variantClasses = {
    default: "bg-gray-900 text-gray-50 hover:bg-gray-900/80",
    outline: "text-gray-950 border border-gray-200 bg-white hover:bg-gray-100",
  };

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)}>
      {children}
    </div>
  );
};

const Select: React.FC<{
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}> = ({ value, onValueChange, children, className = "" }) => {
  return (
    <select
      value={value}
      onChange={(e) => onValueChange?.(e.target.value)}
      className={cn(
        "flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-teal-600 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
    >
      {children}
    </select>
  );
};

const SelectItem: React.FC<{
  value: string;
  children: React.ReactNode;
}> = ({ value, children }) => {
  return <option value={value}>{children}</option>;
};

// Table Components
const Table: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return (
    <div className={cn("w-full overflow-auto", className)}>
      <table className="w-full caption-bottom text-sm">{children}</table>
    </div>
  );
};

const TableHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return <thead className={cn("[&_tr]:border-b", className)}>{children}</thead>;
};

const TableBody: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return (
    <tbody className={cn("[&_tr:last-child]:border-0", className)}>
      {children}
    </tbody>
  );
};

const TableRow: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return (
    <tr
      className={cn(
        "border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50",
        className
      )}
    >
      {children}
    </tr>
  );
};

const TableHead: React.FC<{
  children?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}> = ({ children, className = "", onClick }) => {
  return (
    <th
      className={cn(
        "h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0",
        onClick && "cursor-pointer hover:bg-gray-100",
        className
      )}
      onClick={onClick}
    >
      {children}
    </th>
  );
};

const TableCell: React.FC<{
  children: React.ReactNode;
  className?: string;
  colSpan?: number;
}> = ({ children, className = "", colSpan }) => {
  return (
    <td
      className={cn(
        "p-4 align-middle [&:has([role=checkbox])]:pr-0",
        className
      )}
      colSpan={colSpan}
    >
      {children}
    </td>
  );
};

// ResizableTable Component (Simplified for compatibility)
const ResizableTable: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  return (
    <div className={cn("relative overflow-auto", className)}>
      <table className="w-full caption-bottom text-sm table-fixed">
        {children}
      </table>
    </div>
  );
};

// Specialized Components
const DocumentStatusBadge: React.FC<{ status: string }> = ({ status }) => {
  const lowercase = status.toLowerCase();

  if (lowercase === "draft") {
    return (
      <Badge
        variant="outline"
        className="bg-blue-50 text-blue-700 border-blue-200"
      >
        Draft
      </Badge>
    );
  }

  if (lowercase === "review" || lowercase === "under review") {
    return (
      <Badge
        variant="outline"
        className="bg-amber-50 text-amber-700 border-amber-200"
      >
        Under Review
      </Badge>
    );
  }

  if (lowercase === "approved") {
    return (
      <Badge
        variant="outline"
        className="bg-green-50 text-green-700 border-green-200"
      >
        Approved
      </Badge>
    );
  }

  if (lowercase === "published") {
    return (
      <Badge
        variant="outline"
        className="bg-teal-50 text-teal-700 border-teal-200"
      >
        Published
      </Badge>
    );
  }

  if (lowercase === "expired") {
    return (
      <Badge
        variant="outline"
        className="bg-red-50 text-red-700 border-red-200"
      >
        Expired
      </Badge>
    );
  }

  if (lowercase === "archived") {
    return (
      <Badge
        variant="outline"
        className="bg-gray-100 text-gray-700 border-gray-300"
      >
        Archived
      </Badge>
    );
  }

  // Default
  return (
    <Badge
      variant="outline"
      className="bg-gray-100 text-gray-700 border-gray-300"
    >
      {status}
    </Badge>
  );
};

const ReviewDateCell: React.FC<{ reviewDate: string | undefined }> = ({
  reviewDate,
}) => {
  const getBackgroundColor = () => {
    if (!reviewDate) return "";

    try {
      const reviewDateObj = new Date(reviewDate);
      const today = new Date();

      if (today > reviewDateObj) {
        return "bg-red-50 text-red-700";
      }

      const daysUntilReview = Math.ceil(
        (reviewDateObj.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
      );
      if (daysUntilReview <= 14) {
        return "bg-orange-50 text-orange-700";
      }

      return "";
    } catch {
      return "";
    }
  };

  return (
    <div className={cn("px-2 py-1 rounded", getBackgroundColor())}>
      {reviewDate ? formatDate(reviewDate) : "-"}
    </div>
  );
};

const DocumentTitle: React.FC<{ document: Document }> = ({ document }) => {
  return (
    <div className="flex flex-col">
      <div className="font-medium text-gray-900 hover:text-teal-600 cursor-pointer">
        {document.title}
      </div>
      {document.version && (
        <div className="text-xs text-gray-500 mt-1">
          Version: {document.version}
        </div>
      )}
      {document.description && (
        <div className="text-xs text-gray-500 mt-1 line-clamp-2">
          {document.description}
        </div>
      )}
    </div>
  );
};

const DocumentActions: React.FC<{ document: Document }> = ({ document }) => {
  const handleAction = (action: string) => {
    console.log(`${action} document:`, document.title);
    // In a real app, this would navigate or perform the action
    alert(`${action} action for: ${document.title}`);
  };

  return (
    <div className="flex items-center gap-1">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleAction("View")}
        className="h-8 w-8"
      >
        <Eye className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleAction("Edit")}
        className="h-8 w-8"
      >
        <Edit className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleAction("Download")}
        className="h-8 w-8"
      >
        <Download className="h-4 w-4" />
      </Button>
      <div className="relative">
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <MoreVertical className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Search and Filter Components
const SearchBar: React.FC<{
  onSearch: (searchTerm: string) => void;
  initialSearchTerm?: string;
  className?: string;
}> = ({ onSearch, initialSearchTerm = "", className = "" }) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.length >= 3 || value.length === 0) {
      onSearch(value);
    }
  };

  const clearSearch = () => {
    setSearchTerm("");
    onSearch("");
  };

  return (
    <div className={cn("relative w-full", className)}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>

      <Input
        type="text"
        value={searchTerm}
        onChange={handleSearchChange}
        className="pl-10 pr-10"
        placeholder="Search documents..."
      />

      {searchTerm.length > 0 && (
        <button
          onClick={clearSearch}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
        >
          <X className="h-5 w-5" />
        </button>
      )}
    </div>
  );
};

const StatusFilter: React.FC<{
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: FilterOption[];
}> = ({ appliedFilters, handleStatusChange, statusOptions }) => {
  const handleChange = (value: string) => {
    if (value === "all") {
      handleStatusChange([]);
    } else {
      handleStatusChange([value]);
    }
  };

  return (
    <div className="min-w-[150px]">
      <Select
        value={
          appliedFilters.status.length > 0 ? appliedFilters.status[0] : "all"
        }
        onValueChange={handleChange}
        className="border-gray-300 h-10"
      >
        <SelectItem value="all">All Statuses</SelectItem>
        {statusOptions.map((option) => (
          <SelectItem key={option.id} value={option.id}>
            {option.label} ({option.count})
          </SelectItem>
        ))}
      </Select>
    </div>
  );
};

const ViewToggle: React.FC<{
  viewMode: "table" | "folders";
  setViewMode: (mode: "table" | "folders") => void;
}> = ({ viewMode, setViewMode }) => {
  return (
    <div className="flex gap-1 border rounded-lg p-1">
      <Button
        variant={viewMode === "table" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("table")}
      >
        <TableIcon className="h-4 w-4" />
      </Button>

      <Button
        variant={viewMode === "folders" ? "default" : "ghost"}
        size="sm"
        onClick={() => setViewMode("folders")}
      >
        <Folder className="h-4 w-4" />
      </Button>
    </div>
  );
};

const CreateDocumentDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
}> = ({ open, onOpenChange }) => {
  const [formData, setFormData] = useState<FormValues>({
    title: "",
    department: "",
    category: "",
    processes: "",
    assignee: "",
    approver: "",
    description: "",
    status: "draft",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Creating document:", formData);
    alert(`Document "${formData.title}" created successfully!`);
    onOpenChange(false);
    setFormData({
      title: "",
      department: "",
      category: "",
      processes: "",
      assignee: "",
      approver: "",
      description: "",
      status: "draft",
    });
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="bg-gray-50 p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            Create Document
          </h2>
          <p className="text-gray-600 mt-1">
            Create a new document in the document hub.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <Input
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              placeholder="Document title"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <Input
                value={formData.department}
                onChange={(e) =>
                  setFormData({ ...formData, department: e.target.value })
                }
                placeholder="Department"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <Input
                value={formData.category}
                onChange={(e) =>
                  setFormData({ ...formData, category: e.target.value })
                }
                placeholder="Category"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Document description"
              className="flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-teal-600 focus-visible:ring-offset-2"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 h-10 px-4 py-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none bg-teal-600 text-white hover:bg-teal-700 h-10 px-4 py-2"
            >
              Create Document
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Filter Side Panel
const FilterSidePanel: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  appliedFilters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
}> = ({ isOpen, onClose, appliedFilters, onFiltersChange }) => {
  const [localFilters, setLocalFilters] = useState<FilterState>(appliedFilters);

  useEffect(() => {
    setLocalFilters(appliedFilters);
  }, [appliedFilters]);

  const handleFilterChange = (
    category: keyof FilterState,
    values: string[]
  ) => {
    setLocalFilters((prev) => ({
      ...prev,
      [category]: values,
    }));
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const resetFilters = () => {
    const emptyFilters: FilterState = {
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: [],
    };
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const FilterSection: React.FC<{
    title: string;
    options: FilterOption[];
    selectedValues: string[];
    onChange: (values: string[]) => void;
  }> = ({ title, options, selectedValues, onChange }) => {
    const toggleOption = (optionId: string) => {
      if (selectedValues.includes(optionId)) {
        onChange(selectedValues.filter((id) => id !== optionId));
      } else {
        onChange([...selectedValues, optionId]);
      }
    };

    return (
      <div className="space-y-3">
        <h3 className="font-medium text-gray-900">{title}</h3>
        <div className="space-y-2">
          {options.map((option) => (
            <label
              key={option.id}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <input
                type="checkbox"
                checked={selectedValues.includes(option.id)}
                onChange={() => toggleOption(option.id)}
                className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
              />
              <span className="text-sm text-gray-700">
                {option.label} ({option.count})
              </span>
            </label>
          ))}
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex">
      <div className="bg-white w-80 h-full shadow-xl overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <FilterSection
            title="Status"
            options={statusOptions}
            selectedValues={localFilters.status}
            onChange={(values) => handleFilterChange("status", values)}
          />

          <FilterSection
            title="Department"
            options={departmentOptions}
            selectedValues={localFilters.departments}
            onChange={(values) => handleFilterChange("departments", values)}
          />

          <FilterSection
            title="Category"
            options={categoryOptions}
            selectedValues={localFilters.categories}
            onChange={(values) => handleFilterChange("categories", values)}
          />

          <FilterSection
            title="Assignee"
            options={assigneeOptions}
            selectedValues={localFilters.assignee}
            onChange={(values) => handleFilterChange("assignee", values)}
          />

          <FilterSection
            title="Processes"
            options={processOptions}
            selectedValues={localFilters.processes}
            onChange={(values) => handleFilterChange("processes", values)}
          />
        </div>

        <div className="p-6 border-t border-gray-200 space-y-3">
          <Button onClick={applyFilters} className="w-full">
            Apply Filters
          </Button>
          <Button variant="outline" onClick={resetFilters} className="w-full">
            Reset All
          </Button>
        </div>
      </div>
      <div className="flex-1" onClick={onClose} />
    </div>
  );
};

// Main DocumentTable Component
const DocumentTable: React.FC<DocumentTableProps> = ({
  title = "Document Hub",
  subtitle = "Manage and organize your documents",
  showSearch = true,
  showFilters = true,
  documents = mockDocuments,
}) => {
  const isMobile = useIsMobile();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: [],
  });
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [viewMode, setViewMode] = useState<"table" | "folders">("table");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Filter and search logic
  const filteredDocuments = useMemo(() => {
    let filtered = documents;

    // Apply search filter
    if (searchTerm.length >= 3) {
      filtered = filtered.filter(
        (doc) =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (appliedFilters.status.length > 0) {
      filtered = filtered.filter((doc) =>
        appliedFilters.status.some(
          (status) => doc.status.toLowerCase().replace(/\s+/g, "-") === status
        )
      );
    }

    // Apply department filter
    if (appliedFilters.departments.length > 0) {
      filtered = filtered.filter((doc) =>
        appliedFilters.departments.some(
          (dept) => doc.department.toLowerCase().replace(/\s+/g, "-") === dept
        )
      );
    }

    // Apply category filter
    if (appliedFilters.categories.length > 0) {
      filtered = filtered.filter((doc) =>
        appliedFilters.categories.some(
          (cat) => doc.category.toLowerCase().replace(/\s+/g, "-") === cat
        )
      );
    }

    // Apply assignee filter
    if (appliedFilters.assignee.length > 0) {
      filtered = filtered.filter((doc) =>
        appliedFilters.assignee.some(
          (assignee) =>
            doc.assignee.toLowerCase().replace(/\s+/g, "-") === assignee
        )
      );
    }

    return filtered;
  }, [documents, searchTerm, appliedFilters]);

  // Sorting logic
  const sortedDocuments = useMemo(() => {
    if (!sortField || !sortDirection) return filteredDocuments;

    return [...filteredDocuments].sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === "publishedDate" || sortField === "reviewDate") {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });
  }, [filteredDocuments, sortField, sortDirection]);

  // Sorting handlers
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === "asc") {
        setSortDirection("desc");
      } else if (sortDirection === "desc") {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection("asc");
      }
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return <ArrowUpDown className="h-4 w-4" />;
    if (sortDirection === "asc") return <ArrowUp className="h-4 w-4" />;
    if (sortDirection === "desc") return <ArrowDown className="h-4 w-4" />;
    return <ArrowUpDown className="h-4 w-4" />;
  };

  // Row expansion handlers
  const toggleRowExpansion = (documentId: string) => {
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  // Filter handlers
  const handleStatusChange = (selectedStatuses: string[]) => {
    setAppliedFilters((prev) => ({
      ...prev,
      status: selectedStatuses,
    }));
  };

  const resetAllFilters = () => {
    setAppliedFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: [],
    });
    setSearchTerm("");
  };

  const hasActiveFilters =
    searchTerm.length > 0 ||
    Object.values(appliedFilters).some((arr) => arr.length > 0);

  return (
    <div className="w-full space-y-6 p-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
        <p className="text-gray-600">{subtitle}</p>
      </div>

      {/* Search and Filter Bar */}
      {showSearch && (
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="flex-1 max-w-md">
            <SearchBar onSearch={setSearchTerm} />
          </div>

          <div className="flex items-center gap-3">
            {showFilters && (
              <>
                <StatusFilter
                  appliedFilters={appliedFilters}
                  handleStatusChange={handleStatusChange}
                  statusOptions={statusOptions}
                />

                <Button
                  variant="outline"
                  onClick={() => setIsFilterPanelOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Filter className="h-4 w-4" />
                  Filters
                  {hasActiveFilters && (
                    <Badge variant="default" className="ml-1 bg-teal-600">
                      {Object.values(appliedFilters).reduce(
                        (acc, arr) => acc + arr.length,
                        0
                      ) + (searchTerm.length > 0 ? 1 : 0)}
                    </Badge>
                  )}
                </Button>

                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    onClick={resetAllFilters}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="h-4 w-4" />
                    Reset
                  </Button>
                )}
              </>
            )}

            <ViewToggle viewMode={viewMode} setViewMode={setViewMode} />

            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create
            </Button>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>
          Showing {sortedDocuments.length} of {documents.length} documents
        </span>
        {hasActiveFilters && (
          <span className="text-teal-600">Filters applied</span>
        )}
      </div>

      {/* Table */}
      {viewMode === "table" && (
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <ResizableTable>
            <TableHeader>
              <TableRow>
                <TableHead className="w-8">{/* Expand column */}</TableHead>
                <TableHead
                  onClick={() => handleSort("title")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Document
                    {getSortIcon("title")}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("department")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Department
                    {getSortIcon("department")}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("category")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Category
                    {getSortIcon("category")}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("status")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Status
                    {getSortIcon("status")}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("assignee")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Assignee
                    {getSortIcon("assignee")}
                  </div>
                </TableHead>
                <TableHead
                  onClick={() => handleSort("reviewDate")}
                  className="cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-2">
                    Review Date
                    {getSortIcon("reviewDate")}
                  </div>
                </TableHead>
                <TableHead className="w-32">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedDocuments.map((document) => (
                <React.Fragment key={document.id}>
                  <TableRow>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleRowExpansion(document.id)}
                        className="h-6 w-6"
                      >
                        {expandedRows.has(document.id) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </TableCell>
                    <TableCell>
                      <DocumentTitle document={document} />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-gray-400" />
                        {document.department}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-gray-400" />
                        {document.category}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DocumentStatusBadge status={document.status} />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        {document.assignee}
                      </div>
                    </TableCell>
                    <TableCell>
                      <ReviewDateCell reviewDate={document.reviewDate} />
                    </TableCell>
                    <TableCell>
                      <DocumentActions document={document} />
                    </TableCell>
                  </TableRow>

                  {/* Expanded Row Details */}
                  {expandedRows.has(document.id) && (
                    <TableRow>
                      <TableCell colSpan={8} className="bg-gray-50">
                        <div className="p-4 space-y-3">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">
                                Approver:
                              </span>
                              <div className="flex items-center gap-2 mt-1">
                                <User className="h-4 w-4 text-gray-400" />
                                {document.approver}
                              </div>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">
                                Published Date:
                              </span>
                              <div className="flex items-center gap-2 mt-1">
                                <Calendar className="h-4 w-4 text-gray-400" />
                                {formatDate(document.publishedDate)}
                              </div>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">
                                Processes:
                              </span>
                              <div className="flex items-center gap-2 mt-1">
                                <Settings className="h-4 w-4 text-gray-400" />
                                {document.processes || "-"}
                              </div>
                            </div>
                          </div>
                          {document.description && (
                            <div>
                              <span className="font-medium text-gray-700">
                                Description:
                              </span>
                              <p className="mt-1 text-gray-600">
                                {document.description}
                              </p>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </ResizableTable>
        </div>
      )}

      {/* Empty State */}
      {sortedDocuments.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No documents found
          </h3>
          <p className="text-gray-600 mb-4">
            {hasActiveFilters
              ? "Try adjusting your search or filters to find what you're looking for."
              : "Get started by creating your first document."}
          </p>
          {hasActiveFilters ? (
            <Button variant="outline" onClick={resetAllFilters}>
              Clear filters
            </Button>
          ) : (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Document
            </Button>
          )}
        </div>
      )}

      {/* Dialogs and Panels */}
      <FilterSidePanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
        appliedFilters={appliedFilters}
        onFiltersChange={setAppliedFilters}
      />

      <CreateDocumentDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
    </div>
  );
};

export default DocumentTable;


import React, { useRef, useEffect } from "react";
import { Message } from "./types";
import { ChatMessage } from "./ChatMessage";

interface ChatMessagesContainerProps {
  messages: Message[];
}

export const ChatMessagesContainer: React.FC<ChatMessagesContainerProps> = ({ messages }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    console.log("Messages updated in ChatMessagesContainer:", messages);
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex-1 overflow-auto p-4 pb-0">
      {messages.length === 0 ? (
        <div className="flex justify-center items-center h-full">
          <p className="text-gray-400">No messages yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {messages.map((message) => (
            <ChatMessage key={message.id} message={message} />
          ))}
          <div ref={messagesEndRef} />
        </div>
      )}
    </div>
  );
};


import React, { useState, useEffect } from "react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { useHelpChat } from "./hooks/useHelpChat";
import { ChatContainer } from "./ChatContainer";
import { Maximize2, Minimize2, HelpCircle, X } from "lucide-react";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";

export function HelpOverlay() {
  const { 
    messages,
    newMessage,
    selectedFile,
    setNewMessage,
    setSelectedFile,
    handleSendMessage,
    handleQuestionClick,
    handleFileUpload,
    handleInitialLoad,
  } = useHelpChat();

  const [isOpen, setIsOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [chatWidth, setChatWidth] = useState(400);

  useEffect(() => {
    if (isOpen) {
      handleInitialLoad(true);
    }
  }, [isOpen, handleInitialLoad]);

  // Add escape key handler for better UX
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeHelp();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  const toggleHelp = () => {
    console.log("Help button clicked, current state:", isOpen);
    setIsOpen(!isOpen);
  };

  const closeHelp = () => {
    console.log("Closing help overlay");
    setIsOpen(false);
    setIsFullScreen(false); // Reset fullscreen when closing
  };

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="fixed bottom-6 right-6 z-50 relative">
              <Button 
                onClick={toggleHelp} 
                size="icon" 
                className={`rounded-full w-20 h-20 shadow-xl hover:shadow-2xl transition-all ${
                  isOpen 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-teal-600 hover:bg-teal-700'
                }`}
              >
                <HelpCircle className="h-10 w-10 text-white" />
                {/* Status indicator */}
                <div className={`absolute -top-1 -right-1 w-6 h-6 rounded-full border-2 border-white transition-colors ${
                  isOpen ? 'bg-red-400' : 'bg-green-400'
                }`} />
              </Button>
              
              {/* Close button when help is open */}
              {isOpen && (
                <Button
                  onClick={closeHelp}
                  size="icon"
                  className="absolute -top-2 -left-2 w-8 h-8 rounded-full bg-red-500 hover:bg-red-600 text-white shadow-lg z-10"
                  title="Close help"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>{isOpen ? 'Close help (Click or press Esc)' : 'Need help? Click to open chat'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent 
          className={`p-0 border-l rounded-l-lg overflow-hidden transition-all duration-300 ${
            isFullScreen 
              ? 'w-screen h-screen !max-w-none sm:!max-w-none' 
              : `w-[${chatWidth}px] max-w-[90vw] sm:max-w-[${chatWidth}px]`
          }`} 
          style={!isFullScreen ? { width: `${chatWidth}px` } : undefined}
          side="right"
        >
          {/* Enhanced Header with prominent close button */}
          <div className="absolute top-4 right-4 z-50 flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleFullScreen}
              className="hover:bg-gray-100 text-gray-600 h-12 w-12 rounded-full bg-white/95 backdrop-blur-sm shadow-lg border"
              title={isFullScreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullScreen ? <Minimize2 className="h-6 w-6" /> : <Maximize2 className="h-6 w-6" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={closeHelp}
              className="hover:bg-red-50 text-red-600 h-12 w-12 rounded-full bg-white/95 backdrop-blur-sm shadow-lg border border-red-200"
              title="Close help (Esc)"
            >
              <X className="h-8 w-8" />
            </Button>
          </div>

          {/* Mobile overlay close area */}
          {isOpen && (
            <div 
              className="absolute inset-0 bg-black/30 backdrop-blur-sm z-40 md:hidden"
              onClick={closeHelp}
              aria-label="Close help overlay"
            />
          )}
          
          <div className="relative z-50 h-full bg-white">
            {!isFullScreen ? (
              <ResizablePanelGroup direction="horizontal">
                <ResizablePanel 
                  defaultSize={100} 
                  minSize={300}
                  onResize={(size) => {
                    const newWidth = Math.max(300, Math.min(800, (size / 100) * window.innerWidth));
                    setChatWidth(newWidth);
                  }}
                >
                  <ChatContainer 
                    messages={messages}
                    newMessage={newMessage}
                    selectedFile={selectedFile}
                    onMessageChange={setNewMessage}
                    onSendMessage={handleSendMessage}
                    onQuestionClick={handleQuestionClick}
                    onFileUpload={handleFileUpload}
                    onKeyPress={handleKeyPress}
                    onFileRemove={() => setSelectedFile(null)}
                  />
                </ResizablePanel>
                <ResizableHandle className="w-1 bg-gray-200 hover:bg-gray-300" />
              </ResizablePanelGroup>
            ) : (
              <ChatContainer 
                messages={messages}
                newMessage={newMessage}
                selectedFile={selectedFile}
                onMessageChange={setNewMessage}
                onSendMessage={handleSendMessage}
                onQuestionClick={handleQuestionClick}
                onFileUpload={handleFileUpload}
                onKeyPress={handleKeyPress}
                onFileRemove={() => setSelectedFile(null)}
              />
            )}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}

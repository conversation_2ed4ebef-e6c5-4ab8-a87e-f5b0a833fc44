
import { Message } from "./types";
import { mockDocuments } from "@/data/mockDocuments";
import { mockDocumentsBengaluru } from "@/data/mockDocumentsBengaluru";

export const initialMessages: Message[] = [
  {
    id: 1,
    text: "Hi there! I'm Agent <PERSON>, your manufacturing compliance assistant. How can I help you today?",
    sender: 'agent',
    timestamp: new Date(Date.now() - 2 * 60000),
  }
];

export const suggestedQuestions = [
  "How many documents are past due?",
  "What are the key requirements for ISO 14001?",
  "How do I prepare for an audit?",
  "Where can I find OSHA compliance checklists?"
];

export const generateResponse = (query: string): string => {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes("past due") || lowerQuery.includes("overdue")) {
    const allDocuments = [...mockDocuments, ...mockDocumentsBengaluru];
    const currentDate = new Date('2025-04-27'); // Using the current date from system
    
    const pastDueDocuments = allDocuments.filter(doc => {
      const reviewDate = new Date(doc.reviewDate);
      return reviewDate < currentDate;
    });

    if (pastDueDocuments.length === 0) {
      return "There are no past due documents at the moment.";
    }

    const documentDetails = pastDueDocuments.map(doc => {
      const reviewDate = new Date(doc.reviewDate);
      const daysOverdue = Math.floor((currentDate.getTime() - reviewDate.getTime()) / (1000 * 60 * 60 * 24));
      return `"${doc.title}" is ${daysOverdue} days overdue (Review date: ${doc.reviewDate})`;
    }).join('\n');

    return `I found ${pastDueDocuments.length} past due documents:\n\n${documentDetails}`;
  }
  
  if (lowerQuery.includes("iso") && lowerQuery.includes("9001")) {
    return "ISO 9001 is a quality management standard that helps organizations ensure they meet customer and regulatory requirements. Key requirements include leadership commitment, risk-based thinking, process approach, and continual improvement. Would you like specific information about any section?";
  }
  
  if (lowerQuery.includes("audit") || lowerQuery.includes("auditing")) {
    return "To prepare for a compliance audit, I recommend: 1) Reviewing your documentation, 2) Conducting a pre-audit assessment, 3) Training your team on audit procedures, 4) Ensuring corrective actions from previous audits are completed, and 5) Having key personnel available during the audit. Anything specific you'd like to focus on?";
  }
  
  if (lowerQuery.includes("osha") || lowerQuery.includes("safety")) {
    return "OSHA compliance involves maintaining a safe workplace free from recognized hazards. This includes proper training, hazard communication, emergency action plans, and regular safety inspections. I can provide specific checklists for your industry if you let me know what sector you're in.";
  }
  
  return "That's an interesting question about manufacturing compliance. To provide the most accurate information, could you provide more details about your specific industry and the regulations you're concerned with?";
};

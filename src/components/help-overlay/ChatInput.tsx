
import React from "react";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({ 
  value, 
  onChange, 
  onSend,
  onKeyDown
}) => {
  return (
    <div className="p-4 border-t bg-white">
      <div className="flex items-center gap-2">
        <div className="flex-1 relative">
          <textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={onKeyDown}
            placeholder="Type your question here..."
            className="w-full p-3 pr-10 border rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-teal-500 min-h-[56px] max-h-24"
            rows={1}
          />
        </div>
        <Button 
          onClick={onSend} 
          disabled={!value.trim()}
          className="bg-teal-600 hover:bg-teal-700"
        >
          <Send className="h-5 w-5" />
          <span className="sr-only">Send message</span>
        </Button>
      </div>
    </div>
  );
};


import React from "react";
import { Message } from "./types";
import { format } from "date-fns";
import { FileText } from "lucide-react";

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const { text, sender, timestamp, attachment } = message;

  return (
    <div
      className={`flex ${
        sender === "user" ? "justify-end" : "justify-start"
      } mb-4`}
    >
      <div
        className={`max-w-[75%] rounded-lg p-3 ${
          sender === "user"
            ? "bg-gray-100 text-gray-800"
            : "bg-white border border-gray-200"
        }`}
      >
        {text && <div className="text-sm mb-2">{text}</div>}
        
        {attachment && (
          <div className={`mt-2 p-2 rounded-md flex items-center gap-2 ${
            sender === "user" ? "bg-gray-200" : "bg-gray-100"
          }`}>
            <FileText className="h-4 w-4" />
            <span className="text-xs font-medium truncate max-w-[150px]">{attachment.name}</span>
          </div>
        )}
        
        <div className="mt-1 text-right">
          <span className="text-xs text-gray-500">
            {format(timestamp, "HH:mm")}
          </span>
        </div>
      </div>
    </div>
  );
};

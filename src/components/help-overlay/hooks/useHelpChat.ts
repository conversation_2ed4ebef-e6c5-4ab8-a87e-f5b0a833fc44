import { useState, useRef, useCallback } from 'react';
import { Message } from '../types';
import { initialMessages, generateResponse } from '../chatData';
import { toast } from "sonner";

export const useHelpChat = () => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [newMessage, setNewMessage] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const initialQuerySent = useRef(false);

  const handleInitialLoad = useCallback((isOpen: boolean) => {
    console.log("Initial load triggered, isOpen:", isOpen, "initialQuerySent:", initialQuerySent.current);
    
    if (isOpen && !initialQuerySent.current) {
      console.log("Processing initial load, sending initial query");
      const initialQuery = "How many documents are past due?";
      
      // Clear previous messages and ensure we start with the welcome message
      setMessages([...initialMessages]);
      
      // Add user query with slight delay to simulate typing
      setTimeout(() => {
        const userMessage: Message = {
          id: Date.now(),
          text: initialQuery,
          sender: 'user',
          timestamp: new Date(),
        };
        
        setMessages(prev => [...prev, userMessage]);
        
        // Add agent response with another slight delay
        setTimeout(() => {
          console.log("Adding agent response to initial query");
          const agentResponse: Message = {
            id: Date.now() + 1,
            text: generateResponse(initialQuery),
            sender: 'agent',
            timestamp: new Date(),
          };
          setMessages(prev => [...prev, agentResponse]);
          initialQuerySent.current = true;
        }, 800);
      }, 300);
    }
  }, []);

  const handleSendMessage = useCallback(() => {
    if (newMessage.trim() === "" && !selectedFile) return;
    
    const userMessage: Message = {
      id: Date.now(),
      text: newMessage.trim(),
      sender: 'user',
      timestamp: new Date(),
      attachment: selectedFile || undefined
    };
    
    console.log("Sending user message:", userMessage);
    setMessages(prev => [...prev, userMessage]);
    setNewMessage("");
    
    // Keep a reference to the file for generating the response
    const fileToProcess = selectedFile;
    // Clear selected file after sending
    setSelectedFile(null);
    
    // Longer delay if there's a file to simulate processing
    const processingDelay = fileToProcess ? 2000 : 1000;
    
    setTimeout(() => {
      let responseText = generateResponse(newMessage || "Thanks for your file");
      
      // Add file-specific text if a file was attached
      if (fileToProcess) {
        responseText = `I've processed your file "${fileToProcess.name}". ${responseText}`;
      }
      
      const agentResponse: Message = {
        id: Date.now() + 1,
        text: responseText,
        sender: 'agent',
        timestamp: new Date(),
      };
      console.log("Adding agent response:", agentResponse);
      setMessages(prev => [...prev, agentResponse]);
    }, processingDelay);
  }, [newMessage, selectedFile]);

  const handleQuestionClick = useCallback((question: string) => {
    const userMessage: Message = {
      id: Date.now(),
      text: question,
      sender: 'user',
      timestamp: new Date(),
    };
    
    console.log("Question clicked:", question);
    setMessages(prev => [...prev, userMessage]);
    
    setTimeout(() => {
      const agentResponse: Message = {
        id: Date.now() + 1,
        text: generateResponse(question),
        sender: 'agent',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, agentResponse]);
    }, 1000);
  }, []);

  const handleFileUpload = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      toast.success(`File "${file.name}" ready to upload`);
      
      // Reset input value so the same file can be selected again if needed
      e.target.value = '';
    }
  }, []);

  return {
    messages,
    newMessage,
    selectedFile,
    setNewMessage,
    setSelectedFile,
    handleSendMessage,
    handleQuestionClick,
    handleFileUpload,
    handleInitialLoad,
  };
};

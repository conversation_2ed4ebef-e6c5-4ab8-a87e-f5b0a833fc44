
import React from 'react';
import { <PERSON><PERSON><PERSON>eader } from './ChatHeader';
import { ChatMessagesContainer } from './ChatMessagesContainer';
import { SuggestedQuestions } from './SuggestedQuestions';
import { Message } from './types';
import { suggestedQuestions } from './chatData';
import { Button } from "@/components/ui/button";
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { FileUp, Send, X, FileText } from "lucide-react";

interface ChatContainerProps {
  messages: Message[];
  newMessage: string;
  selectedFile: File | null;
  onMessageChange: (value: string) => void;
  onSendMessage: () => void;
  onQuestionClick: (question: string) => void;
  onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onFileRemove: () => void;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  newMessage,
  selectedFile,
  onMessageChange,
  onSendMessage,
  onQuestionClick,
  onFileUpload,
  onKeyPress,
  onFileRemove,
}) => {
  // Add console.log to check messages and file uploads
  console.log("ChatContainer received messages:", messages);
  console.log("Selected file:", selectedFile);
  
  // Create a unique ID for the file input to avoid potential conflicts
  const fileInputId = "chat-file-" + Math.random().toString(36).substring(7);
  
  return (
    <div className="flex flex-col h-full bg-gray-50">
      <ChatHeader />
      <ChatMessagesContainer messages={messages} />
      <SuggestedQuestions questions={suggestedQuestions} onQuestionClick={onQuestionClick} />
      <div className="p-4 border-t bg-white">
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <textarea
              value={newMessage}
              onChange={(e) => onMessageChange(e.target.value)}
              onKeyDown={onKeyPress}
              placeholder="Type your question here..."
              className="w-full p-3 pr-10 border rounded-lg resize-none focus:outline-none focus:ring-1 focus:ring-teal-500 min-h-[56px] max-h-24"
              rows={1}
            />
          </div>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <input
                    type="file"
                    id={fileInputId}
                    className="hidden"
                    onChange={onFileUpload}
                    accept=".pdf,.doc,.docx,.txt"
                  />
                  <label htmlFor={fileInputId} className="cursor-pointer">
                    <Button 
                      type="button" 
                      variant="outline"
                      size="icon"
                      className="cursor-pointer"
                      tabIndex={-1}
                    >
                      <FileUp className="h-4 w-4" />
                      <span className="sr-only">Upload file</span>
                    </Button>
                  </label>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Upload a document</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  onClick={onSendMessage} 
                  disabled={!newMessage.trim() && !selectedFile}
                  className={`${(!newMessage.trim() && !selectedFile) 
                    ? 'bg-gray-400' 
                    : 'bg-teal-600 hover:bg-teal-700'}`}
                >
                  <Send className="h-4 w-4" />
                  <span className="sr-only">Send message</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Send message</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        {selectedFile && (
          <div className="mt-2 p-2 bg-gray-100 rounded flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="h-4 w-4 mr-2 text-gray-600" />
              <span className="text-sm truncate max-w-[200px]">{selectedFile.name}</span>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onFileRemove}
              className="h-6 w-6 p-0"
            >
              <span className="sr-only">Remove file</span>
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

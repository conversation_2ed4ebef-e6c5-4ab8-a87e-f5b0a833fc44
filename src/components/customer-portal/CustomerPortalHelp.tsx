
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  HelpCircle, 
  X, 
  FileText, 
  Download, 
  Eye, 
  MessageSquare,
  CheckCircle,
  Clock
} from "lucide-react";

interface CustomerPortalHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CustomerPortalHelp: React.FC<CustomerPortalHelpProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5 text-blue-600" />
            Customer Portal Guide
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">What is the Customer Portal?</h3>
            <p className="text-gray-600 mb-4">
              The Customer Portal is a secure interface that allows your customers to access relevant documents, 
              certifications, quality reports, and compliance materials specific to their orders and products.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <FileText className="h-4 w-4 text-blue-600" />
                Document Access
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Quality certificates and compliance reports</li>
                <li>• Product specifications and technical drawings</li>
                <li>• Test results and inspection reports</li>
                <li>• Delivery schedules and tracking information</li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Eye className="h-4 w-4 text-green-600" />
                Real-time Visibility
              </h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Order status and production progress</li>
                <li>• Quality metrics and performance data</li>
                <li>• Delivery tracking and logistics updates</li>
                <li>• Issue notifications and corrective actions</li>
              </ul>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-3">How Customers Use the Portal</h4>
            <div className="space-y-3">
              <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                <div>
                  <p className="font-medium">Access Granted</p>
                  <p className="text-sm text-gray-600">Customers receive secure login credentials for their dedicated portal space</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                <div className="w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                <div>
                  <p className="font-medium">Browse & Download</p>
                  <p className="text-sm text-gray-600">View and download documents, certificates, and reports related to their orders</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                <div className="w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                <div>
                  <p className="font-medium">Track Progress</p>
                  <p className="text-sm text-gray-600">Monitor order status, quality metrics, and delivery schedules in real-time</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-3">Portal Features</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-3 border rounded-lg text-center">
                <Download className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="font-medium">Document Downloads</p>
                <p className="text-xs text-gray-600">Secure access to all relevant files</p>
              </div>
              
              <div className="p-3 border rounded-lg text-center">
                <MessageSquare className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="font-medium">Communication</p>
                <p className="text-xs text-gray-600">Direct messaging and notifications</p>
              </div>
              
              <div className="p-3 border rounded-lg text-center">
                <CheckCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="font-medium">Status Updates</p>
                <p className="text-xs text-gray-600">Real-time order and quality status</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2 flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              Benefits for Your Business
            </h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Reduces customer inquiries by providing self-service access</li>
              <li>• Improves customer satisfaction with transparency</li>
              <li>• Streamlines document sharing and compliance reporting</li>
              <li>• Enhances customer relationships through better communication</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";

export const CustomerPortalTableHeader: React.FC = () => {
  return (
    <TableHeader className="sticky top-0 z-20 bg-gray-50">
      <TableRow>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50"
          style={{ width: '250px', minWidth: '250px' }}
        >
          Customer
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50"
          style={{ width: '200px', minWidth: '180px' }}
        >
          Contact
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50"
          style={{ width: '120px', minWidth: '120px' }}
        >
          Status
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50"
          style={{ width: '140px', minWidth: '140px' }}
        >
          Audit Status
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50 text-center"
          style={{ width: '120px', minWidth: '120px' }}
        >
          Compliance Score
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50 text-center"
          style={{ width: '120px', minWidth: '120px' }}
        >
          Documents
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50"
          style={{ width: '140px', minWidth: '140px' }}
        >
          Last Accessed
        </TableHead>
        <TableHead 
          className="py-3 px-6 text-xs font-semibold text-gray-600 uppercase tracking-wider bg-gray-50 text-center"
          style={{ width: '180px', minWidth: '180px' }}
        >
          Actions
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

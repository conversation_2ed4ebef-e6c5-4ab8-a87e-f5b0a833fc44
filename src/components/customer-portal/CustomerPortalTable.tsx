
import React from "react";
import { TableBody } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ExternalLink, 
  Users,
  Settings
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";
import { CustomerPortalTableHeader } from "./CustomerPortalTableHeader";
import { Customer } from "@/data/mockCustomers";

interface CustomerPortalTableProps {
  customers: Customer[];
  onManageCustomer: (customerId: string) => void;
  onViewPortal: (customerId: string) => void;
}

export const CustomerPortalTable: React.FC<CustomerPortalTableProps> = ({
  customers,
  onManageCustomer,
  onViewPortal
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Read-only":
        return <Badge className="bg-yellow-100 text-yellow-800">Read-only</Badge>;
      case "Access Expired":
        return <Badge className="bg-red-100 text-red-800">Access Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getAuditStatusBadge = (status: string) => {
    switch (status) {
      case "Passed":
        return <Badge className="bg-green-100 text-green-800">Passed</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "Failed":
        return <Badge className="bg-red-100 text-red-800">Failed</Badge>;
      case "Scheduled":
        return <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (isMobile) {
    return (
      <div className="bg-white rounded-xl border border-slate-200 shadow-sm">
        <div className="p-4 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900">Customer Portals</h3>
        </div>
        <div className="divide-y divide-slate-100">
          {customers.map((customer, index) => (
            <div key={customer.id} className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-slate-900">{customer.name}</h4>
                  <p className="text-sm text-slate-600">{customer.qaContact}</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between mb-3">
                <div className="flex gap-2">
                  {getStatusBadge(customer.status)}
                  {getAuditStatusBadge(customer.auditStatus)}
                </div>
                <div className="text-right text-sm text-slate-600">
                  <div>Score: {customer.complianceScore}%</div>
                  <div>{customer.sharedDocuments} docs</div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => onManageCustomer(customer.id)}
                  className="flex-1"
                >
                  <Settings className="h-4 w-4 mr-1" />
                  Manage
                </Button>
                <Button 
                  size="sm"
                  onClick={() => onViewPortal(customer.id)}
                  className="flex-1"
                >
                  <ExternalLink className="h-4 w-4 mr-1" />
                  View Portal
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-slate-200 shadow-sm">
      <div className="p-6 border-b border-slate-200">
        <h3 className="text-lg font-semibold text-slate-900">Customer Portal Access</h3>
      </div>
      <div className="overflow-auto">
        <ResizableTable className="w-full" minColumnWidth={120}>
          <CustomerPortalTableHeader />
          <TableBody>
            {customers.map((customer, index) => (
              <tr key={customer.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors`}>
                <td className="py-4 px-6" style={{ width: '250px', minWidth: '250px' }}>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-slate-900">{customer.name}</div>
                      <div className="text-sm text-slate-600">{customer.industry}</div>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-6" style={{ width: '200px', minWidth: '180px' }}>
                  <div>
                    <div className="font-medium text-slate-900">{customer.qaContact}</div>
                    <div className="text-sm text-slate-600">QA Contact</div>
                  </div>
                </td>
                <td className="py-4 px-6" style={{ width: '120px', minWidth: '120px' }}>
                  {getStatusBadge(customer.status)}
                </td>
                <td className="py-4 px-6" style={{ width: '140px', minWidth: '140px' }}>
                  {getAuditStatusBadge(customer.auditStatus)}
                </td>
                <td className="py-4 px-6 text-center" style={{ width: '120px', minWidth: '120px' }}>
                  <div className="text-lg font-semibold text-green-600">{customer.complianceScore}%</div>
                  <div className="text-xs text-slate-500">Compliance</div>
                </td>
                <td className="py-4 px-6 text-center" style={{ width: '120px', minWidth: '120px' }}>
                  <div className="text-lg font-semibold text-blue-600">{customer.sharedDocuments}</div>
                  <div className="text-xs text-slate-500">Documents</div>
                </td>
                <td className="py-4 px-6" style={{ width: '140px', minWidth: '140px' }}>
                  <div className="text-sm text-slate-600">{customer.lastAccessed}</div>
                </td>
                <td className="py-4 px-6" style={{ width: '180px', minWidth: '180px' }}>
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onManageCustomer(customer.id)}
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Manage
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => onViewPortal(customer.id)}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View Portal
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </TableBody>
        </ResizableTable>
      </div>
    </div>
  );
};

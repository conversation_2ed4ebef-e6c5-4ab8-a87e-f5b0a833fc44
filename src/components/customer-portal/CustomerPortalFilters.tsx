
import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter, 
  Download,
  Plus,
  X
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CustomerPortalFiltersProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  industryFilter: string;
  setIndustryFilter: (industry: string) => void;
  auditStatusFilter: string;
  setAuditStatusFilter: (status: string) => void;
  onAddCustomer: () => void;
  onExportData: () => void;
}

export const CustomerPortalFilters: React.FC<CustomerPortalFiltersProps> = ({
  searchQuery,
  setSearchQuery,
  statusFilter,
  setStatusFilter,
  industryFilter,
  setIndustryFilter,
  auditStatusFilter,
  setAuditStatusFilter,
  onAddCustomer,
  onExportData
}) => {
  const activeFiltersCount = [statusFilter, industryFilter, auditStatusFilter].filter(Boolean).length;

  const clearAllFilters = () => {
    setStatusFilter("");
    setIndustryFilter("");
    setAuditStatusFilter("");
    setSearchQuery("");
  };

  return (
    <div className="space-y-4">
      {/* Search and Actions Row */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="flex-1 relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search customers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={onExportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={onAddCustomer}>
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Filters Row */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filters:</span>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Read-only">Read-only</SelectItem>
            <SelectItem value="Access Expired">Access Expired</SelectItem>
          </SelectContent>
        </Select>

        <Select value={industryFilter} onValueChange={setIndustryFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Industry" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Industries</SelectItem>
            <SelectItem value="Aerospace">Aerospace</SelectItem>
            <SelectItem value="Automotive">Automotive</SelectItem>
            <SelectItem value="Electronics">Electronics</SelectItem>
            <SelectItem value="Medical">Medical</SelectItem>
          </SelectContent>
        </Select>

        <Select value={auditStatusFilter} onValueChange={setAuditStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Audit Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Audit Statuses</SelectItem>
            <SelectItem value="Passed">Passed</SelectItem>
            <SelectItem value="Pending">Pending</SelectItem>
            <SelectItem value="Failed">Failed</SelectItem>
            <SelectItem value="Scheduled">Scheduled</SelectItem>
          </SelectContent>
        </Select>

        {activeFiltersCount > 0 && (
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {activeFiltersCount} filter{activeFiltersCount > 1 ? 's' : ''} active
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

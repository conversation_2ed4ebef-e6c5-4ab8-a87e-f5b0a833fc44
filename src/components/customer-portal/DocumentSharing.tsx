
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FileText, Download, Share, Eye, Calendar, Search } from "lucide-react";

interface SharedDocument {
  id: string;
  name: string;
  type: string;
  size: string;
  sharedDate: string;
  expiryDate: string;
  downloads: number;
  status: 'Active' | 'Expired' | 'Pending';
  sharedWith: string;
}

const mockSharedDocuments: SharedDocument[] = [
  {
    id: "SHARED-001",
    name: "Quality Manual v2.1",
    type: "Quality Manual",
    size: "2.3 MB",
    sharedDate: "2024-01-15",
    expiryDate: "2024-07-15",
    downloads: 15,
    status: "Active",
    sharedWith: "Boeing Team"
  },
  {
    id: "SHARED-002",
    name: "AS9100 Certificate",
    type: "Certificate",
    size: "1.1 MB",
    sharedDate: "2024-01-10",
    expiryDate: "2025-01-10",
    downloads: 8,
    status: "Active",
    sharedWith: "Airbus Quality"
  },
  {
    id: "SHARED-003",
    name: "Process Flow Diagram",
    type: "Process Document",
    size: "5.2 MB",
    sharedDate: "2023-12-20",
    expiryDate: "2024-01-20",
    downloads: 3,
    status: "Expired",
    sharedWith: "Lockheed Martin"
  }
];

export const DocumentSharing: React.FC = () => {
  const [documents] = useState<SharedDocument[]>(mockSharedDocuments);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredDocuments = documents.filter(doc => 
    doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.sharedWith.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const variants = {
      'Active': 'bg-green-100 text-green-800',
      'Expired': 'bg-red-100 text-red-800',
      'Pending': 'bg-yellow-100 text-yellow-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const handleShare = (documentId: string) => {
    console.log("Sharing document:", documentId);
  };

  const handleDownload = (documentId: string) => {
    console.log("Downloading document:", documentId);
  };

  const handlePreview = (documentId: string) => {
    console.log("Previewing document:", documentId);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share className="h-5 w-5" />
            Shared Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-72">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button className="flex items-center gap-2">
              <Share className="h-4 w-4" />
              Share New Document
            </Button>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Shared With</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Shared Date</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Downloads</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDocuments.map((document) => (
                <TableRow key={document.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <div>
                        <div className="font-medium">{document.name}</div>
                        <div className="text-sm text-gray-500">{document.type} • {document.size}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{document.sharedWith}</TableCell>
                  <TableCell>{getStatusBadge(document.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      {document.sharedDate}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      {document.expiryDate}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3 text-gray-400" />
                      {document.downloads}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => handlePreview(document.id)}
                        className="flex items-center gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        Preview
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => handleDownload(document.id)}
                        className="flex items-center gap-1"
                      >
                        <Download className="h-3 w-3" />
                        Download
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

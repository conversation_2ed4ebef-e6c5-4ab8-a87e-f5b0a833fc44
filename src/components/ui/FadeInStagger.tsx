
import React from "react";

// Wrap children in staggered fade-in animation; good for lists/grids.
// Usage: <FadeInStagger><YourList /></FadeInStagger>
export const FadeInStagger: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Allows stagger for direct children, fallback to a single fade-in if not an array
  if (Array.isArray(children)) {
    return (
      <>
        {children.map((child, i) => (
          <div
            key={i}
            className={`animate-fade-in will-change-transform delay-${i * 75}`}
            style={{ animationFillMode: "both" }}
          >
            {child}
          </div>
        ))}
      </>
    );
  }
  // fallback for single children element
  return (
    <div className="animate-fade-in will-change-transform" style={{ animationFillMode: "both" }}>
      {children}
    </div>
  );
};

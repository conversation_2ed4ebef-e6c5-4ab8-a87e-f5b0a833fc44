
export interface AgentOctoIconProps {
  /**
   * Additional CSS classes to apply to the icon container
   */
  className?: string;
  
  /**
   * Size of the icon in pixels (applied to both width and height)
   * @default 24
   */
  size?: number;
  
  /**
   * Alt text for the icon image for accessibility
   * @default "Agent Octo"
   */
  alt?: string;
  
  /**
   * Source URL for the icon image
   * @default "/lovable-uploads/c291cf2c-2b4e-42d6-a251-6223fd0e98af.png"
   */
  src?: string;
  
  /**
   * Fallback text to display when image fails to load
   * @default "AO"
   */
  fallback?: string;
  
  /**
   * Background color to use for fallback display
   * @default "bg-teal-600"
   */
  fallbackBgColor?: string;
  
  /**
   * Text color to use for fallback display
   * @default "text-white"
   */
  fallbackTextColor?: string;
}


import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { AgentOctoIconProps } from "./types";
import { DoctopusIcon } from "@/components/ui/doctopus-icon";

export const AgentOctoIcon: React.FC<AgentOctoIconProps> = ({
  className,
  size = 24,
  alt = "Agent Octo",
  src = "/lovable-uploads/c291cf2c-2b4e-42d6-a251-6223fd0e98af.png",
  fallback = "AO",
  fallbackBgColor = "bg-teal-600",
  fallbackTextColor = "text-white",
}) => {
  const [imageError, setImageError] = useState(false);
  
  const handleImageError = () => {
    setImageError(true);
    console.log("Image failed to load:", src);
  };

  return (
    <div 
      className={cn(
        "flex items-center justify-center overflow-hidden rounded-full", 
        "bg-white border border-teal-100", 
        className
      )} 
      style={{ 
        width: size, 
        height: size,
        maxWidth: size,
        maxHeight: size,
        minWidth: size,
        minHeight: size
      }}
    >
      {src && !imageError ? (
        <img 
          src={src}
          alt={alt} 
          className="w-full h-full"
          onError={handleImageError}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "cover"
          }}
        />
      ) : (
        <div className={cn(
          "w-full h-full flex items-center justify-center",
          fallbackBgColor,
          fallbackTextColor
        )}>
          <DoctopusIcon size={size} className="w-full h-full" />
        </div>
      )}
    </div>
  );
};

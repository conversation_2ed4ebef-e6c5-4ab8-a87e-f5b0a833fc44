
import React from "react";
import { DoctopusIcon } from "./doctopus-icon";
import { cn } from "@/lib/utils";

interface LoadingAnimationProps {
  isLoading: boolean;
  className?: string;
}

export const LoadingAnimation = ({ isLoading, className }: LoadingAnimationProps) => {
  if (!isLoading) return null;
  
  return (
    <div className={cn(
      "fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center",
      className
    )}>
      <div className="bg-white rounded-lg p-6 shadow-lg flex flex-col items-center max-w-xs">
        <div className="relative">
          <DoctopusIcon size={64} className="text-teal-600 animate-pulse" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-12 h-12 border-4 border-t-transparent border-primary rounded-full animate-spin"></div>
          </div>
        </div>
        <div className="mt-4 animate-bounce flex space-x-1">
          {['bg-blue-500', 'bg-teal-500', 'bg-green-500', 'bg-cyan-500', 'bg-emerald-500'].map((color, i) => (
            <div 
              key={i} 
              className={`w-2 h-2 rounded-full ${color}`}
              style={{ animationDelay: `${i * 0.1}s` }}
            />
          ))}
        </div>
        <p className="mt-4 text-sm text-center text-gray-700">Loading...</p>
      </div>
    </div>
  );
};

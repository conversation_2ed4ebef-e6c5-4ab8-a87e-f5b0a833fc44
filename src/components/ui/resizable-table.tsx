
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Table } from './table';

interface ResizableTableProps {
  children: React.ReactNode;
  className?: string;
  minColumnWidth?: number;
  onColumnResize?: (columnIndex: number, newWidth: number) => void;
}

export const ResizableTable: React.FC<ResizableTableProps> = ({ 
  children, 
  className,
  minColumnWidth = 80,
  onColumnResize
}) => {
  const tableRef = useRef<HTMLTableElement>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<number | null>(null);

  const initializeResizeHandles = useCallback(() => {
    const table = tableRef.current;
    if (!table) return;

    const headers = table.querySelectorAll('th');
    
    // Remove existing resize handles
    table.querySelectorAll('.resize-handle').forEach(handle => handle.remove());

    headers.forEach((th, index) => {
      // Skip the last column and action columns
      if (index === headers.length - 1) return;
      
      // Ensure proper positioning
      th.style.position = 'relative';
      th.style.minWidth = th.style.width || `${minColumnWidth}px`;

      // Create resize handle
      const resizeHandle = document.createElement('div');
      resizeHandle.className = 'resize-handle';
      resizeHandle.style.cssText = `
        position: absolute;
        top: 0;
        right: 0;
        width: 4px;
        height: 100%;
        cursor: col-resize;
        background: transparent;
        border-right: 2px solid transparent;
        transition: all 0.2s ease;
        z-index: 10;
        user-select: none;
      `;

      // Hover effects
      const handleMouseEnter = () => {
        if (!isResizing) {
          resizeHandle.style.borderRightColor = '#3b82f6';
          resizeHandle.style.background = 'rgba(59, 130, 246, 0.1)';
        }
      };

      const handleMouseLeave = () => {
        if (!isResizing) {
          resizeHandle.style.borderRightColor = 'transparent';
          resizeHandle.style.background = 'transparent';
        }
      };

      resizeHandle.addEventListener('mouseenter', handleMouseEnter);
      resizeHandle.addEventListener('mouseleave', handleMouseLeave);

      // Resize functionality
      const handleMouseDown = (e: MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        
        setIsResizing(true);
        setResizingColumn(index);
        
        const startX = e.clientX;
        const startWidth = th.offsetWidth;
        
        resizeHandle.style.borderRightColor = '#3b82f6';
        resizeHandle.style.background = 'rgba(59, 130, 246, 0.2)';
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';

        const handleMouseMove = (e: MouseEvent) => {
          const diff = e.clientX - startX;
          const newWidth = Math.max(minColumnWidth, startWidth + diff);
          
          th.style.width = `${newWidth}px`;
          th.style.minWidth = `${newWidth}px`;
          th.style.maxWidth = `${newWidth}px`;
          
          // Call callback if provided
          if (onColumnResize) {
            onColumnResize(index, newWidth);
          }
        };

        const handleMouseUp = () => {
          setIsResizing(false);
          setResizingColumn(null);
          
          resizeHandle.style.borderRightColor = 'transparent';
          resizeHandle.style.background = 'transparent';
          document.body.style.cursor = '';
          document.body.style.userSelect = '';
          
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
      };

      resizeHandle.addEventListener('mousedown', handleMouseDown);
      th.appendChild(resizeHandle);
    });
  }, [isResizing, minColumnWidth, onColumnResize]);

  useEffect(() => {
    // Initialize resize handles after content renders
    const timeoutId = setTimeout(initializeResizeHandles, 0);
    
    return () => {
      clearTimeout(timeoutId);
      // Clean up resize handles
      const table = tableRef.current;
      if (table) {
        table.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
      }
    };
  }, [children, initializeResizeHandles]);

  return (
    <div className="relative overflow-auto">
      <Table 
        ref={tableRef} 
        className={`${className} ${isResizing ? 'select-none' : ''}`}
      >
        {children}
      </Table>
    </div>
  );
};

// Simplified higher-order component for easy integration
export const withResizableColumns = (
  options?: {
    minColumnWidth?: number;
    onColumnResize?: (columnIndex: number, newWidth: number) => void;
  }
) => {
  return function<TProps>(Component: React.ComponentType<TProps>) {
    const WrappedComponent: React.FC<TProps> = (props) => (
      <ResizableTable 
        minColumnWidth={options?.minColumnWidth}
        onColumnResize={options?.onColumnResize}
      >
        <Component {...props} />
      </ResizableTable>
    );
    
    WrappedComponent.displayName = `withResizableColumns(${Component.displayName || Component.name})`;
    
    return WrappedComponent;
  };
};

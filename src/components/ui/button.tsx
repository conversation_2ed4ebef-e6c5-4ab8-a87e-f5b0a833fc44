
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2.5 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-250 ease-smooth focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform-gpu hover:scale-[1.02] active:scale-[0.98] [&_svg]:pointer-events-none [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-teal-600 text-white shadow-soft hover:bg-teal-700 hover:shadow-medium focus:ring-teal-500/50",
        destructive: "bg-red-600 text-white shadow-soft hover:bg-red-700 hover:shadow-medium focus:ring-red-500/50",
        outline: "border border-slate-200 bg-white text-slate-900 shadow-subtle hover:bg-slate-50 hover:border-slate-300 hover:shadow-soft focus:ring-slate-500/50",
        secondary: "bg-slate-100 text-slate-900 shadow-subtle hover:bg-slate-200 hover:shadow-soft focus:ring-slate-500/50",
        ghost: "text-slate-700 hover:bg-slate-100 hover:text-slate-900 focus:ring-slate-500/50",
        link: "text-teal-600 underline-offset-4 hover:underline hover:text-teal-700 focus:ring-teal-500/50",
      },
      size: {
        default: "h-11 px-6 py-2.5",
        sm: "h-9 rounded-lg px-4 text-sm",
        lg: "h-13 rounded-xl px-8 text-base font-semibold",
        icon: "h-11 w-11",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

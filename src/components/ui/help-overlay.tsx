
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { HelpCircle, X, ChevronRight, ChevronLeft } from "lucide-react";

interface HelpContent {
  title: string;
  description: string;
  steps?: string[];
  tips?: string[];
}

interface HelpOverlayProps {
  helpContent: HelpContent[];
  moduleName: string;
}

export const HelpOverlay: React.FC<HelpOverlayProps> = ({ helpContent, moduleName }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < helpContent.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentContent = helpContent[currentStep];

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        className="fixed bottom-4 right-4 z-50 rounded-full bg-blue-600 text-white hover:bg-blue-700 border-blue-600"
        onClick={() => setIsOpen(true)}
      >
        <HelpCircle className="h-5 w-5" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{moduleName} - Help Guide</span>
              <Badge variant="outline">
                {currentStep + 1} of {helpContent.length}
              </Badge>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-lg font-semibold mb-3">{currentContent.title}</h3>
                <p className="text-gray-600 mb-4">{currentContent.description}</p>

                {currentContent.steps && (
                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Steps:</h4>
                    <ol className="list-decimal list-inside space-y-1">
                      {currentContent.steps.map((step, index) => (
                        <li key={index} className="text-sm text-gray-600">{step}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {currentContent.tips && (
                  <div>
                    <h4 className="font-medium mb-2">Tips:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {currentContent.tips.map((tip, index) => (
                        <li key={index} className="text-sm text-blue-600">{tip}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 0}
                className="flex items-center gap-2"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex gap-2">
                {helpContent.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full ${
                      index === currentStep ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="outline"
                onClick={nextStep}
                disabled={currentStep === helpContent.length - 1}
                className="flex items-center gap-2"
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex justify-end">
              <Button onClick={() => setIsOpen(false)}>
                Close Guide
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

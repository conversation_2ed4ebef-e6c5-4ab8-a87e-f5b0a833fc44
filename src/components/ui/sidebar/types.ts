export type SidebarContext = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
}

export const SIDEBAR_COOKIE_NAME = "sidebar:state"
export const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7
export const SIDEBAR_WIDTH = "230px" // Increased from default
export const SIDEBAR_WIDTH_ICON = "70px" // Increased from default
export const SIDEBAR_WIDTH_MOBILE = "280px"
export const SIDEBAR_KEYBOARD_SHORTCUT = "b"

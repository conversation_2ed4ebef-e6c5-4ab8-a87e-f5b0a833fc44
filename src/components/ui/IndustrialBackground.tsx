
import React from "react";

export const IndustrialBackground: React.FC<{ className?: string }> = ({ className = "" }) => (
  <div
    className={`fixed inset-0 pointer-events-none z-0 ${className}`}
    aria-hidden="true"
    style={{
      background:
        "linear-gradient(135deg, rgba(15,23,42,0.86) 0%, rgba(37,99,235,0.05) 100%)",
    }}
  >
    {/* SVG Grid overlay */}
    <svg
      width="100%"
      height="100%"
      className="absolute inset-0 w-full h-full opacity-10 select-none pointer-events-none"
      style={{ minHeight: "100vh" }}
      aria-hidden="true"
    >
      <defs>
        <pattern id="industrialGrid" width="32" height="32" patternUnits="userSpaceOnUse">
          <rect x="0" y="0" width="32" height="32" fill="none" />
          <path
            d="M 32 0 L 0 0 0 32"
            fill="none"
            stroke="#93c5fd"
            strokeWidth="0.5"
            opacity="0.21"
          />
          <circle cx="0.5" cy="0.5" r="0.5" fill="#fbbf24" opacity="0.13"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#industrialGrid)" />
    </svg>
    {/* Top right accent gradient */}
    <div className="absolute top-0 right-0 h-36 w-48 bg-gradient-to-bl from-blue-400/10 to-orange-200/0 rounded-full blur-xl opacity-50" />
  </div>
);

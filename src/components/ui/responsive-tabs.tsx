
import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"
import { ChevronDown } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"

const ResponsiveTabs = TabsPrimitive.Root

interface ResponsiveTabsListProps extends React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> {
  tabItems?: Array<{ value: string; label: string; icon?: React.ReactNode }>
  currentValue?: string
  onValueChange?: (value: string) => void
}

const ResponsiveTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  ResponsiveTabsListProps
>(({ className, children, tabItems, currentValue, onValueChange, ...props }, ref) => {
  const isMobile = useIsMobile()
  
  if (isMobile && tabItems && currentValue) {
    const currentTab = tabItems.find(item => item.value === currentValue)
    
    return (
      <div className="w-full mb-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-full justify-between">
              <div className="flex items-center gap-2">
                {currentTab?.icon}
                {currentTab?.label || 'Select tab'}
              </div>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-full">
            {tabItems.map((item) => (
              <DropdownMenuItem
                key={item.value}
                onClick={() => onValueChange?.(item.value)}
                className="flex items-center gap-2"
              >
                {item.icon}
                {item.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  return (
    <TabsPrimitive.List
      ref={ref}
      className={cn(
        "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground overflow-x-auto scrollbar-hide",
        isMobile && "w-full min-w-0",
        className
      )}
      {...props}
    >
      <div className={cn("flex", isMobile && "min-w-max")}>
        {children}
      </div>
    </TabsPrimitive.List>
  )
})
ResponsiveTabsList.displayName = "ResponsiveTabsList"

const ResponsiveTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => {
  const isMobile = useIsMobile()
  
  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-teal-600 data-[state=active]:shadow-sm",
        isMobile && "min-w-0 flex-shrink-0",
        className
      )}
      {...props}
    />
  )
})
ResponsiveTabsTrigger.displayName = "ResponsiveTabsTrigger"

const ResponsiveTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
))
ResponsiveTabsContent.displayName = "ResponsiveTabsContent"

export { ResponsiveTabs, ResponsiveTabsList, ResponsiveTabsTrigger, ResponsiveTabsContent }

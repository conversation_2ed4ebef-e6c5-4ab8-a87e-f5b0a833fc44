
import React from "react";

interface DoctopusIconProps {
  className?: string;
  size?: number;
}

export const DoctopusIcon: React.FC<DoctopusIconProps> = ({ className, size = 24 }) => {
  return (
    <div className={className} style={{ width: size, height: size }}>
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 512 512" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M340 140C340 188.601 300.601 228 252 228C203.399 228 164 188.601 164 140C164 91.3989 203.399 52 252 52C300.601 52 340 91.3989 340 140Z"
          fill="#3E9B9C"
          stroke="#0A373A"
          strokeWidth="16"
        />
        <path
          d="M252 228C203.399 228 164 188.601 164 140H132C132 206.274 186.726 260 252 260V228Z"
          fill="#3E9B9C"
        />
        <path
          d="M252 228C300.601 228 340 188.601 340 140H372C372 206.274 317.274 260 252 260V228Z"
          fill="#3E9B9C"
        />
        <path
          d="M150 130H354"
          stroke="#0A373A"
          strokeWidth="16"
          strokeLinecap="round"
        />
        <path
          d="M252 92C252 92 222 208 252 208"
          stroke="#0A373A"
          strokeWidth="8"
        />
        <path
          d="M252 92C252 92 282 208 252 208"
          stroke="#0A373A"
          strokeWidth="8"
        />
        <path
          d="M340 250C340 250 380 310 380 350C380 372 360 390 340 390C320 390 300 370 300 350C300 330 310 320 310 300"
          stroke="#0A373A"
          strokeWidth="16"
          strokeLinecap="round"
        />
        <path
          d="M164 250C164 250 124 310 124 350C124 372 144 390 164 390C184 390 204 370 204 350C204 330 194 320 194 300"
          stroke="#0A373A"
          strokeWidth="16"
          strokeLinecap="round"
        />
        <path
          d="M236 260C236 260 196 310 196 370C196 392 216 410 236 410C256 410 276 390 276 370"
          stroke="#0A373A"
          strokeWidth="16"
          strokeLinecap="round"
        />
        <path
          d="M268 260C268 260 308 310 308 370C308 392 288 410 268 410C248 410 228 390 228 370"
          stroke="#0A373A"
          strokeWidth="16"
          strokeLinecap="round"
        />
        <circle cx="220" cy="140" r="24" fill="white" stroke="#0A373A" strokeWidth="8"/>
        <circle cx="284" cy="140" r="24" fill="white" stroke="#0A373A" strokeWidth="8"/>
        <circle cx="220" cy="140" r="8" fill="#0A373A"/>
        <circle cx="284" cy="140" r="8" fill="#0A373A"/>
        <path
          d="M240 180C240 180 252 190 264 180"
          stroke="#0A373A"
          strokeWidth="8"
          strokeLinecap="round"
        />
        <path
          d="M160 80L150 58"
          stroke="#0A373A"
          strokeWidth="8"
          strokeLinecap="round"
        />
        <path
          d="M344 80L354 58"
          stroke="#0A373A"
          strokeWidth="8"
          strokeLinecap="round"
        />
        <rect x="240" y="180" width="24" height="8" rx="4" fill="#0A373A"/>
      </svg>
    </div>
  );
};

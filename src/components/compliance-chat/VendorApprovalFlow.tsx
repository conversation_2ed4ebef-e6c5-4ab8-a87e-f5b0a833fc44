
import React from 'react';
import { ComplianceMessage } from './types';
import { useToast } from '@/hooks/use-toast';

export const VENDOR_STEPS = [
  "1. Analyzing vendor profile and historical data",
  "2. Reviewing attached documents and certifications",
  "3. Checking compliance requirements and standards",
  "4. Generating approval recommendations",
  "5. Creating final document with approval status"
];

interface VendorApprovalFlowProps {
  onMessageAdd: (message: ComplianceMessage) => void;
}

export function VendorApprovalFlow({ onMessageAdd }: VendorApprovalFlowProps) {
  const { toast } = useToast();

  const handleVendorApproval = async () => {
    const steps = [...VENDOR_STEPS];
    
    const initialMessage: ComplianceMessage = {
      id: Date.now().toString(),
      text: "I'll help you generate a vendor approval document. Here are the steps I'll follow:",
      sender: 'assistant',
      timestamp: new Date(),
    };
    onMessageAdd(initialMessage);

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      const stepMessage: ComplianceMessage = {
        id: (Date.now() + i).toString(),
        text: steps[i],
        sender: 'assistant',
        timestamp: new Date(),
      };
      onMessageAdd(stepMessage);
    }

    setTimeout(() => {
      const resultMessage: ComplianceMessage = {
        id: Date.now().toString(),
        text: "✅ Vendor Approval Document Generated\n\nBased on the analysis:\n- Vendor meets all compliance requirements\n- Documentation is up to date\n- Risk assessment: Low\n- Recommended for approval\n\nWould you like me to explain any specific aspect of the assessment?",
        sender: 'assistant',
        timestamp: new Date(),
      };
      onMessageAdd(resultMessage);
      
      toast({
        title: "Document Generated",
        description: "Vendor approval document has been generated successfully.",
      });
    }, 2000);
  };

  return { handleVendorApproval };
}

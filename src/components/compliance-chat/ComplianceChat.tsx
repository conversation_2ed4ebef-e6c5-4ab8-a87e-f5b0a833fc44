
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { FileSearch, PhoneCall } from 'lucide-react';
import { ComplianceChatMessages } from './ComplianceChatMessages';
import { ComplianceChatInput } from './ComplianceChatInput';
import { SuggestedQuestions } from './SuggestedQuestions';
import { useComplianceChat } from './hooks/useComplianceChat';
import { AgentOctoIcon } from '@/components/ui/agent-octo-icon';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";

const suggestedQuestions = [
  { id: '1', text: 'What are the current quality issues in my supply chain?' },
  { id: '2', text: 'Which suppliers need immediate attention?' },
  { id: '3', text: 'Show me compliance requirements for automotive suppliers' },
  { id: '4', text: 'What documents are expiring this month?' },
  { id: '5', text: 'Help me prepare for an upcoming audit' },
  { id: '6', text: 'What are the RoHS compliance requirements?' }
];

export function ComplianceChat() {
  const [isOpen, setIsOpen] = useState(false);
  const [showSupport, setShowSupport] = useState(false);
  const { messages, isAnalyzing, setIsAnalyzing, handleSendMessage, handleClearChat } = useComplianceChat();

  const handleQuestionSelect = (text: string) => {
    handleSendMessage(text);
  };

  const SupportDialog = () => (
    <Dialog open={showSupport} onOpenChange={setShowSupport}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Contact Support</DialogTitle>
          <DialogDescription>Our support team is here to help you with compliance queries.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <h4 className="font-medium">Support Hours</h4>
            <p className="text-sm text-gray-500">Monday - Friday: 9 AM - 6 PM EST</p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Contact Information</h4>
            <p className="text-sm text-gray-500">Email: <EMAIL></p>
            <p className="text-sm text-gray-500">Phone: +****************</p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Response Time</h4>
            <p className="text-sm text-gray-500">We typically respond within 4 hours during business hours</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button 
            size="icon" 
            className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all relative bg-white hover:bg-gray-50 border"
          >
            <AgentOctoIcon 
              size={40}
              className="absolute inset-0 m-auto"
              src="/lovable-uploads/c291cf2c-2b4e-42d6-a251-6223fd0e98af.png"
            />
            <span className="absolute -top-1 -right-1 px-2 py-1 bg-teal-600 text-xs font-medium rounded-full shadow-sm z-10 text-white">
              Help
            </span>
          </Button>
        </SheetTrigger>
        <SheetContent 
          side="right" 
          className="p-0 fixed bottom-4 right-4 top-auto left-auto border rounded-lg shadow-xl bg-white translate-x-0 translate-y-0 w-[350px] lg:w-[450px] h-[450px] lg:h-[75vh] max-h-[80vh] overflow-hidden"
        >
          <ResizablePanelGroup 
            direction="vertical" 
            className="h-full w-full min-h-[300px] min-w-[300px]"
          >
            <ResizablePanel defaultSize={100} minSize={20}>
              <div className="h-full flex flex-col">
                {/* Header */}
                <div className="p-3 border-b bg-teal-600 flex items-center gap-3 rounded-t-lg shrink-0">
                  <div className="w-8 h-8 flex-shrink-0">
                    <AgentOctoIcon size={32} className="w-full h-full" src="/lovable-uploads/c291cf2c-2b4e-42d6-a251-6223fd0e98af.png" />
                  </div>
                  <div className="flex flex-col justify-center text-left">
                    <h3 className="text-white text-sm font-semibold mb-0">Octo</h3>
                    <span className="text-xs text-teal-100">Your Compliance Assistant</span>
                  </div>
                </div>
                
                {/* Content */}
                <div className="flex flex-col flex-1 min-h-0">
                  <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-white min-h-0">
                    {messages.length === 0 && (
                      <div className="space-y-4">
                        <div className="text-gray-700 text-sm">
                          Hello! I'm Octo, your compliance assistant. I can help you with regulatory requirements, documentation, and compliance-related queries for electronics manufacturing. How may I assist you today?
                        </div>
                        <SuggestedQuestions questions={suggestedQuestions} onQuestionSelect={handleQuestionSelect} />
                      </div>
                    )}
                    <ComplianceChatMessages messages={messages} />
                  </div>

                  {/* Footer */}
                  <div className="p-3 border-t bg-white flex-shrink-0 rounded-b-lg">
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 text-xs"
                        onClick={() => setShowSupport(true)}
                      >
                        <PhoneCall className="h-3 w-3" />
                        Contact Support
                      </Button>
                    </div>
                    <ComplianceChatInput 
                      onSendMessage={handleSendMessage}
                      className="min-h-[40px]"
                    />
                  </div>
                </div>
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={0} minSize={0} maxSize={0} className="hidden">
              {/* Hidden panel to enable resizing */}
            </ResizablePanel>
          </ResizablePanelGroup>
        </SheetContent>
      </Sheet>
      <SupportDialog />
    </div>
  );
}


import { useState } from 'react';
import { ComplianceMessage } from '../types';
import { useToast } from '@/hooks/use-toast';
import { generateResponse } from '../utils/chatUtils';

export function useComplianceChat() {
  const [messages, setMessages] = useState<ComplianceMessage[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { toast } = useToast();

  const handleSendMessage = async (text: string, file?: File) => {
    const userMessage: ComplianceMessage = {
      id: Date.now().toString(),
      text,
      sender: 'user',
      timestamp: new Date(),
      attachment: file
    };

    setMessages(prev => [...prev, userMessage]);
    setIsAnalyzing(true);

    // Simulate processing time - longer if file is attached
    const processingTime = file ? 2000 : 1000;
    
    setTimeout(() => {
      // Generate appropriate response based on if there's a file
      const responseText = file 
        ? `I've analyzed the document "${file.name}". ${generateResponse(text || "Please analyze this document")}`
        : generateResponse(text);
        
      const assistantMessage: ComplianceMessage = {
        id: (Date.now() + 1).toString(),
        text: responseText,
        sender: 'assistant',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setIsAnalyzing(false);
    }, processingTime);
  };

  const handleClearChat = () => {
    setMessages([]);
    toast({
      title: "Chat Cleared",
      description: "All messages have been cleared.",
    });
  };

  return {
    messages,
    isAnalyzing,
    setIsAnalyzing,
    handleSendMessage,
    handleClearChat
  };
}

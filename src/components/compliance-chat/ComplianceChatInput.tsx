
import React, { useState, useRef, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { FileUp, Send, X } from 'lucide-react';
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ComplianceChatInputProps {
  onSendMessage: (text: string, file?: File) => void;
  className?: string;
}

export function ComplianceChatInput({ onSendMessage, className }: ComplianceChatInputProps) {
  const [message, setMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (message.trim() || selectedFile) {
      onSendMessage(message.trim(), selectedFile || undefined);
      setMessage('');
      setSelectedFile(null);
      
      // Focus back on textarea after sending
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      toast.success(`File "${file.name}" selected`);
      
      // Reset the input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleClearText = () => {
    setMessage('');
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [message]);

  return (
    <div className="flex flex-col gap-2">
      {selectedFile && (
        <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
          <FileUp className="h-4 w-4" />
          <span className="flex-1 truncate">{selectedFile.name}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSelectedFile(null)}
            className="h-6 w-6 p-0"
          >
            <span className="sr-only">Remove file</span>
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      <div className="relative flex items-end gap-2">
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Ask Octo about compliance..."
            className={cn(
              "pr-16 bg-gray-100 border-gray-200 rounded-lg resize-y",
              className
            )}
          />
          <div className="absolute right-2 bottom-2 flex items-center gap-1">
            {message && (
              <Button
                className="h-8 w-8 p-0 bg-transparent hover:bg-transparent text-gray-400 hover:text-gray-600"
                size="icon"
                variant="ghost"
                onClick={handleClearText}
                title="Clear text"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear</span>
              </Button>
            )}
            <Button
              className="h-8 w-8 p-0 bg-transparent hover:bg-transparent text-gray-500"
              size="icon"
              variant="ghost"
              onClick={handleSend}
              disabled={!message.trim() && !selectedFile}
            >
              <Send className="h-5 w-5" />
              <span className="sr-only">Send</span>
            </Button>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="icon"
          className="h-10 w-10 rounded-full border-gray-200"
          onClick={() => fileInputRef.current?.click()}
        >
          <FileUp className="h-4 w-4" />
          <span className="sr-only">Attach file</span>
        </Button>
      </div>
      
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept=".pdf,.doc,.docx,.txt"
      />
    </div>
  );
}

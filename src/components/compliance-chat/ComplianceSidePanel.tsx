
import React from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileText, <PERSON><PERSON>heck, AlertCircle } from 'lucide-react';

export function ComplianceSidePanel() {
  return (
    <div className="h-full flex flex-col bg-white border-l">
      <div className="p-4 border-b bg-white">
        <h3 className="text-xl font-semibold text-gray-800">Resources</h3>
      </div>
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-8">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-gray-500" />
              <h4 className="text-lg font-medium text-gray-800">Compliance Guidelines</h4>
            </div>
            <p className="text-gray-600 pl-7">
              Access latest compliance policies and procedures
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <FileCheck className="h-5 w-5 text-gray-500" />
              <h4 className="text-lg font-medium text-gray-800">Vendor Requirements</h4>
            </div>
            <p className="text-gray-600 pl-7">
              View detailed vendor approval requirements
            </p>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-gray-500" />
              <h4 className="text-lg font-medium text-gray-800">Recent Alerts</h4>
            </div>
            <div className="pl-7">
              <p className="text-gray-600 mb-2">Important compliance updates</p>
              <ul className="list-disc pl-5 space-y-2 text-gray-600">
                <li>Updated ISO 27001 requirements</li>
                <li>New vendor approval process</li>
                <li>Q2 compliance review deadline</li>
              </ul>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}


import React from 'react';
import { ComplianceMessage } from './types';
import { format } from 'date-fns';
import { FileText } from 'lucide-react';

interface ComplianceChatMessagesProps {
  messages: ComplianceMessage[];
}

export function ComplianceChatMessages({ messages }: ComplianceChatMessagesProps) {
  return (
    <div className="space-y-6">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`${
            message.sender === 'user' ? 'ml-auto' : 'mr-auto'
          }`}
        >
          <div
            className={`max-w-[85%] rounded-lg p-4 ${
              message.sender === 'user'
                ? 'bg-teal-600 text-white ml-auto'
                : 'bg-gray-100 text-gray-800'
            }`}
          >
            <div className="text-base">{message.text}</div>
            {message.attachment && (
              <div className="mt-2 flex items-center gap-2 text-xs">
                <FileText className="h-4 w-4" />
                <span>{message.attachment.name}</span>
              </div>
            )}
            <div className="text-xs mt-2 opacity-70 text-right">
              {format(message.timestamp, 'HH:mm')}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

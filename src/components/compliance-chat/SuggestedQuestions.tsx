
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { SuggestedQuestion } from './types';

interface SuggestedQuestionsProps {
  questions: SuggestedQuestion[];
  onQuestionSelect: (text: string) => void;
}

export function SuggestedQuestions({ questions, onQuestionSelect }: SuggestedQuestionsProps) {
  return (
    <div className="space-y-3">
      <p className="text-teal-700 font-semibold">Quick Compliance Queries:</p>
      <div className="flex flex-col gap-3">
        {questions.map((question) => (
          <Button
            key={question.id}
            variant="outline"
            className="justify-start text-left h-auto py-3 px-4 whitespace-normal text-teal-800 hover:bg-teal-50 border-teal-100 bg-teal-50/80"
            onClick={() => onQuestionSelect(question.text)}
          >
            {question.text}
          </Button>
        ))}
      </div>
    </div>
  );
}


import React from 'react';
import { <PERSON><PERSON>earch, ArrowUpFromLine, Loader2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { ComplianceMessage } from './types';
import { useToast } from '@/hooks/use-toast';
import { DocumentAnalysis } from './DocumentAnalysis';
import { VendorApprovalFlow } from './VendorApprovalFlow';

interface ChatActionsProps {
  isAnalyzing: boolean;
  onMessageAdd: (message: ComplianceMessage) => void;
  setIsAnalyzing: (value: boolean) => void;
}

export function ChatActions({ isAnalyzing, onMessageAdd, setIsAnalyzing }: ChatActionsProps) {
  const { toast } = useToast();
  const { handleAnalyzeDocument } = DocumentAnalysis({ onMessageAdd, setIsAnalyzing });
  const { handleVendorApproval } = VendorApprovalFlow({ onMessageAdd });

  const handleEscalate = () => {
    const escalationMessage: ComplianceMessage = {
      id: Date.now().toString(),
      text: "I've escalated this to the compliance team. They will review and respond within 24 hours. You'll receive a notification when they do.",
      sender: 'assistant',
      timestamp: new Date(),
    };
    onMessageAdd(escalationMessage);

    toast({
      title: "Case Escalated",
      description: "Your case has been assigned to the compliance team. Reference: #" + Math.floor(Math.random() * 10000),
      variant: "destructive",
    });
  };

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handleAnalyzeDocument}
        disabled={isAnalyzing}
      >
        {isAnalyzing ? (
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
        ) : (
          <FileSearch className="h-4 w-4 mr-2" />
        )}
        Analyze Document
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={handleEscalate}
      >
        <ArrowUpFromLine className="h-4 w-4 mr-2" />
        Escalate to Team
      </Button>
    </div>
  );
}

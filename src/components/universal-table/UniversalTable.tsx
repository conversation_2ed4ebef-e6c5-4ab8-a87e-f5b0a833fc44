
import React, { forwardRef } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { UniversalTableCore } from './components/UniversalTableCore';
import { UniversalTableProps, UniversalTableRef } from './types/UniversalTableTypes';

// Main export component that handles mobile detection and rendering
export const UniversalTable = forwardRef<UniversalTableRef, UniversalTableProps>(
  (props, ref) => {
    const isMobile = useIsMobile();
    
    // For now, render the core table component
    // In future phases, we'll add mobile-specific rendering here
    return <UniversalTableCore {...props} ref={ref} />;
  }
);

UniversalTable.displayName = 'UniversalTable';

// Re-export types for convenience
export type { 
  UniversalTableProps, 
  UniversalTableRef, 
  UniversalTableColumn,
  UniversalTableFilter,
  UniversalTableSort
} from './types/UniversalTableTypes';

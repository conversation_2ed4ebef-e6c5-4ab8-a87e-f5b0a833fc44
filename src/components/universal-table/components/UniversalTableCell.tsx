
import React from 'react';
import { TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { UniversalTableColumn } from '../types/UniversalTableTypes';

interface UniversalTableCellProps {
  column: UniversalTableColumn;
  item: any;
  value: any;
}

export const UniversalTableCell: React.FC<UniversalTableCellProps> = ({
  column,
  item,
  value
}) => {
  const renderCellContent = () => {
    // Custom renderer takes precedence
    if (column.cellRenderer) {
      return column.cellRenderer(item, value);
    }

    // Handle null/undefined values
    if (value === null || value === undefined) {
      return <span className="text-gray-400">--</span>;
    }

    // Render based on cell type
    switch (column.cellType) {
      case 'badge':
        return (
          <Badge variant={getBadgeVariant(value)}>
            {String(value)}
          </Badge>
        );
      
      case 'button':
        return (
          <Button variant="outline" size="sm">
            {String(value)}
          </Button>
        );
      
      case 'date':
        return formatDate(value);
      
      case 'number':
        return formatNumber(value);
      
      case 'text':
      default:
        return String(value);
    }
  };

  return (
    <TableCell 
      className={`px-3 py-2 ${column.sticky ? 'sticky left-0 bg-white' : ''}`}
      style={{
        width: column.width,
        minWidth: column.minWidth,
        maxWidth: column.maxWidth
      }}
    >
      {renderCellContent()}
    </TableCell>
  );
};

// Utility functions
function getBadgeVariant(value: any): "default" | "secondary" | "destructive" | "outline" {
  const str = String(value).toLowerCase();
  
  if (str.includes('active') || str.includes('success') || str.includes('approved')) {
    return 'default';
  }
  if (str.includes('inactive') || str.includes('error') || str.includes('rejected')) {
    return 'destructive';
  }
  if (str.includes('pending') || str.includes('warning')) {
    return 'secondary';
  }
  
  return 'outline';
}

function formatDate(value: any): string {
  try {
    const date = new Date(value);
    if (isNaN(date.getTime())) return String(value);
    
    return date.toLocaleDateString();
  } catch {
    return String(value);
  }
}

function formatNumber(value: any): string {
  const num = Number(value);
  if (isNaN(num)) return String(value);
  
  return num.toLocaleString();
}


import React, { forwardRef, useImperativeHandle } from 'react';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, ChevronRight, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { UniversalTableProps, UniversalTableRef, UniversalTableSort } from '../types/UniversalTableTypes';
import { useUniversalTable } from '@/hooks/useUniversalTable';
import { UniversalTablePagination } from './UniversalTablePagination';
import { UniversalTableCell } from './UniversalTableCell';

export const UniversalTableCore = forwardRef<UniversalTableRef, UniversalTableProps>(
  ({
    data,
    columns,
    totalCount,
    currentPage = 1,
    pageSize = 10,
    pageSizeOptions = [10, 25, 50, 100],
    showPagination = true,
    sortable = true,
    defaultSort,
    onSort,
    filterable = false,
    filters = [],
    onFilter,
    selectable = false,
    selectedRows = [],
    onSelectionChange,
    rowIdField = 'id',
    expandable = false,
    expandedRows = [],
    onRowExpand,
    renderExpandedContent,
    actions = [],
    bulkActions = [],
    className = '',
    emptyMessage = 'No data found',
    loading = false,
    dense = false,
    striped = true,
    onPageChange,
    onPageSizeChange,
    onRowClick,
    onRowDoubleClick,
    mobileBreakpoint = 768,
    mobileRenderMode = 'stack'
  }, ref) => {
    const {
      state,
      paginatedData,
      totalPages,
      handleSort,
      handlePageChange,
      handlePageSizeChange,
      handleSelectionChange,
      handleRowExpand,
      clearSelection,
      selectAll,
      clearFilters,
      getSelectedItems
    } = useUniversalTable({
      data,
      initialState: {
        currentPage,
        pageSize,
        sortBy: defaultSort || null,
        filters,
        selectedRows,
        expandedRows
      },
      rowIdField
    });

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      refresh: () => {
        // Implementation for refresh
      },
      clearSelection,
      selectAll,
      clearFilters,
      exportData: (format) => {
        // Implementation for export
        console.log(`Exporting data in ${format} format`);
      }
    }));

    const handleSortClick = (column: any) => {
      if (!column.sortable) return;
      
      let newSort: UniversalTableSort | null = null;
      
      if (state.sortBy?.field === column.field) {
        if (state.sortBy.direction === 'asc') {
          newSort = { field: column.field, direction: 'desc' };
        } else if (state.sortBy.direction === 'desc') {
          newSort = null;
        }
      } else {
        newSort = { field: column.field, direction: 'asc' };
      }
      
      handleSort(newSort);
      onSort?.(newSort);
    };

    const renderSortIcon = (column: any) => {
      if (!column.sortable) return null;
      
      if (state.sortBy?.field === column.field) {
        return state.sortBy.direction === 'asc' 
          ? <ArrowUp className="ml-1 h-4 w-4" />
          : <ArrowDown className="ml-1 h-4 w-4" />;
      }
      
      return <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />;
    };

    const handleRowSelection = (rowId: string, checked: boolean) => {
      const newSelection = checked
        ? [...state.selectedRows, rowId]
        : state.selectedRows.filter(id => id !== rowId);
      
      handleSelectionChange(newSelection);
      onSelectionChange?.(newSelection);
    };

    const handleSelectAll = (checked: boolean) => {
      const newSelection = checked
        ? paginatedData.map(item => String((item as any)[rowIdField]))
        : [];
      
      handleSelectionChange(newSelection);
      onSelectionChange?.(newSelection);
    };

    const toggleRowExpansion = (rowId: string) => {
      const newExpanded = state.expandedRows.includes(rowId)
        ? state.expandedRows.filter(id => id !== rowId)
        : [...state.expandedRows, rowId];
      
      handleRowExpand(newExpanded);
      onRowExpand?.(newExpanded);
    };

    const visibleColumns = columns.filter(col => !col.hidden);
    const hasActions = actions.length > 0;
    const allSelected = paginatedData.length > 0 && 
      paginatedData.every(item => state.selectedRows.includes(String((item as any)[rowIdField])));
    const someSelected = paginatedData.some(item => 
      state.selectedRows.includes(String((item as any)[rowIdField])));

    // Determine the checkbox state for select all
    const selectAllCheckboxState = allSelected ? true : (someSelected ? 'indeterminate' : false);

    return (
      <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
        {/* Bulk Actions */}
        {selectable && state.selectedRows.length > 0 && bulkActions.length > 0 && (
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {state.selectedRows.length} row(s) selected
              </span>
              {bulkActions.map(action => (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={() => action.onClick(getSelectedItems())}
                  disabled={action.disabled?.(getSelectedItems())}
                  className="gap-2"
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Table */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {/* Selection column */}
                {selectable && (
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectAllCheckboxState}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                )}
                
                {/* Expansion column */}
                {expandable && (
                  <TableHead className="w-12">
                    <span className="sr-only">Expand</span>
                  </TableHead>
                )}
                
                {/* Data columns */}
                {visibleColumns.map(column => (
                  <TableHead
                    key={column.id}
                    className={`${column.sortable ? 'cursor-pointer select-none hover:bg-gray-50' : ''} ${column.width ? `w-[${column.width}]` : ''}`}
                    style={{
                      width: column.width,
                      minWidth: column.minWidth,
                      maxWidth: column.maxWidth
                    }}
                    onClick={() => handleSortClick(column)}
                  >
                    {column.headerRenderer ? column.headerRenderer() : (
                      <div className="flex items-center">
                        {column.header}
                        {renderSortIcon(column)}
                      </div>
                    )}
                  </TableHead>
                ))}
                
                {/* Actions column */}
                {hasActions && (
                  <TableHead className="w-24 text-right">Actions</TableHead>
                )}
              </TableRow>
            </TableHeader>
            
            <TableBody>
              {loading ? (
                <TableRow>
                  <td colSpan={visibleColumns.length + (selectable ? 1 : 0) + (expandable ? 1 : 0) + (hasActions ? 1 : 0)}>
                    <div className="flex items-center justify-center py-8">
                      <div className="text-gray-500">Loading...</div>
                    </div>
                  </td>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <td colSpan={visibleColumns.length + (selectable ? 1 : 0) + (expandable ? 1 : 0) + (hasActions ? 1 : 0)}>
                    <div className="flex items-center justify-center py-8">
                      <div className="text-gray-500">{emptyMessage}</div>
                    </div>
                  </td>
                </TableRow>
              ) : (
                paginatedData.map((item, index) => {
                  const rowId = String((item as any)[rowIdField]);
                  const isSelected = state.selectedRows.includes(rowId);
                  const isExpanded = state.expandedRows.includes(rowId);
                  
                  return (
                    <React.Fragment key={rowId}>
                      <TableRow
                        className={`
                          ${striped && index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                          ${dense ? 'h-10' : 'h-12'}
                          ${onRowClick ? 'cursor-pointer hover:bg-blue-50' : ''}
                          ${isSelected ? 'bg-blue-50' : ''}
                        `}
                        onClick={() => onRowClick?.(item)}
                        onDoubleClick={() => onRowDoubleClick?.(item)}
                      >
                        {/* Selection cell */}
                        {selectable && (
                          <td className="px-3">
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={(checked) => handleRowSelection(rowId, checked as boolean)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </td>
                        )}
                        
                        {/* Expansion cell */}
                        {expandable && (
                          <td className="px-3">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleRowExpansion(rowId);
                              }}
                            >
                              {isExpanded ? (
                                <ChevronDown className="h-4 w-4" />
                              ) : (
                                <ChevronRight className="h-4 w-4" />
                              )}
                            </Button>
                          </td>
                        )}
                        
                        {/* Data cells */}
                        {visibleColumns.map(column => (
                          <UniversalTableCell
                            key={column.id}
                            column={column}
                            item={item}
                            value={(item as any)[column.field]}
                          />
                        ))}
                        
                        {/* Actions cell */}
                        {hasActions && (
                          <td className="px-3 text-right">
                            <div className="flex items-center justify-end gap-1">
                              {actions
                                .filter(action => !action.hidden?.(item))
                                .map(action => (
                                  <Button
                                    key={action.id}
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      action.onClick(item);
                                    }}
                                    disabled={action.disabled?.(item)}
                                    className="h-8 w-8 p-0"
                                  >
                                    {action.icon}
                                  </Button>
                                ))}
                            </div>
                          </td>
                        )}
                      </TableRow>
                      
                      {/* Expanded content */}
                      {expandable && isExpanded && renderExpandedContent && (
                        <TableRow>
                          <td colSpan={visibleColumns.length + (selectable ? 1 : 0) + (expandable ? 1 : 0) + (hasActions ? 1 : 0)}>
                            <div className="p-4 bg-gray-50">
                              {renderExpandedContent(item)}
                            </div>
                          </td>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {showPagination && (
          <UniversalTablePagination
            currentPage={state.currentPage}
            totalPages={totalPages}
            pageSize={state.pageSize}
            pageSizeOptions={pageSizeOptions}
            totalCount={totalCount || data.length}
            onPageChange={(page) => {
              handlePageChange(page);
              onPageChange?.(page);
            }}
            onPageSizeChange={(size) => {
              handlePageSizeChange(size);
              onPageSizeChange?.(size);
            }}
          />
        )}
      </div>
    );
  }
);

UniversalTableCore.displayName = 'UniversalTableCore';

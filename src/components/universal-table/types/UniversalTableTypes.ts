
export type SortDirection = 'asc' | 'desc' | null;
export type FilterType = 'text' | 'select' | 'date' | 'number' | 'boolean';
export type CellType = 'text' | 'badge' | 'button' | 'custom' | 'date' | 'number';

export interface UniversalTableColumn<T = any> {
  id: string;
  field: keyof T | string;
  header: string;
  sortable?: boolean;
  filterable?: boolean;
  filterType?: FilterType;
  width?: string;
  minWidth?: string;
  maxWidth?: string;
  cellType?: CellType;
  cellRenderer?: (item: T, value: any) => React.ReactNode;
  headerRenderer?: () => React.ReactNode;
  sticky?: boolean;
  hidden?: boolean;
  resizable?: boolean;
}

export interface UniversalTableFilter {
  id: string;
  field: string;
  value: any;
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte';
}

export interface UniversalTableSort {
  field: string;
  direction: SortDirection;
}

export interface UniversalTableState {
  currentPage: number;
  pageSize: number;
  sortBy: UniversalTableSort | null;
  filters: UniversalTableFilter[];
  selectedRows: string[];
  expandedRows: string[];
}

export interface UniversalTableProps<T = any> {
  // Data
  data: T[];
  columns: UniversalTableColumn<T>[];
  
  // Pagination
  totalCount?: number;
  currentPage?: number;
  pageSize?: number;
  pageSizeOptions?: number[];
  showPagination?: boolean;
  
  // Sorting
  sortable?: boolean;
  defaultSort?: UniversalTableSort;
  onSort?: (sort: UniversalTableSort | null) => void;
  
  // Filtering
  filterable?: boolean;
  filters?: UniversalTableFilter[];
  onFilter?: (filters: UniversalTableFilter[]) => void;
  
  // Selection
  selectable?: boolean;
  selectedRows?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  rowIdField?: keyof T;
  
  // Expansion
  expandable?: boolean;
  expandedRows?: string[];
  onRowExpand?: (expandedIds: string[]) => void;
  renderExpandedContent?: (item: T) => React.ReactNode;
  
  // Actions
  actions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: T) => void;
    disabled?: (item: T) => boolean;
    hidden?: (item: T) => boolean;
  }>;
  
  // Bulk actions
  bulkActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (selectedItems: T[]) => void;
    disabled?: (selectedItems: T[]) => boolean;
  }>;
  
  // Customization
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
  dense?: boolean;
  striped?: boolean;
  
  // Callbacks
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  onRowClick?: (item: T) => void;
  onRowDoubleClick?: (item: T) => void;
  
  // Mobile
  mobileBreakpoint?: number;
  mobileRenderMode?: 'stack' | 'cards' | 'accordion';
}

export interface UniversalTableRef {
  refresh: () => void;
  clearSelection: () => void;
  selectAll: () => void;
  clearFilters: () => void;
  exportData: (format: 'csv' | 'xlsx' | 'json') => void;
}

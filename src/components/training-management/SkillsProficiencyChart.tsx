
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, CartesianGrid, XAxis, YAxis } from 'recharts';

const skillsChartConfig = {
  novice: {
    label: "Novice",
    color: "#FFBB28",
  },
  competent: {
    label: "Competent", 
    color: "#00C49F",
  },
  expert: {
    label: "Expert",
    color: "#0088FE",
  },
};

const skillCompetencyData = [
  { skill: 'Safety Protocols', novice: 10, competent: 35, expert: 55, total: 100 },
  { skill: 'Quality Standards', novice: 15, competent: 50, expert: 35, total: 100 },
  { skill: 'Equipment Operation', novice: 25, competent: 45, expert: 30, total: 100 },
  { skill: 'Process Control', novice: 20, competent: 55, expert: 25, total: 100 },
  { skill: 'Documentation', novice: 12, competent: 48, expert: 40, total: 100 }
];

export const TrainingSkillsProficiencyChart: React.FC = () => {
  console.log('Training Skills Competency Data:', skillCompetencyData);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Skills Proficiency Overview</CardTitle>
      </CardHeader>
      <CardContent>
        {skillCompetencyData && skillCompetencyData.length > 0 ? (
          <ChartContainer config={skillsChartConfig} className="h-[400px] w-full">
            <BarChart 
              data={skillCompetencyData} 
              layout="horizontal"
              margin={{ top: 20, right: 40, left: 180, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                type="number" 
                domain={[0, 100]} 
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                dataKey="skill" 
                type="category" 
                width={160}
                fontSize={12}
                interval={0}
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  `${value}%`,
                  name === 'expert' ? 'Expert' : 
                  name === 'competent' ? 'Competent' : 'Novice'
                ]}
              />
              <Bar 
                dataKey="expert" 
                stackId="a" 
                fill="#0088FE" 
                name="Expert"
                radius={[0, 2, 2, 0]}
              />
              <Bar 
                dataKey="competent" 
                stackId="a" 
                fill="#00C49F" 
                name="Competent"
              />
              <Bar 
                dataKey="novice" 
                stackId="a" 
                fill="#FFBB28" 
                name="Novice"
                radius={[2, 0, 0, 2]}
              />
            </BarChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-[400px] text-gray-500">
            <div className="text-center">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <p>No skills data available</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

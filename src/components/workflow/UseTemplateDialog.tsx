
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';

interface UseTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: {
    id: string;
    name: string;
    category: string;
    triggers: string[];
  };
}

export const UseTemplateDialog: React.FC<UseTemplateDialogProps> = ({
  open,
  onOpenChange,
  template
}) => {
  const [workflowName, setWorkflowName] = useState(`${template.name} - Custom`);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create Workflow from Template</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Template: {template.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Badge variant="outline">{template.category}</Badge>
                <div>
                  <h4 className="font-medium mb-2">Included Triggers:</h4>
                  <div className="flex flex-wrap gap-2">
                    {template.triggers.map((trigger, index) => (
                      <Badge key={index} variant="secondary">{trigger}</Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div>
            <Label htmlFor="new-workflow-name">Workflow Name</Label>
            <Input 
              id="new-workflow-name" 
              value={workflowName}
              onChange={(e) => setWorkflowName(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea 
              id="description" 
              placeholder="Describe how this workflow will be used"
            />
          </div>

          <div>
            <Label htmlFor="department">Department</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="quality">Quality</SelectItem>
                <SelectItem value="operations">Operations</SelectItem>
                <SelectItem value="compliance">Compliance</SelectItem>
                <SelectItem value="hr">Human Resources</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="auto-start">Auto-start</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Auto-start workflow?" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="yes">Yes, start immediately</SelectItem>
                <SelectItem value="no">No, create as draft</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={() => onOpenChange(false)}>
              Create Workflow
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

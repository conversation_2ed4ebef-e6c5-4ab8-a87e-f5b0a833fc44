import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Zap, Play, Pause, Settings, Plus, ArrowRight, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { WorkflowConfigDialog } from './WorkflowConfigDialog';
import { UseTemplateDialog } from './UseTemplateDialog';

const workflows = [
  {
    id: '1',
    name: 'Quality Issue Escalation',
    description: 'Automatically escalates quality issues based on severity and duration',
    status: 'active',
    triggerCount: 24,
    successRate: 94,
    lastRun: '2 hours ago',
    steps: 5
  },
  {
    id: '2',
    name: 'Supplier Document Review',
    description: 'Reviews and approves supplier documentation submissions',
    status: 'active',
    triggerCount: 12,
    successRate: 88,
    lastRun: '4 hours ago',
    steps: 3
  },
  {
    id: '3',
    name: 'CAPA Assignment',
    description: 'Assigns corrective actions based on department and expertise',
    status: 'paused',
    triggerCount: 8,
    successRate: 96,
    lastRun: '1 day ago',
    steps: 4
  }
];

const workflowTemplates = [
  { id: '1', name: 'Document Approval', category: 'Quality', triggers: ['Document Upload', 'Review Request'] },
  { id: '2', name: 'Audit Scheduling', category: 'Compliance', triggers: ['Due Date', 'Risk Level'] },
  { id: '3', name: 'Training Assignment', category: 'HR', triggers: ['New Hire', 'Certification Expiry'] },
  { id: '4', name: 'Equipment Maintenance', category: 'Operations', triggers: ['Usage Hours', 'Performance Metrics'] }
];

const recentExecutions = [
  { id: '1', workflow: 'Quality Issue Escalation', status: 'completed', duration: '2.3s', timestamp: '10 min ago' },
  { id: '2', workflow: 'Supplier Document Review', status: 'running', duration: '1.8s', timestamp: '15 min ago' },
  { id: '3', workflow: 'CAPA Assignment', status: 'failed', duration: '0.5s', timestamp: '1 hour ago' },
  { id: '4', workflow: 'Quality Issue Escalation', status: 'completed', duration: '1.9s', timestamp: '2 hours ago' }
];

export const WorkflowAutomationEngine: React.FC = () => {
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedWorkflowForConfig, setSelectedWorkflowForConfig] = useState('');
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'running': return <Play className="h-4 w-4 text-blue-600" />;
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'active': return <Play className="h-4 w-4 text-green-600" />;
      case 'paused': return <Pause className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      'active': 'bg-green-100 text-green-800',
      'paused': 'bg-yellow-100 text-yellow-800',
      'completed': 'bg-green-100 text-green-800',
      'running': 'bg-blue-100 text-blue-800',
      'failed': 'bg-red-100 text-red-800'
    };
    return <Badge className={variants[status]}>{status}</Badge>;
  };

  const handleConfigureWorkflow = (workflowName: string) => {
    setSelectedWorkflowForConfig(workflowName);
    setConfigDialogOpen(true);
  };

  const handleUseTemplate = (template: any) => {
    setSelectedTemplate(template);
    setTemplateDialogOpen(true);
  };

  const handlePauseResume = (workflow: any) => {
    console.log(`${workflow.status === 'active' ? 'Pausing' : 'Resuming'} workflow:`, workflow.name);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="h-8 w-8 text-purple-600" />
            Workflow Automation Engine
          </h1>
          <p className="text-gray-600">Automate business processes and improve efficiency</p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Workflow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Workflow</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="workflow-name">Workflow Name</Label>
                <Input id="workflow-name" placeholder="Enter workflow name" />
              </div>
              <div>
                <Label htmlFor="workflow-template">Template</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {workflowTemplates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name} - {template.category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowCreateDialog(false)}>
                  Create Workflow
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                <p className="text-3xl font-bold">12</p>
              </div>
              <Zap className="h-12 w-12 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Executions Today</p>
                <p className="text-3xl font-bold">156</p>
              </div>
              <Play className="h-12 w-12 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-3xl font-bold">94%</p>
              </div>
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Time Saved</p>
                <p className="text-3xl font-bold">24h</p>
              </div>
              <Clock className="h-12 w-12 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="workflows" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="workflows">Active Workflows</TabsTrigger>
          <TabsTrigger value="executions">Recent Executions</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="workflows" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {workflows.map((workflow) => (
              <Card key={workflow.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      {getStatusIcon(workflow.status)}
                      {workflow.name}
                    </CardTitle>
                    {getStatusBadge(workflow.status)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600">{workflow.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Triggers Today</p>
                      <p className="font-medium">{workflow.triggerCount}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Success Rate</p>
                      <p className="font-medium">{workflow.successRate}%</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Steps</p>
                      <p className="font-medium">{workflow.steps}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Last Run</p>
                      <p className="font-medium">{workflow.lastRun}</p>
                    </div>
                  </div>

                  <Progress value={workflow.successRate} className="h-2" />

                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleConfigureWorkflow(workflow.name)}
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Configure
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handlePauseResume(workflow)}
                    >
                      {workflow.status === 'active' ? 'Pause' : 'Resume'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="executions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Workflow Executions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentExecutions.map((execution) => (
                  <div key={execution.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(execution.status)}
                      <div>
                        <h4 className="font-medium">{execution.workflow}</h4>
                        <p className="text-sm text-gray-600">Duration: {execution.duration}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-gray-500">{execution.timestamp}</span>
                      {getStatusBadge(execution.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {workflowTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle>{template.name}</CardTitle>
                  <Badge variant="outline">{template.category}</Badge>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">Triggers:</p>
                    <div className="flex flex-wrap gap-2">
                      {template.triggers.map((trigger, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {trigger}
                        </Badge>
                      ))}
                    </div>
                    <Button 
                      className="w-full mt-4"
                      onClick={() => handleUseTemplate(template)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Use Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Configuration Dialog */}
      <WorkflowConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        workflowName={selectedWorkflowForConfig}
      />

      {/* Template Usage Dialog */}
      {selectedTemplate && (
        <UseTemplateDialog
          open={templateDialogOpen}
          onOpenChange={setTemplateDialogOpen}
          template={selectedTemplate}
        />
      )}

      {/* ... keep existing code (create workflow dialog) */}
    </div>
  );
};

import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, Eye, MoreHorizontal, Users, BookOpen } from "lucide-react";
import { TrainingAssignment } from "@/types/trainingAdmin";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TrainingAdminTableProps {
  trainings: TrainingAssignment[];
  selectedTrainings: string[];
  onSelectTraining: (trainingId: string) => void;
  onSelectAll: () => void;
}

export const TrainingAdminTable: React.FC<TrainingAdminTableProps> = ({
  trainings,
  selectedTrainings,
  onSelectTraining,
  onSelectAll
}) => {
  const getStatusBadge = (status: TrainingAssignment['status']) => {
    const statusConfig = {
      'Not Started': { variant: 'outline' as const, className: 'border-gray-300 text-gray-700' },
      'In Progress': { variant: 'default' as const, className: 'bg-blue-100 text-blue-800 border-blue-200' },
      'Completed': { variant: 'default' as const, className: 'bg-green-100 text-green-800 border-green-200' },
      'Overdue': { variant: 'destructive' as const, className: 'bg-red-100 text-red-800 border-red-200' }
    };
    
    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'High': 'bg-red-50 text-red-700 border-red-200',
      'Medium': 'bg-yellow-50 text-yellow-700 border-yellow-200',
      'Low': 'bg-gray-50 text-gray-700 border-gray-200'
    };
    
    return (
      <Badge variant="outline" className={priorityConfig[priority as keyof typeof priorityConfig]}>
        {priority}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      'Video': 'bg-purple-100 text-purple-800',
      'PDF': 'bg-orange-100 text-orange-800',
      'Quiz': 'bg-yellow-100 text-yellow-800',
      'Interactive': 'bg-blue-100 text-blue-800'
    };
    
    return (
      <Badge className={typeConfig[type as keyof typeof typeConfig]}>
        {type}
      </Badge>
    );
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50">
            <TableHead className="w-12">
              <Checkbox
                checked={selectedTrainings.length === trainings.length && trainings.length > 0}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead className="font-semibold">Training Name</TableHead>
            <TableHead className="font-semibold">Type</TableHead>
            <TableHead className="font-semibold">Assigned To</TableHead>
            <TableHead className="font-semibold">Department</TableHead>
            <TableHead className="font-semibold">Status</TableHead>
            <TableHead className="font-semibold">Priority</TableHead>
            <TableHead className="font-semibold">Due Date</TableHead>
            <TableHead className="font-semibold">Progress</TableHead>
            <TableHead className="font-semibold">Avg Time</TableHead>
            <TableHead className="font-semibold text-center">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {trainings.map((training) => (
            <TableRow key={training.id} className="hover:bg-gray-50">
              <TableCell>
                <Checkbox
                  checked={selectedTrainings.includes(training.id)}
                  onCheckedChange={() => onSelectTraining(training.id)}
                />
              </TableCell>
              
              <TableCell>
                <div className="min-w-[200px]">
                  <p className="font-medium text-gray-900">{training.trainingName}</p>
                  <p className="text-sm text-gray-500">ID: {training.id}</p>
                </div>
              </TableCell>
              
              <TableCell>
                {getTypeBadge(training.trainingType)}
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2 min-w-[180px]">
                  <div className="flex -space-x-1">
                    {training.assignedTo.slice(0, 3).map((employee, index) => (
                      <Avatar key={employee.id} className="h-6 w-6 border-2 border-white">
                        <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
                          {employee.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    {training.assignedTo.length > 3 && (
                      <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                        <span className="text-xs text-gray-600">+{training.assignedTo.length - 3}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <Users className="h-3 w-3" />
                    {training.totalAssigned}
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <Badge variant="outline" className="bg-gray-50">
                  {training.department}
                </Badge>
              </TableCell>
              
              <TableCell>
                {getStatusBadge(training.status)}
              </TableCell>
              
              <TableCell>
                {getPriorityBadge(training.priority)}
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  {new Date(training.dueDate).toLocaleDateString()}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="min-w-[120px]">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600">{training.completionPercentage}%</span>
                    <span className="text-xs text-gray-500">{training.completed}/{training.totalAssigned}</span>
                  </div>
                  <Progress value={training.completionPercentage} className="h-2" />
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1 text-sm">
                  <Clock className="h-4 w-4 text-gray-400" />
                  {training.averageTimeSpent}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Eye className="h-4 w-4" />
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>Send Reminder</DropdownMenuItem>
                      <DropdownMenuItem>Reassign</DropdownMenuItem>
                      <DropdownMenuItem>Generate Report</DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">Archive</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {trainings.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">No training assignments found</p>
          <p className="text-gray-400 text-sm">Try adjusting your filters or create new training assignments</p>
        </div>
      )}
    </div>
  );
};


import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Search, Download } from "lucide-react";

interface TrainingActivity {
  id: string;
  employeeName: string;
  trainingTitle: string;
  timeSpent: string;
  videoWatchedPercent: number;
  quizScore: number;
  completionStatus: 'Completed' | 'In Progress' | 'Not Started';
  lastAccessed: string;
}

const mockTrainingActivity: TrainingActivity[] = [
  {
    id: "ACT-001",
    employeeName: "<PERSON>",
    trainingTitle: "ISO 9001:2015 Quality Management Systems",
    timeSpent: "2:45",
    videoWatchedPercent: 100,
    quizScore: 92,
    completionStatus: "Completed",
    lastAccessed: "2024-11-25"
  },
  {
    id: "ACT-002",
    employeeName: "<PERSON>",
    trainingTitle: "Root Cause Analysis for Manufacturing Deviations",
    timeSpent: "1:23",
    videoWatchedPercent: 65,
    quizScore: 0,
    completionStatus: "In Progress",
    lastAccessed: "2024-11-25"
  },
  {
    id: "ACT-003",
    employeeName: "Emily Davis",
    trainingTitle: "Fire Safety Training",
    timeSpent: "0:45",
    videoWatchedPercent: 100,
    quizScore: 88,
    completionStatus: "Completed",
    lastAccessed: "2024-11-24"
  },
  {
    id: "ACT-004",
    employeeName: "David Rodriguez",
    trainingTitle: "Chemical Handling Safety",
    timeSpent: "0:00",
    videoWatchedPercent: 0,
    quizScore: 0,
    completionStatus: "Not Started",
    lastAccessed: "Never"
  },
  {
    id: "ACT-005",
    employeeName: "Lisa Thompson",
    trainingTitle: "GMP Basics",
    timeSpent: "1:15",
    videoWatchedPercent: 85,
    quizScore: 0,
    completionStatus: "In Progress",
    lastAccessed: "2024-11-23"
  },
  {
    id: "ACT-006",
    employeeName: "James Wilson",
    trainingTitle: "Lean Manufacturing Principles",
    timeSpent: "3:20",
    videoWatchedPercent: 100,
    quizScore: 95,
    completionStatus: "Completed",
    lastAccessed: "2024-11-22"
  }
];

export const TrainingActivityTable: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const filteredActivities = mockTrainingActivity.filter(activity => {
    const matchesSearch = activity.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activity.trainingTitle.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || activity.completionStatus === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Completed": return "default";
      case "In Progress": return "secondary";
      case "Not Started": return "outline";
      default: return "outline";
    }
  };

  const getQuizScoreBadge = (score: number, status: string) => {
    if (status !== "Completed" || score === 0) return null;
    
    return (
      <Badge variant={score >= 70 ? "default" : "destructive"}>
        {score}% {score >= 70 ? "Pass" : "Fail"}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Header and Filters */}
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold">Training Activity Dashboard</h3>
            <p className="text-sm text-gray-600">Detailed tracking of employee training progress and performance</p>
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
        </div>
        
        <div className="flex flex-col md:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by employee or training..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Not Started">Not Started</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Activity Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b bg-gray-50">
              <th className="text-left p-4 font-semibold">Employee Name</th>
              <th className="text-left p-4 font-semibold">Training Title</th>
              <th className="text-left p-4 font-semibold">Time Spent</th>
              <th className="text-left p-4 font-semibold">Video Progress</th>
              <th className="text-left p-4 font-semibold">Quiz Score</th>
              <th className="text-left p-4 font-semibold">Status</th>
              <th className="text-left p-4 font-semibold">Last Accessed</th>
            </tr>
          </thead>
          <tbody>
            {filteredActivities.map((activity) => (
              <tr key={activity.id} className="border-b hover:bg-gray-50">
                <td className="p-4">
                  <div className="font-medium">{activity.employeeName}</div>
                </td>
                <td className="p-4">
                  <div className="max-w-xs">
                    <p className="font-medium truncate">{activity.trainingTitle}</p>
                  </div>
                </td>
                <td className="p-4">
                  <div className="font-mono text-sm">{activity.timeSpent}</div>
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-teal-600 h-2 rounded-full" 
                        style={{ width: `${activity.videoWatchedPercent}%` }}
                      />
                    </div>
                    <span className="text-sm">{activity.videoWatchedPercent}%</span>
                  </div>
                </td>
                <td className="p-4">
                  {getQuizScoreBadge(activity.quizScore, activity.completionStatus) || (
                    <span className="text-sm text-gray-500">-</span>
                  )}
                </td>
                <td className="p-4">
                  <Badge variant={getStatusBadgeVariant(activity.completionStatus)}>
                    {activity.completionStatus}
                  </Badge>
                </td>
                <td className="p-4">
                  <span className="text-sm text-gray-600">{activity.lastAccessed}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredActivities.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No training activities found matching your criteria.
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-teal-600">{filteredActivities.length}</div>
            <div className="text-sm text-gray-600">Total Activities</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {filteredActivities.filter(a => a.completionStatus === "Completed").length}
            </div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {filteredActivities.filter(a => a.completionStatus === "In Progress").length}
            </div>
            <div className="text-sm text-gray-600">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {Math.round(filteredActivities.filter(a => a.quizScore >= 70 && a.completionStatus === "Completed").length / Math.max(filteredActivities.filter(a => a.completionStatus === "Completed").length, 1) * 100)}%
            </div>
            <div className="text-sm text-gray-600">Pass Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
};

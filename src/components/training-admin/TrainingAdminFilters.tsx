
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Filter, Send, UserPlus, CheckSquare, MoreHorizontal } from "lucide-react";
import { TrainingFilters } from "@/types/trainingAdmin";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TrainingAdminFiltersProps {
  filters: TrainingFilters;
  onFiltersChange: (filters: TrainingFilters) => void;
  selectedCount: number;
  onBulkAction: (action: "remind" | "reassign" | "complete") => void;
}

export const TrainingAdminFilters: React.FC<TrainingAdminFiltersProps> = ({
  filters,
  onFiltersChange,
  selectedCount,
  onBulkAction
}) => {
  const updateFilter = (key: keyof TrainingFilters, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="p-6 border-b border-gray-200">
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex flex-col md:flex-row gap-4 flex-1">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <div className="min-w-[150px]">
              <Select value={filters.role} onValueChange={(value) => updateFilter("role", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="Quality Manager">Quality Manager</SelectItem>
                  <SelectItem value="Production Staff">Production Staff</SelectItem>
                  <SelectItem value="Maintenance Technician">Maintenance Technician</SelectItem>
                  <SelectItem value="Process Engineer">Process Engineer</SelectItem>
                  <SelectItem value="Lab Technician">Lab Technician</SelectItem>
                  <SelectItem value="All Employees">All Employees</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select value={filters.department} onValueChange={(value) => updateFilter("department", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="Quality Assurance">Quality Assurance</SelectItem>
                  <SelectItem value="Production">Production</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="All Departments">All Departments</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select value={filters.status} onValueChange={(value) => updateFilter("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Not Started">Not Started</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select value={filters.dueDateRange} onValueChange={(value) => updateFilter("dueDateRange", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Due Date Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Dates</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="next-7-days">Next 7 days</SelectItem>
                  <SelectItem value="next-30-days">Next 30 days</SelectItem>
                  <SelectItem value="next-90-days">Next 90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        {selectedCount > 0 && (
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="px-3 py-1">
              {selectedCount} selected
            </Badge>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("remind")}
                className="gap-2"
              >
                <Send className="h-4 w-4" />
                Send Reminder
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("reassign")}
                className="gap-2"
              >
                <UserPlus className="h-4 w-4" />
                Reassign
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("complete")}
                className="gap-2"
              >
                <CheckSquare className="h-4 w-4" />
                Mark Complete
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Export Selected</DropdownMenuItem>
                  <DropdownMenuItem>Generate Report</DropdownMenuItem>
                  <DropdownMenuItem>Archive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

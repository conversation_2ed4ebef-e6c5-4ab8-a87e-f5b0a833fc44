
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Filter, Send, TrendingUp, Clock, MoreHorizontal } from "lucide-react";
import { OverdueFilters } from "@/types/overdueTraining";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface OverdueTrainingsFiltersProps {
  filters: OverdueFilters;
  onFiltersChange: (filters: OverdueFilters) => void;
  selectedCount: number;
  onBulkAction: (action: "remind" | "escalate" | "extend") => void;
}

export const OverdueTrainingsFilters: React.FC<OverdueTrainingsFiltersProps> = ({
  filters,
  onFiltersChange,
  selectedCount,
  onBulkAction
}) => {
  const updateFilter = (key: keyof OverdueFilters, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  return (
    <div className="p-6 border-b border-gray-200">
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex flex-col md:flex-row gap-4 flex-1">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <div className="min-w-[150px]">
              <Select value={filters.department} onValueChange={(value) => updateFilter("department", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="Quality Assurance">Quality Assurance</SelectItem>
                  <SelectItem value="Production">Production</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="All Departments">All Departments</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select value={filters.role} onValueChange={(value) => updateFilter("role", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="Quality Manager">Quality Manager</SelectItem>
                  <SelectItem value="Production Staff">Production Staff</SelectItem>
                  <SelectItem value="Maintenance Technician">Maintenance Technician</SelectItem>
                  <SelectItem value="Lab Technician">Lab Technician</SelectItem>
                  <SelectItem value="All Employees">All Employees</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="min-w-[150px]">
              <Select value={filters.overdueDays} onValueChange={(value) => updateFilter("overdueDays", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="All Overdue" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Overdue</SelectItem>
                  <SelectItem value="1-7">1-7 days</SelectItem>
                  <SelectItem value="8-30">8-30 days</SelectItem>
                  <SelectItem value="30+">30+ days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        
        {selectedCount > 0 && (
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="px-3 py-1">
              {selectedCount} selected
            </Badge>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("remind")}
                className="gap-2"
              >
                <Send className="h-4 w-4" />
                Send Reminder
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("escalate")}
                className="gap-2"
              >
                <TrendingUp className="h-4 w-4" />
                Escalate to Manager
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => onBulkAction("extend")}
                className="gap-2"
              >
                <Clock className="h-4 w-4" />
                Extend Due Date
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Export Selected</DropdownMenuItem>
                  <DropdownMenuItem>Generate Report</DropdownMenuItem>
                  <DropdownMenuItem>Archive</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

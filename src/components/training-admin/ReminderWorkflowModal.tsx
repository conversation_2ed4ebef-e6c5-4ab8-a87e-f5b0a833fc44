
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, Bell, Send, User, Calendar, ChevronRight, ChevronLeft } from "lucide-react";
import { OverdueTraining } from "@/types/overdueTraining";

interface ReminderWorkflowModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTrainings: OverdueTraining[];
}

export const ReminderWorkflowModal: React.FC<ReminderWorkflowModalProps> = ({
  open,
  onOpenChange,
  selectedTrainings
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [reminderType, setReminderType] = useState("email");
  const [sendToManager, setSendToManager] = useState(false);
  const [scheduleType, setScheduleType] = useState("now");
  const [customMessage, setCustomMessage] = useState(
    `Dear {{EmployeeName}},

This is a reminder that your training "{{TrainingTitle}}" is overdue as of {{DueDate}}.

Please complete this training as soon as possible to maintain compliance.

Click the button below to access your training:

Thank you for your attention to this matter.

Best regards,
Training Administration Team`
  );

  const steps = [
    { number: 1, title: "Select Recipients", description: "Confirm selected trainings" },
    { number: 2, title: "Choose Type", description: "Select reminder method" },
    { number: 3, title: "Customize Message", description: "Edit reminder content" },
    { number: 4, title: "Preview & Send", description: "Review and confirm" }
  ];

  const handleNext = () => {
    if (currentStep < 4) setCurrentStep(currentStep + 1);
  };

  const handlePrevious = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const handleSend = () => {
    console.log("Sending reminders:", {
      trainings: selectedTrainings,
      type: reminderType,
      sendToManager,
      scheduleType,
      message: customMessage
    });
    onOpenChange(false);
    setCurrentStep(1);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-3">Selected Trainings ({selectedTrainings.length})</h3>
              <div className="max-h-60 overflow-y-auto space-y-2">
                {selectedTrainings.map((training) => (
                  <div key={training.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">{training.employeeName}</p>
                        <p className="text-sm text-gray-500">{training.trainingTitle}</p>
                      </div>
                    </div>
                    <Badge variant="destructive" className="text-xs">
                      {training.daysOverdue} days overdue
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      case 2:
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-semibold">Reminder Type</Label>
              <RadioGroup value={reminderType} onValueChange={setReminderType} className="mt-3">
                <div className="flex items-center space-x-2 p-3 border rounded-lg">
                  <RadioGroupItem value="email" id="email" />
                  <Mail className="h-4 w-4 text-blue-600" />
                  <Label htmlFor="email" className="flex-1">Email Notification</Label>
                </div>
                <div className="flex items-center space-x-2 p-3 border rounded-lg">
                  <RadioGroupItem value="in-app" id="in-app" />
                  <Bell className="h-4 w-4 text-green-600" />
                  <Label htmlFor="in-app" className="flex-1">In-App Notification</Label>
                </div>
                <div className="flex items-center space-x-2 p-3 border rounded-lg">
                  <RadioGroupItem value="both" id="both" />
                  <div className="flex gap-1">
                    <Mail className="h-4 w-4 text-blue-600" />
                    <Bell className="h-4 w-4 text-green-600" />
                  </div>
                  <Label htmlFor="both" className="flex-1">Both Email & In-App</Label>
                </div>
              </RadioGroup>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="manager" 
                  checked={sendToManager}
                  onCheckedChange={(checked) => setSendToManager(checked === true)}
                />
                <Label htmlFor="manager">Also send reminder to manager</Label>
              </div>
              
              <div>
                <Label className="text-base font-semibold">Schedule</Label>
                <Select value={scheduleType} onValueChange={setScheduleType}>
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="now">Send immediately</SelectItem>
                    <SelectItem value="tomorrow">Send tomorrow morning</SelectItem>
                    <SelectItem value="next-week">Send next Monday</SelectItem>
                    <SelectItem value="custom">Custom schedule</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        );
      
      case 3:
        return (
          <div className="space-y-4">
            <div>
              <Label className="text-base font-semibold">Customize Message</Label>
              <p className="text-sm text-gray-500 mb-3">
                Use tokens like {`{{EmployeeName}}`}, {`{{TrainingTitle}}`}, {`{{DueDate}}`} for personalization
              </p>
              <Textarea
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                rows={12}
                className="font-mono text-sm"
              />
            </div>
            
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Available tokens:</strong> {`{{EmployeeName}}`}, {`{{TrainingTitle}}`}, {`{{DueDate}}`}, {`{{DaysOverdue}}`}, {`{{ManagerName}}`}
              </p>
            </div>
          </div>
        );
      
      case 4:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Email Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Subject:</Label>
                  <p className="mt-1 p-2 bg-gray-50 rounded">
                    Training Reminder: {selectedTrainings[0]?.trainingTitle} - Action Required
                  </p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Message:</Label>
                  <div className="mt-1 p-4 bg-gray-50 rounded-lg text-sm whitespace-pre-wrap">
                    {customMessage
                      .replace(/\{\{EmployeeName\}\}/g, selectedTrainings[0]?.employeeName || 'Employee Name')
                      .replace(/\{\{TrainingTitle\}\}/g, selectedTrainings[0]?.trainingTitle || 'Training Title')
                      .replace(/\{\{DueDate\}\}/g, selectedTrainings[0]?.dueDate || 'Due Date')
                      .replace(/\{\{DaysOverdue\}\}/g, String(selectedTrainings[0]?.daysOverdue || 0))
                      .replace(/\{\{ManagerName\}\}/g, selectedTrainings[0]?.managerName || 'Manager Name')
                    }
                  </div>
                  
                  <div className="mt-4 text-center">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      Complete Training Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="font-medium">Recipients:</Label>
                <p>{selectedTrainings.length} employees</p>
              </div>
              <div>
                <Label className="font-medium">Type:</Label>
                <p className="capitalize">{reminderType.replace('-', ' ')}</p>
              </div>
              <div>
                <Label className="font-medium">Manager Copy:</Label>
                <p>{sendToManager ? 'Yes' : 'No'}</p>
              </div>
              <div>
                <Label className="font-medium">Schedule:</Label>
                <p className="capitalize">{scheduleType.replace('-', ' ')}</p>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Reminder Workflow
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Step indicator */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center gap-2 ${
                  currentStep >= step.number ? 'text-blue-600' : 'text-gray-400'
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step.number 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {step.number}
                  </div>
                  <div className="hidden md:block">
                    <p className="font-medium">{step.title}</p>
                    <p className="text-xs">{step.description}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className="h-4 w-4 mx-4 text-gray-400" />
                )}
              </div>
            ))}
          </div>
          
          {/* Step content */}
          <div className="min-h-[400px]">
            {renderStepContent()}
          </div>
          
          {/* Navigation buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            
            {currentStep < 4 ? (
              <Button onClick={handleNext} className="gap-2">
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button onClick={handleSend} className="gap-2">
                <Send className="h-4 w-4" />
                Send Reminders
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

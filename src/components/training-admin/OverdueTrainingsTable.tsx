
import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, Send, TrendingUp, MoreHorizontal, User } from "lucide-react";
import { OverdueTraining } from "@/types/overdueTraining";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface OverdueTrainingsTableProps {
  trainings: OverdueTraining[];
  selectedTrainings: string[];
  onSelectTraining: (trainingId: string) => void;
  onSelectAll: () => void;
}

export const OverdueTrainingsTable: React.FC<OverdueTrainingsTableProps> = ({
  trainings,
  selectedTrainings,
  onSelectTraining,
  onSelectAll
}) => {
  const getUrgencyBadge = (urgencyLevel: OverdueTraining['urgencyLevel'], daysOverdue: number) => {
    const urgencyConfig = {
      'low': { variant: 'outline' as const, className: 'border-gray-300 text-gray-700 bg-gray-50' },
      'medium': { variant: 'default' as const, className: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
      'high': { variant: 'default' as const, className: 'bg-orange-100 text-orange-800 border-orange-200' },
      'critical': { variant: 'destructive' as const, className: 'bg-red-100 text-red-800 border-red-200' }
    };
    
    const config = urgencyConfig[urgencyLevel];
    return (
      <div className="flex items-center gap-2">
        <Badge variant={config.variant} className={config.className}>
          {daysOverdue > 0 ? `${daysOverdue} days overdue` : 'Due soon'}
        </Badge>
        <Badge variant="outline" className="text-xs">
          {urgencyLevel.charAt(0).toUpperCase() + urgencyLevel.slice(1)}
        </Badge>
      </div>
    );
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="bg-gray-50">
            <TableHead className="w-12">
              <Checkbox
                checked={selectedTrainings.length === trainings.length && trainings.length > 0}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead className="font-semibold">Employee Name</TableHead>
            <TableHead className="font-semibold">Training Title</TableHead>
            <TableHead className="font-semibold">Due Date</TableHead>
            <TableHead className="font-semibold">Days Overdue</TableHead>
            <TableHead className="font-semibold">Manager</TableHead>
            <TableHead className="font-semibold">Last Reminder</TableHead>
            <TableHead className="font-semibold text-center">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {trainings.map((training) => (
            <TableRow key={training.id} className="hover:bg-gray-50">
              <TableCell>
                <Checkbox
                  checked={selectedTrainings.includes(training.id)}
                  onCheckedChange={() => onSelectTraining(training.id)}
                />
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{training.employeeName}</p>
                    <p className="text-sm text-gray-500">{training.department}</p>
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="min-w-[200px]">
                  <p className="font-medium text-gray-900">{training.trainingTitle}</p>
                  <p className="text-sm text-gray-500">ID: {training.trainingId}</p>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  {new Date(training.dueDate).toLocaleDateString()}
                </div>
              </TableCell>
              
              <TableCell>
                {getUrgencyBadge(training.urgencyLevel, training.daysOverdue)}
              </TableCell>
              
              <TableCell>
                <div>
                  <p className="font-medium text-gray-900">{training.managerName}</p>
                  <p className="text-sm text-gray-500">Manager</p>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1 text-sm">
                  <Clock className="h-4 w-4 text-gray-400" />
                  {training.lastReminderSent === "Never" 
                    ? <span className="text-gray-500">Never</span>
                    : new Date(training.lastReminderSent).toLocaleDateString()
                  }
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="gap-1">
                    <Send className="h-3 w-3" />
                    Remind
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Escalate to Manager
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Clock className="h-4 w-4 mr-2" />
                        Extend Due Date
                      </DropdownMenuItem>
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>Contact Employee</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      {trainings.length === 0 && (
        <div className="text-center py-12">
          <Clock className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <p className="text-gray-500 text-lg">No overdue trainings found</p>
          <p className="text-gray-400 text-sm">All trainings are up to date!</p>
        </div>
      )}
    </div>
  );
};


import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookOpen, CheckCircle, AlertTriangle, Clock, Users, TrendingUp } from "lucide-react";
import { TrainingAssignment } from "@/types/trainingAdmin";

interface TrainingAdminMetricsProps {
  trainings: TrainingAssignment[];
}

export const TrainingAdminMetrics: React.FC<TrainingAdminMetricsProps> = ({ trainings }) => {
  const totalAssigned = trainings.reduce((sum, training) => sum + training.totalAssigned, 0);
  const totalCompleted = trainings.reduce((sum, training) => sum + training.completed, 0);
  const overdue = trainings.filter(t => t.status === "Overdue").length;
  const inProgress = trainings.filter(t => t.status === "In Progress").length;
  const completionRate = totalAssigned > 0 ? Math.round((totalCompleted / totalAssigned) * 100) : 0;
  
  // Calculate average time spent
  const avgTimeSpentMinutes = trainings.reduce((sum, training) => {
    const timeStr = training.averageTimeSpent;
    if (timeStr.includes('h')) {
      const hours = parseFloat(timeStr.replace('h', '').replace('min', ''));
      return sum + (hours * 60);
    } else if (timeStr.includes('min')) {
      return sum + parseFloat(timeStr.replace('min', ''));
    }
    return sum;
  }, 0) / trainings.length;
  
  const avgTimeDisplay = avgTimeSpentMinutes >= 60 
    ? `${(avgTimeSpentMinutes / 60).toFixed(1)}h`
    : `${Math.round(avgTimeSpentMinutes)}min`;

  const metrics = [
    {
      title: "Total Assigned",
      value: totalAssigned.toLocaleString(),
      icon: BookOpen,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: "+12% vs last month"
    },
    {
      title: "Completed",
      value: totalCompleted.toLocaleString(),
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: "+8% vs last month"
    },
    {
      title: "Overdue",
      value: overdue.toString(),
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-100",
      change: "-15% vs last month"
    },
    {
      title: "In Progress",
      value: inProgress.toString(),
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      change: "+5% vs last month"
    },
    {
      title: "Avg Time Spent",
      value: avgTimeDisplay,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      change: "-10% vs last month"
    },
    {
      title: "Completion Rate",
      value: `${completionRate}%`,
      icon: Users,
      color: "text-indigo-600",
      bgColor: "bg-indigo-100",
      change: "+3% vs last month"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {metrics.map((metric) => (
        <Card key={metric.title} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                <metric.icon className={`h-5 w-5 ${metric.color}`} />
              </div>
              <Badge variant="outline" className="text-xs">
                {metric.change}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">{metric.title}</p>
              <p className={`text-2xl font-bold ${metric.color}`}>{metric.value}</p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};


import React from "react";
import { <PERSON>Left, Eye, Edit, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { Document } from "@/components/document-hub/DocumentTable";

interface DocumentHeaderProps {
  document: Document;
}

export const DocumentHeader: React.FC<DocumentHeaderProps> = ({ document }) => {
  const navigate = useNavigate();
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Published":
        return "bg-green-100 text-green-800";
      case "Draft":
        return "bg-yellow-100 text-yellow-800";
      case "Needs Approval":
        return "bg-blue-100 text-blue-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      case "Expired":
        return "bg-gray-200 text-gray-800";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <>
      <div className="mb-6 flex justify-between items-center">
        <Button 
          variant="outline" 
          className="flex items-center gap-2"
          onClick={() => navigate("/")}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Documents
        </Button>
        
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
          <Button variant="outline" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 w-full">
        <div className="flex justify-between items-start mb-2">
          <div>
            <div className="flex items-center mb-3">
              <h2 className="text-2xl font-bold text-gray-800">{document.title}</h2>
              <Badge className="ml-3 px-2 py-1">{document.version || "v1.0"}</Badge>
              <Badge className={`ml-2 px-2 py-1 ${getStatusColor(document.status)}`}>{document.status}</Badge>
            </div>
            <p className="text-sm text-gray-500">{document.id}</p>
          </div>
        </div>
      </div>
    </>
  );
};

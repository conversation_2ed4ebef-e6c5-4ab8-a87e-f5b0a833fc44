
import React from "react";
import { Check<PERSON>ir<PERSON>, Clock, User, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Approver {
  id: number;
  name: string;
  status: "pending" | "approved" | "rejected";
}

interface ApprovalStatus {
  approved: number;
  pending: number;
  total: number;
}

interface ApprovalProcessProps {
  approvalStatus: ApprovalStatus;
  approvers: Approver[];
  onApprove: (approverId: number) => void;
  onReject: (approverId: number) => void;
  onResend: (approverId: number) => void;
}

export const ApprovalProcess: React.FC<ApprovalProcessProps> = ({
  approvalStatus,
  approvers,
  onApprove,
  onReject,
  onResend
}) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-5 mb-6">
      <h3 className="text-lg font-semibold mb-4">Approval Process</h3>
      <div className="mb-2 text-sm text-gray-600">
        <p>Parallel Approval</p>
        <p>A new version is being reviewed and is awaiting approval from team members.</p>
      </div>
      
      <div className="flex items-center gap-4 py-3 border-y border-gray-200 mb-4">
        <div className="flex items-center">
          <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
          <span className="text-sm">{approvalStatus.approved} Approved</span>
        </div>
        
        <div className="flex items-center">
          <Clock className="h-4 w-4 text-amber-500 mr-1" />
          <span className="text-sm">{approvalStatus.pending} Pending</span>
        </div>
        
        <div className="flex items-center ml-auto">
          <span className="text-sm text-gray-500">{approvalStatus.approved}/{approvalStatus.total} completed</span>
        </div>
      </div>
      
      <div className="space-y-3">
        {approvers.map((approver) => (
          <div key={approver.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
            <div className="flex items-center">
              <div className="w-6 text-center mr-2">{approver.id}</div>
              <User className="h-4 w-4 text-gray-500 mr-2" />
              <span>{approver.name}</span>
            </div>
            
            <div className="flex items-center gap-2">
              {approver.status === "approved" ? (
                <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>
              ) : approver.status === "rejected" ? (
                <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Rejected</Badge>
              ) : (
                <>
                  <Button 
                    onClick={() => onApprove(approver.id)} 
                    size="sm" 
                    variant="outline"
                    className="border-green-500 text-green-600 hover:bg-green-50"
                  >
                    <CheckCircle className="h-3.5 w-3.5 mr-1" />
                    Approve
                  </Button>
                  <Button 
                    onClick={() => onReject(approver.id)} 
                    size="sm" 
                    variant="outline" 
                    className="border-red-500 text-red-600 hover:bg-red-50"
                  >
                    Reject
                  </Button>
                </>
              )}
              <Button 
                onClick={() => onResend(approver.id)}
                size="sm"
                variant="ghost"
              >
                <Send className="h-3.5 w-3.5" />
                <span className="sr-only">Resend</span>
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

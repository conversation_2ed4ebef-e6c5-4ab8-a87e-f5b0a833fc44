import React, { useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { DocumentInfo } from "./DocumentInfo";
import { ApprovalProcess } from "./ApprovalProcess";
import { DocumentActions } from "./DocumentActions";
import { CommentSystem } from "@/components/comments/CommentSystem";
import { DocumentTimeline } from "./DocumentTimeline";
import { DocumentVersionsTab } from "./DocumentVersionsTab";
import { DocumentLogsTimeline } from "./DocumentLogsTimeline";
import { Document } from "@/components/document-hub/DocumentTable";

interface Approver {
  id: number;
  name: string;
  status: "pending" | "approved" | "rejected";
}

interface ApprovalStatus {
  approved: number;
  pending: number;
  total: number;
}

interface DocumentTabsProps {
  document: Document;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  approvalStatus: ApprovalStatus;
  approvers: Approver[];
  onApprove: (approverId: number) => void;
  onReject: (approverId: number) => void;
  onResend: (approverId: number) => void;
  onStartReview: () => void;
  onEditDetails: () => void;
}

export const DocumentTabs: React.FC<DocumentTabsProps> = ({
  document,
  activeTab,
  setActiveTab,
  approvalStatus,
  approvers,
  onApprove,
  onReject,
  onResend,
  onStartReview,
  onEditDetails
}) => {
  const [searchParams] = useSearchParams();
  
  // Handle URL tab parameter for direct navigation
  React.useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['details', 'versions', 'comments', 'logs'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams, setActiveTab]);

  // Mock logs data - in production, fetch real logs related to this document!
  const logs = [
    {
      id: "log-1",
      user: "John Doe",
      action: "Created",
      description: "Document was initially created.",
      timestamp: "2024-01-10 09:00"
    },
    {
      id: "log-2",
      user: "Jane Smith",
      action: "Edited",
      description: "Updated section 2 for recent requirement change.",
      timestamp: "2024-01-11 11:12"
    },
    {
      id: "log-3",
      user: "Sarah Anderson",
      action: "Status Change",
      description: "Status changed from Draft to Published.",
      timestamp: "2024-01-12 15:30"
    },
    {
      id: "log-4",
      user: "Mike Johnson",
      action: "Commented",
      description: "Left a review comment about formatting.",
      timestamp: "2024-01-13 09:15"
    },
    {
      id: "log-5",
      user: "John Doe",
      action: "Revision",
      description: "Uploaded version 1.1.",
      timestamp: "2024-01-14 10:45"
    }
  ];

  return (
    <Tabs defaultValue="details" className="w-full" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="bg-white border-b border-gray-200 w-full flex justify-start rounded-none p-0 h-auto">
        <TabsTrigger 
          value="details" 
          className="px-6 py-3 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none"
        >
          Details
        </TabsTrigger>
        <TabsTrigger 
          value="versions" 
          className="px-6 py-3 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none"
        >
          Versions
        </TabsTrigger>
        <TabsTrigger 
          value="comments" 
          className="px-6 py-3 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none"
        >
          Comments
        </TabsTrigger>
        <TabsTrigger 
          value="logs" 
          className="px-6 py-3 rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none"
        >
          Logs
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="details" className="p-0 mt-6">
        <DocumentTimeline 
          document={document}
          approvalStatus={approvalStatus}
          approvers={approvers}
          onApprove={onApprove}
          onReject={onReject}
          onResend={onResend}
          onStartReview={onStartReview}
          onEditDetails={onEditDetails}
        />
      </TabsContent>
      
      <TabsContent value="versions" className="p-0 mt-6">
        <DocumentVersionsTab document={document} />
      </TabsContent>
      
      <TabsContent value="comments" className="p-0 mt-6">
        <CommentSystem documentId={document.id} />
      </TabsContent>
      
      <TabsContent value="logs" className="p-0 mt-6">
        <DocumentLogsTimeline logs={logs} />
      </TabsContent>
    </Tabs>
  );
};

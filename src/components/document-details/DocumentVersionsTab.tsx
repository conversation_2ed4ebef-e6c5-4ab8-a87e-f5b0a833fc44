
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Download, 
  FileText, 
  Calendar, 
  User, 
  Eye, 
  GitCompare,
  Clock,
  CheckCircle
} from "lucide-react";
import { Document } from "@/components/document-hub/DocumentTable";

interface DocumentVersion {
  id: string;
  version: string;
  status: 'Current' | 'Previous' | 'Draft' | 'Archived';
  uploadDate: string;
  uploadedBy: string;
  fileSize: string;
  description: string;
  changes?: string[];
  downloadUrl?: string;
}

interface DocumentVersionsTabProps {
  document: Document;
}

export const DocumentVersionsTab: React.FC<DocumentVersionsTabProps> = ({ document: docData }) => {
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null);
  const [compareVersion, setCompareVersion] = useState<DocumentVersion | null>(null);
  const [showCompareModal, setShowCompareModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  // Mock version data
  const versions: DocumentVersion[] = [
    {
      id: '1',
      version: '3.0',
      status: 'Current',
      uploadDate: '2024-01-15 14:30',
      uploadedBy: 'Sarah Johnson',
      fileSize: '2.4 MB',
      description: 'Final approved version with all stakeholder feedback incorporated',
      changes: [
        'Updated safety procedures section',
        'Added new compliance requirements',
        'Revised quality control checklist',
        'Updated contact information'
      ],
      downloadUrl: '/documents/qms-v3.0.pdf'
    },
    {
      id: '2',
      version: '2.1',
      status: 'Previous',
      uploadDate: '2024-01-10 11:20',
      uploadedBy: 'John Smith',
      fileSize: '2.2 MB',
      description: 'Previous version with initial stakeholder feedback',
      changes: [
        'Added risk assessment section',
        'Updated process flow diagrams',
        'Revised documentation standards'
      ],
      downloadUrl: '/documents/qms-v2.1.pdf'
    },
    {
      id: '3',
      version: '2.0',
      status: 'Previous',
      uploadDate: '2024-01-05 09:15',
      uploadedBy: 'Mike Wilson',
      fileSize: '2.1 MB',
      description: 'Major revision with new regulatory compliance updates',
      changes: [
        'Complete restructure of document',
        'Added new regulatory sections',
        'Updated all process descriptions'
      ],
      downloadUrl: '/documents/qms-v2.0.pdf'
    },
    {
      id: '4',
      version: '1.5',
      status: 'Archived',
      uploadDate: '2023-12-20 16:45',
      uploadedBy: 'Jane Doe',
      fileSize: '1.9 MB',
      description: 'Legacy version before major updates',
      changes: [
        'Minor text corrections',
        'Updated appendices',
        'Fixed formatting issues'
      ],
      downloadUrl: '/documents/qms-v1.5.pdf'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Current':
        return <Badge className="bg-green-100 text-green-800">Current</Badge>;
      case 'Previous':
        return <Badge variant="outline">Previous</Badge>;
      case 'Draft':
        return <Badge className="bg-yellow-100 text-yellow-800">Draft</Badge>;
      case 'Archived':
        return <Badge variant="secondary">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleDownload = (version: DocumentVersion) => {
    // Mock download functionality
    console.log(`Downloading ${version.version}: ${version.downloadUrl}`);
    // In a real app, this would trigger the actual download
    const link = window.document.createElement('a');
    link.href = version.downloadUrl || '#';
    link.download = `${docData.title}_v${version.version}.pdf`;
    link.click();
  };

  const handleCompare = (version1: DocumentVersion, version2: DocumentVersion) => {
    setSelectedVersion(version1);
    setCompareVersion(version2);
    setShowCompareModal(true);
  };

  const handlePreview = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setShowPreviewModal(true);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Version History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {versions.map((version, index) => (
              <div key={version.id} className="relative">
                <div className="flex items-start gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  {/* Version indicator */}
                  <div className="flex flex-col items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      version.status === 'Current' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                    }`}>
                      v{version.version}
                    </div>
                    {index < versions.length - 1 && (
                      <div className="w-px h-8 bg-gray-200 mt-2" />
                    )}
                  </div>

                  {/* Version details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900">Version {version.version}</h3>
                          {getStatusBadge(version.status)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{version.description}</p>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {version.uploadDate}
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {version.uploadedBy}
                          </div>
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            {version.fileSize}
                          </div>
                        </div>

                        {/* Changes */}
                        {version.changes && version.changes.length > 0 && (
                          <div className="mb-3">
                            <p className="text-xs font-medium text-gray-700 mb-1">Changes in this version:</p>
                            <ul className="text-xs text-gray-600 space-y-1">
                              {version.changes.map((change, changeIndex) => (
                                <li key={changeIndex} className="flex items-start gap-2">
                                  <span className="text-green-600 mt-0.5">•</span>
                                  <span>{change}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(version)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(version)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        {index > 0 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCompare(version, versions[index - 1])}
                          >
                            <GitCompare className="h-4 w-4 mr-1" />
                            Compare
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Compare Modal */}
      <Dialog open={showCompareModal} onOpenChange={setShowCompareModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Compare Versions: v{selectedVersion?.version} vs v{compareVersion?.version}
            </DialogTitle>
          </DialogHeader>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-green-700 mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Version {selectedVersion?.version} (Newer)
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 mb-3">{selectedVersion?.description}</p>
                {selectedVersion?.changes?.map((change, index) => (
                  <div key={index} className="p-2 bg-green-50 rounded text-sm border-l-4 border-green-400">
                    + {change}
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Version {compareVersion?.version} (Previous)
              </h3>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 mb-3">{compareVersion?.description}</p>
                {compareVersion?.changes?.map((change, index) => (
                  <div key={index} className="p-2 bg-gray-50 rounded text-sm border-l-4 border-gray-300">
                    {change}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Modal */}
      <Dialog open={showPreviewModal} onOpenChange={setShowPreviewModal}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Preview: Version {selectedVersion?.version}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => selectedVersion && handleDownload(selectedVersion)}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          <div className="border rounded-lg p-6 bg-gray-50 min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">Document Preview</p>
              <p className="text-sm text-gray-500">Version {selectedVersion?.version} preview would be displayed here</p>
              <p className="text-xs text-gray-400 mt-2">File size: {selectedVersion?.fileSize}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

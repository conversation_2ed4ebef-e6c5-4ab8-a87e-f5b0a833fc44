
import React from "react";
import { FileText, Building, User, Clock, Calendar, Globe } from "lucide-react";
import { Document } from "@/components/document-hub/DocumentTable";

interface DocumentInfoProps {
  document: Document;
}

export const DocumentInfo: React.FC<DocumentInfoProps> = ({ document }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 w-full">
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex flex-col space-y-4">
          <div>
            <div className="flex items-center mb-1">
              <FileText className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Doc ID</h3>
            </div>
            <p className="text-gray-800 pl-6">{document.id}</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <Building className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Department</h3>
            </div>
            <p className="text-gray-800 pl-6">{document.department}</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <User className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Process</h3>
            </div>
            <p className="text-gray-800 pl-6">Human Resource</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <Clock className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Review Period</h3>
            </div>
            <p className="text-gray-800 pl-6">Monthly</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-5">
        <div className="flex flex-col space-y-4">
          <div>
            <div className="flex items-center mb-1">
              <User className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Assignee</h3>
            </div>
            <p className="text-gray-800 pl-6">{document.assignee}</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <User className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Approver</h3>
            </div>
            <p className="text-gray-800 pl-6">Vinodh Peddi</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <Calendar className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Next Review</h3>
            </div>
            <p className="text-gray-800 pl-6">Mar 31 2024</p>
          </div>
          
          <div>
            <div className="flex items-center mb-1">
              <Globe className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-500">Origin</h3>
            </div>
            <p className="text-gray-800 pl-6">Internal</p>
          </div>
        </div>
      </div>
    </div>
  );
};

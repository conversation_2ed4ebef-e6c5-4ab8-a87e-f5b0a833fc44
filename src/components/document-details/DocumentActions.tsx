
import React from "react";
import { AlertCircle, User, Edit } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface DocumentActionsProps {
  onStartReview: () => void;
  onEditDetails: () => void;
}

export const DocumentActions: React.FC<DocumentActionsProps> = ({ 
  onStartReview,
  onEditDetails
}) => {
  return (
    <>
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-5 flex items-start justify-between mb-6">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 mr-3" />
          <div>
            <h3 className="font-medium text-amber-800">Review Date Passed</h3>
            <p className="text-sm text-amber-700">This document is due for review.</p>
          </div>
        </div>
        <Button 
          onClick={onStartReview} 
          className="bg-amber-500 hover:bg-amber-600 text-white"
        >
          Start Review
        </Button>
      </div>
      
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-5 flex items-center mb-6">
        <User className="h-5 w-5 text-amber-500 mr-3" />
        <div className="flex-1">
          <h3 className="font-medium text-amber-800">Waiting for approvals</h3>
          <p className="text-sm text-amber-700">The document will be ready to publish once all approvers have approved.</p>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row md:items-center gap-3 justify-end">
        <h3 className="font-medium text-gray-700">Quick Actions</h3>
        <Button 
          onClick={onStartReview}
          className="bg-teal-600 hover:bg-teal-700 text-white"
        >
          Start Review
        </Button>
        <Button 
          onClick={onEditDetails}
          variant="outline"
        >
          <Edit className="h-4 w-4 mr-1" />
          Edit Details
        </Button>
      </div>
    </>
  );
};

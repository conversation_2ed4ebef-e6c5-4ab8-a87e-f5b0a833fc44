
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Clock, Logs } from "lucide-react";

export interface DocumentLog {
  id: string;
  user: string;
  action: string;
  description: string;
  timestamp: string;
}

interface DocumentLogsTimelineProps {
  logs: DocumentLog[];
}

export const DocumentLogsTimeline: React.FC<DocumentLogsTimelineProps> = ({ logs }) => {
  if (!logs || logs.length === 0) {
    return (
      <Card>
        <CardContent className="py-10 text-center text-gray-500">
          <Logs className="mx-auto mb-2 h-6 w-6 text-gray-400" />
          No log entries found for this document.
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
          <Logs className="h-5 w-5 text-blue-600" />
          Activity Logs
        </h3>
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
          <div className="space-y-6">
            {logs.map((log, idx) => (
              <div key={log.id} className="relative flex items-start min-h-[40px]">
                {/* Timeline dot */}
                <div className="absolute left-4 w-4 h-4 rounded-full border-2 border-white bg-blue-500 z-10"></div>
                {/* Content */}
                <div className="ml-12 flex-1">
                  <div className="bg-white border border-gray-100 rounded-lg p-4 shadow-sm">
                    <div className="flex items-center gap-2 mb-1 text-gray-900 font-medium">
                      <User className="h-4 w-4 text-blue-500" />
                      {log.user}
                      <span className="mx-2 text-xs text-slate-400 font-normal">|</span>
                      <Badge variant="outline" className="text-xs border-blue-200 text-blue-600 mr-2">
                        {log.action}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-700 mb-2">
                      {log.description}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-400">
                      <Clock className="h-3 w-3" />
                      <span>{log.timestamp}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

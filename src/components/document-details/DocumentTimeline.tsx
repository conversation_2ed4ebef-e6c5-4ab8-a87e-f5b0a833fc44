
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  User, 
  Calendar, 
  CheckCircle, 
  Clock, 
  XCircle, 
  Edit, 
  Upload,
  MessageSquare
} from "lucide-react";
import { Document } from "@/components/document-hub/DocumentTable";

interface Approver {
  id: number;
  name: string;
  status: "pending" | "approved" | "rejected";
}

interface ApprovalStatus {
  approved: number;
  pending: number;
  total: number;
}

interface TimelineEvent {
  id: string;
  type: 'created' | 'published' | 'review' | 'approved' | 'rejected' | 'updated';
  title: string;
  description: string;
  timestamp: string;
  user: string;
  icon: React.ReactNode;
  status?: 'completed' | 'pending' | 'failed';
}

interface DocumentTimelineProps {
  document: Document;
  approvalStatus: ApprovalStatus;
  approvers: Approver[];
  onApprove: (approverId: number) => void;
  onReject: (approverId: number) => void;
  onResend: (approverId: number) => void;
  onStartReview: () => void;
  onEditDetails: () => void;
}

export const DocumentTimeline: React.FC<DocumentTimelineProps> = ({
  document,
  approvalStatus,
  approvers,
  onApprove,
  onReject,
  onResend,
  onStartReview,
  onEditDetails
}) => {
  // Mock timeline events
  const timelineEvents: TimelineEvent[] = [
    {
      id: '1',
      type: 'created',
      title: 'Document Created',
      description: 'Initial document created and uploaded',
      timestamp: '2024-01-10 09:00',
      user: 'John Smith',
      icon: <FileText className="h-4 w-4" />,
      status: 'completed'
    },
    {
      id: '2',
      type: 'published',
      title: 'Document Published',
      description: 'Document published for review',
      timestamp: document.publishedDate + ' 14:30',
      user: document.assignee,
      icon: <Upload className="h-4 w-4" />,
      status: 'completed'
    },
    {
      id: '3',
      type: 'review',
      title: 'Review Process Started',
      description: 'Document sent for approval review',
      timestamp: '2024-01-12 10:15',
      user: document.assignee,
      icon: <Clock className="h-4 w-4" />,
      status: 'pending'
    },
    {
      id: '4',
      type: 'approved',
      title: 'Partial Approval',
      description: `${approvalStatus.approved} out of ${approvalStatus.total} approvers have approved`,
      timestamp: '2024-01-13 16:45',
      user: approvers.find(a => a.status === 'approved')?.name || 'Unknown',
      icon: <CheckCircle className="h-4 w-4" />,
      status: approvalStatus.pending > 0 ? 'pending' : 'completed'
    }
  ];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'failed': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Document Info Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold mb-2">{document.title}</h2>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>Assignee: {document.assignee}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Published: {document.publishedDate}</span>
                </div>
                <Badge variant={document.status === 'published' ? 'default' : 'secondary'}>
                  {document.status}
                </Badge>
              </div>
            </div>
            <Button variant="outline" onClick={onEditDetails}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Details
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-gray-700">Department</p>
              <p className="text-sm text-gray-600">{document.department}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-gray-700">Category</p>
              <p className="text-sm text-gray-600">{document.category}</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm font-medium text-gray-700">Review Date</p>
              <p className="text-sm text-gray-600">{document.reviewDate}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-6">Document Timeline</h3>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            <div className="space-y-6">
              {timelineEvents.map((event, index) => (
                <div key={event.id} className="relative flex items-start">
                  {/* Timeline dot */}
                  <div className={`absolute left-4 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(event.status)} z-10`}></div>
                  
                  {/* Content */}
                  <div className="ml-12 flex-1">
                    <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            {event.icon}
                            <h4 className="font-medium text-gray-900">{event.title}</h4>
                            {getStatusIcon(event.status)}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>{event.timestamp}</span>
                            <span>by {event.user}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Approval Process */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Approval Process</h3>
          
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-gray-600">
                {approvalStatus.approved} of {approvalStatus.total} approved
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(approvalStatus.approved / approvalStatus.total) * 100}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-3">
            {approvers.map((approver) => (
              <div key={approver.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    approver.status === 'approved' ? 'bg-green-500' :
                    approver.status === 'rejected' ? 'bg-red-500' : 'bg-yellow-500'
                  }`}></div>
                  <span className="font-medium">{approver.name}</span>
                  <Badge variant={
                    approver.status === 'approved' ? 'default' :
                    approver.status === 'rejected' ? 'destructive' : 'secondary'
                  }>
                    {approver.status}
                  </Badge>
                </div>
                
                {approver.status === 'pending' && (
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onResend(approver.id)}
                    >
                      Resend
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="mt-6 flex gap-3">
            <Button onClick={onStartReview}>
              Start Review Process
            </Button>
            <Button variant="outline">
              <MessageSquare className="h-4 w-4 mr-2" />
              Add Comment
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useFormValidation } from "@/hooks/useFormValidation";
import { materialSchema, MaterialFormData } from "@/schemas/materialValidation";
import { toast } from "sonner";

interface AddMaterialDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AddMaterialDialog: React.FC<AddMaterialDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const form = useFormValidation({
    schema: materialSchema,
    defaultValues: {
      materialId: "",
      materialName: "",
      type: "",
      category: "",
      uom: ""
    }
  });

  const handleSubmit = (data: MaterialFormData) => {
    console.log("Creating material:", data);
    toast.success("Material added successfully");
    onOpenChange(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[750px] max-h-[85vh] p-0">
        <DialogHeader className="bg-gray-50 p-6 border-b border-gray-200">
          <DialogTitle className="text-xl font-semibold text-gray-800">Create Material</DialogTitle>
          <DialogDescription className="text-gray-600">
            Add a new material to the inventory hub. Fill in the required fields below.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <FormField
                control={form.control}
                name="materialId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Material ID<span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="Enter Material ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="materialName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Material Name<span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="Enter Material name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type<span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-10 bg-white">
                          <SelectValue placeholder="Enter material type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-white">
                        <SelectItem value="Material Type">Material Type</SelectItem>
                        <SelectItem value="Type 1">Type 1</SelectItem>
                        <SelectItem value="test">Test</SelectItem>
                        <SelectItem value="testing type">Testing Type</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category<span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-10 bg-white">
                          <SelectValue placeholder="Enter material category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-white">
                        <SelectItem value="Category Type">Category Type</SelectItem>
                        <SelectItem value="Category 1">Category 1</SelectItem>
                        <SelectItem value="test">Test</SelectItem>
                        <SelectItem value="faed">Faed</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="uom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>UOM<span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="h-10 bg-white">
                          <SelectValue placeholder="Enter material units" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="bg-white">
                        <SelectItem value="INR">INR</SelectItem>
                        <SelectItem value="UoM">UoM</SelectItem>
                        <SelectItem value="Test">Test</SelectItem>
                        <SelectItem value="dddwada">Dddwada</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <DialogFooter className="border-t border-gray-200 pt-6 mt-6 px-0">
              <Button 
                type="button"
                onClick={() => onOpenChange(false)} 
                variant="outline"
                className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                className="bg-teal-600 hover:bg-teal-700 text-white"
              >
                Create Material
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

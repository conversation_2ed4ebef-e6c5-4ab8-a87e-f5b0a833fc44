
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Material } from "@/types/inventory";
import { MobileInventoryTableRow } from "./MobileInventoryTableRow";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";

interface InventoryTableRowProps {
  material: Material;
  index: number;
  isMobile: boolean;
}

export const InventoryTableRow: React.FC<InventoryTableRowProps> = ({ material, index, isMobile }) => {
  const handleEdit = () => {
    toast.success(`Editing material: ${material.name}`);
  };

  const handleDelete = () => {
    toast.success(`Deleting material: ${material.name}`);
  };

  if (isMobile) {
    return <MobileInventoryTableRow material={material} index={index} />;
  }

  return (
    <TableRow className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-100`}>
      <TableCell className="py-4 px-4">
        <div className="flex flex-col">
          <span className="text-blue-600 font-medium">{material.name}</span>
          <span className="text-xs text-gray-500 mt-1">ID: {material.id}</span>
        </div>
      </TableCell>
      <TableCell className="py-4 px-4">{material.type}</TableCell>
      <TableCell className="py-4 px-4">{material.category}</TableCell>
      <TableCell className="py-4 px-4">{material.uom}</TableCell>
      <TableCell className="py-4 px-4">
        <div className="flex items-center justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4 text-gray-600" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="z-50 bg-white">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDelete}>
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </TableCell>
    </TableRow>
  );
};

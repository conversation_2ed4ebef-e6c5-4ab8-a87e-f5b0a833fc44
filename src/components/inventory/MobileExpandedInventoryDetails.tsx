
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Material } from "@/types/inventory";

interface MobileExpandedInventoryDetailsProps {
  material: Material;
  index: number;
}

export const MobileExpandedInventoryDetails: React.FC<MobileExpandedInventoryDetailsProps> = ({ material, index }) => {
  const detailRows = [
    { label: "Type", value: material.type },
    { label: "Category", value: material.category },
    { label: "UOM", value: material.uom },
  ];

  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
      <TableCell colSpan={3} className="px-2 pb-3 pt-0">
        <div className="bg-gray-50 rounded-lg p-3 space-y-2 text-sm">
          {detailRows.map((row, i) => (
            <div key={i} className="grid grid-cols-3 gap-2">
              <div className="col-span-1 text-gray-500 font-medium">{row.label}:</div>
              <div className="col-span-2">{row.value}</div>
            </div>
          ))}
        </div>
      </TableCell>
    </TableRow>
  );
};

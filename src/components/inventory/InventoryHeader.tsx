
import React from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button";

export const InventoryHeader: React.FC = () => {
  const isMobile = useIsMobile();
  const { toggleSidebar } = useSidebar();
  
  return (
    <div className="flex flex-col w-full">
      <div className="flex justify-between items-center w-full">
        <div className="flex items-center">
          {isMobile && (
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={toggleSidebar}
              className="mr-2"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">Inventory Hub</h1>
            {!isMobile && (
              <p className="text-gray-500 text-sm">Manage materials and inventory</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

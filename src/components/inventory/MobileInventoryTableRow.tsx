
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Material } from "@/types/inventory";
import { MobileExpandButton } from "@/components/assets/table/MobileExpandButton";
import { Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { MobileExpandedInventoryDetails } from "./MobileExpandedInventoryDetails";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MobileInventoryTableRowProps {
  material: Material;
  index: number;
}

export const MobileInventoryTableRow: React.FC<MobileInventoryTableRowProps> = ({ material, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleEdit = () => {
    toast.success(`Editing material: ${material.name}`);
  };

  const handleDelete = () => {
    toast.success(`Deleting material: ${material.name}`);
  };

  return (
    <>
      <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100`}>
        <TableCell className="w-10 p-2">
          <MobileExpandButton isExpanded={isExpanded} onClick={toggleExpand} />
        </TableCell>
        <TableCell className="py-3 px-2">
          <div className="flex flex-col">
            <span className="text-blue-600 font-medium">{material.name}</span>
            <span className="text-xs text-gray-500 mt-1">ID: {material.id}</span>
          </div>
        </TableCell>
      </TableRow>
      
      {isExpanded && (
        <>
          {/* Action buttons row - moved to the top when expanded */}
          <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
            <TableCell colSpan={2} className="px-2 py-1">
              <div className="flex items-center justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={handleEdit}
                      >
                        <Edit className="h-4 w-4 text-amber-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={handleDelete}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableCell>
          </TableRow>
          
          <MobileExpandedInventoryDetails material={material} index={index} />
        </>
      )}
    </>
  );
};

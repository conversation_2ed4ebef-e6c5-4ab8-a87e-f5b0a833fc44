
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowDown, ArrowU<PERSON>, ArrowUpDown } from "lucide-react";
import { Material } from "@/types/inventory";

type SortField = keyof Material | null;
type SortDirection = 'asc' | 'desc' | null;

interface SortableHeaderProps {
  field: SortField;
  children: React.ReactNode;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
}

const SortableHeader: React.FC<SortableHeaderProps> = ({ 
  field, 
  children, 
  sortField, 
  sortDirection, 
  onSort 
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHead 
      onClick={() => onSort(field)}
      className="py-3 px-4 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 group cursor-pointer"
    >
      <div className="flex items-center">
        {children}
        {renderSortIcon(field)}
      </div>
    </TableHead>
  );
};

interface MobileInventoryTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
}

export const MobileInventoryTableHeader: React.FC<MobileInventoryTableHeaderProps> = ({ 
  sortField, 
  sortDirection, 
  onSort 
}) => {
  return (
    <TableHeader>
      <TableRow>
        <TableHead className="w-10 p-0"></TableHead>
        <SortableHeader field="id" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>
          ID
        </SortableHeader>
        <SortableHeader field="name" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>
          Name
        </SortableHeader>
      </TableRow>
    </TableHeader>
  );
};


import React from "react";
import { Material } from "@/types/inventory";
import { useIsMobile } from "@/hooks/use-mobile";
import { useInventoryPagination } from "@/hooks/useInventoryPagination";
import { DataTable, TableColumn } from "../shared/table/DataTable";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { useTableSort } from "@/components/assets/table/hooks/useTableSort";
import { VendorTablePagination } from "../vendors/VendorTablePagination";

interface InventoryTableProps {
  materials: Material[];
}

export const InventoryTable: React.FC<InventoryTableProps> = ({ materials }) => {
  const isMobile = useIsMobile();
  const { 
    currentPage, 
    pageSize, 
    totalPages,
    setCurrentPage,
    setPageSize, 
    paginatedData
  } = useInventoryPagination(materials);

  // Create a custom sort hook for materials
  const { sortField, sortDirection, handleSort, sortedItems } = useTableSort<Material>(paginatedData);

  const handleEdit = (material: Material) => {
    toast.success(`Editing material: ${material.name}`);
  };

  const handleDelete = (material: Material) => {
    toast.success(`Deleting material: ${material.name}`);
  };

  // Create wrapper functions for pagination to match vendor table interface
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
  };

  // Define columns
  const columns: TableColumn[] = [
    {
      field: "name",
      header: "Material",
      sortable: true,
      cellRenderer: (material: Material) => (
        <div className="flex flex-col">
          <span className="text-blue-600 font-medium hover:text-blue-700 cursor-pointer">{material.name}</span>
          <span className="text-xs text-gray-500 mt-1">ID: {material.id}</span>
        </div>
      )
    },
    {
      field: "type",
      header: "Type",
      sortable: true
    },
    {
      field: "category",
      header: "Category",
      sortable: true
    },
    {
      field: "uom",
      header: "UOM",
      sortable: true
    }
  ];

  // Define actions renderer
  const renderActions = (material: Material) => (
    <div className="flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4 text-gray-600" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="z-50 bg-white">
          <DropdownMenuItem onClick={() => handleEdit(material)}>
            <Edit className="mr-2 h-4 w-4 text-blue-600" /> Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleDelete(material)}>
            <Trash2 className="mr-2 h-4 w-4 text-red-500" /> Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  if (!materials || materials.length === 0) {
    return (
      <DataTable
        data={[]}
        columns={columns}
        filteredData={[]}
        currentPage={1}
        pageSize={pageSize}
        totalCount={0}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        emptyMessage="No materials found"
      />
    );
  }

  return (
    <div className="flex flex-col">
      <DataTable
        data={materials}
        columns={columns}
        filteredData={sortedItems}
        currentPage={currentPage}
        pageSize={pageSize}
        totalCount={materials.length}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        renderActions={renderActions}
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        className="rounded-t-lg rounded-b-none border-b-0"
      />
      <VendorTablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        total={materials.length}
        className="rounded-b-lg border border-t-0 border-gray-200"
      />
    </div>
  );
};


import React from "react";
import { Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { AddMaterialDialog } from "./AddMaterialDialog";
import { useIsMobile } from "@/hooks/use-mobile";

export const AddMaterialButton: React.FC = () => {
  const [open, setOpen] = React.useState(false);
  const isMobile = useIsMobile();

  return (
    <>
      <Button 
        className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2"
        onClick={() => setOpen(true)}
      >
        <Plus className="h-4 w-4" />
        {!isMobile && "Add Material"}
        {isMobile && "Add"}
      </Button>
      <AddMaterialDialog open={open} onOpenChange={setOpen} />
    </>
  );
};

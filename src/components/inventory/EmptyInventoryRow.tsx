
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Package2 } from "lucide-react";

interface EmptyInventoryRowProps {
  colSpan: number;
}

export const EmptyInventoryRow: React.FC<EmptyInventoryRowProps> = ({ colSpan }) => {
  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="h-60 text-center">
        <div className="flex flex-col items-center justify-center p-6 text-gray-500">
          <Package2 className="h-12 w-12 mb-2 text-gray-400" />
          <p className="text-base">No materials found</p>
          <p className="text-sm mt-1">Add your first material to get started</p>
        </div>
      </TableCell>
    </TableRow>
  );
};

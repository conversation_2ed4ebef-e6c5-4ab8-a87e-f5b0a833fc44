
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>Down, ArrowUp, ArrowUpDown } from "lucide-react";
import { MobileInventoryTableHeader } from "./MobileInventoryTableHeader";
import { Material } from "@/types/inventory";

type SortField = keyof Material | null;
type SortDirection = 'asc' | 'desc' | null;

interface SortableHeaderProps {
  field: SortField;
  children: React.ReactNode;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  className?: string;
}

const SortableHeader: React.FC<SortableHeaderProps> = ({ 
  field, 
  children, 
  sortField, 
  sortDirection, 
  onSort,
  className = ""
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHead 
      onClick={() => onSort(field)}
      className={`py-3 px-4 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 group cursor-pointer ${className}`}
    >
      <div className="flex items-center">
        {children}
        {renderSortIcon(field)}
      </div>
    </TableHead>
  );
};

interface InventoryTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  isMobile: boolean;
}

export const InventoryTableHeader: React.FC<InventoryTableHeaderProps> = ({ 
  sortField, 
  sortDirection, 
  onSort,
  isMobile
}) => {
  if (isMobile) {
    return <MobileInventoryTableHeader sortField={sortField} sortDirection={sortDirection} onSort={onSort} />;
  }

  return (
    <TableHeader>
      <TableRow>
        <SortableHeader field="name" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>Material</SortableHeader>
        <SortableHeader field="type" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>Type</SortableHeader>
        <SortableHeader field="category" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>Category</SortableHeader>
        <SortableHeader field="uom" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>UOM</SortableHeader>
        <TableHead className="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wide">
          Manage
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

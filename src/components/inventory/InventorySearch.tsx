
import React from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface InventorySearchProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export const InventorySearch: React.FC<InventorySearchProps> = ({ value, onChange, className }) => {
  const isMobile = useIsMobile();
  
  const handleClear = () => {
    onChange("");
  };
  
  return (
    <div className={cn("relative w-full md:max-w-md", className)}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>
      <Input
        type="text"
        placeholder="Search by ID, Material name, Type, Category, UOM, Manage..."
        className={cn(
          "pl-10 pr-8 py-2 w-full",
          isMobile ? "text-base h-11" : "text-sm"
        )}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      {value.length > 0 && (
        <button 
          onClick={handleClear}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

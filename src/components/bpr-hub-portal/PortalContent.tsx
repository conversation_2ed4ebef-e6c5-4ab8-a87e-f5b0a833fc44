
import React from "react";
import { OverviewTab } from "./tabs/OverviewTab";
import { DocumentsTab } from "./tabs/DocumentsTab";
import { AuditsTab } from "./tabs/AuditsTab";
import { CapasTab } from "./tabs/CapasTab";
import { PerformanceTab } from "./tabs/PerformanceTab";
import { MessagesTab } from "./tabs/MessagesTab";

interface PortalContentProps {
  activeTab: string;
}

export const PortalContent: React.FC<PortalContentProps> = ({ activeTab }) => {
  switch (activeTab) {
    case "overview":
      return <OverviewTab />;
    case "documents":
      return <DocumentsTab />;
    case "audits":
      return <AuditsTab />;
    case "capas":
      return <CapasTab />;
    case "performance":
      return <PerformanceTab />;
    case "messages":
      return <MessagesTab />;
    default:
      return <OverviewTab />;
  }
};

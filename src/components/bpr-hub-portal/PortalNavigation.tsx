
import React from "react";
import { Tabs, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BarChart4, FileText, Calendar, MessageSquare, CheckCircle2 } from "lucide-react";

interface PortalNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export const PortalNavigation: React.FC<PortalNavigationProps> = ({ activeTab, setActiveTab }) => {
  return (
    <Tabs 
      orientation="vertical" 
      value={activeTab} 
      onValueChange={setActiveTab} 
      className="w-full"
    >
      <TabsList className="flex flex-col items-stretch h-auto bg-transparent space-y-1 p-2">
        <TabsTrigger 
          value="overview" 
          className="justify-start text-left px-4 py-3"
        >
          <BarChart4 className="mr-2 h-4 w-4" />
          Overview
        </TabsTrigger>
        <TabsTrigger 
          value="documents" 
          className="justify-start text-left px-4 py-3"
        >
          <FileText className="mr-2 h-4 w-4" />
          Shared Documents
        </TabsTrigger>
        <TabsTrigger 
          value="audits" 
          className="justify-start text-left px-4 py-3"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Audit History
        </TabsTrigger>
        <TabsTrigger 
          value="capas" 
          className="justify-start text-left px-4 py-3"
        >
          <CheckCircle2 className="mr-2 h-4 w-4" />
          CAPA Status
        </TabsTrigger>
        <TabsTrigger 
          value="performance" 
          className="justify-start text-left px-4 py-3"
        >
          <BarChart4 className="mr-2 h-4 w-4" />
          Performance Metrics
        </TabsTrigger>
        <TabsTrigger 
          value="messages" 
          className="justify-start text-left px-4 py-3"
        >
          <MessageSquare className="mr-2 h-4 w-4" />
          Messages
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
};

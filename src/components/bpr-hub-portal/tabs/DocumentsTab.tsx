
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export const DocumentsTab = () => {
  // Mock document data
  const documents = [
    {
      id: 1,
      name: "Internal Audit Report Q1 2025",
      type: "PDF",
      uploadDate: "Mar 10, 2025"
    },
    {
      id: 2,
      name: "Supplier Quality Manual v2",
      type: "PDF",
      uploadDate: "Jan 5, 2025"
    },
    {
      id: 3,
      name: "Process Control Plan",
      type: "Excel",
      uploadDate: "Feb 15, 2025"
    },
    {
      id: 4,
      name: "Root Cause Analysis - NC-2024-56",
      type: "PDF",
      uploadDate: "Dec 22, 2024"
    },
    {
      id: 5,
      name: "Training Records - Quality Team",
      type: "PDF",
      uploadDate: "Feb 28, 2025"
    }
  ];

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">Shared Documents</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-medium text-xs uppercase tracking-wider">Document Name</TableHead>
              <TableHead className="font-medium text-xs uppercase tracking-wider">Type</TableHead>
              <TableHead className="font-medium text-xs uppercase tracking-wider">Upload Date</TableHead>
              <TableHead className="w-24"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.map((doc) => (
              <TableRow key={doc.id}>
                <TableCell className="text-sm">{doc.name}</TableCell>
                <TableCell className="text-sm">{doc.type}</TableCell>
                <TableCell className="text-sm">{doc.uploadDate}</TableCell>
                <TableCell>
                  <Button variant="ghost" size="icon">
                    <Download className="h-4 w-4 text-teal-600" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

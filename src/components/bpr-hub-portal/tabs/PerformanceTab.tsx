
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON>ontent, CardTitle } from "@/components/ui/card";

export const PerformanceTab = () => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">Performance Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">On-Time Delivery</h3>
            <div className="h-4 w-full bg-gray-200 rounded-full mt-1">
              <div className="h-4 bg-blue-500 rounded-full" style={{ width: '94%' }}></div>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-sm text-gray-500">Target: 95%</span>
              <span className="text-sm font-medium">Actual: 94%</span>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Quality - First Pass Yield</h3>
            <div className="h-4 w-full bg-gray-200 rounded-full mt-1">
              <div className="h-4 bg-green-500 rounded-full" style={{ width: '98%' }}></div>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-sm text-gray-500">Target: 98%</span>
              <span className="text-sm font-medium">Actual: 98%</span>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Response Time</h3>
            <div className="h-4 w-full bg-gray-200 rounded-full mt-1">
              <div className="h-4 bg-amber-500 rounded-full" style={{ width: '85%' }}></div>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-sm text-gray-500">Target: 100%</span>
              <span className="text-sm font-medium">Actual: 85%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

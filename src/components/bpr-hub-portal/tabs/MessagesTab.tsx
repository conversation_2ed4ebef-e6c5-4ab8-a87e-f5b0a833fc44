
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export const MessagesTab = () => {
  return (
    <Card>
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-xl">Messages</CardTitle>
        <Button size="sm">New Message</Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-4 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">AS9100 Certificate Expiry</h4>
              <Badge variant="outline">New</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-2">Please provide an updated AS9100 certificate as the current one has expired.</p>
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>From: <PERSON> (Boeing QA)</span>
              <span>Mar 15, 2025</span>
            </div>
          </div>
          
          <div className="p-4 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">CAPA-2024-032 Update</h4>
              <Badge variant="outline" className="bg-gray-100">Read</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-2">Requesting status update on corrective actions for calibration records issue.</p>
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>From: Jane Doe (Boeing QA)</span>
              <span>Mar 10, 2025</span>
            </div>
          </div>
          
          <div className="p-4 border rounded-md">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">Upcoming Supplier Review</h4>
              <Badge variant="outline" className="bg-gray-100">Read</Badge>
            </div>
            <p className="text-sm text-gray-600 mb-2">Scheduling quarterly supplier review meeting for April 20th.</p>
            <div className="flex justify-between items-center text-xs text-gray-500">
              <span>From: Michael Chen (Boeing Sourcing)</span>
              <span>Mar 5, 2025</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

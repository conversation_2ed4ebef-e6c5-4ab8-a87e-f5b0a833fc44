
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON>ontent, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export const OverviewTab = () => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">Supplier Compliance Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Compliance Score</h3>
            <div className="text-2xl font-bold text-green-600">92%</div>
          </div>
          <div className="h-2 w-full bg-gray-200 rounded-full mt-1">
            <div className="h-2 bg-green-500 rounded-full" style={{ width: '92%' }}></div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-md">
              <h4 className="font-medium mb-2">Certifications</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>ISO 9001</span>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Valid</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>AS9100</span>
                  <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Expired</Badge>
                </div>
              </div>
            </div>
            
            <div className="p-4 border rounded-md">
              <h4 className="font-medium mb-2">Open CAPAs</h4>
              <div className="text-xl font-bold text-amber-500">2</div>
              <p className="text-sm text-gray-500 mt-1">Both require action by supplier</p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-md">
              <h4 className="font-medium mb-2">Last Audit</h4>
              <div className="text-md font-medium">July 12, 2024</div>
              <p className="text-sm text-gray-500 mt-1">Minor Finding</p>
            </div>
            
            <div className="p-4 border rounded-md">
              <h4 className="font-medium mb-2">Supplier Contact</h4>
              <div className="text-md font-medium">Rahul QA Manager</div>
              <p className="text-sm text-gray-500 mt-1"><EMAIL></p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardT<PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export const AuditsTab = () => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">Audit History</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Audit Type</TableHead>
              <TableHead>Findings</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>Jul 12, 2024</TableCell>
              <TableCell>Process Audit</TableCell>
              <TableCell>Minor Finding (1)</TableCell>
              <TableCell><Badge variant="outline">Closed</Badge></TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Jan 20, 2024</TableCell>
              <TableCell>Annual Audit</TableCell>
              <TableCell>No Findings</TableCell>
              <TableCell><Badge variant="outline">Completed</Badge></TableCell>
            </TableRow>
            <TableRow>
              <TableCell>Sep 5, 2023</TableCell>
              <TableCell>Quality System Audit</TableCell>
              <TableCell>Major Finding (1), Minor (2)</TableCell>
              <TableCell><Badge variant="outline">Closed</Badge></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

export const CapasTab = () => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-xl">CAPA Status</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>CAPA ID</TableHead>
              <TableHead>Issue</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">CAPA-2024-032</TableCell>
              <TableCell>Calibration records not updated for CMM machine</TableCell>
              <TableCell>Apr 15, 2025</TableCell>
              <TableCell><Badge className="bg-amber-100 text-amber-800">In Progress</Badge></TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">CAPA-2024-021</TableCell>
              <TableCell>Material traceability documentation incomplete</TableCell>
              <TableCell>Mar 30, 2025</TableCell>
              <TableCell><Badge className="bg-amber-100 text-amber-800">In Progress</Badge></TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">CAPA-2023-097</TableCell>
              <TableCell>Training records not updated for new process</TableCell>
              <TableCell>Dec 10, 2024</TableCell>
              <TableCell><Badge className="bg-green-100 text-green-800">Closed</Badge></TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

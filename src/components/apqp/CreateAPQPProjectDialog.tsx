
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { APQPProjectWizard } from "./wizard/APQPProjectWizard";

interface CreateAPQPProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProjectCreated?: (project: any) => void;
}

export const CreateAPQPProjectDialog: React.FC<CreateAPQPProjectDialogProps> = ({
  open,
  onOpenChange,
  onProjectCreated
}) => {
  const handleProjectComplete = (projectData: any) => {
    if (onProjectCreated) {
      onProjectCreated(projectData);
    }
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New APQP Project</DialogTitle>
        </DialogHeader>
        <APQPProjectWizard onComplete={handleProjectComplete} />
      </DialogContent>
    </Dialog>
  );
};

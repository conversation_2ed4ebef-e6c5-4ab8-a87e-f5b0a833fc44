
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, User, Target, AlertCircle } from "lucide-react";
import { APQPProjectDetails } from "./APQPProjectDetails";
import { formatDate } from "@/utils/dateUtils";

interface APQPProject {
  id: string;
  name: string;
  customer: string;
  startDate: string;
  endDate: string;
  phase: string;
  status: string;
  progress: number;
  productType: string;
  teamLead: string;
  priority: string;
}

interface APQPProjectCardProps {
  project: APQPProject;
}

export const APQPProjectCard: React.FC<APQPProjectCardProps> = ({ project }) => {
  const [showDetails, setShowDetails] = useState(false);

  // Transform the project data to match the expected interface
  const transformedProject = {
    id: project.id,
    projectName: project.name,
    partNumber: `PN-${project.id}`,
    supplier: project.customer,
    status: project.status,
    startDate: project.startDate,
    targetDate: project.endDate,
    currentPhase: project.phase,
    completionPercentage: project.progress,
    riskLevel: project.priority === "High" ? "High" as const : 
              project.priority === "Medium" ? "Medium" as const : "Low" as const,
    teamLead: project.teamLead,
    description: `${project.productType} project for ${project.customer}`
  };

  const getStatusColor = (status: string) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    
    switch (status.toLowerCase()) {
      case 'on track': 
      case 'active': return 'bg-green-100 text-green-800';
      case 'at risk': return 'bg-yellow-100 text-yellow-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (risk: string) => {
    if (!risk) return 'text-gray-600';
    
    switch (risk.toLowerCase()) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <>
      <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => setShowDetails(true)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{project.name || 'Untitled Project'}</CardTitle>
            <Badge className={getStatusColor(project.status)}>
              {project.status || 'Unknown'}
            </Badge>
          </div>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>{transformedProject.partNumber}</span>
            <span>•</span>
            <span>{project.customer || 'Unknown Customer'}</span>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="font-medium">{project.progress || 0}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${project.progress || 0}%` }}
            ></div>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span className="text-gray-600">Target: {formatDate(project.endDate)}</span>
            </div>
            <div className="flex items-center gap-1">
              <AlertCircle className={`h-3 w-3 ${getRiskColor(transformedProject.riskLevel)}`} />
              <span className={getRiskColor(transformedProject.riskLevel)}>{transformedProject.riskLevel}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-1 text-sm">
            <User className="h-3 w-3" />
            <span className="text-gray-600">Lead: {project.teamLead || 'TBD'}</span>
          </div>
          
          <div className="pt-2">
            <Button 
              size="sm" 
              className="w-full"
              onClick={(e) => {
                e.stopPropagation();
                setShowDetails(true);
              }}
            >
              View Project Details
            </Button>
          </div>
        </CardContent>
      </Card>

      <APQPProjectDetails
        open={showDetails}
        onOpenChange={setShowDetails}
        project={transformedProject}
      />
    </>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { APQPGateReview } from "./APQPGateReview";
import { APQPProjectsList } from "./APQPProjectsList";
import { APQPDeliverables } from "./APQPDeliverables";
import { CreateAPQPProjectDialog } from "./CreateAPQPProjectDialog";
import { Button } from "@/components/ui/button";
import { Plus, FileText, CheckCircle, Package, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export const APQPTabs: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("projects");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const handleExportProjectData = () => {
    toast({
      title: "Exporting Project Data",
      description: "APQP project data is being exported...",
    });
    
    // Simulate export
    setTimeout(() => {
      const exportData = {
        exportDate: new Date().toISOString(),
        totalProjects: 23,
        activeProjects: 18,
        completedProjects: 67,
        projects: [
          {
            id: "APQP-001",
            name: "Engine Component Development",
            phase: "Phase 3 - Product Design & Development",
            status: "On Track",
            progress: 65
          },
          {
            id: "APQP-002", 
            name: "Brake System Validation",
            phase: "Phase 4 - Process Design & Development",
            status: "At Risk",
            progress: 45
          }
        ]
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `APQP_Export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export Complete",
        description: "APQP project data has been exported successfully",
      });
    }, 2000);
  };

  const handleProjectCreated = (project: any) => {
    toast({
      title: "Project Created",
      description: `APQP project "${project.name}" has been created successfully`,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">APQP Management</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportProjectData} className="gap-2">
            <Download className="h-4 w-4" />
            Export Data
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Project
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="projects" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Projects
          </TabsTrigger>
          <TabsTrigger value="gate-review" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Gate Review
          </TabsTrigger>
          <TabsTrigger value="deliverables" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Deliverables
          </TabsTrigger>
        </TabsList>

        <TabsContent value="projects" className="mt-6">
          <APQPProjectsList />
        </TabsContent>

        <TabsContent value="gate-review" className="mt-6">
          <APQPGateReview />
        </TabsContent>

        <TabsContent value="deliverables" className="mt-6">
          <APQPDeliverables />
        </TabsContent>
      </Tabs>

      <CreateAPQPProjectDialog 
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  );
};

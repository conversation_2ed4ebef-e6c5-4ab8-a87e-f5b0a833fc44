
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { CheckCircle, Clock, AlertTriangle, User, Calendar, Eye, FileText, Download } from "lucide-react";
import { toast } from "sonner";

export const APQPGateReview: React.FC = () => {
  const [selectedPhase, setSelectedPhase] = useState("all");
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedReview, setSelectedReview] = useState<any>(null);

  const gateReviews = [
    {
      id: "GR-001",
      projectId: "APQP-Q5-2025",
      projectName: "IoT Sensor Hub – Project Q5",
      phase: "Phase 1: Planning",
      gateName: "Gate 1 - Project Approval",
      status: "approved",
      reviewDate: "2025-05-15",
      reviewer: "<PERSON>",
      score: 85,
      criteria: [
        { name: "Project Charter", status: "approved", score: 90 },
        { name: "Risk Assessment", status: "approved", score: 80 },
        { name: "Resource Plan", status: "approved", score: 85 }
      ]
    },
    {
      id: "GR-002",
      projectId: "APQP-Q6-2025",
      projectName: "Automotive ECU Module",
      phase: "Phase 2: Product Design",
      gateName: "Gate 2 - Design Approval",
      status: "pending",
      reviewDate: "2025-06-01",
      reviewer: "Michael Chen",
      score: 0,
      criteria: [
        { name: "Design FMEA", status: "pending", score: 0 },
        { name: "Product Specifications", status: "in-progress", score: 75 },
        { name: "Design Verification", status: "pending", score: 0 }
      ]
    },
    {
      id: "GR-003",
      projectId: "APQP-Q7-2025",
      projectName: "Medical Device Controller",
      phase: "Phase 4: Validation",
      gateName: "Gate 4 - Production Approval",
      status: "conditional",
      reviewDate: "2025-05-25",
      reviewer: "Emily Rodriguez",
      score: 72,
      criteria: [
        { name: "Production Trial", status: "approved", score: 85 },
        { name: "Capability Study", status: "conditional", score: 65 },
        { name: "Control Plan", status: "approved", score: 80 }
      ]
    }
  ];

  const phases = [
    "all",
    "Phase 1: Planning",
    "Phase 2: Product Design",
    "Phase 3: Process Design",
    "Phase 4: Validation",
    "Phase 5: Launch"
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "conditional":
        return <AlertTriangle className="h-4 w-4 text-amber-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-slate-400" />;
      default:
        return <Clock className="h-4 w-4 text-slate-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "approved": "bg-green-50 text-green-800 border-green-200",
      "conditional": "bg-amber-50 text-amber-800 border-amber-200",
      "pending": "bg-slate-50 text-slate-600 border-slate-200",
      "in-progress": "bg-blue-50 text-blue-800 border-blue-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["pending"];
  };

  const filteredReviews = selectedPhase === "all" 
    ? gateReviews 
    : gateReviews.filter(review => review.phase === selectedPhase);

  const handleStartReview = (review: any) => {
    toast.success(`Started review for ${review.gateName}`);
    // Simulate review process
    setTimeout(() => {
      toast.success("Review checklist loaded. Beginning systematic review process.");
    }, 1500);
  };

  const handleViewDetails = (review: any) => {
    setSelectedReview(review);
    setShowDetailModal(true);
    toast.success(`Loading detailed information for ${review.gateName}`);
  };

  const handleScheduleReview = () => {
    toast.success("Review scheduling dialog opened");
    setTimeout(() => {
      toast.success("New gate review scheduled for June 10, 2025 at 2:00 PM");
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-slate-900">Gate Reviews</h2>
          <p className="text-sm text-slate-600">Manage APQP gate review process and approvals</p>
        </div>
        <Button 
          className="bg-teal-600 hover:bg-teal-700 text-white"
          onClick={handleScheduleReview}
        >
          Schedule Review
        </Button>
      </div>

      {/* Phase Filter */}
      <div className="flex flex-wrap gap-2">
        {phases.map((phase) => (
          <Button
            key={phase}
            variant={selectedPhase === phase ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPhase(phase)}
            className={selectedPhase === phase ? "bg-teal-600 hover:bg-teal-700" : ""}
          >
            {phase === "all" ? "All Phases" : phase}
          </Button>
        ))}
      </div>

      {/* Gate Reviews */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <Card key={review.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{review.gateName}</CardTitle>
                  <p className="text-sm text-slate-600">{review.projectName}</p>
                  <p className="text-xs text-slate-500">{review.phase}</p>
                </div>
                <Badge variant="outline" className={getStatusBadge(review.status)}>
                  {review.status.replace('-', ' ')}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <Calendar className="h-4 w-4" />
                  <span>Review: {new Date(review.reviewDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <User className="h-4 w-4" />
                  <span>Reviewer: {review.reviewer}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <span className="font-medium">Score: {review.score}%</span>
                </div>
              </div>

              {review.score > 0 && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-slate-700">Overall Progress</span>
                    <span className="text-sm text-slate-600">{review.score}%</span>
                  </div>
                  <Progress value={review.score} className="h-2" />
                </div>
              )}

              <div className="space-y-3">
                <h4 className="font-medium text-slate-900">Review Criteria</h4>
                {review.criteria.map((criterion, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-md">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(criterion.status)}
                      <span className="text-sm font-medium">{criterion.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getStatusBadge(criterion.status)}>
                        {criterion.status.replace('-', ' ')}
                      </Badge>
                      {criterion.score > 0 && (
                        <span className="text-sm text-slate-600">{criterion.score}%</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex gap-2 pt-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleViewDetails(review)}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  View Details
                </Button>
                {review.status === "pending" && (
                  <Button 
                    size="sm" 
                    className="bg-teal-600 hover:bg-teal-700 text-white"
                    onClick={() => handleStartReview(review)}
                  >
                    Start Review
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReviews.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-slate-400 mb-2">No gate reviews found</div>
            <p className="text-sm text-slate-600">No reviews match the selected phase</p>
          </CardContent>
        </Card>
      )}

      {/* Detail Modal */}
      <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {selectedReview?.gateName} - Detailed Review
            </DialogTitle>
          </DialogHeader>
          
          {selectedReview && (
            <div className="space-y-6 p-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-3">Review Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Project:</span>
                      <span>{selectedReview.projectName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phase:</span>
                      <span>{selectedReview.phase}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Reviewer:</span>
                      <span>{selectedReview.reviewer}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Review Date:</span>
                      <span>{selectedReview.reviewDate}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-semibold mb-3">Review Status</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Overall Score:</span>
                      <span className="font-medium">{selectedReview.score}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <Badge className={getStatusBadge(selectedReview.status)}>
                        {selectedReview.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Criteria Count:</span>
                      <span>{selectedReview.criteria.length}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-3">Detailed Criteria Assessment</h3>
                <div className="space-y-3">
                  {selectedReview.criteria.map((criterion: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">{criterion.name}</h4>
                        <Badge className={getStatusBadge(criterion.status)}>
                          {criterion.status}
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Assessment Score</span>
                        <span className="font-medium">{criterion.score}%</span>
                      </div>
                      {criterion.score > 0 && (
                        <Progress value={criterion.score} className="mt-2 h-2" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                  Close
                </Button>
                <Button 
                  onClick={() => {
                    toast.success(`Downloading review report for ${selectedReview.gateName}`);
                    setShowDetailModal(false);
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Report
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

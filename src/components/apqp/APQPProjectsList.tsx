
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { APQPProjectCard } from "./APQPProjectCard";
import { CreateAPQPProjectDialog } from "./CreateAPQPProjectDialog";
import { Search, Plus, Filter } from "lucide-react";

export const APQPProjectsList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [projects, setProjects] = useState([
    {
      id: "APQP-Q5-2025",
      name: "IoT Sensor Hub – Project Q5",
      customer: "Infineon",
      startDate: "2025-05-01",
      endDate: "2025-09-30",
      phase: "Planning",
      status: "Active",
      progress: 15,
      productType: "RF-enabled PCB Assembly",
      teamLead: "<PERSON><PERSON> Nair",
      priority: "High"
    },
    {
      id: "APQP-Q6-2025",
      name: "Automotive ECU Module",
      customer: "<PERSON><PERSON>",
      startDate: "2025-04-15",
      endDate: "2025-10-15",
      phase: "Design",
      status: "Active",
      progress: 45,
      productType: "Electronic Control Unit",
      teamLead: "Marcus Chen",
      priority: "Medium"
    },
    {
      id: "APQP-Q7-2025",
      name: "Medical Device Controller",
      customer: "Medtronic",
      startDate: "2025-03-01",
      endDate: "2025-08-30",
      phase: "Validation",
      status: "Active",
      progress: 75,
      productType: "Medical Grade PCB",
      teamLead: "Sarah Johnson",
      priority: "High"
    }
  ]);

  const filteredProjects = projects.filter(project =>
    (project.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (project.customer || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (project.id || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProjectCreated = (newProject: any) => {
    const projectId = `APQP-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    const project = {
      id: projectId,
      name: newProject.projectName || "New APQP Project",
      customer: newProject.customer || "TBD",
      startDate: newProject.startDate || new Date().toISOString().split('T')[0],
      endDate: newProject.endDate || new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      phase: "Planning",
      status: "Active",
      progress: 0,
      productType: newProject.productType || "TBD",
      teamLead: newProject.teamLead || "TBD",
      priority: newProject.priority || "Medium"
    };
    setProjects(prev => [project, ...prev]);
    setShowCreateDialog(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-slate-900">APQP Projects</h2>
          <p className="text-sm text-slate-600">Manage and track your APQP projects</p>
        </div>
        <Button 
          onClick={() => setShowCreateDialog(true)}
          className="bg-teal-600 hover:bg-teal-700 text-white"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Project
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search projects by name, customer, or ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button variant="outline">
          <Filter className="h-4 w-4 mr-2" />
          Filter
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProjects.map((project) => (
          <APQPProjectCard key={project.id} project={project} />
        ))}
      </div>

      {filteredProjects.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-slate-400 mb-2">No projects found</div>
            <p className="text-sm text-slate-600">Try adjusting your search criteria</p>
          </CardContent>
        </Card>
      )}

      <CreateAPQPProjectDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  );
};

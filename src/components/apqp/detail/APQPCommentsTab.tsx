
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Send, Reply } from "lucide-react";

interface APQPCommentsTabProps {
  project: any;
}

export const APQPCommentsTab: React.FC<APQPCommentsTabProps> = ({ project }) => {
  const [newComment, setNewComment] = useState("");

  const comments = [
    {
      id: 1,
      author: "<PERSON><PERSON>",
      role: "QA Lead",
      timestamp: "2025-05-20 14:30",
      content: "Risk assessment has been completed. Identified 3 high-priority risks that need immediate attention before moving to Phase 2.",
      type: "update"
    },
    {
      id: 2,
      author: "<PERSON>", 
      role: "Design Engineer",
      timestamp: "2025-05-19 16:45",
      content: "Design specifications are ready for review. Please find the latest drawings in the shared folder.",
      type: "info"
    },
    {
      id: 3,
      author: "<PERSON><PERSON>",
      role: "Manufacturing Lead",
      timestamp: "2025-05-18 11:20",
      content: "Manufacturing capability assessment shows we need additional equipment for RF testing. Budget approval required.",
      type: "action-required"
    },
    {
      id: 4,
      author: "Maria Rodriguez",
      role: "Project Manager", 
      timestamp: "2025-05-17 09:15",
      content: "Project kickoff meeting scheduled for May 22nd. All team members please confirm attendance.",
      type: "meeting"
    }
  ];

  const activityLog = [
    {
      id: 1,
      action: "Project Created",
      user: "Maria Rodriguez",
      timestamp: "2025-05-15 10:00",
      details: "APQP project created for IoT Sensor Hub"
    },
    {
      id: 2,
      action: "Team Assigned",
      user: "Maria Rodriguez", 
      timestamp: "2025-05-15 14:30",
      details: "Core team members assigned to project"
    },
    {
      id: 3,
      action: "Phase 1 Started",
      user: "System",
      timestamp: "2025-05-16 08:00",
      details: "Planning phase initiated"
    },
    {
      id: 4,
      action: "Document Uploaded",
      user: "Priya Nair",
      timestamp: "2025-05-18 15:45",
      details: "Risk Assessment document uploaded"
    }
  ];

  const getTypeColor = (type: string) => {
    const typeMap = {
      "update": "bg-blue-50 text-blue-800 border-blue-200",
      "info": "bg-slate-50 text-slate-600 border-slate-200", 
      "action-required": "bg-red-50 text-red-800 border-red-200",
      "meeting": "bg-purple-50 text-purple-800 border-purple-200"
    };
    return typeMap[type as keyof typeof typeMap] || typeMap["info"];
  };

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      // Handle comment submission
      console.log("New comment:", newComment);
      setNewComment("");
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Comments Section */}
      <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Team Comments & Discussions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Add new comment */}
            <div className="border border-slate-200 rounded-md p-4">
              <Textarea
                placeholder="Add a comment or update..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                className="min-h-[100px] resize-none"
              />
              <div className="flex justify-end mt-3">
                <Button 
                  onClick={handleSubmitComment}
                  className="bg-teal-600 hover:bg-teal-700 text-white"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Post Comment
                </Button>
              </div>
            </div>

            {/* Comments list */}
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.id} className="border border-slate-200 rounded-md p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8 bg-teal-100 text-teal-800">
                        {comment.author.split(' ').map(n => n[0]).join('')}
                      </Avatar>
                      <div>
                        <div className="font-medium text-slate-900">{comment.author}</div>
                        <div className="text-sm text-slate-500">{comment.role}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getTypeColor(comment.type)}>
                        {comment.type.replace('-', ' ')}
                      </Badge>
                      <span className="text-sm text-slate-500">{comment.timestamp}</span>
                    </div>
                  </div>
                  <p className="text-slate-700 mb-3">{comment.content}</p>
                  <Button variant="ghost" size="sm">
                    <Reply className="h-4 w-4 mr-2" />
                    Reply
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Log */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Activity Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activityLog.map((activity) => (
                <div key={activity.id} className="border-l-2 border-teal-200 pl-4 pb-4">
                  <div className="font-medium text-slate-900">{activity.action}</div>
                  <div className="text-sm text-slate-600">{activity.details}</div>
                  <div className="text-xs text-slate-500 mt-1">
                    {activity.user} • {activity.timestamp}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

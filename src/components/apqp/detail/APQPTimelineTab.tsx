
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, CheckCircle2 } from "lucide-react";

interface APQPTimelineTabProps {
  project: any;
}

export const APQPTimelineTab: React.FC<APQPTimelineTabProps> = ({ project }) => {
  const timelineItems = [
    {
      id: 1,
      phase: "Phase 1: Planning",
      milestone: "Project Charter",
      dueDate: "2025-05-15",
      status: "completed",
      description: "Project scope and objectives defined"
    },
    {
      id: 2,
      phase: "Phase 1: Planning", 
      milestone: "Risk Assessment",
      dueDate: "2025-05-20",
      status: "completed",
      description: "Initial risk analysis completed"
    },
    {
      id: 3,
      phase: "Phase 1: Planning",
      milestone: "Resource Planning",
      dueDate: "2025-05-25",
      status: "in-progress",
      description: "Team assignment and resource allocation"
    },
    {
      id: 4,
      phase: "Phase 2: Product Design",
      milestone: "Design Freeze",
      dueDate: "2025-06-15",
      status: "upcoming",
      description: "Final product design approval"
    },
    {
      id: 5,
      phase: "Phase 2: Product Design",
      milestone: "DFMEA Completion",
      dueDate: "2025-06-20",
      status: "upcoming",
      description: "Design FMEA analysis and documentation"
    },
    {
      id: 6,
      phase: "Phase 3: Process Design",
      milestone: "Process Flow",
      dueDate: "2025-07-25",
      status: "upcoming",
      description: "Manufacturing process flow definition"
    },
    {
      id: 7,
      phase: "Phase 4: Validation",
      milestone: "Production Trial",
      dueDate: "2025-08-20",
      status: "upcoming",
      description: "First production run validation"
    },
    {
      id: 8,
      phase: "Phase 5: Launch",
      milestone: "Production Launch",
      dueDate: "2025-09-20",
      status: "upcoming",
      description: "Full production launch"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case "in-progress":
        return <Clock className="h-5 w-5 text-blue-600" />;
      default:
        return <Calendar className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "completed": "bg-green-50 text-green-800 border-green-200",
      "in-progress": "bg-blue-50 text-blue-800 border-blue-200",
      "upcoming": "bg-slate-50 text-slate-600 border-slate-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["upcoming"];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-slate-200"></div>
            
            <div className="space-y-6">
              {timelineItems.map((item, index) => (
                <div key={item.id} className="relative flex items-start gap-4">
                  {/* Timeline dot */}
                  <div className="relative z-10 flex-shrink-0">
                    {getStatusIcon(item.status)}
                  </div>
                  
                  {/* Timeline content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <h4 className="font-medium text-slate-900">{item.milestone}</h4>
                        <p className="text-sm text-slate-600 mt-1">{item.description}</p>
                        <p className="text-xs text-slate-500 mt-1">{item.phase}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getStatusBadge(item.status)}>
                          {item.status.replace('-', ' ')}
                        </Badge>
                        <span className="text-sm text-slate-600">{formatDate(item.dueDate)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">2</div>
            <div className="text-sm text-slate-600">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">1</div>
            <div className="text-sm text-slate-600">In Progress</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-slate-600">5</div>
            <div className="text-sm text-slate-600">Upcoming</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

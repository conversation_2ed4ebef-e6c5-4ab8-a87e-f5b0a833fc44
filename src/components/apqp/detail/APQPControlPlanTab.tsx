
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Edit, Upload } from "lucide-react";

interface APQPControlPlanTabProps {
  project: any;
}

export const APQPControlPlanTab: React.FC<APQPControlPlanTabProps> = ({ project }) => {
  const controlPlan = {
    id: "CP-Q5-001",
    name: "Control Plan - IoT Sensor Hub",
    version: "v2.1",
    status: "Draft",
    lastModified: "2025-05-18",
    owner: "<PERSON><PERSON>",
    approver: "<PERSON><PERSON>"
  };

  const controlPoints = [
    {
      id: "CP001",
      operation: "Component Incoming",
      characteristic: "Electrical Test",
      specification: "±5% tolerance",
      method: "ATE Testing",
      frequency: "100%",
      responsible: "QA Team"
    },
    {
      id: "CP002", 
      operation: "PCB Assembly",
      characteristic: "Solder Joint Quality",
      specification: "IPC-A-610 Class 2",
      method: "Visual Inspection",
      frequency: "Every 10 units",
      responsible: "Production"
    },
    {
      id: "CP003",
      operation: "Final Test",
      characteristic: "RF Performance",
      specification: "-85 dBm sensitivity",
      method: "Automated RF Test",
      frequency: "100%",
      responsible: "Test Engineer"
    },
    {
      id: "CP004",
      operation: "Packaging",
      characteristic: "ESD Protection",
      specification: "ESD Safe Packaging",
      method: "Visual Check",
      frequency: "Each lot",
      responsible: "Packaging Team"
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Approved": "bg-green-50 text-green-800 border-green-200",
      "Draft": "bg-amber-50 text-amber-800 border-amber-200",
      "Under Review": "bg-blue-50 text-blue-800 border-blue-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["Draft"];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">Control Plan</h3>
          <p className="text-sm text-slate-600">Quality control plan for manufacturing process</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Upload New Version
          </Button>
          <Button className="bg-teal-600 hover:bg-teal-700 text-white">
            <Edit className="h-4 w-4 mr-2" />
            Edit Control Plan
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-slate-600" />
              <div>
                <CardTitle className="text-base">{controlPlan.name}</CardTitle>
                <p className="text-sm text-slate-600">{controlPlan.id}</p>
              </div>
            </div>
            <Badge variant="outline" className={getStatusBadge(controlPlan.status)}>
              {controlPlan.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-slate-700">Version:</span>
              <p className="text-slate-600">{controlPlan.version}</p>
            </div>
            <div>
              <span className="font-medium text-slate-700">Owner:</span>
              <p className="text-slate-600">{controlPlan.owner}</p>
            </div>
            <div>
              <span className="font-medium text-slate-700">Approver:</span>
              <p className="text-slate-600">{controlPlan.approver}</p>
            </div>
            <div>
              <span className="font-medium text-slate-700">Last Modified:</span>
              <p className="text-slate-600">{new Date(controlPlan.lastModified).toLocaleDateString()}</p>
            </div>
          </div>
          
          <div className="flex gap-2 pt-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download Excel
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Control Points</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-slate-200">
                  <th className="text-left p-3 text-sm font-medium text-slate-700">ID</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Operation</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Characteristic</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Specification</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Method</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Frequency</th>
                  <th className="text-left p-3 text-sm font-medium text-slate-700">Responsible</th>
                </tr>
              </thead>
              <tbody>
                {controlPoints.map((point, index) => (
                  <tr key={point.id} className={`border-b border-slate-100 ${index % 2 === 0 ? 'bg-slate-50' : 'bg-white'}`}>
                    <td className="p-3 text-sm text-slate-900">{point.id}</td>
                    <td className="p-3 text-sm text-slate-900">{point.operation}</td>
                    <td className="p-3 text-sm text-slate-900">{point.characteristic}</td>
                    <td className="p-3 text-sm text-slate-600">{point.specification}</td>
                    <td className="p-3 text-sm text-slate-600">{point.method}</td>
                    <td className="p-3 text-sm text-slate-600">{point.frequency}</td>
                    <td className="p-3 text-sm text-slate-600">{point.responsible}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Edit, Plus } from "lucide-react";

interface APQPFMEATabProps {
  project: any;
}

export const APQPFMEATab: React.FC<APQPFMEATabProps> = ({ project }) => {
  const fmeaDocuments = [
    {
      id: "DFMEA-Q5-001",
      name: "Design FMEA - IoT Sensor Hub",
      type: "DFMEA",
      status: "In Progress",
      lastModified: "2025-05-20",
      version: "v1.2",
      owner: "<PERSON>"
    },
    {
      id: "PFMEA-Q5-001",
      name: "Process FMEA - PCB Assembly",
      type: "PFMEA", 
      status: "Not Started",
      lastModified: "",
      version: "v1.0",
      owner: "<PERSON><PERSON>"
    }
  ];

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "Completed": "bg-green-50 text-green-800 border-green-200",
      "In Progress": "bg-blue-50 text-blue-800 border-blue-200",
      "Not Started": "bg-slate-50 text-slate-600 border-slate-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["Not Started"];
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">FMEA Documents</h3>
          <p className="text-sm text-slate-600">Failure Mode and Effects Analysis documents for this project</p>
        </div>
        <Button className="bg-teal-600 hover:bg-teal-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Create New FMEA
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {fmeaDocuments.map((doc) => (
          <Card key={doc.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-slate-600" />
                  <div>
                    <CardTitle className="text-base">{doc.name}</CardTitle>
                    <p className="text-sm text-slate-600">{doc.id}</p>
                  </div>
                </div>
                <Badge variant="outline" className={getStatusBadge(doc.status)}>
                  {doc.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-slate-700">Type:</span>
                  <p className="text-slate-600">{doc.type}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-700">Version:</span>
                  <p className="text-slate-600">{doc.version}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-700">Owner:</span>
                  <p className="text-slate-600">{doc.owner}</p>
                </div>
                <div>
                  <span className="font-medium text-slate-700">Last Modified:</span>
                  <p className="text-slate-600">
                    {doc.lastModified ? new Date(doc.lastModified).toLocaleDateString() : "N/A"}
                  </p>
                </div>
              </div>
              
              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>FMEA Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border border-slate-200 rounded-md">
              <div className="text-2xl font-bold text-slate-900">12</div>
              <div className="text-sm text-slate-600">Total Failure Modes</div>
            </div>
            <div className="text-center p-4 border border-slate-200 rounded-md">
              <div className="text-2xl font-bold text-red-600">3</div>
              <div className="text-sm text-slate-600">High Risk (RPN &gt; 100)</div>
            </div>
            <div className="text-center p-4 border border-slate-200 rounded-md">
              <div className="text-2xl font-bold text-amber-600">6</div>
              <div className="text-sm text-slate-600">Medium Risk (RPN 50-100)</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle2, Circle, Clock } from "lucide-react";

interface APQPPhasesTabProps {
  project: any;
}

export const APQPPhasesTab: React.FC<APQPPhasesTabProps> = ({ project }) => {
  const phases = [
    {
      id: 1,
      name: "Phase 1: Planning",
      status: "In Progress",
      progress: 75,
      deliverables: [
        { name: "Project Charter", status: "completed", dueDate: "2025-05-15" },
        { name: "Risk Assessment", status: "completed", dueDate: "2025-05-20" },
        { name: "Resource Planning", status: "in-progress", dueDate: "2025-05-25" },
        { name: "Initial Timeline", status: "pending", dueDate: "2025-05-30" }
      ]
    },
    {
      id: 2,
      name: "Phase 2: Product Design",
      status: "Not Started",
      progress: 0,
      deliverables: [
        { name: "Design Freeze", status: "pending", dueDate: "2025-06-15" },
        { name: "DFMEA", status: "pending", dueDate: "2025-06-20" },
        { name: "Design Validation", status: "pending", dueDate: "2025-07-10" },
        { name: "Prototype Build", status: "pending", dueDate: "2025-07-15" }
      ]
    },
    {
      id: 3,
      name: "Phase 3: Process Design",
      status: "Not Started",
      progress: 0,
      deliverables: [
        { name: "Process Flow", status: "pending", dueDate: "2025-07-25" },
        { name: "PFMEA", status: "pending", dueDate: "2025-08-01" },
        { name: "Control Plan", status: "pending", dueDate: "2025-08-10" },
        { name: "Work Instructions", status: "pending", dueDate: "2025-08-15" }
      ]
    },
    {
      id: 4,
      name: "Phase 4: Validation",
      status: "Not Started",
      progress: 0,
      deliverables: [
        { name: "Production Trial", status: "pending", dueDate: "2025-08-20" },
        { name: "MSA", status: "pending", dueDate: "2025-08-25" },
        { name: "Process Capability", status: "pending", dueDate: "2025-09-05" },
        { name: "PPAP Submission", status: "pending", dueDate: "2025-09-10" }
      ]
    },
    {
      id: 5,
      name: "Phase 5: Launch",
      status: "Not Started",
      progress: 0,
      deliverables: [
        { name: "Production Launch", status: "pending", dueDate: "2025-09-20" },
        { name: "Post-Launch Review", status: "pending", dueDate: "2025-09-25" },
        { name: "Lessons Learned", status: "pending", dueDate: "2025-09-30" }
      ]
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-amber-600" />;
      default:
        return <Circle className="h-4 w-4 text-slate-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "completed": "bg-green-50 text-green-800 border-green-200",
      "in-progress": "bg-amber-50 text-amber-800 border-amber-200",
      "pending": "bg-slate-50 text-slate-600 border-slate-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["pending"];
  };

  const getPhaseStatusBadge = (status: string) => {
    const statusMap = {
      "In Progress": "bg-blue-50 text-blue-800 border-blue-200",
      "Completed": "bg-green-50 text-green-800 border-green-200",
      "Not Started": "bg-slate-50 text-slate-600 border-slate-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["Not Started"];
  };

  return (
    <div className="space-y-6">
      {phases.map((phase) => (
        <Card key={phase.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{phase.name}</CardTitle>
              <Badge variant="outline" className={getPhaseStatusBadge(phase.status)}>
                {phase.status}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Progress</span>
                <span className="text-sm text-slate-600">{phase.progress}%</span>
              </div>
              <Progress value={phase.progress} className="h-2" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <h4 className="font-medium text-slate-900">Deliverables</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {phase.deliverables.map((deliverable, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border border-slate-200 rounded-md">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(deliverable.status)}
                      <div>
                        <div className="font-medium text-slate-900">{deliverable.name}</div>
                        <div className="text-sm text-slate-500">Due: {new Date(deliverable.dueDate).toLocaleDateString()}</div>
                      </div>
                    </div>
                    <Badge variant="outline" className={getStatusBadge(deliverable.status)}>
                      {deliverable.status.replace('-', ' ')}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

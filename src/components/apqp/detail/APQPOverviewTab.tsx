
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Mail, Phone } from "lucide-react";

interface APQPOverviewTabProps {
  project: any;
}

export const APQPOverviewTab: React.FC<APQPOverviewTabProps> = ({ project }) => {
  const teamMembers = [
    { name: "<PERSON><PERSON>", role: "QA Lead", email: "<EMAIL>", phone: "******-0101" },
    { name: "<PERSON>", role: "Design Engineer", email: "<EMAIL>", phone: "******-0102" },
    { name: "<PERSON><PERSON>", role: "Manufacturing Lead", email: "<EMAIL>", phone: "******-0103" },
    { name: "<PERSON>", role: "Project Manager", email: "<EMAIL>", phone: "******-0105" }
  ];

  const projectDetails = [
    { label: "Project ID", value: project.id },
    { label: "Product Type", value: project.productType },
    { label: "Customer", value: project.customer },
    { label: "Priority", value: project.priority },
    { label: "Current Phase", value: project.phase },
    { label: "Status", value: project.status },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Project Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {projectDetails.map((detail, index) => (
            <div key={index} className="flex justify-between items-center py-2 border-b border-slate-100 last:border-b-0">
              <span className="text-sm font-medium text-slate-600">{detail.label}</span>
              <span className="text-sm text-slate-900">{detail.value}</span>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {teamMembers.map((member, index) => (
            <div key={index} className="p-3 border border-slate-200 rounded-md">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <div className="font-medium text-slate-900">{member.name}</div>
                  <Badge variant="outline" className="text-xs">{member.role}</Badge>
                </div>
                <User className="h-4 w-4 text-slate-400" />
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <Mail className="h-3 w-3" />
                  <span>{member.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <Phone className="h-3 w-3" />
                  <span>{member.phone}</span>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Project Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-slate-600">
            The IoT Sensor Hub Project Q5 is a comprehensive APQP initiative focused on developing 
            an RF-enabled PCB assembly for Infineon. This project encompasses the design, validation, 
            and production launch of a next-generation sensor hub capable of wireless communication 
            and real-time data processing. The project follows our standard 5-phase APQP methodology 
            ensuring quality and compliance throughout the development lifecycle.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { CalendarIcon, CheckCircle2 } from "lucide-react";
import { format } from "date-fns";

interface PhasePlanningStepProps {
  data: any;
  onUpdate: (stepKey: string, data: any) => void;
}

export const PhasePlanningStep: React.FC<PhasePlanningStepProps> = ({ data, onUpdate }) => {
  const [formData, setFormData] = useState({
    phases: [
      {
        id: 1,
        name: "Phase 1: Planning",
        description: "Project initiation and planning",
        startDate: new Date("2025-05-01"),
        endDate: new Date("2025-05-31"),
        milestones: ["Project Charter", "Risk Assessment", "Resource Planning"]
      },
      {
        id: 2,
        name: "Phase 2: Product Design",
        description: "Product design and development",
        startDate: new Date("2025-06-01"),
        endDate: new Date("2025-07-15"),
        milestones: ["Design Freeze", "DFMEA", "Design Validation"]
      },
      {
        id: 3,
        name: "Phase 3: Process Design",
        description: "Manufacturing process design",
        startDate: new Date("2025-07-16"),
        endDate: new Date("2025-08-15"),
        milestones: ["Process Flow", "PFMEA", "Control Plan"]
      },
      {
        id: 4,
        name: "Phase 4: Validation",
        description: "Product and process validation",
        startDate: new Date("2025-08-16"),
        endDate: new Date("2025-09-15"),
        milestones: ["Production Trial", "MSA", "Process Capability"]
      },
      {
        id: 5,
        name: "Phase 5: Launch",
        description: "Production launch and monitoring",
        startDate: new Date("2025-09-16"),
        endDate: new Date("2025-09-30"),
        milestones: ["Production Launch", "Post-Launch Review", "Lessons Learned"]
      }
    ]
  });

  useEffect(() => {
    if (data.phasePlanning) {
      setFormData(prev => ({ ...prev, ...data.phasePlanning }));
    }
  }, [data.phasePlanning]);

  const handlePhaseUpdate = (phaseIndex: number, field: string, value: any) => {
    const newPhases = [...formData.phases];
    newPhases[phaseIndex] = { ...newPhases[phaseIndex], [field]: value };
    const newData = { ...formData, phases: newPhases };
    setFormData(newData);
    onUpdate('phasePlanning', newData);
  };

  const handleMilestoneUpdate = (phaseIndex: number, milestoneIndex: number, value: string) => {
    const newPhases = [...formData.phases];
    const newMilestones = [...newPhases[phaseIndex].milestones];
    newMilestones[milestoneIndex] = value;
    newPhases[phaseIndex] = { ...newPhases[phaseIndex], milestones: newMilestones };
    const newData = { ...formData, phases: newPhases };
    setFormData(newData);
    onUpdate('phasePlanning', newData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>APQP Phase Planning</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {formData.phases.map((phase, phaseIndex) => (
              <Card key={phase.id} className="border border-slate-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <CheckCircle2 className="h-5 w-5 text-teal-600" />
                    {phase.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start text-left font-normal">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {phase.startDate ? format(phase.startDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={phase.startDate}
                            onSelect={(date) => handlePhaseUpdate(phaseIndex, 'startDate', date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start text-left font-normal">
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {phase.endDate ? format(phase.endDate, "PPP") : "Pick a date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={phase.endDate}
                            onSelect={(date) => handlePhaseUpdate(phaseIndex, 'endDate', date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Key Milestones</Label>
                    <div className="space-y-2">
                      {phase.milestones.map((milestone, milestoneIndex) => (
                        <Input
                          key={milestoneIndex}
                          value={milestone}
                          onChange={(e) => handleMilestoneUpdate(phaseIndex, milestoneIndex, e.target.value)}
                          placeholder={`Milestone ${milestoneIndex + 1}`}
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

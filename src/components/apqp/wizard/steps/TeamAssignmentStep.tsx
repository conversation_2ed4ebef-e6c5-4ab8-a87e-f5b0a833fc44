
import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { User, Mail, Phone } from "lucide-react";

interface TeamAssignmentStepProps {
  data: any;
  onUpdate: (stepKey: string, data: any) => void;
}

export const TeamAssignmentStep: React.FC<TeamAssignmentStepProps> = ({ data, onUpdate }) => {
  const [formData, setFormData] = useState({
    qaLead: "<PERSON><PERSON>",
    designEngineer: "<PERSON>",
    manufacturingLead: "<PERSON><PERSON>",
    projectManager: "",
    qualityEngineer: "",
    processEngineer: ""
  });

  useEffect(() => {
    if (data.teamAssignment) {
      setFormData(prev => ({ ...prev, ...data.teamAssignment }));
    }
  }, [data.teamAssignment]);

  const handleInputChange = (field: string, value: string) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    onUpdate('teamAssignment', newData);
  };

  const teamMembers = [
    { name: "Priya Nair", role: "QA Lead", email: "<EMAIL>", phone: "******-0101" },
    { name: "Jason Liu", role: "Design Engineer", email: "<EMAIL>", phone: "******-0102" },
    { name: "Sanjay Patel", role: "Manufacturing Lead", email: "<EMAIL>", phone: "******-0103" },
    { name: "Alan Cheng", role: "Manufacturing Engineer", email: "<EMAIL>", phone: "******-0104" },
    { name: "Maria Rodriguez", role: "Project Manager", email: "<EMAIL>", phone: "******-0105" },
    { name: "David Kim", role: "Quality Engineer", email: "<EMAIL>", phone: "******-0106" },
    { name: "Sarah Johnson", role: "Process Engineer", email: "<EMAIL>", phone: "******-0107" }
  ];

  const roles = [
    { key: "qaLead", label: "QA Lead *", required: true },
    { key: "designEngineer", label: "Design Engineer *", required: true },
    { key: "manufacturingLead", label: "Manufacturing Lead *", required: true },
    { key: "projectManager", label: "Project Manager", required: false },
    { key: "qualityEngineer", label: "Quality Engineer", required: false },
    { key: "processEngineer", label: "Process Engineer", required: false }
  ];

  const getSelectedMemberInfo = (memberName: string) => {
    return teamMembers.find(member => member.name === memberName);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Team Assignment</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {roles.map(role => (
            <div key={role.key} className="space-y-2">
              <Label htmlFor={role.key}>{role.label}</Label>
              <Select 
                value={formData[role.key as keyof typeof formData]} 
                onValueChange={(value) => handleInputChange(role.key, value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={`Select ${role.label.replace(' *', '')}`} />
                </SelectTrigger>
                <SelectContent>
                  {teamMembers.map(member => (
                    <SelectItem key={member.name} value={member.name}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>{member.name} - {member.role}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {formData[role.key as keyof typeof formData] && (
                <div className="mt-2 p-3 bg-slate-50 rounded-md">
                  {(() => {
                    const memberInfo = getSelectedMemberInfo(formData[role.key as keyof typeof formData]);
                    return memberInfo ? (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-slate-600">
                          <Mail className="h-4 w-4" />
                          <span>{memberInfo.email}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-slate-600">
                          <Phone className="h-4 w-4" />
                          <span>{memberInfo.phone}</span>
                        </div>
                      </div>
                    ) : null;
                  })()}
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Selected Team Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(formData).map(([roleKey, memberName]) => {
              if (!memberName) return null;
              const roleInfo = roles.find(r => r.key === roleKey);
              const memberInfo = getSelectedMemberInfo(memberName);
              
              return (
                <div key={roleKey} className="p-3 border border-slate-200 rounded-md">
                  <div className="font-medium text-slate-900">{roleInfo?.label.replace(' *', '')}</div>
                  <div className="text-sm text-slate-600">{memberName}</div>
                  {memberInfo && (
                    <div className="text-xs text-slate-500 mt-1">{memberInfo.email}</div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

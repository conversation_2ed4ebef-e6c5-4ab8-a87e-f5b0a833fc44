
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, Calendar, Users, FileText, Target } from "lucide-react";
import { format } from "date-fns";

interface FinalReviewStepProps {
  data: any;
  onUpdate: (stepKey: string, data: any) => void;
}

export const FinalReviewStep: React.FC<FinalReviewStepProps> = ({ data }) => {
  const projectInfo = data.projectInfo || {};
  const teamAssignment = data.teamAssignment || {};
  const phasePlanning = data.phasePlanning || {};
  const deliverables = data.deliverables || {};

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "pending": { variant: "outline" as const, className: "border-slate-200 text-slate-600 bg-slate-50" },
      "uploaded": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "placeholder": { variant: "outline" as const, className: "border-blue-200 text-blue-800 bg-blue-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["pending"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status === "pending" ? "Not Set" : status === "uploaded" ? "Uploaded" : "Placeholder"}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <CheckCircle2 className="h-12 w-12 text-teal-600 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-slate-900">Final Review</h2>
        <p className="text-slate-600">Review all project details before creating your APQP project</p>
      </div>

      {/* Project Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Project Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="font-medium text-slate-900">Project Name</div>
              <div className="text-slate-600">{projectInfo.name || "Not specified"}</div>
            </div>
            <div>
              <div className="font-medium text-slate-900">Customer</div>
              <div className="text-slate-600">{projectInfo.customer || "Not specified"}</div>
            </div>
            <div>
              <div className="font-medium text-slate-900">Product Type</div>
              <div className="text-slate-600">{projectInfo.productType || "Not specified"}</div>
            </div>
            <div>
              <div className="font-medium text-slate-900">Priority</div>
              <div className="text-slate-600">{projectInfo.priority || "Not specified"}</div>
            </div>
            <div>
              <div className="font-medium text-slate-900">Duration</div>
              <div className="text-slate-600">
                {projectInfo.startDate && projectInfo.endDate 
                  ? `${format(projectInfo.startDate, "MMM dd, yyyy")} - ${format(projectInfo.endDate, "MMM dd, yyyy")}`
                  : "Dates not set"
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Team Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Assignment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(teamAssignment).map(([role, member]) => {
              if (!member) return null;
              return (
                <div key={role} className="p-3 border border-slate-200 rounded-md">
                  <div className="font-medium text-slate-900 capitalize">
                    {role.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                  <div className="text-slate-600">{member as string}</div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Phase Planning */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Phase Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          {phasePlanning.phases && (
            <div className="space-y-3">
              {phasePlanning.phases.map((phase: any, index: number) => (
                <div key={phase.id} className="flex items-center justify-between p-3 border border-slate-200 rounded-md">
                  <div>
                    <div className="font-medium text-slate-900">{phase.name}</div>
                    <div className="text-sm text-slate-600">
                      {phase.startDate && phase.endDate 
                        ? `${format(phase.startDate, "MMM dd")} - ${format(phase.endDate, "MMM dd, yyyy")}`
                        : "Dates not set"
                      }
                    </div>
                  </div>
                  <div className="text-sm text-slate-500">
                    {phase.milestones ? phase.milestones.length : 0} milestones
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Deliverables */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Deliverables Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {deliverables.deliverables && (
            <div className="space-y-3">
              {deliverables.deliverables.map((deliverable: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border border-slate-200 rounded-md">
                  <div>
                    <div className="font-medium text-slate-900">
                      {deliverable.name}
                      {deliverable.required && <span className="text-red-500 ml-1">*</span>}
                    </div>
                    <div className="text-sm text-slate-600">Phase: {deliverable.phase}</div>
                  </div>
                  {getStatusBadge(deliverable.status)}
                </div>
              ))}
            </div>
          )}
          
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-slate-50 rounded-md">
              <div className="text-lg font-bold text-slate-900">
                {deliverables.deliverables ? deliverables.deliverables.length : 0}
              </div>
              <div className="text-sm text-slate-600">Total Deliverables</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-md">
              <div className="text-lg font-bold text-green-700">
                {deliverables.deliverables ? deliverables.deliverables.filter((d: any) => d.status === "uploaded").length : 0}
              </div>
              <div className="text-sm text-green-600">Ready</div>
            </div>
            <div className="text-center p-3 bg-amber-50 rounded-md">
              <div className="text-lg font-bold text-amber-700">
                {deliverables.deliverables ? deliverables.deliverables.filter((d: any) => d.status === "pending").length : 0}
              </div>
              <div className="text-sm text-amber-600">Pending</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

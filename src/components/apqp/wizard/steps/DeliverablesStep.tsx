
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, FileText, Check, Plus } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface DeliverablesStepProps {
  data: any;
  onUpdate: (stepKey: string, data: any) => void;
}

export const DeliverablesStep: React.FC<DeliverablesStepProps> = ({ data, onUpdate }) => {
  const [formData, setFormData] = useState({
    deliverables: [
      { name: "Design FMEA", required: true, status: "pending", phase: "Design" },
      { name: "Prototype Plan", required: true, status: "pending", phase: "Design" },
      { name: "Control Plan", required: true, status: "pending", phase: "Process" },
      { name: "Process Flow", required: true, status: "pending", phase: "Process" },
      { name: "Risk Analysis", required: true, status: "pending", phase: "Planning" },
      { name: "MSA Study", required: false, status: "pending", phase: "Validation" },
      { name: "Process Capability Study", required: false, status: "pending", phase: "Validation" },
      { name: "Production Part Approval", required: true, status: "pending", phase: "Launch" }
    ]
  });

  useEffect(() => {
    if (data.deliverables) {
      setFormData(prev => ({ ...prev, ...data.deliverables }));
    }
  }, [data.deliverables]);

  const handleDeliverableStatusChange = (index: number, status: string) => {
    const newDeliverables = [...formData.deliverables];
    newDeliverables[index] = { ...newDeliverables[index], status };
    const newData = { ...formData, deliverables: newDeliverables };
    setFormData(newData);
    onUpdate('deliverables', newData);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "pending": { variant: "outline" as const, className: "border-slate-200 text-slate-600 bg-slate-50" },
      "uploaded": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "placeholder": { variant: "outline" as const, className: "border-blue-200 text-blue-800 bg-blue-50" },
    };

    const config = statusMap[status as keyof typeof statusMap] || statusMap["pending"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {status === "pending" ? "Not Set" : status === "uploaded" ? "Uploaded" : "Placeholder Added"}
      </Badge>
    );
  };

  const getPhaseBadge = (phase: string) => {
    const phaseMap = {
      "Planning": { variant: "outline" as const, className: "border-blue-200 text-blue-800 bg-blue-50" },
      "Design": { variant: "outline" as const, className: "border-purple-200 text-purple-800 bg-purple-50" },
      "Process": { variant: "outline" as const, className: "border-amber-200 text-amber-800 bg-amber-50" },
      "Validation": { variant: "outline" as const, className: "border-green-200 text-green-800 bg-green-50" },
      "Launch": { variant: "outline" as const, className: "border-teal-200 text-teal-800 bg-teal-50" },
    };

    const config = phaseMap[phase as keyof typeof phaseMap] || phaseMap["Planning"];
    return (
      <Badge variant={config.variant} className={config.className}>
        {phase}
      </Badge>
    );
  };

  const phases = ["Planning", "Design", "Process", "Validation", "Launch"];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>APQP Deliverables Setup</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {phases.map(phase => {
              const phaseDeliverables = formData.deliverables.filter(d => d.phase === phase);
              
              return (
                <Card key={phase} className="border border-slate-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Phase: {phase}</CardTitle>
                      {getPhaseBadge(phase)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {phaseDeliverables.map((deliverable, index) => {
                        const globalIndex = formData.deliverables.findIndex(d => d.name === deliverable.name);
                        return (
                          <div key={deliverable.name} className="flex items-center justify-between p-3 border border-slate-200 rounded-md">
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-slate-500" />
                              <div>
                                <div className="font-medium text-slate-900">
                                  {deliverable.name}
                                  {deliverable.required && <span className="text-red-500 ml-1">*</span>}
                                </div>
                                <div className="text-sm text-slate-500">
                                  {deliverable.required ? "Required" : "Optional"}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-3">
                              {getStatusBadge(deliverable.status)}
                              
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeliverableStatusChange(globalIndex, "uploaded")}
                                  className={deliverable.status === "uploaded" ? "bg-green-50 border-green-200" : ""}
                                >
                                  <Upload className="h-4 w-4 mr-1" />
                                  Upload
                                </Button>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeliverableStatusChange(globalIndex, "placeholder")}
                                  className={deliverable.status === "placeholder" ? "bg-blue-50 border-blue-200" : ""}
                                >
                                  <Plus className="h-4 w-4 mr-1" />
                                  Placeholder
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Deliverables Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-slate-50 rounded-md">
              <div className="text-2xl font-bold text-slate-900">
                {formData.deliverables.length}
              </div>
              <div className="text-sm text-slate-600">Total Deliverables</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-md">
              <div className="text-2xl font-bold text-green-700">
                {formData.deliverables.filter(d => d.status === "uploaded").length}
              </div>
              <div className="text-sm text-green-600">Uploaded</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-md">
              <div className="text-2xl font-bold text-blue-700">
                {formData.deliverables.filter(d => d.status === "placeholder").length}
              </div>
              <div className="text-sm text-blue-600">Placeholders</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { ProjectInfoStep } from "./steps/ProjectInfoStep";
import { TeamAssignmentStep } from "./steps/TeamAssignmentStep";
import { PhasePlanningStep } from "./steps/PhasePlanningStep";
import { DeliverablesStep } from "./steps/DeliverablesStep";
import { FinalReviewStep } from "./steps/FinalReviewStep";

interface APQPProjectWizardProps {
  onComplete: (projectData: any) => void;
}

export const APQPProjectWizard: React.FC<APQPProjectWizardProps> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [projectData, setProjectData] = useState({
    projectInfo: {},
    teamAssignment: {},
    phasePlanning: {},
    deliverables: {},
  });

  const steps = [
    { number: 1, title: "Project Info", component: ProjectInfoStep },
    { number: 2, title: "Team Assignment", component: TeamAssignmentStep },
    { number: 3, title: "Phase Planning", component: PhasePlanningStep },
    { number: 4, title: "Deliverables Setup", component: DeliverablesStep },
    { number: 5, title: "Final Review", component: FinalReviewStep },
  ];

  const CurrentStepComponent = steps[currentStep - 1].component;

  const updateProjectData = (stepKey: string, data: any) => {
    setProjectData(prev => ({
      ...prev,
      [stepKey]: data
    }));
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    console.log("Final project data:", projectData);
    onComplete(projectData.projectInfo);
  };

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-8">
        {steps.map((step, index) => (
          <div key={step.number} className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              currentStep >= step.number 
                ? 'bg-teal-600 text-white' 
                : 'bg-slate-200 text-slate-600'
            }`}>
              {step.number}
            </div>
            <div className="ml-2 hidden sm:block">
              <p className={`text-sm font-medium ${
                currentStep >= step.number ? 'text-slate-900' : 'text-slate-500'
              }`}>
                {step.title}
              </p>
            </div>
            {index < steps.length - 1 && (
              <div className={`w-8 h-0.5 mx-4 ${
                currentStep > step.number ? 'bg-teal-600' : 'bg-slate-200'
              }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <div className="min-h-[400px]">
        <CurrentStepComponent 
          data={projectData}
          onUpdate={updateProjectData}
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-between pt-6 border-t">
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>
        
        {currentStep === steps.length ? (
          <Button 
            className="bg-teal-600 hover:bg-teal-700 text-white"
            onClick={handleSubmit}
          >
            Create Project
          </Button>
        ) : (
          <Button 
            className="bg-teal-600 hover:bg-teal-700 text-white"
            onClick={handleNext}
          >
            Next
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

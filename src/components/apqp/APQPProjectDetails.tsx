
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Users, Target, AlertCircle, CheckCircle } from "lucide-react";
import { formatDate } from "@/utils/dateUtils";

interface APQPProject {
  id: string;
  projectName: string;
  partNumber: string;
  supplier: string;
  status: string;
  startDate: string;
  targetDate: string;
  currentPhase: string;
  completionPercentage: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  teamLead: string;
  description?: string;
}

interface APQPProjectDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: APQPProject | null;
}

export const APQPProjectDetails: React.FC<APQPProjectDetailsProps> = ({
  open,
  onOpenChange,
  project
}) => {
  if (!project) return null;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'on track': return 'bg-green-100 text-green-800';
      case 'at risk': return 'bg-yellow-100 text-yellow-800';
      case 'delayed': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const phases = [
    { name: 'Planning & Definition', completed: project.currentPhase === 'Planning' ? 50 : project.currentPhase === 'Planning' ? 100 : 100 },
    { name: 'Product Design & Development', completed: project.currentPhase === 'Design' ? 75 : project.currentPhase === 'Design' ? 100 : project.currentPhase === 'Planning' ? 0 : 100 },
    { name: 'Process Design & Development', completed: project.currentPhase === 'Process' ? 60 : project.currentPhase === 'Process' ? 100 : ['Planning', 'Design'].includes(project.currentPhase) ? 0 : 100 },
    { name: 'Product & Process Validation', completed: project.currentPhase === 'Validation' ? 30 : project.currentPhase === 'Validation' ? 100 : ['Planning', 'Design', 'Process'].includes(project.currentPhase) ? 0 : 100 },
    { name: 'Feedback Assessment & Corrective Action', completed: project.currentPhase === 'Feedback' ? 20 : 0 }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            APQP Project Details - {project.projectName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Project Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Project Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Part Number:</span>
                  <span className="font-medium">{project.partNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Supplier:</span>
                  <span className="font-medium">{project.supplier}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Team Lead:</span>
                  <span className="font-medium">{project.teamLead}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status:</span>
                  <Badge className={getStatusColor(project.status)}>
                    {project.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Risk Level:</span>
                  <Badge className={getRiskColor(project.riskLevel)}>
                    {project.riskLevel}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Timeline</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Start Date:</span>
                  <span className="font-medium">{formatDate(project.startDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Target Date:</span>
                  <span className="font-medium">{formatDate(project.targetDate)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Progress:</span>
                  <span className="font-medium">{project.completionPercentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${project.completionPercentage}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* APQP Phases */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">APQP Phases</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {phases.map((phase, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="text-sm font-medium w-8">{index + 1}</div>
                      <div>
                        <div className="font-medium">{phase.name}</div>
                        <div className="text-xs text-gray-500">Phase {index + 1}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${phase.completed}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium w-12">{phase.completed}%</span>
                      {phase.completed === 100 ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : phase.completed > 0 ? (
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                      ) : (
                        <div className="h-4 w-4" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Project Description */}
          {project.description && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Project Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700">{project.description}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { CalendarDays, User, FileText, Clock, CheckCircle2, AlertCircle } from "lucide-react";

export const APQPDeliverables: React.FC = () => {
  const [selectedPhase, setSelectedPhase] = useState("all");

  const deliverables = [
    {
      id: "DEL-001",
      name: "Project Charter",
      project: "IoT Sensor Hub – Project Q5",
      projectId: "APQP-Q5-2025",
      phase: "Phase 1: Planning",
      dueDate: "2025-05-15",
      status: "completed",
      owner: "<PERSON><PERSON> Nair",
      progress: 100
    },
    {
      id: "DEL-002",
      name: "Risk Assessment",
      project: "IoT Sensor Hub – Project Q5",
      projectId: "APQP-Q5-2025",
      phase: "Phase 1: Planning",
      dueDate: "2025-05-20",
      status: "completed",
      owner: "<PERSON><PERSON> Nair",
      progress: 100
    },
    {
      id: "DEL-003",
      name: "Design FMEA",
      project: "Automotive ECU Module",
      projectId: "APQP-Q6-2025",
      phase: "Phase 2: Product Design",
      dueDate: "2025-06-15",
      status: "in-progress",
      owner: "Marcus Chen",
      progress: 65
    },
    {
      id: "DEL-004",
      name: "Process Flow",
      project: "Medical Device Controller",
      projectId: "APQP-Q7-2025",
      phase: "Phase 3: Process Design",
      dueDate: "2025-06-30",
      status: "pending",
      owner: "Sarah Johnson",
      progress: 0
    },
    {
      id: "DEL-005",
      name: "Control Plan",
      project: "IoT Sensor Hub – Project Q5",
      projectId: "APQP-Q5-2025",
      phase: "Phase 3: Process Design",
      dueDate: "2025-07-10",
      status: "overdue",
      owner: "Sanjay Patel",
      progress: 30
    }
  ];

  const phases = [
    "all",
    "Phase 1: Planning",
    "Phase 2: Product Design", 
    "Phase 3: Process Design",
    "Phase 4: Validation",
    "Phase 5: Launch"
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Clock className="h-4 w-4 text-blue-600" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <FileText className="h-4 w-4 text-slate-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      "completed": "bg-green-50 text-green-800 border-green-200",
      "in-progress": "bg-blue-50 text-blue-800 border-blue-200",
      "pending": "bg-slate-50 text-slate-600 border-slate-200",
      "overdue": "bg-red-50 text-red-800 border-red-200"
    };
    return statusMap[status as keyof typeof statusMap] || statusMap["pending"];
  };

  const filteredDeliverables = selectedPhase === "all" 
    ? deliverables 
    : deliverables.filter(d => d.phase === selectedPhase);

  const getDeliverableStats = () => {
    const completed = deliverables.filter(d => d.status === "completed").length;
    const inProgress = deliverables.filter(d => d.status === "in-progress").length;
    const overdue = deliverables.filter(d => d.status === "overdue").length;
    const pending = deliverables.filter(d => d.status === "pending").length;
    
    return { completed, inProgress, overdue, pending, total: deliverables.length };
  };

  const stats = getDeliverableStats();

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-slate-900">APQP Deliverables</h2>
          <p className="text-sm text-slate-600">Track deliverables across all APQP projects</p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-slate-900">{stats.total}</div>
            <div className="text-sm text-slate-600">Total</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-sm text-slate-600">Completed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
            <div className="text-sm text-slate-600">In Progress</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-slate-600">{stats.pending}</div>
            <div className="text-sm text-slate-600">Pending</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
            <div className="text-sm text-slate-600">Overdue</div>
          </CardContent>
        </Card>
      </div>

      {/* Phase Filter */}
      <div className="flex flex-wrap gap-2">
        {phases.map((phase) => (
          <Button
            key={phase}
            variant={selectedPhase === phase ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPhase(phase)}
            className={selectedPhase === phase ? "bg-teal-600 hover:bg-teal-700" : ""}
          >
            {phase === "all" ? "All Phases" : phase}
          </Button>
        ))}
      </div>

      {/* Deliverables List */}
      <div className="space-y-4">
        {filteredDeliverables.map((deliverable) => (
          <Card key={deliverable.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-3">
                  {getStatusIcon(deliverable.status)}
                  <div>
                    <h4 className="font-semibold text-slate-900">{deliverable.name}</h4>
                    <p className="text-sm text-slate-600">{deliverable.project}</p>
                    <p className="text-xs text-slate-500">{deliverable.phase}</p>
                  </div>
                </div>
                <Badge variant="outline" className={getStatusBadge(deliverable.status)}>
                  {deliverable.status.replace('-', ' ')}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <CalendarDays className="h-4 w-4" />
                  <span>Due: {new Date(deliverable.dueDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <User className="h-4 w-4" />
                  <span>Owner: {deliverable.owner}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-slate-600">
                  <FileText className="h-4 w-4" />
                  <span>ID: {deliverable.id}</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-slate-700">Progress</span>
                  <span className="text-sm text-slate-600">{deliverable.progress}%</span>
                </div>
                <Progress value={deliverable.progress} className="h-2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredDeliverables.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-slate-400 mb-2">No deliverables found</div>
            <p className="text-sm text-slate-600">No deliverables match the selected phase</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

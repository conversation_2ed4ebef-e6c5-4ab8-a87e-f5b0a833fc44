import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, FileText, CheckCircle2, FileSignature, AlertTriangle } from "lucide-react";
import { 
  mockSupplierAudits, 
  mockSupplierDocuments, 
  mockCAPAAssignments, 
  mockContractRenewals, 
  mockComplianceDeclarations 
} from "@/data/mockSupplierPortalData";

export const SupplierPortalHome: React.FC = () => {
  const getStatusBadge = (status: string, type: 'audit' | 'document' | 'capa' | 'contract' | 'compliance') => {
    let variant: "default" | "secondary" | "destructive" | "outline" = "default";
    let className = "";
    
    switch (status) {
      case "Scheduled":
      case "Valid":
      case "Renewed":
      case "Completed":
        variant = "default";
        className = "bg-green-100 text-green-800";
        break;
      case "In Progress":
      case "Under Review":
        variant = "default";
        className = "bg-blue-100 text-blue-800";
        break;
      case "Expiring Soon":
      case "Action Required":
        variant = "default";
        className = "bg-yellow-100 text-yellow-800";
        break;
      case "Overdue":
      case "Expired":
      case "Pending Upload":
        variant = "destructive";
        break;
      case "Open":
      case "Not Started":
        variant = "outline";
        break;
      default:
        variant = "secondary";
    }
    
    return <Badge variant={variant} className={className}>{status}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "Critical":
        return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
      case "High":
        return <Badge className="bg-orange-100 text-orange-800">High</Badge>;
      case "Medium":
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case "Low":
        return <Badge className="bg-green-100 text-green-800">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <div className="space-y-6 w-full">
      {/* My Audits */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            My Audits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockSupplierAudits.map((audit) => (
              <div key={audit.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{audit.title}</h4>
                    {getPriorityBadge(audit.priority)}
                  </div>
                  <p className="text-sm text-gray-600">{audit.type}</p>
                  <p className="text-sm text-gray-500">Due: {new Date(audit.dueDate).toLocaleDateString()}</p>
                  {audit.auditor && <p className="text-sm text-gray-500">Auditor: {audit.auditor}</p>}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(audit.status, 'audit')}
                  <Button variant="outline" size="sm">View Details</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* My Documents */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-600" />
            My Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockSupplierDocuments.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{doc.title}</h4>
                  <p className="text-sm text-gray-600">{doc.type}</p>
                  {doc.expiryDate && (
                    <p className="text-sm text-gray-500">
                      Expires: {new Date(doc.expiryDate).toLocaleDateString()}
                    </p>
                  )}
                  {doc.dueDate && (
                    <p className="text-sm text-gray-500">
                      Due: {new Date(doc.dueDate).toLocaleDateString()}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(doc.status, 'document')}
                  <Button variant="outline" size="sm">
                    {doc.status === "Pending Upload" ? "Upload" : "View"}
                  </Button>
                </div>
              </div>
            ))}
            <div className="p-3 border-2 border-dashed border-yellow-300 rounded-lg bg-yellow-50">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Submit updated ISO 9001 cert – due Aug 10, 2025</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CAPA Assignments */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-purple-600" />
            CAPA Assignments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockCAPAAssignments.map((capa) => (
              <div key={capa.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{capa.title}</h4>
                    {getPriorityBadge(capa.priority)}
                  </div>
                  <p className="text-sm text-gray-600">{capa.description}</p>
                  <p className="text-sm text-gray-500">Due: {new Date(capa.dueDate).toLocaleDateString()}</p>
                  <p className="text-sm text-gray-500">Assigned by: {capa.assignedBy}</p>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(capa.status, 'capa')}
                  <Button variant="outline" size="sm">Update</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Contract Renewals */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5 text-orange-600" />
            Contract Renewals & Terms
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockContractRenewals.map((contract) => (
              <div key={contract.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{contract.contractTitle}</h4>
                  <p className="text-sm text-gray-600">
                    Value: {new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(contract.value)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Current End: {new Date(contract.currentEndDate).toLocaleDateString()}
                  </p>
                  <p className="text-sm text-gray-500">
                    Renewal Due: {new Date(contract.renewalDueDate).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(contract.status, 'contract')}
                  <Button variant="outline" size="sm">Review Terms</Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Compliance Declarations */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-red-600" />
            Upcoming Compliance Declarations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {mockComplianceDeclarations.map((declaration) => (
              <div key={declaration.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{declaration.title}</h4>
                  <p className="text-sm text-gray-600">{declaration.type}</p>
                  <p className="text-sm text-gray-600">{declaration.description}</p>
                  <p className="text-sm text-gray-500">Due: {new Date(declaration.dueDate).toLocaleDateString()}</p>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(declaration.status, 'compliance')}
                  <Button variant="outline" size="sm">
                    {declaration.status === "Not Started" ? "Start" : "Continue"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

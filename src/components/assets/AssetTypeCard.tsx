
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface AssetTypeCardProps {
  name: string;
  count: number;
  icon: React.ReactNode;
}

export const AssetTypeCard: React.FC<AssetTypeCardProps> = ({ name, count, icon }) => {
  return (
    <Card className="bg-white">
      <CardContent className="p-4 flex items-center justify-between">
        <div className="flex items-center">
          <div className="bg-gray-50 p-3 rounded-lg mr-3">
            {/* Apply teal color to the icon */}
            {React.cloneElement(icon as React.ReactElement, { 
              className: "h-5 w-5 text-teal-600" 
            })}
          </div>
          <div>
            <p className="font-medium">{name}</p>
            <p className="text-sm text-gray-500">{count} units</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

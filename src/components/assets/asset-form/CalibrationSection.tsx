
import React from "react";
import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { AssetFormData } from "@/schemas/assetValidation";

interface CalibrationSectionProps {
  form: UseFormReturn<AssetFormData>;
  showCalibrationFields: boolean;
}

export function CalibrationSection({ form, showCalibrationFields }: CalibrationSectionProps) {
  return (
    <>
      <FormField
        control={form.control}
        name="calibrationRequired"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Calibration Required</FormLabel>
            <Select 
              onValueChange={field.onChange}
              value={field.value}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="Yes">Yes</SelectItem>
                <SelectItem value="No">No</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      
      {showCalibrationFields && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="calibrationDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Calibration Dt</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="calibrationCertNo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Calibration Certificate No.</FormLabel>
                <FormControl>
                  <Input placeholder="Enter certificate no." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="calibrationPeriod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Calibration Period</FormLabel>
                <Select 
                  onValueChange={field.onChange}
                  value={field.value || ""}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select calibration period" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Monthly">Monthly</SelectItem>
                    <SelectItem value="Quarterly">Quarterly</SelectItem>
                    <SelectItem value="Half-yearly">Half-yearly</SelectItem>
                    <SelectItem value="Yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      )}
    </>
  );
}

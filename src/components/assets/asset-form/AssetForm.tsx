
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { AssetDetailsSection } from "./AssetDetailsSection";
import { CalibrationSection } from "./CalibrationSection";
import { useFormValidation } from "@/hooks/useFormValidation";
import { assetSchema, AssetFormData } from "@/schemas/assetValidation";

// Export the type with both names for compatibility
export type AssetFormValues = AssetFormData;

interface AssetFormProps {
  onSubmit: (values: AssetFormData) => void;
  onCancel: () => void;
}

export function AssetForm({ onSubmit, onCancel }: AssetFormProps) {
  const form = useFormValidation({
    schema: assetSchema,
    defaultValues: {
      id: "",
      name: "",
      description: "",
      owner: "",
      location: "",
      purchaseDate: "",
      status: "",
      calibrationRequired: "Yes" as const,
      calibrationDate: "",
      calibrationCertNo: "",
      calibrationPeriod: ""
    }
  });

  const handleSubmit = (values: AssetFormData) => {
    console.log("Submitting asset:", values);
    onSubmit(values);
  };

  const calibrationRequired = form.watch("calibrationRequired");

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 py-4">
        <AssetDetailsSection form={form} />
        
        <CalibrationSection 
          form={form} 
          showCalibrationFields={calibrationRequired === "Yes"} 
        />
        
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" className="bg-teal-600 hover:bg-teal-700">
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
}

export type { AssetFormData };

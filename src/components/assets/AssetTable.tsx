
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { ChevronDown, ChevronUp, ArrowUpDown, Eye } from "lucide-react";

interface Asset {
  id: string;
  name: string;
  type: string;
  status: string;
  location: string;
  lastMaintenance: string;
  nextMaintenance: string;
  condition: string;
  value: number;
}

interface AssetTableProps {
  assets: Asset[];
  filteredAssets: Asset[];
  searchQuery: string;
  statusFilter: string;
}

export const AssetTable: React.FC<AssetTableProps> = ({ filteredAssets }) => {
  const [sortField, setSortField] = useState<keyof Asset | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (field: keyof Asset) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field: keyof Asset) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === 'asc' 
      ? <ChevronUp className="ml-1 h-4 w-4" />
      : <ChevronDown className="ml-1 h-4 w-4" />;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Maintenance":
        return <Badge className="bg-orange-100 text-orange-800">Maintenance</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const sortedAssets = [...filteredAssets].sort((a, b) => {
    if (!sortField) return 0;
    
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortDirection === 'asc' ? comparison : -comparison;
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return 0;
  });

  return (
    <div className="bg-white rounded-xl border border-slate-200 shadow-sm">
      <div className="p-6 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Assets</h2>
      </div>
      <div className="overflow-auto">
        <ResizableTable className="w-full" minColumnWidth={120}>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Asset Name
                  {getSortIcon('name')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('type')}
              >
                <div className="flex items-center">
                  Type
                  {getSortIcon('type')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  Status
                  {getSortIcon('status')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('location')}
              >
                <div className="flex items-center">
                  Location
                  {getSortIcon('location')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('condition')}
              >
                <div className="flex items-center">
                  Condition
                  {getSortIcon('condition')}
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                onClick={() => handleSort('value')}
              >
                <div className="flex items-center">
                  Value
                  {getSortIcon('value')}
                </div>
              </TableHead>
              <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedAssets.length > 0 ? (
              sortedAssets.map((asset, index) => (
                <TableRow key={asset.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <TableCell>
                    <div className="font-medium text-blue-600">{asset.name}</div>
                    <div className="text-xs text-gray-500">ID: {asset.id}</div>
                  </TableCell>
                  <TableCell>{asset.type}</TableCell>
                  <TableCell>{getStatusBadge(asset.status)}</TableCell>
                  <TableCell>{asset.location}</TableCell>
                  <TableCell>{asset.condition}</TableCell>
                  <TableCell>${asset.value.toLocaleString()}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No assets match the current filters.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </ResizableTable>
      </div>
    </div>
  );
};

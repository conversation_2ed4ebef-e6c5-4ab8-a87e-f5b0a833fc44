
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>Down, ArrowU<PERSON>, ArrowUpDown } from "lucide-react";
import { Asset } from "@/types/assets";
import { MobileTableHeader } from "./MobileTableHeader";

type SortField = keyof Asset | null;
type SortDirection = 'asc' | 'desc' | null;

interface SortableHeaderProps {
  field: SortField;
  children: React.ReactNode;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  className?: string;
}

const SortableHeader: React.FC<SortableHeaderProps> = ({ 
  field, 
  children, 
  sortField, 
  sortDirection, 
  onSort,
  className = ""
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHead 
      onClick={() => onSort(field)}
      className={`py-3 px-4 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 group cursor-pointer ${className}`}
    >
      <div className="flex items-center">
        {children}
        {renderSortIcon(field)}
      </div>
    </TableHead>
  );
};

interface AssetTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  isMobile: boolean;
}

export const AssetTableHeader: React.FC<AssetTableHeaderProps> = ({ 
  sortField, 
  sortDirection, 
  onSort,
  isMobile
}) => {
  if (isMobile) {
    return <MobileTableHeader sortField={sortField} sortDirection={sortDirection} onSort={onSort} />;
  }

  // Column width classes
  const nameColumnClass = "w-[250px]"; // Make name column wider
  const standardColumnClass = "w-[120px]";

  return (
    <TableHeader>
      <TableRow>
        <SortableHeader field="name" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={nameColumnClass}>Name</SortableHeader>
        <SortableHeader field="description" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>Description</SortableHeader>
        <SortableHeader field="location" sortField={sortField} sortDirection={sortDirection} onSort={onSort}>Location</SortableHeader>
        <SortableHeader field="owner" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Owner</SortableHeader>
        <SortableHeader field="purchaseDate" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Purchase Dt</SortableHeader>
        <SortableHeader field="calibrationDate" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Calibration Dt</SortableHeader>
        <SortableHeader field="calibrationCertNo" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Calibration Cert no.</SortableHeader>
        <SortableHeader field="calibrationPeriod" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Calibration Period</SortableHeader>
        <SortableHeader field="status" sortField={sortField} sortDirection={sortDirection} onSort={onSort} className={standardColumnClass}>Status</SortableHeader>
        <TableHead className="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
          Actions
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

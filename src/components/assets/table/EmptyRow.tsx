
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";

interface EmptyRowProps {
  searchQuery: string;
  statusFilter?: string;
}

export const EmptyRow: React.FC<EmptyRowProps> = ({ searchQuery, statusFilter }) => {
  return (
    <TableRow>
      <TableCell colSpan={10} className="py-6 text-center text-gray-500">
        {statusFilter ? 
          `No assets found matching "${searchQuery}" and status "${statusFilter}"` : 
          `No assets found matching "${searchQuery}"`}
      </TableCell>
    </TableRow>
  );
};


import React from "react";
import { Asset } from "@/types/assets";
import { MobileTableRow } from "./MobileTableRow";
import { TableCell, TableRow } from "@/components/ui/table";
import { Edit, Trash2, Calendar, MoreVertical } from "lucide-react";
import { StatusBadge } from "../StatusBadge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface AssetTableRowProps {
  asset: Asset;
  index: number;
  isMobile: boolean;
}

export const AssetTableRow: React.FC<AssetTableRowProps> = ({ asset, index, isMobile }) => {
  if (isMobile) {
    return <MobileTableRow asset={asset} index={index} />;
  }

  return (
    <TableRow className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gray-100`}>
      <TableCell className="py-4 px-4 w-[250px]">
        <div className="flex flex-col">
          <span className="text-blue-600 font-medium hover:text-blue-700 cursor-pointer">{asset.name}</span>
          <span className="text-xs text-gray-500 mt-1">ID: {asset.id}</span>
        </div>
      </TableCell>
      <TableCell className="py-4 px-4">
        <div className="max-w-[150px] overflow-hidden break-words">
          {asset.description}
        </div>
      </TableCell>
      <TableCell className="py-4 px-4">{asset.location}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">{asset.owner || "--"}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">{asset.purchaseDate}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">{asset.calibrationDate}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">{asset.calibrationCertNo}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">{asset.calibrationPeriod}</TableCell>
      <TableCell className="py-4 px-4 w-[120px]">
        <StatusBadge status={asset.status} />
      </TableCell>
      <TableCell className="py-4 px-4">
        <div className="flex items-center justify-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4 text-gray-600" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="z-50 bg-white">
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4 text-blue-600" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Trash2 className="mr-2 h-4 w-4 text-red-500" /> Delete
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calendar className="mr-2 h-4 w-4 text-blue-600" /> Schedule maintenance
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </TableCell>
    </TableRow>
  );
};

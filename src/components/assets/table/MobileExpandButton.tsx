
import React from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

interface MobileExpandButtonProps {
  isExpanded: boolean;
  onClick: () => void;
}

export const MobileExpandButton: React.FC<MobileExpandButtonProps> = ({ isExpanded, onClick }) => {
  return (
    <button onClick={onClick} className="focus:outline-none">
      {isExpanded ? (
        <ChevronDown size={16} className="text-gray-400" />
      ) : (
        <ChevronRight size={16} className="text-gray-400" />
      )}
    </button>
  );
};

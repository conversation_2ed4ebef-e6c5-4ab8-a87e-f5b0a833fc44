
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>Down, ArrowU<PERSON>, ArrowUpDown } from "lucide-react";
import { Asset } from "@/types/assets";

type SortField = keyof Asset | null;
type SortDirection = 'asc' | 'desc' | null;

interface MobileTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
}

export const MobileTableHeader: React.FC<MobileTableHeaderProps> = ({
  sortField,
  sortDirection,
  onSort,
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHeader>
      <TableRow>
        <TableHead className="w-10 p-2 bg-gray-50">
          <span className="sr-only">Expand</span>
        </TableHead>
        <TableHead 
          className="py-3 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 cursor-pointer group"
          onClick={() => onSort('name')}
        >
          <div className="flex items-center">
            Name
            {renderSortIcon('name')}
          </div>
        </TableHead>
        <TableHead 
          className="py-3 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 cursor-pointer group w-[100px]"
          onClick={() => onSort('status')}
        >
          <div className="flex items-center">
            Status
            {renderSortIcon('status')}
          </div>
        </TableHead>
        <TableHead className="w-12 p-2 bg-gray-50">
          <span className="sr-only">Actions</span>
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

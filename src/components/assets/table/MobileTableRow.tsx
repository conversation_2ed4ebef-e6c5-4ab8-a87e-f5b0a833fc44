
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Asset } from "@/types/assets";
import { StatusBadge } from "../StatusBadge";
import { MobileExpandButton } from "./MobileExpandButton";
import { MobileExpandedDetails } from "./MobileExpandedDetails";
import { Edit, Trash2, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MobileTableRowProps {
  asset: Asset;
  index: number;
}

export const MobileTableRow: React.FC<MobileTableRowProps> = ({ asset, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleAction = (action: string) => {
    toast.success(`${action} asset: ${asset.name}`);
    console.log(`${action} asset:`, asset);
  };

  return (
    <>
      <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100`}>
        <TableCell className="w-10 p-2">
          <MobileExpandButton isExpanded={isExpanded} onClick={toggleExpand} />
        </TableCell>
        <TableCell className="py-3 px-2">
          <div className="flex flex-col">
            <span className="text-teal-600 font-medium">{asset.name}</span>
            <span className="text-xs text-gray-500 mt-1">ID: {asset.id}</span>
          </div>
        </TableCell>
        <TableCell className="py-3 px-2 w-[100px]">
          <StatusBadge status={asset.status} />
        </TableCell>
      </TableRow>
      
      {isExpanded && (
        <>
          {/* Action buttons row - moved to the top when expanded */}
          <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
            <TableCell colSpan={3} className="px-2 py-1">
              <div className="flex items-center justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("Edit")}
                      >
                        <Edit className="h-4 w-4 text-teal-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("Delete")}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("Schedule maintenance")}
                      >
                        <Calendar className="h-4 w-4 text-teal-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Schedule maintenance</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableCell>
          </TableRow>
          
          <MobileExpandedDetails asset={asset} index={index} />
        </>
      )}
    </>
  );
};


import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Asset } from "@/types/assets";

interface MobileExpandedDetailsProps {
  asset: Asset;
  index: number;
}

export const MobileExpandedDetails: React.FC<MobileExpandedDetailsProps> = ({ asset, index }) => {
  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
      <TableCell colSpan={4} className="px-4 py-2">
        <div className="pl-6 space-y-2">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Description:</span>
            <span className="text-gray-700">{asset.description || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Location:</span>
            <span className="text-gray-700">{asset.location || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Owner:</span>
            <span className="text-gray-700">{asset.owner || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Purchase Date:</span>
            <span className="text-gray-700">{asset.purchaseDate || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Calibration Date:</span>
            <span className="text-gray-700">{asset.calibrationDate || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Calibration Cert No:</span>
            <span className="text-gray-700">{asset.calibrationCertNo || "--"}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Calibration Period:</span>
            <span className="text-gray-700">{asset.calibrationPeriod || "--"}</span>
          </div>
        </div>
      </TableCell>
    </TableRow>
  );
};


import React, { useState, KeyboardEvent } from "react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Input } from "@/components/ui/input";

interface TablePaginationProps {
  pageCount: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onPageInput?: (input: string) => void;
  displayRange?: { start: number; end: number };
  totalCount?: number;
}

export const TablePagination: React.FC<TablePaginationProps> = ({
  pageCount,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onPageInput,
  displayRange,
  totalCount
}) => {
  const [pageInputValue, setPageInputValue] = useState(currentPage.toString());

  // Handle direct page navigation input
  const handlePageInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && onPageInput) {
      onPageInput(pageInputValue);
      // Update input field to reflect validated page number
      setPageInputValue(currentPage.toString());
    }
  };

  // Generate pagination items
  const getPageItems = () => {
    const items = [];
    
    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink 
          onClick={() => onPageChange(1)}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // If there are many pages, add ellipsis after first page
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show pages around current page
    const startPage = Math.max(2, currentPage - 1);
    const endPage = Math.min(pageCount - 1, currentPage + 1);

    for (let i = startPage; i <= endPage; i++) {
      if (i > 1 && i < pageCount) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => onPageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }

    // If there are many pages, add ellipsis before last page
    if (currentPage < pageCount - 2 && pageCount > 3) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if more than one page
    if (pageCount > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            onClick={() => onPageChange(pageCount)}
            isActive={currentPage === pageCount}
          >
            {pageCount}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <div className="py-4 px-6 flex flex-col md:flex-row md:items-center justify-between border-t border-gray-100 bg-gray-50 gap-4">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        <div className="flex items-center">
          <span className="text-sm text-gray-600 mr-2">Rows per page:</span>
          <Select 
            value={pageSize.toString()} 
            onValueChange={(value) => onPageSizeChange(Number(value))}
          >
            <SelectTrigger className="h-8 w-20">
              <SelectValue placeholder={pageSize} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {displayRange && totalCount !== undefined && (
          <div className="hidden md:block text-sm text-gray-600">
            Showing {displayRange.start}-{displayRange.end} of {totalCount} items
          </div>
        )}
      </div>

      <div className="flex items-center gap-4">
        {pageCount > 5 && onPageInput && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Go to:</span>
            <Input
              type="number"
              min={1}
              max={pageCount}
              value={pageInputValue}
              onChange={(e) => setPageInputValue(e.target.value)}
              onKeyDown={handlePageInputKeyDown}
              className="h-8 w-16 text-center"
            />
          </div>
        )}

        <Pagination>
          <PaginationContent className="flex items-center gap-1">
            <PaginationPrevious
              className="h-8 flex items-center gap-1 px-2"
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              aria-disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Previous</span>
            </PaginationPrevious>
            
            <div className="hidden md:flex items-center gap-1">
              {getPageItems()}
            </div>
            
            <PaginationNext
              className="h-8 flex items-center gap-1 px-2"
              onClick={() => onPageChange(Math.min(pageCount, currentPage + 1))}
              aria-disabled={currentPage >= pageCount}
            >
              <span className="hidden sm:inline">Next</span>
              <ChevronRight className="h-4 w-4" />
            </PaginationNext>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};


import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { AssetForm, AssetFormValues } from "./asset-form/AssetForm";

interface AddAssetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAssetAdded?: () => void;
}

export function AddAssetDialog({
  open,
  onOpenChange,
  onAssetAdded,
}: AddAssetDialogProps) {
  const { toast } = useToast();
  
  const handleSubmit = (values: AssetFormValues) => {
    // Here you would typically call an API to add the asset
    console.log("Asset to be added:", values);
    
    toast({
      title: "Asset Added",
      description: `Successfully added asset: ${values.name}`,
    });
    
    if (onAssetAdded) {
      onAssetAdded();
    }
    
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[750px] max-h-[85vh] p-0">
        <DialogHeader className="bg-gray-50 p-6 border-b border-gray-200">
          <DialogTitle className="text-xl font-semibold text-gray-800">Create Asset</DialogTitle>
          <DialogDescription className="text-gray-600">
            Add a new asset to the asset hub. Fill in the required fields below.
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6">
          <AssetForm 
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

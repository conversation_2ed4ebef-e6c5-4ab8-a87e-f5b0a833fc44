
import React from "react";
import { TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AssetStatusTabsProps {
  currentValue?: string;
}

export const AssetStatusTabs: React.FC<AssetStatusTabsProps> = ({ currentValue = "all" }) => {
  return (
    <TabsList>
      <TabsTrigger value="all">All Assets</TabsTrigger>
      <TabsTrigger value="in-use">In Use</TabsTrigger>
      <TabsTrigger value="available">Available</TabsTrigger>
      <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
    </TabsList>
  );
};

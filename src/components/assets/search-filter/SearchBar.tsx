
import React from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({ 
  searchQuery, 
  setSearchQuery, 
  placeholder = "Search by ID, name, description, location, owner or status",
  className
}) => {
  const isMobile = useIsMobile();

  const handleClear = () => {
    setSearchQuery("");
  };
  
  return (
    <div className={cn("relative w-full", className)}>
      <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
      <Input 
        placeholder={placeholder} 
        className={cn(
          "pl-9 pr-8 py-2",
          isMobile ? "text-base h-11" : "text-sm"
        )}
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
      {searchQuery.length > 0 && (
        <button 
          onClick={handleClear}
          className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};


import React from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

interface AddAssetButtonProps {
  onClick: () => void;
}

export const AddAssetButton: React.FC<AddAssetButtonProps> = ({ onClick }) => {
  return (
    <Button 
      size="sm" 
      className="bg-teal-600 hover:bg-teal-700"
      onClick={onClick}
    >
      <Plus className="h-4 w-4 mr-2" />
      Add Asset
    </Button>
  );
};


import React from "react";
import { Filter } from "lucide-react";
import { Button } from "@/components/ui/button";

interface FilterButtonProps {
  onClick?: () => void;
}

export const FilterButton: React.FC<FilterButtonProps> = ({
  onClick
}) => {
  return (
    <Button
      variant="outline"
      size="sm"
      className="gap-2"
      onClick={onClick}
    >
      <Filter className="h-4 w-4" />
      Filter
    </Button>
  );
};

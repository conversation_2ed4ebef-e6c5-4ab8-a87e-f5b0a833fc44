
import React from "react";
import { Asset } from "@/types/assets";

interface StatusBadgeProps {
  status: Asset['status'];
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusColor = (status: Asset['status']) => {
    switch (status) {
      case "In Use": return "text-blue-700 bg-blue-100";
      case "Available": return "text-green-700 bg-green-100";
      case "Under Maintenance": return "text-orange-700 bg-orange-100";
      case "Retired": return "text-gray-700 bg-gray-100";
      default: return "text-gray-700 bg-gray-100";
    }
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(status)}`}>
      {status}
    </span>
  );
};

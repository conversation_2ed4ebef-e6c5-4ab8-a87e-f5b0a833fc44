
import React, { useEffect } from "react";
import { FilterSidePanelHeader } from "./FilterSidePanelHeader";
import { FilterSidePanelContent } from "./FilterSidePanelContent";
import { FilterSidePanelFooter } from "./FilterSidePanelFooter";
import { FilterState } from "@/types/filter";
import { useIsMobile } from "@/hooks/use-mobile";
import { useFilterState } from "@/hooks/useFilterState";

interface FilterSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
}

export const FilterSidePanel: React.FC<FilterSidePanelProps> = ({
  isOpen,
  onClose,
  filters,
  onApplyFilters
}) => {
  const isMobile = useIsMobile();
  const { 
    filters: localFilters, 
    setFilters,
    handleFilterChange, 
    handleResetFilters, 
    countActiveFilters,
    handleApplyFilters 
  } = useFilterState({ initialFilters: filters });
  
  // Update local filters when props change
  useEffect(() => {
    setFilters(filters);
  }, [filters, setFilters]);

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {isMobile && <div className="fixed inset-0 bg-black/50 z-30" onClick={onClose} />}
      <div 
        className={`fixed z-40 bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out
          ${isMobile ? 'inset-y-0 inset-x-0 w-full h-[90vh] bottom-0 top-auto rounded-t-xl' : 'inset-y-0 right-0 w-80'}`}
      >
        <div className="flex flex-col h-full">
          <FilterSidePanelHeader 
            activeFilterCount={countActiveFilters()} 
            onClose={onClose} 
          />
          
          <FilterSidePanelContent 
            filters={localFilters} 
            onFilterChange={handleFilterChange} 
          />
          
          <FilterSidePanelFooter 
            onReset={handleResetFilters} 
            onApply={handleApply} 
          />
        </div>
      </div>
    </>
  );
};

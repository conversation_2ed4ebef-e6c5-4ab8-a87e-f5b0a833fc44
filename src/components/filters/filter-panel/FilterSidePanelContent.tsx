
import React from "react";
import { FilterSection } from "../FilterSection";
import { FilterState } from "@/types/filter";
import { 
  categoriesOptions, 
  departmentsOptions, 
  assigneeOptions,
  statusOptions,
  processesOptions
} from "@/data/filterOptions";
import { useIsMobile } from "@/hooks/use-mobile";

interface FilterSidePanelContentProps {
  filters: FilterState;
  onFilterChange: (filterType: keyof FilterState, selectedIds: string[]) => void;
}

export const FilterSidePanelContent: React.FC<FilterSidePanelContentProps> = ({
  filters,
  onFilterChange
}) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="overflow-y-auto flex-1 p-4">
      <div className="space-y-6">
        {/* Status filter - only shown for mobile */}
        {isMobile && (
          <FilterSection
            title="Status"
            options={statusOptions.map(option => ({
              ...option,
              label: `${option.label} (${option.count})`
            }))}
            selectedOptions={filters.status}
            onFilterChange={(selectedIds) => onFilterChange("status", selectedIds)}
          />
        )}

        <FilterSection
          title="Categories"
          options={categoriesOptions}
          selectedOptions={filters.categories}
          onFilterChange={(selectedIds) => onFilterChange("categories", selectedIds)}
        />

        <FilterSection
          title="Departments"
          options={departmentsOptions}
          selectedOptions={filters.departments}
          onFilterChange={(selectedIds) => onFilterChange("departments", selectedIds)}
        />

        <FilterSection
          title="Processes"
          options={processesOptions}
          selectedOptions={filters.processes || []}
          onFilterChange={(selectedIds) => onFilterChange("processes", selectedIds)}
        />

        <FilterSection
          title="Assignee"
          options={assigneeOptions}
          selectedOptions={filters.assignee}
          onFilterChange={(selectedIds) => onFilterChange("assignee", selectedIds)}
        />
      </div>
    </div>
  );
};

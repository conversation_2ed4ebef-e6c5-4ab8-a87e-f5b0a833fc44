
import React from "react";
import { X, Filter } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface FilterSidePanelHeaderProps {
  activeFilterCount: number;
  onClose: () => void;
}

export const FilterSidePanelHeader: React.FC<FilterSidePanelHeaderProps> = ({
  activeFilterCount,
  onClose
}) => {
  return (
    <div className="flex justify-between items-center border-b p-4">
      <div className="flex items-center">
        <Filter className="h-5 w-5 mr-2 text-gray-600" />
        <h3 className="text-lg font-medium">Filters</h3>
        {activeFilterCount > 0 && (
          <Badge variant="secondary" className="ml-2">
            {activeFilterCount}
          </Badge>
        )}
      </div>
      <button 
        onClick={onClose}
        className="text-gray-500 hover:text-gray-700"
      >
        <X className="h-5 w-5" />
      </button>
    </div>
  );
};

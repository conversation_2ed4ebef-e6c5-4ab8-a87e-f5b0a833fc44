
import React from "react";
import { Button } from "@/components/ui/button";

interface FilterSidePanelFooterProps {
  onReset: () => void;
  onApply: () => void;
}

export const FilterSidePanelFooter: React.FC<FilterSidePanelFooterProps> = ({
  onReset,
  onApply
}) => {
  return (
    <div className="border-t p-4 flex justify-between">
      <Button 
        variant="outline" 
        onClick={onReset}
        className="w-1/2 mr-2 transition-all duration-200 hover:scale-105"
      >
        Reset
      </Button>
      <Button 
        onClick={onApply}
        className="w-1/2 ml-2 transition-all duration-200 hover:scale-105"
      >
        Apply Filters
      </Button>
    </div>
  );
};

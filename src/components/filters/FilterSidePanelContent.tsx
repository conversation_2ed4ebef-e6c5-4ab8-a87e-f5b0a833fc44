
import React from "react";
import { FilterSection } from "./FilterSection";
import { 
  categoriesOptions, 
  departmentsOptions, 
  assigneeOptions,
  processesOptions
} from "@/data/filterOptions";

export interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
  processes?: string[];
}

interface FilterSidePanelContentProps {
  filters: FilterState;
  onFilterChange: (filterType: keyof FilterState, selectedIds: string[]) => void;
}

export const FilterSidePanelContent: React.FC<FilterSidePanelContentProps> = ({
  filters,
  onFilterChange
}) => {
  return (
    <div className="overflow-y-auto flex-1 p-4">
      <div className="space-y-6">
        <FilterSection
          title="Categories"
          options={categoriesOptions}
          selectedOptions={filters.categories}
          onFilterChange={(selectedIds) => onFilterChange("categories", selectedIds)}
        />

        <FilterSection
          title="Departments"
          options={departmentsOptions}
          selectedOptions={filters.departments}
          onFilterChange={(selectedIds) => onFilterChange("departments", selectedIds)}
        />

        <FilterSection
          title="Processes"
          options={processesOptions}
          selectedOptions={filters.processes || []}
          onFilterChange={(selectedIds) => onFilterChange("processes", selectedIds)}
        />

        <FilterSection
          title="Assignee"
          options={assigneeOptions}
          selectedOptions={filters.assignee}
          onFilterChange={(selectedIds) => onFilterChange("assignee", selectedIds)}
        />
      </div>
    </div>
  );
};

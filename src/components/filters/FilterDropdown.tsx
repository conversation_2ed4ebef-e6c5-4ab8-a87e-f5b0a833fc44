
import React, { useState, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { FilterOption } from "@/types/filter";

interface FilterDropdownProps {
  label: string;
  options: FilterOption[];
  selectedOptions?: string[];
  onFilterChange: (selectedIds: string[]) => void;
  className?: string;
}

export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  selectedOptions = [],
  onFilterChange,
  className,
}) => {
  const [localSelectedOptions, setLocalSelectedOptions] = useState<string[]>(selectedOptions);
  const [isOpen, setIsOpen] = useState(false);

  // Update local state when props change
  useEffect(() => {
    setLocalSelectedOptions(selectedOptions);
  }, [selectedOptions]);

  const handleCheckboxChange = (optionId: string) => {
    const updatedSelection = localSelectedOptions.includes(optionId)
      ? localSelectedOptions.filter((id) => id !== optionId)
      : [...localSelectedOptions, optionId];
    
    setLocalSelectedOptions(updatedSelection);
    // Apply filter immediately
    onFilterChange(updatedSelection);
  };

  const handleClear = () => {
    setLocalSelectedOptions([]);
    onFilterChange([]);
    setIsOpen(false);
  };

  const getDisplayLabel = () => {
    if (localSelectedOptions.length === 0) {
      return label;
    } else if (localSelectedOptions.length === 1) {
      const selected = options.find(option => option.id === localSelectedOptions[0]);
      return selected ? selected.label : label;
    } else {
      return `${localSelectedOptions.length} selected`;
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div className={`relative w-full ${className || ''}`}>
          <div className="flex w-full items-center justify-between border rounded-md py-2 px-3 text-sm cursor-pointer bg-white">
            <span className={localSelectedOptions.length > 0 ? "text-gray-900" : "text-gray-500"}>
              {getDisplayLabel()}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-[220px] p-0 bg-white z-50" align="start">
        <div className="flex flex-col p-2 max-h-[280px] overflow-y-auto">
          {options.map((option) => (
            <div key={option.id} className="flex items-center space-x-2 py-1">
              <Checkbox
                id={option.id}
                checked={localSelectedOptions.includes(option.id)}
                onCheckedChange={() => handleCheckboxChange(option.id)}
              />
              <label
                htmlFor={option.id}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
              >
                {option.label}
              </label>
            </div>
          ))}
        </div>
        <div className="flex justify-end p-2 border-t">
          <Button
            variant="outline"
            size="sm"
            className="text-xs"
            onClick={handleClear}
          >
            Clear
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

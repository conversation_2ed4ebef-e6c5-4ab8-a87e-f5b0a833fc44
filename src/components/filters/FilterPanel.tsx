import React, { useState } from "react";
import { X } from "lucide-react";
import { FilterDropdown } from "./FilterDropdown";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

// Mock data for filters
const statusOptions = [
  { id: "published", label: "Published" },
  { id: "draft", label: "Draft" },
  { id: "needs-approval", label: "Needs Approval" },
  { id: "rejected", label: "Rejected" },
  { id: "expired", label: "Expired" },
  { id: "not-uploaded", label: "Not Uploaded" }
];

const categoriesOptions = [
  { id: "report", label: "Report" },
  { id: "process", label: "Process" },
  { id: "manual", label: "Manual" },
  { id: "chart", label: "Chart" },
  { id: "standard", label: "Standard" }
];

const departmentsOptions = [
  { id: "maintenance", label: "Maintenance" },
  { id: "project-management", label: "Project Management" },
  { id: "production", label: "Production" },
  { id: "quality-assurance", label: "Quality Assurance" },
  { id: "human-resources", label: "Human Resources" }
];

const assigneeOptions = [
  { id: "harsh-jha", label: "Harsh Jha" },
  { id: "milanjeet-singh", label: "<PERSON>jeet Singh" },
  { id: "vishnu-tripathi", label: "Vishnu Tripathi" },
  { id: "maitreyi-sharma", label: "<PERSON>treyi Sharma" },
  { id: "rehan-qureshi", label: "Rehan Qureshi" }
];

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyFilters: (filters: FilterState) => void;
}

interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  isOpen,
  onClose,
  onApplyFilters
}) => {
  const [filters, setFilters] = useState<FilterState>({
    status: [],
    categories: [],
    departments: [],
    assignee: []
  });
  const isMobile = useIsMobile();

  const handleFilterChange = (filterType: keyof FilterState, selectedIds: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedIds
    }));
  };

  const handleReset = () => {
    setFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: []
    });
  };

  const handleApply = () => {
    onApplyFilters(filters);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className={`
      ${isMobile ? 'fixed inset-0 z-50 bg-black/50' : 'absolute top-16 right-0 z-50'} 
    `}>
      <div className={`
        bg-white border border-gray-200 shadow-lg p-4
        ${isMobile ? 'absolute inset-y-16 inset-x-0 rounded-t-lg h-[90vh] overflow-y-auto' : 'w-80 rounded-lg'}
      `}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Filters</h3>
          <div className="flex space-x-2">
            <Button variant="ghost" size="sm" onClick={handleReset}>
              Reset
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Document Status</h4>
            <FilterDropdown
              label="Select statuses..."
              options={statusOptions}
              onFilterChange={(selectedIds) => handleFilterChange("status", selectedIds)}
            />
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Categories</h4>
            <FilterDropdown
              label="Select categories..."
              options={categoriesOptions}
              onFilterChange={(selectedIds) => handleFilterChange("categories", selectedIds)}
            />
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Departments</h4>
            <FilterDropdown
              label="Select departments..."
              options={departmentsOptions}
              onFilterChange={(selectedIds) => handleFilterChange("departments", selectedIds)}
            />
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Assignee</h4>
            <FilterDropdown
              label="Select assignee"
              options={assigneeOptions}
              onFilterChange={(selectedIds) => handleFilterChange("assignee", selectedIds)}
            />
          </div>
        </div>

        <div className={`${isMobile ? 'sticky bottom-0 bg-white pt-4' : 'mt-6'}`}>
          <Button className="w-full" onClick={handleApply}>
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

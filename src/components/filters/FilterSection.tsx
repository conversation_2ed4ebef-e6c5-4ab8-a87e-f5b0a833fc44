
import React from "react";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { FilterDropdown } from "./FilterDropdown";
import { FilterOption } from "@/types/filter";

interface FilterSectionProps {
  title: string;
  options: FilterOption[];
  selectedOptions: string[];
  onFilterChange: (selectedIds: string[]) => void;
}

export const FilterSection: React.FC<FilterSectionProps> = ({
  title,
  options,
  selectedOptions,
  onFilterChange
}) => {
  const getOptionLabel = (optionId: string): string => {
    const option = options.find(opt => opt.id === optionId);
    return option ? option.label : optionId;
  };

  return (
    <div>
      <h4 className="text-sm font-medium mb-2 flex items-center justify-between">
        <span>{title}</span>
        {selectedOptions.length > 0 && (
          <Badge variant="outline" className="text-xs">
            {selectedOptions.length}
          </Badge>
        )}
      </h4>
      <FilterDropdown
        label={`Select ${title.toLowerCase()}...`}
        options={options.map(option => ({
          ...option,
          label: `${option.label} (${option.count})`
        }))}
        selectedOptions={selectedOptions}
        onFilterChange={(selectedIds) => onFilterChange(selectedIds)}
      />
      {selectedOptions.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {selectedOptions.map(optionId => (
            <Badge 
              key={optionId} 
              variant="secondary" 
              className="flex items-center gap-1 text-xs"
            >
              {getOptionLabel(optionId)}
              <button
                onClick={() => onFilterChange(selectedOptions.filter(id => id !== optionId))}
                className="ml-1 text-gray-500 hover:text-gray-700"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

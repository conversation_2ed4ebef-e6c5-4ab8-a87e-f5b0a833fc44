
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FileText, CheckCircle, AlertCircle, Clock, User, Calendar, Package } from 'lucide-react';

interface BatchRecordDetailProps {
  batchId: string;
  onBack: () => void;
}

interface BatchRecord {
  id: string;
  productName: string;
  batchNumber: string;
  status: 'In Progress' | 'Under Review' | 'Approved' | 'Rejected';
  startDate: string;
  endDate: string;
  quantity: number;
  operator: string;
  reviewer: string;
  approver?: string;
  steps: BatchStep[];
  qualityChecks: QualityCheck[];
  materials: Material[];
}

interface BatchStep {
  id: string;
  name: string;
  status: 'Pending' | 'In Progress' | 'Completed' | 'Failed';
  startTime: string;
  endTime?: string;
  operator: string;
  parameters: Parameter[];
  notes: string;
}

interface Parameter {
  name: string;
  target: string;
  actual: string;
  unit: string;
  status: 'Pass' | 'Fail' | 'Warning';
}

interface QualityCheck {
  id: string;
  test: string;
  result: string;
  specification: string;
  status: 'Pass' | 'Fail' | 'Pending';
  tester: string;
  timestamp: string;
}

interface Material {
  id: string;
  name: string;
  lotNumber: string;
  quantity: number;
  unit: string;
  supplier: string;
}

const mockBatchRecord: BatchRecord = {
  id: 'BATCH-2025-001',
  productName: 'Paracetamol 500mg Tablets',
  batchNumber: 'PA500-240604-001',
  status: 'Under Review',
  startDate: '2025-06-04 08:00',
  endDate: '2025-06-04 16:30',
  quantity: 10000,
  operator: 'John Smith',
  reviewer: 'Sarah Johnson',
  steps: [
    {
      id: 'STEP-001',
      name: 'Material Weighing',
      status: 'Completed',
      startTime: '08:00',
      endTime: '08:45',
      operator: 'John Smith',
      parameters: [
        { name: 'Paracetamol', target: '5000g', actual: '5001g', unit: 'g', status: 'Pass' },
        { name: 'Microcrystalline Cellulose', target: '2500g', actual: '2499g', unit: 'g', status: 'Pass' },
        { name: 'Starch', target: '1000g', actual: '1001g', unit: 'g', status: 'Pass' }
      ],
      notes: 'All materials weighed within specification'
    },
    {
      id: 'STEP-002',
      name: 'Blending',
      status: 'Completed',
      startTime: '09:00',
      endTime: '10:30',
      operator: 'John Smith',
      parameters: [
        { name: 'Blend Time', target: '15 min', actual: '15.2 min', unit: 'min', status: 'Pass' },
        { name: 'Speed', target: '25 rpm', actual: '25 rpm', unit: 'rpm', status: 'Pass' },
        { name: 'Temperature', target: '20-25°C', actual: '22°C', unit: '°C', status: 'Pass' }
      ],
      notes: 'Blend uniform, no lumps observed'
    },
    {
      id: 'STEP-003',
      name: 'Tablet Compression',
      status: 'Completed',
      startTime: '11:00',
      endTime: '15:30',
      operator: 'Mike Wilson',
      parameters: [
        { name: 'Compression Force', target: '8-12 kN', actual: '10.2 kN', unit: 'kN', status: 'Pass' },
        { name: 'Tablet Weight', target: '650±20mg', actual: '652mg', unit: 'mg', status: 'Pass' },
        { name: 'Hardness', target: '60-80N', actual: '72N', unit: 'N', status: 'Pass' }
      ],
      notes: 'Production ran smoothly, all parameters within limits'
    },
    {
      id: 'STEP-004',
      name: 'Coating',
      status: 'In Progress',
      startTime: '16:00',
      operator: 'Lisa Chen',
      parameters: [
        { name: 'Coating Weight Gain', target: '3±0.5%', actual: '2.8%', unit: '%', status: 'Warning' },
        { name: 'Inlet Temperature', target: '65±5°C', actual: '67°C', unit: '°C', status: 'Pass' }
      ],
      notes: 'Monitoring coating weight gain closely'
    }
  ],
  qualityChecks: [
    {
      id: 'QC-001',
      test: 'Content Uniformity',
      result: '98.5-101.2%',
      specification: '95.0-105.0%',
      status: 'Pass',
      tester: 'Dr. Emma Davis',
      timestamp: '2025-06-04 14:30'
    },
    {
      id: 'QC-002',
      test: 'Dissolution',
      result: '85% in 30 min',
      specification: 'NLT 80% in 30 min',
      status: 'Pass',
      tester: 'Dr. Emma Davis',
      timestamp: '2025-06-04 15:15'
    },
    {
      id: 'QC-003',
      test: 'Friability',
      result: '0.12%',
      specification: 'NMT 1.0%',
      status: 'Pass',
      tester: 'Mark Thompson',
      timestamp: '2025-06-04 16:00'
    }
  ],
  materials: [
    { id: 'MAT-001', name: 'Paracetamol USP', lotNumber: 'PA-2025-041', quantity: 5000, unit: 'g', supplier: 'PharmaCorp' },
    { id: 'MAT-002', name: 'Microcrystalline Cellulose', lotNumber: 'MCC-2025-038', quantity: 2500, unit: 'g', supplier: 'ExcipientCo' },
    { id: 'MAT-003', name: 'Corn Starch', lotNumber: 'CS-2025-022', quantity: 1000, unit: 'g', supplier: 'NaturalSupply' }
  ]
};

export const BatchRecordDetail: React.FC<BatchRecordDetailProps> = ({ batchId, onBack }) => {
  const [batchRecord] = useState<BatchRecord>(mockBatchRecord);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      case 'Pending': return 'bg-gray-100 text-gray-800';
      case 'Pass': return 'bg-green-100 text-green-800';
      case 'Fail': return 'bg-red-100 text-red-800';
      case 'Warning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'In Progress': return <Clock className="h-4 w-4 text-blue-600" />;
      case 'Failed': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 w-full">
      <div className="bg-white border-b border-gray-200 p-4 w-full">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl font-bold">{batchRecord.batchNumber}</h1>
                <p className="text-sm text-gray-600">{batchRecord.productName}</p>
              </div>
              <Badge className={getStatusColor(batchRecord.status)}>
                {batchRecord.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 w-full">
        <div className="w-full max-w-6xl mx-auto">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="process">Process Steps</TabsTrigger>
              <TabsTrigger value="quality">Quality Control</TabsTrigger>
              <TabsTrigger value="materials">Materials</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-600" />
                      <div>
                        <p className="text-sm text-gray-500">Quantity</p>
                        <p className="font-medium">{batchRecord.quantity.toLocaleString()} tablets</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-green-600" />
                      <div>
                        <p className="text-sm text-gray-500">Operator</p>
                        <p className="font-medium">{batchRecord.operator}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-orange-600" />
                      <div>
                        <p className="text-sm text-gray-500">Start Date</p>
                        <p className="font-medium">{batchRecord.startDate}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-purple-600" />
                      <div>
                        <p className="text-sm text-gray-500">Reviewer</p>
                        <p className="font-medium">{batchRecord.reviewer}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Batch Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Product</p>
                        <p className="font-medium">{batchRecord.productName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Batch Number</p>
                        <p className="font-medium">{batchRecord.batchNumber}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Start Date</p>
                        <p className="font-medium">{batchRecord.startDate}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">End Date</p>
                        <p className="font-medium">{batchRecord.endDate || 'In Progress'}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="process">
              <div className="space-y-4">
                {batchRecord.steps.map((step, index) => (
                  <Card key={step.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {getStepIcon(step.status)}
                          <div>
                            <h3 className="font-medium">{step.name}</h3>
                            <p className="text-sm text-gray-600">Step {index + 1}</p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(step.status)}>
                          {step.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-500">Start Time</p>
                          <p className="font-medium">{step.startTime}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">End Time</p>
                          <p className="font-medium">{step.endTime || 'In Progress'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Operator</p>
                          <p className="font-medium">{step.operator}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-2 mb-4">
                        <h4 className="font-medium">Parameters</h4>
                        {step.parameters.map((param, paramIndex) => (
                          <div key={paramIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm">{param.name}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-600">Target: {param.target}</span>
                              <span className="text-sm">Actual: {param.actual}</span>
                              <Badge variant="outline" className={getStatusColor(param.status)}>
                                {param.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-500">Notes</p>
                        <p className="text-sm">{step.notes}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="quality">
              <div className="space-y-4">
                {batchRecord.qualityChecks.map((check) => (
                  <Card key={check.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{check.test}</h3>
                        <Badge className={getStatusColor(check.status)}>
                          {check.status}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Result</p>
                          <p className="font-medium">{check.result}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Specification</p>
                          <p className="font-medium">{check.specification}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Tester</p>
                          <p className="font-medium">{check.tester}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Timestamp</p>
                          <p className="font-medium">{check.timestamp}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="materials">
              <Card>
                <CardHeader>
                  <CardTitle>Materials Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {batchRecord.materials.map((material) => (
                      <div key={material.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{material.name}</p>
                          <p className="text-sm text-gray-600">Lot: {material.lotNumber}</p>
                          <p className="text-sm text-gray-600">Supplier: {material.supplier}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{material.quantity} {material.unit}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <div className="bg-white border-t border-gray-200 p-4 w-full">
        <div className="w-full max-w-6xl mx-auto flex gap-3 justify-end">
          <Button variant="outline">
            Export PDF
          </Button>
          <Button variant="outline">
            Print Record
          </Button>
          <Button>
            Approve Batch
          </Button>
        </div>
      </div>
    </div>
  );
};

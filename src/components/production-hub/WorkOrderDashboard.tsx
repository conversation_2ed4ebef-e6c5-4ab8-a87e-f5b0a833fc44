import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Play, FileText, Settings, Wrench, Package, Users, Calendar, Eye } from 'lucide-react';

interface WorkOrderDashboardProps {
  onCreateWorkOrder: () => void;
  onExecuteWorkOrder: (id: string) => void;
  onReviewBatch: (id: string) => void;
  onOpenConsole: () => void;
  onOpenCMMS?: () => void;
  onOpenGemba?: () => void;
}

interface BatchRecord {
  id: string;
  batchNumber: string;
  productName: string;
  status: 'In Progress' | 'Under Review' | 'Approved' | 'Rejected';
  startDate: string;
  operator: string;
}

const mockBatchRecords: BatchRecord[] = [
  {
    id: 'BATCH-2025-001',
    batchNumber: 'PA500-240604-001',
    productName: 'Paracetamol 500mg Tablets',
    status: 'Under Review',
    startDate: '2025-06-04',
    operator: '<PERSON>'
  },
  {
    id: 'BATCH-2025-002',
    batchNumber: 'VC1000-240603-001',
    productName: 'Vitamin C 1000mg Tablets',
    status: 'In Progress',
    startDate: '2025-06-03',
    operator: 'Sarah Johnson'
  },
  {
    id: 'BATCH-2025-003',
    batchNumber: 'OM-240602-001',
    productName: 'Omega-3 Liquid Capsules',
    status: 'Approved',
    startDate: '2025-06-02',
    operator: 'Mike Wilson'
  }
];

export const WorkOrderDashboard: React.FC<WorkOrderDashboardProps> = ({
  onCreateWorkOrder,
  onExecuteWorkOrder,
  onReviewBatch,
  onOpenConsole,
  onOpenCMMS,
  onOpenGemba
}) => {
  const [batchRecords] = useState<BatchRecord[]>(mockBatchRecords);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex-1 overflow-auto p-6 w-full">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onCreateWorkOrder}>
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Plus className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium">Create Work Order</h3>
              <p className="text-sm text-gray-600">Start new production order</p>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onOpenConsole}>
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-green-100 p-3 rounded-lg">
              <Settings className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium">Operator Console</h3>
              <p className="text-sm text-gray-600">Real-time operations</p>
            </div>
          </CardContent>
        </Card>

        {onOpenGemba && (
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onOpenGemba}>
            <CardContent className="flex items-center gap-4 p-6">
              <div className="bg-yellow-100 p-3 rounded-lg">
                <Eye className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-medium">Gemba Walks</h3>
                <p className="text-sm text-gray-600">Floor observations & findings</p>
              </div>
            </CardContent>
          </Card>
        )}

        {onOpenCMMS && (
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onOpenCMMS}>
            <CardContent className="flex items-center gap-4 p-6">
              <div className="bg-orange-100 p-3 rounded-lg">
                <Wrench className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h3 className="font-medium">CMMS</h3>
                <p className="text-sm text-gray-600">Maintenance management</p>
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-purple-100 p-3 rounded-lg">
              <FileText className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h3 className="font-medium">Quality Control</h3>
              <p className="text-sm text-gray-600">QC documentation</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Work Orders */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Active Work Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div>
                  <h4 className="font-medium">WO-2025-001</h4>
                  <p className="text-sm text-gray-600">Batch Production - Paracetamol 500mg</p>
                </div>
                <Badge variant="secondary">In Progress</Badge>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => onExecuteWorkOrder('WO-2025-001')}>
                  <Play className="h-4 w-4 mr-1" />
                  Execute
                </Button>
                <Button size="sm" variant="outline" onClick={() => onReviewBatch('BATCH-2025-001')}>
                  <FileText className="h-4 w-4 mr-1" />
                  Review
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div>
                  <h4 className="font-medium">WO-2025-002</h4>
                  <p className="text-sm text-gray-600">Quality Control - Vitamin C 1000mg</p>
                </div>
                <Badge>Pending</Badge>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => onExecuteWorkOrder('WO-2025-002')}>
                  <Play className="h-4 w-4 mr-1" />
                  Execute
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Batch Records */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Batch Records</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {batchRecords.map((batch) => (
              <div 
                key={batch.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onReviewBatch(batch.id)}
              >
                <div className="flex items-center gap-4">
                  <div className="bg-blue-100 p-2 rounded">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{batch.batchNumber}</h4>
                    <p className="text-sm text-gray-600">{batch.productName}</p>
                    <p className="text-xs text-gray-500">Operator: {batch.operator} • {batch.startDate}</p>
                  </div>
                </div>
                <Badge className={getStatusColor(batch.status)}>
                  {batch.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

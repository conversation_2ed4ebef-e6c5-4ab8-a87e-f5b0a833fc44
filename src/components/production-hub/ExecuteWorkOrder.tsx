
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Eye } from 'lucide-react';
import { GembaWalkModal } from '@/components/production-hub/GembaWalkModal';
import { ExecuteWorkOrderHeader } from '@/components/production-hub/execute/ExecuteWorkOrderHeader';
import { ExecuteWorkOrderContent } from '@/components/production-hub/execute/ExecuteWorkOrderContent';
import { ExecuteWorkOrderActions } from '@/components/production-hub/execute/ExecuteWorkOrderActions';
import { LogDeviationModal } from '@/components/production-hub/LogDeviationModal';
import { useWorkOrderExecution } from '@/hooks/useWorkOrderExecution';

interface ExecuteWorkOrderProps {
  workOrderId: string;
  onBack: () => void;
}

export const ExecuteWorkOrder: React.FC<ExecuteWorkOrderProps> = ({ workOrderId, onBack }) => {
  const [showDeviationModal, setShowDeviationModal] = useState(false);
  const [showGembaModal, setShowGembaModal] = useState(false);
  
  const {
    currentStepIndex,
    steps,
    stepData,
    timer,
    isTimerRunning,
    currentStep,
    handleStartStep,
    handlePauseStep,
    handleCompleteStep,
    handleDataChange,
    isStepComplete
  } = useWorkOrderExecution();

  const handleGembaSave = (gembaData: any) => {
    console.log('Gemba walk data saved:', gembaData);
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4 mb-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-xl font-bold">Execute Work Order</h1>
            <p className="text-sm text-gray-600">{workOrderId}</p>
          </div>
          
          {/* Gemba Walk Button */}
          <Button 
            variant="outline" 
            className="gap-2 bg-teal-50 border-teal-200 text-teal-700 hover:bg-teal-100"
            onClick={() => setShowGembaModal(true)}
          >
            <Eye className="h-4 w-4" />
            Start Gemba Walk
          </Button>
        </div>

        <ExecuteWorkOrderHeader 
          steps={steps}
          currentStepIndex={currentStepIndex}
        />
      </div>

      {/* Content */}
      <ExecuteWorkOrderContent
        currentStep={currentStep}
        currentStepIndex={currentStepIndex}
        steps={steps}
        stepData={stepData}
        timer={timer}
        onDataChange={handleDataChange}
      />

      {/* Actions */}
      <ExecuteWorkOrderActions
        currentStep={currentStep}
        isStepComplete={isStepComplete}
        onStartStep={handleStartStep}
        onPauseStep={handlePauseStep}
        onCompleteStep={handleCompleteStep}
        onRaiseDeviation={() => setShowDeviationModal(true)}
      />

      {/* Modals */}
      {showDeviationModal && (
        <LogDeviationModal
          stepName={currentStep.name}
          onClose={() => setShowDeviationModal(false)}
          onSubmit={(deviationData) => {
            console.log('Deviation logged:', deviationData);
            setShowDeviationModal(false);
          }}
        />
      )}

      <GembaWalkModal
        open={showGembaModal}
        onClose={() => setShowGembaModal(false)}
        onSave={handleGembaSave}
      />
    </div>
  );
};

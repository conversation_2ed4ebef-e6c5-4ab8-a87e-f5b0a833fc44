
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, FileText, Download, CheckCircle, AlertCircle } from 'lucide-react';

interface BatchRecordReviewProps {
  workOrderId: string;
  onBack: () => void;
}

interface BatchRecord {
  id: string;
  workOrderId: string;
  batchNumber: string;
  productName: string;
  status: 'Complete' | 'Under Review' | 'Approved';
  completedSteps: number;
  totalSteps: number;
  deviations: number;
  reviewedBy?: string;
  approvedBy?: string;
  completedAt: string;
}

const mockBatchRecord: BatchRecord = {
  id: 'BMR-2025-001',
  workOrderId: 'WO-2025-GMP-011',
  batchNumber: 'BATCH-001',
  productName: 'GMP Supplement Powder',
  status: 'Complete',
  completedSteps: 8,
  totalSteps: 8,
  deviations: 1,
  completedAt: '2025-06-15 14:30'
};

export const BatchRecordReview: React.FC<BatchRecordReviewProps> = ({ workOrderId, onBack }) => {
  const [batchRecord] = useState(mockBatchRecord);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved': return 'bg-green-100 text-green-800';
      case 'Complete': return 'bg-blue-100 text-blue-800';
      case 'Under Review': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const stepData = [
    { name: 'Raw Material Verification', status: 'Complete', time: '09:00', operator: 'John Smith' },
    { name: 'Weighing & Dispensing', status: 'Complete', time: '09:30', operator: 'Jane Doe' },
    { name: 'Mixing', status: 'Complete', time: '10:00', operator: 'Mike Wilson' },
    { name: 'Granulation', status: 'Complete', time: '10:45', operator: 'Sarah Johnson' },
    { name: 'Drying', status: 'Complete', time: '11:30', operator: 'Tom Brown' },
    { name: 'Sizing', status: 'Complete', time: '13:00', operator: 'Lisa Davis' },
    { name: 'Blending', status: 'Complete', time: '13:30', operator: 'Mark Taylor' },
    { name: 'Final QC Check', status: 'Complete', time: '14:00', operator: 'Emily Clark' }
  ];

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Batch Manufacturing Record</h1>
            <p className="text-sm text-gray-600">{batchRecord.id}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          
          {/* Batch Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Batch Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Batch Number</p>
                  <p className="font-mono font-semibold">{batchRecord.batchNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Product</p>
                  <p className="font-semibold">{batchRecord.productName}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <Badge className={getStatusColor(batchRecord.status)}>
                    {batchRecord.status}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Completed</p>
                  <p>{batchRecord.completedAt}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <p className="text-sm text-gray-600">Progress</p>
                  <p className="font-semibold">{batchRecord.completedSteps}/{batchRecord.totalSteps} Steps</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Deviations</p>
                  <p className="font-semibold text-orange-600">{batchRecord.deviations}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Work Order</p>
                  <p className="font-mono text-sm">{batchRecord.workOrderId}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Manufacturing Steps */}
          <Card>
            <CardHeader>
              <CardTitle>Manufacturing Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stepData.map((step, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <div>
                        <p className="font-medium">{step.name}</p>
                        <p className="text-sm text-gray-600">Operator: {step.operator}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{step.time}</p>
                      <Badge variant="outline" className="text-xs">
                        {step.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Deviations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                Deviations ({batchRecord.deviations})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium">Temperature Deviation - Drying Step</p>
                    <p className="text-sm text-gray-600 mt-1">
                      Temperature exceeded 42°C for 5 minutes during drying process.
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Action Taken:</span> Adjusted heating system, continued monitoring
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Reported by:</span> Tom Brown at 12:15
                    </p>
                  </div>
                  <Badge className="bg-orange-100 text-orange-800">
                    Closed
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Review Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-3">
                <Button variant="outline" className="gap-2">
                  <Download className="h-4 w-4" />
                  Download PDF
                </Button>
                <Button variant="outline" className="gap-2">
                  <FileText className="h-4 w-4" />
                  Print Record
                </Button>
                <Button className="gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Approve Record
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

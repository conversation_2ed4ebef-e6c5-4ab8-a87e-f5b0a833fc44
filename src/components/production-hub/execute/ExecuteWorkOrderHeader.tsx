
import React from 'react';

interface ProcessStep {
  id: string;
  name: string;
  status: 'pending' | 'in-progress' | 'completed';
}

interface ExecuteWorkOrderHeaderProps {
  steps: ProcessStep[];
  currentStepIndex: number;
}

export const ExecuteWorkOrderHeader: React.FC<ExecuteWorkOrderHeaderProps> = ({
  steps,
  currentStepIndex
}) => {
  return (
    <div className="flex gap-2 overflow-x-auto pb-2">
      {steps.map((step, index) => (
        <div
          key={step.id}
          className={`flex-shrink-0 px-3 py-1 rounded-full text-xs font-medium ${
            index === currentStepIndex ? 'bg-teal-600 text-white' :
            index < currentStepIndex ? 'bg-green-600 text-white' :
            'bg-gray-200 text-gray-600'
          }`}
        >
          {step.name}
        </div>
      ))}
    </div>
  );
};

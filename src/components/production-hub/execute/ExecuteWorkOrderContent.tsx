import React from 'react';
import { Card, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileText, Timer } from 'lucide-react';
import { ProcessImprovementFlags } from '../ProcessImprovementFlags';

interface ProcessStep {
  id: string;
  name: string;
  instruction: string;
  estimatedTime: number;
  dataCapture: Array<{
    field: string;
    type: 'numeric' | 'timer' | 'text' | 'checkbox';
    unit?: string;
    required: boolean;
  }>;
  status: 'pending' | 'in-progress' | 'completed';
}

interface ExecuteWorkOrderContentProps {
  currentStep: ProcessStep;
  currentStepIndex: number;
  steps: ProcessStep[];
  stepData: Record<string, any>;
  timer: number;
  onDataChange: (field: string, value: any) => void;
}

export const ExecuteWorkOrderContent: React.FC<ExecuteWorkOrderContentProps> = ({
  currentStep,
  currentStepIndex,
  steps,
  stepData,
  timer,
  onDataChange
}) => {
  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex-1 overflow-y-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">{currentStep.name}</CardTitle>
              <Badge className={getStepStatusColor(currentStep.status)}>
                {currentStep.status === 'in-progress' ? 'In Progress' : 
                 currentStep.status === 'completed' ? 'Completed' : 'Pending'}
              </Badge>
            </div>
            <div className="text-right text-sm text-gray-600">
              <p>Step {currentStepIndex + 1} of {steps.length}</p>
              <p>Est. {currentStep.estimatedTime} min</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Work Instruction */}
          <div>
            <h3 className="font-medium mb-2">Work Instruction</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm">{currentStep.instruction}</p>
              <Button variant="outline" size="sm" className="mt-2 gap-2">
                <FileText className="h-4 w-4" />
                View SOP
              </Button>
            </div>
          </div>

          {/* Timer */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Timer className="h-5 w-5 text-gray-600" />
                <span className="font-medium">Timer</span>
              </div>
              <div className="text-2xl font-mono font-bold text-teal-600">
                {formatTime(timer)}
              </div>
            </div>
          </div>

          {/* Data Capture */}
          <div>
            <h3 className="font-medium mb-4">Data Capture</h3>
            <div className="space-y-4">
              {currentStep.dataCapture.map((field) => (
                <div key={field.field}>
                  <Label htmlFor={field.field}>
                    {field.field} {field.unit && `(${field.unit})`}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  
                  {field.type === 'numeric' ? (
                    <Input
                      id={field.field}
                      type="number"
                      value={stepData[currentStep.id]?.[field.field] || ''}
                      onChange={(e) => onDataChange(field.field, e.target.value)}
                      placeholder={`Enter ${field.field.toLowerCase()}`}
                    />
                  ) : field.type === 'text' ? (
                    <Input
                      id={field.field}
                      value={stepData[currentStep.id]?.[field.field] || ''}
                      onChange={(e) => onDataChange(field.field, e.target.value)}
                      placeholder={`Enter ${field.field.toLowerCase()}`}
                    />
                  ) : field.type === 'checkbox' ? (
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id={field.field}
                        checked={stepData[currentStep.id]?.[field.field] || false}
                        onChange={(e) => onDataChange(field.field, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <label htmlFor={field.field} className="text-sm">Confirmed</label>
                    </div>
                  ) : field.type === 'timer' ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        value={stepData[currentStep.id]?.[field.field] || ''}
                        onChange={(e) => onDataChange(field.field, e.target.value)}
                        placeholder="Auto-captured from timer"
                        disabled
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDataChange(field.field, Math.floor(timer / 60))}
                      >
                        Capture Current
                      </Button>
                    </div>
                  ) : null}
                </div>
              ))}
            </div>
          </div>

          {/* Process Improvement Flags */}
          <ProcessImprovementFlags 
            processStepId={currentStep.id}
            processStepName={currentStep.name}
          />
        </CardContent>
      </Card>
    </div>
  );
};

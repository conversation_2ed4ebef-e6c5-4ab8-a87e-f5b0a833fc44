
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Play, Pause, Square, AlertTriangle } from 'lucide-react';

interface ProcessStep {
  id: string;
  name: string;
  status: 'pending' | 'in-progress' | 'completed';
}

interface ExecuteWorkOrderActionsProps {
  currentStep: ProcessStep;
  isStepComplete: () => boolean;
  onStartStep: () => void;
  onPauseStep: () => void;
  onCompleteStep: () => void;
  onRaiseDeviation: () => void;
}

export const ExecuteWorkOrderActions: React.FC<ExecuteWorkOrderActionsProps> = ({
  currentStep,
  isStepComplete,
  onStartStep,
  onPauseStep,
  onCompleteStep,
  onRaiseDeviation
}) => {
  return (
    <div className="bg-white border-t border-gray-200 p-4">
      <div className="flex gap-3">
        {currentStep.status === 'pending' && (
          <Button className="flex-1 gap-2" onClick={onStartStep}>
            <Play className="h-4 w-4" />
            Start Step
          </Button>
        )}
        
        {currentStep.status === 'in-progress' && (
          <>
            <Button variant="outline" onClick={onPauseStep} className="gap-2">
              <Pause className="h-4 w-4" />
              Pause
            </Button>
            <Button 
              className="flex-1 gap-2" 
              onClick={onCompleteStep}
              disabled={!isStepComplete()}
            >
              <Square className="h-4 w-4" />
              Complete Step
            </Button>
          </>
        )}
      </div>
      
      {/* Emergency/Deviation Button */}
      <Button 
        variant="destructive" 
        className="w-full mt-3 gap-2"
        onClick={onRaiseDeviation}
      >
        <AlertTriangle className="h-4 w-4" />
        Raise Deviation
      </Button>
    </div>
  );
};

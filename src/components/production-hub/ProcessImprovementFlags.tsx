
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, TrendingUp, ArrowRight, Eye, FileText } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface GembaFlag {
  id: string;
  issue: string;
  frequency: number;
  lastReported: string;
  severity: 'Low' | 'Medium' | 'High';
  category: 'Safety' | 'Quality' | '5S' | 'Efficiency';
  processStep: string;
}

interface ProcessImprovementFlagsProps {
  processStepId: string;
  processStepName: string;
}

const mockGembaFlags: GembaFlag[] = [
  {
    id: 'GF001',
    issue: 'Unlabeled bins causing confusion',
    frequency: 3,
    lastReported: '2025-06-03',
    severity: 'High',
    category: 'Safety',
    processStep: 'Mixing'
  },
  {
    id: 'GF002',
    issue: 'Temperature gauge hard to read',
    frequency: 2,
    lastReported: '2025-06-02',
    severity: 'Medium',
    category: 'Quality',
    processStep: 'Mixing'
  }
];

export const ProcessImprovementFlags: React.FC<ProcessImprovementFlagsProps> = ({
  processStepId,
  processStepName
}) => {
  const [showCreateCapa, setShowCreateCapa] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState<GembaFlag | null>(null);
  const [capaDescription, setCapaDescription] = useState('');
  const [assignee, setAssignee] = useState('');
  const [dueDate, setDueDate] = useState('');

  const processFlags = mockGembaFlags.filter(flag => 
    flag.processStep.toLowerCase() === processStepName.toLowerCase()
  );

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Safety': return '🛡️';
      case 'Quality': return '⭐';
      case '5S': return '🏭';
      case 'Efficiency': return '⚡';
      default: return '📋';
    }
  };

  const handleCreateCapa = (flag: GembaFlag) => {
    setSelectedFlag(flag);
    setCapaDescription(`Address recurring issue: ${flag.issue}`);
    setShowCreateCapa(true);
  };

  const handleSubmitCapa = () => {
    if (selectedFlag) {
      const capaData = {
        flagId: selectedFlag.id,
        description: capaDescription,
        assignee,
        dueDate,
        processStep: processStepName
      };
      console.log('CAPA created:', capaData);
      setShowCreateCapa(false);
      setSelectedFlag(null);
      setCapaDescription('');
      setAssignee('');
      setDueDate('');
    }
  };

  if (processFlags.length === 0) {
    return null;
  }

  return (
    <>
      <Card className="mt-4 border-orange-200 bg-orange-50">
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-orange-600" />
            Top Gemba Flags for {processStepName}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {processFlags.slice(0, 3).map((flag) => (
            <div key={flag.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
              <div className="flex items-center gap-3 flex-1">
                <span className="text-lg">{getCategoryIcon(flag.category)}</span>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {flag.issue}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={getSeverityColor(flag.severity)} variant="outline">
                      {flag.severity}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      Reported {flag.frequency} times
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 text-xs"
                  onClick={() => handleCreateCapa(flag)}
                >
                  <ArrowRight className="h-3 w-3" />
                  Create CAPA
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Create CAPA Dialog */}
      <Dialog open={showCreateCapa} onOpenChange={setShowCreateCapa}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Corrective Action (CAPA)</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 p-1">
            {selectedFlag && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Based on Gemba Flag</h4>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">{getCategoryIcon(selectedFlag.category)}</span>
                  <span className="text-sm text-gray-700">{selectedFlag.issue}</span>
                </div>
                <div className="flex gap-2">
                  <Badge className={getSeverityColor(selectedFlag.severity)} variant="outline">
                    {selectedFlag.severity}
                  </Badge>
                  <Badge variant="outline">
                    {selectedFlag.frequency} reports
                  </Badge>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="capa-description">Corrective Action Description</Label>
              <Textarea
                id="capa-description"
                value={capaDescription}
                onChange={(e) => setCapaDescription(e.target.value)}
                placeholder="Describe the corrective action to address this recurring issue..."
                rows={4}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="assignee">Assign To</Label>
                <Select value={assignee} onValueChange={setAssignee}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="john.smith">John Smith (Production)</SelectItem>
                    <SelectItem value="sarah.johnson">Sarah Johnson (QA)</SelectItem>
                    <SelectItem value="mike.wilson">Mike Wilson (Maintenance)</SelectItem>
                    <SelectItem value="lisa.chen">Lisa Chen (Engineering)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="due-date">Due Date</Label>
                <input
                  id="due-date"
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowCreateCapa(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitCapa}
                className="flex-1 gap-2"
                disabled={!capaDescription || !assignee || !dueDate}
              >
                <FileText className="h-4 w-4" />
                Create CAPA
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

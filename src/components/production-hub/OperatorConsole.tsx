
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Play, Pause, AlertTriangle, Users, Timer } from 'lucide-react';

interface OperatorConsoleProps {
  onBack: () => void;
}

interface ActiveTask {
  id: string;
  workOrderId: string;
  stepName: string;
  operator: string;
  startTime: string;
  estimatedEnd: string;
  status: 'running' | 'paused' | 'overdue';
}

const mockActiveTasks: ActiveTask[] = [
  {
    id: '1',
    workOrderId: 'WO-2025-GMP-011',
    stepName: 'Mixing',
    operator: '<PERSON>',
    startTime: '14:30',
    estimatedEnd: '15:00',
    status: 'running'
  },
  {
    id: '2',
    workOrderId: 'WO-2025-VIT-012',
    stepName: 'Drying',
    operator: '<PERSON>',
    startTime: '13:45',
    estimatedEnd: '14:15',
    status: 'overdue'
  },
  {
    id: '3',
    workOrderId: 'WO-2025-OMG-013',
    stepName: 'Quality Check',
    operator: '<PERSON>',
    startTime: '14:45',
    estimatedEnd: '15:15',
    status: 'paused'
  }
];

export const OperatorConsole: React.FC<OperatorConsoleProps> = ({ onBack }) => {
  const [activeTasks] = useState(mockActiveTasks);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      case 'overdue': return <AlertTriangle className="h-4 w-4" />;
      default: return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <h1 className="text-xl font-bold">Operator Console</h1>
            <p className="text-sm text-gray-600">Real-time production monitoring</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Play className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold">3</p>
                    <p className="text-sm text-gray-600">Active Tasks</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold">8</p>
                    <p className="text-sm text-gray-600">Operators Online</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <div>
                    <p className="text-2xl font-bold">1</p>
                    <p className="text-sm text-gray-600">Overdue</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Timer className="h-5 w-5 text-orange-600" />
                  <div>
                    <p className="text-2xl font-bold">2</p>
                    <p className="text-sm text-gray-600">Deviations Today</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Active Tasks */}
          <Card>
            <CardHeader>
              <CardTitle>Active Production Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeTasks.map((task) => (
                  <div key={task.id} className="border rounded-lg p-4 bg-white">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Badge className={getStatusColor(task.status)}>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(task.status)}
                            {task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                          </div>
                        </Badge>
                        <h3 className="font-semibold">{task.stepName}</h3>
                      </div>
                      <div className="text-sm text-gray-600">
                        {task.workOrderId}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Operator</p>
                        <p className="font-medium">{task.operator}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Started</p>
                        <p className="font-medium">{task.startTime}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Est. Completion</p>
                        <p className={`font-medium ${task.status === 'overdue' ? 'text-red-600' : ''}`}>
                          {task.estimatedEnd}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                      {task.status === 'paused' && (
                        <Button size="sm" className="gap-1">
                          <Play className="h-4 w-4" />
                          Resume
                        </Button>
                      )}
                      {task.status === 'running' && (
                        <Button size="sm" variant="outline" className="gap-1">
                          <Pause className="h-4 w-4" />
                          Pause
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Alerts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                Recent Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div>
                    <p className="font-medium text-red-800">Temperature Deviation - Drying Step</p>
                    <p className="text-sm text-red-600">WO-2025-VIT-012 • Line 2 • 14:15</p>
                  </div>
                  <Badge className="bg-red-100 text-red-800">Active</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div>
                    <p className="font-medium text-yellow-800">Task Overdue</p>
                    <p className="text-sm text-yellow-600">Drying step exceeded estimated time by 15 minutes</p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-800">Monitoring</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

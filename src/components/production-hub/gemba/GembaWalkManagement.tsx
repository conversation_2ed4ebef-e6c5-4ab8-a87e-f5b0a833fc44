import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Plus, ArrowLeft } from 'lucide-react';
import { GembaWalkModal } from '../GembaWalkModal';
import { FindingsLog } from './FindingsLog';
import { GembaAnalytics } from './GembaAnalytics';
import { useToast } from '@/hooks/use-toast';

interface GembaWalkData {
  area: string;
  checklist: {
    safety: boolean;
    fiveS: boolean;
    processCompliance: boolean;
  };
  notes: string;
  images: File[];
  observer: string;
}

interface GembaWalkManagementProps {
  onBack?: () => void;
}

export const GembaWalkManagement: React.FC<GembaWalkManagementProps> = ({ onBack }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { toast } = useToast();

  const handleGembaWalkSubmit = (data: GembaWalkData) => {
    console.log('Gemba Walk submitted:', data);
    
    // Create findings based on the notes and checklist
    const findings = [];
    
    if (!data.checklist.safety) {
      findings.push({
        description: `Safety compliance issue noted in ${data.area}`,
        type: 'Issue',
        area: data.area
      });
    }
    
    if (!data.checklist.fiveS) {
      findings.push({
        description: `5S implementation needs improvement in ${data.area}`,
        type: 'Observation',
        area: data.area
      });
    }
    
    if (data.notes) {
      findings.push({
        description: data.notes,
        type: 'Observation',
        area: data.area
      });
    }
    
    toast({
      title: "Gemba Walk Submitted",
      description: `Walk completed for ${data.area}. ${findings.length} findings created.`,
    });
  };

  const handleConvertToCAPA = (findingId: string) => {
    toast({
      title: "CAPA Created",
      description: `Finding ${findingId} has been converted to a CAPA record.`,
    });
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="outline" onClick={onBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <h1 className="text-3xl font-bold">Gemba Walk Management</h1>
        </div>
        <Button onClick={() => setIsModalOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          New Gemba Walk
        </Button>
      </div>

      <Tabs defaultValue="findings" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="findings" className="font-bold">Findings Log</TabsTrigger>
          <TabsTrigger value="analytics" className="font-bold">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="findings">
          <FindingsLog onConvertToCAPA={handleConvertToCAPA} />
        </TabsContent>
        
        <TabsContent value="analytics">
          <GembaAnalytics />
        </TabsContent>
      </Tabs>

      <GembaWalkModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSave={handleGembaWalkSubmit}
        onSubmit={handleGembaWalkSubmit}
      />
    </div>
  );
};

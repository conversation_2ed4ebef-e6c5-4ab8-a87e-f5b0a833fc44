
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';

const departmentTrends = [
  { department: 'Production A', walks: 15, month: 'Jan' },
  { department: 'Production B', walks: 12, month: 'Jan' },
  { department: 'Quality Lab', walks: 8, month: 'Jan' },
  { department: 'Packaging', walks: 10, month: 'Jan' },
  { department: 'Production A', walks: 18, month: 'Feb' },
  { department: 'Production B', walks: 14, month: 'Feb' },
  { department: 'Quality Lab', walks: 11, month: 'Feb' },
  { department: 'Packaging', walks: 13, month: 'Feb' }
];

const issueTypesData = [
  { name: 'Issues', value: 45, color: '#ef4444' },
  { name: 'Best Practices', value: 28, color: '#22c55e' },
  { name: 'Observations', value: 32, color: '#3b82f6' }
];

const issueHeatmap = [
  { type: 'Safety Issues', count: 12, severity: 'high' },
  { type: 'Equipment Problems', count: 8, severity: 'medium' },
  { type: '5S Violations', count: 15, severity: 'low' },
  { type: 'Process Deviations', count: 6, severity: 'high' },
  { type: 'Documentation Issues', count: 4, severity: 'low' }
];

const resolutionTrends = [
  { month: 'Jan', avgDays: 8.5 },
  { month: 'Feb', avgDays: 7.2 },
  { month: 'Mar', avgDays: 6.8 },
  { month: 'Apr', avgDays: 7.5 },
  { month: 'May', avgDays: 6.1 },
  { month: 'Jun', avgDays: 5.9 }
];

export const GembaAnalytics: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Gemba Analytics Dashboard</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Gemba Walks by Department */}
        <Card>
          <CardHeader>
            <CardTitle className="font-bold">Gemba Walks by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={departmentTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="department" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="walks" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Issues vs Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle className="font-bold">Issues vs Best Practices</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={issueTypesData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {issueTypesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div className="flex justify-center gap-4 mt-4">
              {issueTypesData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                  <span className="text-sm">{item.name}: {item.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Issue Types Heatmap */}
        <Card>
          <CardHeader>
            <CardTitle className="font-bold">Most Frequent Issue Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {issueHeatmap.map((issue, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div 
                      className={`w-4 h-4 rounded-full ${
                        issue.severity === 'high' ? 'bg-red-500' :
                        issue.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                    ></div>
                    <span className="font-medium">{issue.type}</span>
                  </div>
                  <div className="text-right">
                    <span className="text-lg font-bold">{issue.count}</span>
                    <span className="text-sm text-gray-500 ml-1">occurrences</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Time to Resolution */}
        <Card>
          <CardHeader>
            <CardTitle className="font-bold">Average Time to Resolution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={resolutionTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value} days`, 'Avg Resolution Time']} />
                <Line type="monotone" dataKey="avgDays" stroke="#8b5cf6" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

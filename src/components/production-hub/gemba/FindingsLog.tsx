
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { AlertTriangle, CheckCircle, Eye, ArrowUpCircle, Search } from 'lucide-react';

interface Finding {
  id: string;
  description: string;
  type: 'Issue' | 'Observation' | 'Improvement';
  area: string;
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  status: 'Open' | 'In Progress' | 'Closed';
  foundBy: string;
  foundDate: string;
  actions: string[];
}

interface FindingsLogProps {
  onConvertToCAPA: (findingId: string) => void;
}

export const FindingsLog: React.FC<FindingsLogProps> = ({ onConvertToCAPA }) => {
  const [statusFilter, setStatusFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock findings data
  const [findings] = useState<Finding[]>([
    {
      id: "F001",
      description: "Safety equipment not properly stored in Assembly Area 1",
      type: "Issue",
      area: "Assembly Area 1",
      severity: "High",
      status: "Open",
      foundBy: "John Smith",
      foundDate: "2024-01-25",
      actions: ["Immediate cleanup required", "Training refresher needed"]
    },
    {
      id: "F002",
      description: "5S implementation could be improved in packaging area",
      type: "Observation",
      area: "Packaging",
      severity: "Medium",
      status: "In Progress",
      foundBy: "Sarah Johnson",
      foundDate: "2024-01-24",
      actions: ["5S audit scheduled", "Team training planned"]
    },
    {
      id: "F003",
      description: "Suggestion for workflow optimization in Quality Lab",
      type: "Improvement",
      area: "Quality Lab",
      severity: "Low",
      status: "Closed",
      foundBy: "Mike Chen",
      foundDate: "2024-01-23",
      actions: ["Process mapping completed", "Implementation successful"]
    }
  ]);

  const filteredFindings = findings.filter(finding => {
    const matchesStatus = statusFilter === "all" || finding.status === statusFilter;
    const matchesSearch = finding.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         finding.area.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Issue": return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "Observation": return <Eye className="h-4 w-4 text-blue-600" />;
      case "Improvement": return <ArrowUpCircle className="h-4 w-4 text-green-600" />;
      default: return <Eye className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    const colors = {
      "Critical": "bg-red-600 text-white",
      "High": "bg-red-100 text-red-800",
      "Medium": "bg-yellow-100 text-yellow-800",
      "Low": "bg-green-100 text-green-800"
    };
    return <Badge className={colors[severity as keyof typeof colors]}>{severity}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      "Open": "bg-red-100 text-red-800",
      "In Progress": "bg-blue-100 text-blue-800",
      "Closed": "bg-green-100 text-green-800"
    };
    return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Gemba Walk Findings</CardTitle>
          <div className="flex gap-4 items-center">
            <div className="flex items-center gap-2 flex-1">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search findings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Open">Open</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Closed">Closed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Area</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Found By</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFindings.map((finding) => (
                <TableRow key={finding.id}>
                  <TableCell className="font-medium">{finding.id}</TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate" title={finding.description}>
                      {finding.description}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(finding.type)}
                      {finding.type}
                    </div>
                  </TableCell>
                  <TableCell>{finding.area}</TableCell>
                  <TableCell>{getSeverityBadge(finding.severity)}</TableCell>
                  <TableCell>{getStatusBadge(finding.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{finding.foundBy}</div>
                      <div className="text-sm text-gray-500">{finding.foundDate}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onConvertToCAPA(finding.id)}
                        disabled={finding.status === "Closed"}
                      >
                        Convert to CAPA
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredFindings.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No findings found matching your criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

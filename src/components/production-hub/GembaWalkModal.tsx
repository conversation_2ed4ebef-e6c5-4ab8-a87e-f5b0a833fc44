
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Camera, Save, Mic, MicOff } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface GembaWalkModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: GembaWalkData) => void;
  onSubmit?: (data: GembaWalkData) => void;
}

interface GembaWalkData {
  area: string;
  checklist: {
    safety: boolean;
    fiveS: boolean;
    processCompliance: boolean;
  };
  notes: string;
  images: File[];
  observer: string;
}

const areas = [
  'Production Floor A',
  'Production Floor B',
  'Quality Control Lab',
  'Packaging Area',
  'Warehouse',
  'Maintenance Shop'
];

export const GembaWalkModal: React.FC<GembaWalkModalProps> = ({ 
  open, 
  onClose, 
  onSave,
  onSubmit
}) => {
  const [area, setArea] = useState('');
  const [checklist, setChecklist] = useState({
    safety: false,
    fiveS: false,
    processCompliance: false
  });
  const [notes, setNotes] = useState('');
  const [images, setImages] = useState<File[]>([]);
  const [observer, setObserver] = useState('');
  const [isListening, setIsListening] = useState(false);

  const handleSave = () => {
    const data: GembaWalkData = {
      area,
      checklist,
      notes,
      images,
      observer
    };
    onSave(data);
    onClose();
    resetForm();
  };

  const handleSubmit = () => {
    const data: GembaWalkData = {
      area,
      checklist,
      notes,
      images,
      observer
    };
    
    if (onSubmit) {
      onSubmit(data);
    } else {
      onSave(data);
    }
    
    onClose();
    resetForm();
  };

  const resetForm = () => {
    setArea('');
    setChecklist({ safety: false, fiveS: false, processCompliance: false });
    setNotes('');
    setImages([]);
    setObserver('');
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newImages = Array.from(event.target.files);
      setImages(prev => [...prev, ...newImages]);
    }
  };

  const toggleVoiceToText = () => {
    if (!isListening && 'webkitSpeechRecognition' in window) {
      const recognition = new (window as any).webkitSpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';

      recognition.onstart = () => setIsListening(true);
      recognition.onend = () => setIsListening(false);
      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setNotes(prev => prev + (prev ? ' ' : '') + transcript);
      };

      recognition.start();
    } else {
      setIsListening(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Gemba Walk Checklist</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 p-1">
          {/* Observer */}
          <div className="space-y-2">
            <Label htmlFor="observer" className="font-bold">Observer</Label>
            <Input
              id="observer"
              value={observer}
              onChange={(e) => setObserver(e.target.value)}
              placeholder="Enter observer name"
              className="text-base p-3"
            />
          </div>

          {/* Area Selection */}
          <div className="space-y-2">
            <Label htmlFor="area" className="font-bold">Area</Label>
            <Select value={area} onValueChange={setArea}>
              <SelectTrigger className="text-base p-3 h-12">
                <SelectValue placeholder="Select area to observe" />
              </SelectTrigger>
              <SelectContent>
                {areas.map((areaOption) => (
                  <SelectItem key={areaOption} value={areaOption}>
                    {areaOption}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Checklist */}
          <div className="space-y-4">
            <Label className="text-base font-bold">Checklist Items</Label>
            <div className="space-y-4">
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <Checkbox
                  id="safety"
                  checked={checklist.safety}
                  onCheckedChange={(checked) => 
                    setChecklist(prev => ({ ...prev, safety: !!checked }))
                  }
                  className="h-5 w-5"
                />
                <Label htmlFor="safety" className="text-base cursor-pointer font-medium">
                  Safety Compliance
                </Label>
              </div>
              
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <Checkbox
                  id="fiveS"
                  checked={checklist.fiveS}
                  onCheckedChange={(checked) => 
                    setChecklist(prev => ({ ...prev, fiveS: !!checked }))
                  }
                  className="h-5 w-5"
                />
                <Label htmlFor="fiveS" className="text-base cursor-pointer font-medium">
                  5S Implementation
                </Label>
              </div>
              
              <div className="flex items-center space-x-3 p-3 border rounded-lg">
                <Checkbox
                  id="processCompliance"
                  checked={checklist.processCompliance}
                  onCheckedChange={(checked) => 
                    setChecklist(prev => ({ ...prev, processCompliance: !!checked }))
                  }
                  className="h-5 w-5"
                />
                <Label htmlFor="processCompliance" className="text-base cursor-pointer font-medium">
                  Process Compliance
                </Label>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Label htmlFor="notes" className="font-bold">Notes & Observations</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={toggleVoiceToText}
                className="gap-2"
              >
                {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                {isListening ? 'Stop' : 'Voice'}
              </Button>
            </div>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter observations, findings, and recommendations..."
              rows={4}
              className="text-base p-3"
            />
          </div>

          {/* Image Upload */}
          <div className="space-y-2">
            <Label className="font-bold">Upload Images</Label>
            <div className="flex items-center gap-3">
              <Button
                type="button"
                variant="outline"
                className="gap-2 text-base p-3 h-12"
                onClick={() => document.getElementById('image-upload')?.click()}
              >
                <Camera className="h-5 w-5" />
                Take Photo / Upload
              </Button>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                capture="environment"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>
            {images.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {images.map((image, index) => (
                  <Badge key={index} variant="secondary">
                    {image.name}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 text-base p-3 h-12"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              variant="outline"
              className="flex-1 gap-2 text-base p-3 h-12"
              disabled={!area || !observer}
            >
              <Save className="h-5 w-5" />
              Save Draft
            </Button>
            <Button
              onClick={handleSubmit}
              className="flex-1 gap-2 text-base p-3 h-12"
              disabled={!area || !observer}
            >
              <Save className="h-5 w-5" />
              Submit & Create Findings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

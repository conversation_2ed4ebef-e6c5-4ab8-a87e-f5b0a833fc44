
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { X, Upload } from 'lucide-react';

interface LogDeviationModalProps {
  stepName: string;
  onClose: () => void;
  onSubmit: (deviationData: any) => void;
}

export const LogDeviationModal: React.FC<LogDeviationModalProps> = ({ stepName, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    step: stepName,
    type: '',
    description: '',
    immediateAction: '',
    escalateToQA: false,
    file: null as File | null
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({ ...prev, file }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Log Deviation</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div>
              <Label htmlFor="step">Step</Label>
              <Input
                id="step"
                value={formData.step}
                disabled
                className="bg-gray-50"
              />
            </div>

            <div>
              <Label htmlFor="type">Type</Label>
              <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select deviation type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equipment-failure">Equipment Failure</SelectItem>
                  <SelectItem value="material-issue">Material Issue</SelectItem>
                  <SelectItem value="process-deviation">Process Deviation</SelectItem>
                  <SelectItem value="safety-incident">Safety Incident</SelectItem>
                  <SelectItem value="quality-issue">Quality Issue</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Motor stopped mid-process"
                className="min-h-[100px]"
                required
              />
            </div>

            <div>
              <Label htmlFor="file">Photo/Evidence</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <input
                  type="file"
                  id="file"
                  accept="image/*,.pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <label
                  htmlFor="file"
                  className="flex flex-col items-center cursor-pointer"
                >
                  <Upload className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-sm text-gray-600">
                    {formData.file ? formData.file.name : 'Tap to upload photo or document'}
                  </span>
                </label>
              </div>
            </div>

            <div>
              <Label htmlFor="immediateAction">Immediate Action Taken</Label>
              <Textarea
                id="immediateAction"
                value={formData.immediateAction}
                onChange={(e) => setFormData(prev => ({ ...prev, immediateAction: e.target.value }))}
                placeholder="Switched to backup equipment"
                className="min-h-[80px]"
                required
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="escalateToQA">Escalate to QA?</Label>
              <Switch
                id="escalateToQA"
                checked={formData.escalateToQA}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, escalateToQA: checked }))}
              />
            </div>
          </div>

          {/* Footer */}
          <div className="border-t p-4">
            <div className="flex gap-3">
              <Button variant="outline" className="flex-1" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="flex-1"
                disabled={!formData.description || !formData.immediateAction || !formData.type}
              >
                Submit Deviation
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

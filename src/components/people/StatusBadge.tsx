
import React from "react";

interface StatusBadgeProps {
  status: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getColorClasses = () => {
    switch (status.toLowerCase()) {
      case "on duty":
        return "bg-green-100 text-green-700 border-green-200";
      case "on vacation":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "suspended":
        return "bg-red-100 text-red-700 border-red-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getColorClasses()}`}>
      {status}
    </span>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Settings, Users, Shield, Clock, Plus, Trash2, Edit } from "lucide-react";

interface Department {
  id: string;
  name: string;
  manager: string;
  employeeCount: number;
  status: "Active" | "Inactive";
}

interface Role {
  id: string;
  name: string;
  permissions: string[];
  userCount: number;
}

interface PeopleSettingsProps {
  onBack?: () => void;
}

const mockDepartments: Department[] = [
  { id: "DEPT001", name: "Quality Assurance", manager: "<PERSON>", employeeCount: 15, status: "Active" },
  { id: "DEPT002", name: "<PERSON>", manager: "<PERSON>", employeeCount: 42, status: "Active" },
  { id: "DEPT003", name: "Engineering", manager: "David <PERSON>", employeeCount: 28, status: "Active" },
  { id: "DEPT004", name: "Human Resources", manager: "Lisa Thompson", employeeCount: 8, status: "Active" },
  { id: "DEPT005", name: "Finance", manager: "Robert <PERSON>", employeeCount: 12, status: "Active" },
];

const mockRoles: Role[] = [
  { id: "ROLE001", name: "Administrator", permissions: ["Full Access"], userCount: 3 },
  { id: "ROLE002", name: "Manager", permissions: ["View All", "Edit Team", "Reports"], userCount: 12 },
  { id: "ROLE003", name: "Team Lead", permissions: ["View Team", "Edit Team"], userCount: 8 },
  { id: "ROLE004", name: "Employee", permissions: ["View Own"], userCount: 67 },
  { id: "ROLE005", name: "Auditor", permissions: ["View All", "Audit Access"], userCount: 5 },
];

export const PeopleSettings: React.FC<PeopleSettingsProps> = ({ onBack }) => {
  const [departments, setDepartments] = useState<Department[]>(mockDepartments);
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [settings, setSettings] = useState({
    autoApproveRegistrations: false,
    requireManagerApproval: true,
    enableSelfService: true,
    mandatoryTraining: true,
    passwordComplexity: true,
    sessionTimeout: 480, // minutes
    maxLoginAttempts: 5,
  });

  const getStatusBadge = (status: string) => {
    return status === "Active" ? (
      <Badge className="bg-green-100 text-green-800">Active</Badge>
    ) : (
      <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Settings className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold">People Management Settings</h2>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-approve">Auto-approve registrations</Label>
                <Switch
                  id="auto-approve"
                  checked={settings.autoApproveRegistrations}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, autoApproveRegistrations: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="manager-approval">Require manager approval for changes</Label>
                <Switch
                  id="manager-approval"
                  checked={settings.requireManagerApproval}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, requireManagerApproval: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="self-service">Enable employee self-service</Label>
                <Switch
                  id="self-service"
                  checked={settings.enableSelfService}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, enableSelfService: checked }))
                  }
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="mandatory-training">Mandatory training enforcement</Label>
                <Switch
                  id="mandatory-training"
                  checked={settings.mandatoryTraining}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, mandatoryTraining: checked }))
                  }
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="session-timeout">Session timeout (minutes)</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={settings.sessionTimeout}
                  onChange={(e) =>
                    setSettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))
                  }
                  className="w-32"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="max-attempts">Max login attempts</Label>
                <Input
                  id="max-attempts"
                  type="number"
                  value={settings.maxLoginAttempts}
                  onChange={(e) =>
                    setSettings(prev => ({ ...prev, maxLoginAttempts: parseInt(e.target.value) }))
                  }
                  className="w-32"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Departments Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Departments
            </CardTitle>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add Department
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Department</TableHead>
                <TableHead>Manager</TableHead>
                <TableHead>Employees</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departments.map((dept) => (
                <TableRow key={dept.id}>
                  <TableCell className="font-medium">{dept.name}</TableCell>
                  <TableCell>{dept.manager}</TableCell>
                  <TableCell>{dept.employeeCount}</TableCell>
                  <TableCell>{getStatusBadge(dept.status)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Roles & Permissions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Roles & Permissions
            </CardTitle>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add Role
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Role</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">{role.name}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {role.permissions.map((permission, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{role.userCount}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Save Settings */}
      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={onBack}>
          Cancel
        </Button>
        <Button>
          Save Settings
        </Button>
      </div>
    </div>
  );
};

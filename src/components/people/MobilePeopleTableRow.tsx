
import React from "react";
import { Person } from "@/types/people";
import { StatusBadge } from "./StatusBadge";
import { TableCell, TableRow } from "@/components/ui/table";

interface MobilePeopleTableRowProps {
  person: Person;
  index: number;
  onClick: () => void;
  isSelected: boolean;
}

export const MobilePeopleTableRow: React.FC<MobilePeopleTableRowProps> = ({ 
  person, 
  index, 
  onClick, 
  isSelected 
}) => {
  return (
    <TableRow 
      className={`cursor-pointer transition-colors ${
        isSelected ? 'bg-blue-50 border-blue-200' : ''
      }`}
      onClick={onClick}
    >
      <TableCell className="p-4">
        <div className="space-y-2">
          <div className="flex justify-between items-start">
            <div>
              <div className="font-medium text-blue-600 hover:text-blue-800 text-sm cursor-pointer">
                {person.name}
              </div>
              <div className="text-xs text-gray-500">{person.id}</div>
            </div>
            <StatusBadge status={person.status} />
          </div>
          <div className="text-xs text-gray-600">
            <div>{person.jobTitle}</div>
            <div>{person.department}</div>
            <div>{person.email}</div>
          </div>
        </div>
      </TableCell>
    </TableRow>
  );
};

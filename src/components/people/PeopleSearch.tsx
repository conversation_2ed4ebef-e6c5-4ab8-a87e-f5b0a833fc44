
import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface PeopleSearchProps {
  value: string;
  onChange: (value: string) => void;
}

export const PeopleSearch: React.FC<PeopleSearchProps> = ({ value, onChange }) => {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        type="text"
        placeholder="Search employees..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10"
      />
    </div>
  );
};

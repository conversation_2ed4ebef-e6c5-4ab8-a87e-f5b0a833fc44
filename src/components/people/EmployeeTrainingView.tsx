
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Play, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  BookOpen,
  Video,
  Users,
  Calendar
} from "lucide-react";
import { TrainingSessionManager } from "@/components/training/TrainingSessionManager";
import { mockTrainingData } from "./mockData/trainingData";
import { EmployeeTraining } from "@/types/training";

export const EmployeeTrainingView: React.FC = () => {
  const [activeTraining, setActiveTraining] = useState<EmployeeTraining | null>(null);
  const [trainings] = useState<EmployeeTraining[]>(mockTrainingData);

  const handleStartTraining = (training: EmployeeTraining) => {
    setActiveTraining(training);
  };

  const handleCompleteTraining = () => {
    setActiveTraining(null);
  };

  const handleExitTraining = () => {
    setActiveTraining(null);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Completed
        </Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
          <Clock className="h-3 w-3" />
          In Progress
        </Badge>;
      case "Not Started":
        return <Badge className="bg-gray-100 text-gray-800 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Not Started
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTrainingIcon = (type: string) => {
    return type === "Video" ? (
      <Video className="h-4 w-4 text-blue-600" />
    ) : (
      <BookOpen className="h-4 w-4 text-green-600" />
    );
  };

  if (activeTraining) {
    return (
      <TrainingSessionManager
        training={activeTraining}
        onComplete={handleCompleteTraining}
        onExit={handleExitTraining}
      />
    );
  }

  const notStartedTrainings = trainings.filter(t => t.status === "Not Started");
  const inProgressTrainings = trainings.filter(t => t.status === "In Progress");
  const completedTrainings = trainings.filter(t => t.status === "Completed");

  return (
    <div className="space-y-6">
      {/* Training Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Trainings</p>
                <p className="text-2xl font-bold">{trainings.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Progress</p>
                <p className="text-2xl font-bold">{inProgressTrainings.length}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{completedTrainings.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold">{notStartedTrainings.length}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Training Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Trainings</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="progress">In Progress</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {trainings.map((training) => (
              <Card key={training.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getTrainingIcon(training.training.type)}
                      <CardTitle className="text-lg">{training.training.title}</CardTitle>
                    </div>
                    {getStatusBadge(training.status)}
                  </div>
                  <p className="text-sm text-gray-600">{training.training.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Duration:</span>
                      <p>{training.training.estimatedDuration} min</p>
                    </div>
                    <div>
                      <span className="font-medium">Due Date:</span>
                      <p className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(training.dueDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {training.progress > 0 && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{training.progress}%</span>
                      </div>
                      <Progress value={training.progress} className="h-2" />
                    </div>
                  )}

                  <div className="flex gap-2">
                    {training.status !== "Completed" && (
                      <Button 
                        onClick={() => handleStartTraining(training)}
                        className="flex-1 gap-2"
                      >
                        <Play className="h-4 w-4" />
                        {training.status === "Not Started" ? "Start" : "Continue"}
                      </Button>
                    )}
                    
                    {training.status === "Completed" && training.quizScore && (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">Score: {training.quizScore}%</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {notStartedTrainings.map((training) => (
              <Card key={training.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getTrainingIcon(training.training.type)}
                      <CardTitle className="text-lg">{training.training.title}</CardTitle>
                    </div>
                    {getStatusBadge(training.status)}
                  </div>
                  <p className="text-sm text-gray-600">{training.training.description}</p>
                </CardHeader>
                <CardContent>
                  <Button 
                    onClick={() => handleStartTraining(training)}
                    className="w-full gap-2"
                  >
                    <Play className="h-4 w-4" />
                    Start Training
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {inProgressTrainings.map((training) => (
              <Card key={training.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getTrainingIcon(training.training.type)}
                      <CardTitle className="text-lg">{training.training.title}</CardTitle>
                    </div>
                    {getStatusBadge(training.status)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{training.progress}%</span>
                    </div>
                    <Progress value={training.progress} className="h-2" />
                  </div>
                  <Button 
                    onClick={() => handleStartTraining(training)}
                    className="w-full gap-2"
                  >
                    <Play className="h-4 w-4" />
                    Continue Training
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {completedTrainings.map((training) => (
              <Card key={training.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      {getTrainingIcon(training.training.type)}
                      <CardTitle className="text-lg">{training.training.title}</CardTitle>
                    </div>
                    {getStatusBadge(training.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  {training.quizScore && (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Completed with score: {training.quizScore}%
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

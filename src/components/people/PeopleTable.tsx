
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, ChevronDown, ChevronUp, ArrowUpDown } from "lucide-react";
import { TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { Person } from "@/types/people";
import { MobilePeopleTableHeader } from "./MobilePeopleTableHeader";
import { MobilePeopleTableRow } from "./MobilePeopleTableRow";

interface PeopleTableProps {
  people: Person[];
  searchTerm: string;
}

export const PeopleTable: React.FC<PeopleTableProps> = ({ people, searchTerm }) => {
  const navigate = useNavigate();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [sortField, setSortField] = useState<keyof Person>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  const toggleRowExpansion = (personId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(personId)) {
      newExpanded.delete(personId);
    } else {
      newExpanded.add(personId);
    }
    setExpandedRows(newExpanded);
  };

  const handleViewEmployee = (personId: string) => {
    console.log("Navigating to employee detail:", personId);
    navigate(`/people/${personId}`);
  };

  const handleSort = (field: keyof Person) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: keyof Person) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === 'asc' 
      ? <ChevronUp className="ml-1 h-4 w-4" />
      : <ChevronDown className="ml-1 h-4 w-4" />;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      case "On Leave":
        return <Badge className="bg-yellow-100 text-yellow-800">On Leave</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const sortedPeople = [...people].sort((a, b) => {
    if (!sortField) return 0;
    
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortDirection === 'asc' ? comparison : -comparison;
    }
    
    if (Array.isArray(aValue) && Array.isArray(bValue)) {
      const aLength = aValue.length;
      const bLength = bValue.length;
      return sortDirection === 'asc' ? aLength - bLength : bLength - aLength;
    }
    
    return 0;
  });

  if (people.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No employees found matching your search criteria.</p>
      </div>
    );
  }

  return (
    <div className="w-full min-w-0">
      {/* Mobile View */}
      <div className="block lg:hidden w-full min-w-0">
        <MobilePeopleTableHeader 
          sortField={sortField as any}
          sortDirection={sortDirection as any}
          onSort={handleSort as any}
        />
        <div className="space-y-2">
          {sortedPeople.map((person, index) => (
            <MobilePeopleTableRow
              key={person.id}
              person={person}
              index={index}
              onClick={() => handleViewEmployee(person.id)}
              isSelected={false}
            />
          ))}
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden lg:block w-full min-w-0">
        <div className="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden">
          <ResizableTable className="w-full" minColumnWidth={120}>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Employee
                    {getSortIcon('name')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('jobTitle')}
                >
                  <div className="flex items-center">
                    Job Title
                    {getSortIcon('jobTitle')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('department')}
                >
                  <div className="flex items-center">
                    Department
                    {getSortIcon('department')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center">
                    Email
                    {getSortIcon('email')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('processes')}
                >
                  <div className="flex items-center">
                    Processes
                    {getSortIcon('processes')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center">
                    Status
                    {getSortIcon('status')}
                  </div>
                </TableHead>
                <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedPeople.map((person, index) => (
                <TableRow 
                  key={person.id} 
                  className={`cursor-pointer hover:bg-gray-50 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                  onClick={() => handleViewEmployee(person.id)}
                >
                  <TableCell>
                    <div>
                      <div 
                        className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewEmployee(person.id);
                        }}
                      >
                        {person.name}
                      </div>
                      <div className="text-sm text-gray-500">ID: {person.id}</div>
                    </div>
                  </TableCell>
                  <TableCell>{person.jobTitle}</TableCell>
                  <TableCell>{person.department || "Not assigned"}</TableCell>
                  <TableCell>{person.email || "Not provided"}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {person.processes.slice(0, 2).map((process) => (
                        <Badge key={process} variant="outline" className="text-xs">
                          {process}
                        </Badge>
                      ))}
                      {person.processes.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{person.processes.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(person.status)}</TableCell>
                  <TableCell>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewEmployee(person.id);
                      }}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </ResizableTable>
        </div>
      </div>
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Trophy, Award, Calendar, Users, Plus, Download, AlertTriangle, CheckCircle } from 'lucide-react';
import { formatDate } from '@/utils/dateUtils';

const certifications = [
  {
    id: '1',
    name: 'ISO 9001 Lead Auditor',
    holder: '<PERSON>',
    issueDate: '2024-03-15',
    expiryDate: '2026-03-15',
    status: 'Active',
    credentialId: 'ISO-9001-LA-2024-001',
    issuingBody: 'International Organization for Standardization',
    category: 'Quality Management'
  },
  {
    id: '2',
    name: 'Safety Officer Certification',
    holder: '<PERSON>',
    issueDate: '2024-01-20',
    expiryDate: '2025-01-20',
    status: 'Expiring Soon',
    credentialId: 'SOC-2024-002',
    issuingBody: 'Occupational Safety and Health Administration',
    category: 'Safety'
  },
  {
    id: '3',
    name: 'GMP Compliance Specialist',
    holder: 'Emma Davis',
    issueDate: '2023-11-10',
    expiryDate: '2024-11-10',
    status: 'Expired',
    credentialId: 'GMP-CS-2023-003',
    issuingBody: 'Food and Drug Administration',
    category: 'Compliance'
  },
  {
    id: '4',
    name: 'Lean Six Sigma Green Belt',
    holder: 'Alex Rodriguez',
    issueDate: '2024-05-08',
    expiryDate: '2027-05-08',
    status: 'Active',
    credentialId: 'LSS-GB-2024-004',
    issuingBody: 'American Society for Quality',
    category: 'Process Improvement'
  }
];

const certificationPrograms = [
  {
    name: 'ISO 13485 Medical Device Quality',
    duration: '5 days',
    cost: '$2,500',
    provider: 'Quality Institute',
    nextSession: '2024-07-15'
  },
  {
    name: 'FDA 21 CFR Part 820 Specialist',
    duration: '3 days',
    cost: '$1,800',
    provider: 'Regulatory Academy',
    nextSession: '2024-08-10'
  },
  {
    name: 'Risk Management Professional',
    duration: '4 days',
    cost: '$2,200',
    provider: 'Risk Institute',
    nextSession: '2024-07-25'
  }
];

export const CertificationManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('current');
  const [searchTerm, setSearchTerm] = useState('');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'Expiring Soon':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="h-3 w-3 mr-1" />Expiring Soon</Badge>;
      case 'Expired':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getExpiryStatus = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Expired';
    if (diffDays <= 30) return 'Expiring Soon';
    return 'Active';
  };

  const exportCertifications = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      certifications: certifications.map(cert => ({
        ...cert,
        expiryStatus: getExpiryStatus(cert.expiryDate)
      }))
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `certifications-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const filteredCertifications = certifications.filter(cert =>
    cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.holder.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Certification Management</h2>
          <p className="text-gray-600">Track and manage professional certifications and credentials</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportCertifications}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Certification
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
            <div className="text-2xl font-bold">45</div>
            <div className="text-sm text-gray-600">Total Certifications</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold">38</div>
            <div className="text-sm text-gray-600">Active</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
            <div className="text-2xl font-bold">5</div>
            <div className="text-sm text-gray-600">Expiring Soon</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <div className="text-2xl font-bold">2</div>
            <div className="text-sm text-gray-600">Expired</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different views */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('current')}
            className={`whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium ${
              activeTab === 'current'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            Current Certifications
          </button>
          <button
            onClick={() => setActiveTab('programs')}
            className={`whitespace-nowrap border-b-2 py-2 px-1 text-sm font-medium ${
              activeTab === 'programs'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
            }`}
          >
            Available Programs
          </button>
        </nav>
      </div>

      {activeTab === 'current' && (
        <div className="space-y-4">
          {/* Search */}
          <div className="relative max-w-md">
            <Input
              placeholder="Search certifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Certifications Table */}
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Certification</TableHead>
                    <TableHead>Holder</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Issue Date</TableHead>
                    <TableHead>Expiry Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCertifications.map((cert) => (
                    <TableRow key={cert.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{cert.name}</div>
                          <div className="text-sm text-gray-600">{cert.credentialId}</div>
                        </div>
                      </TableCell>
                      <TableCell>{cert.holder}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{cert.category}</Badge>
                      </TableCell>
                      <TableCell>{formatDate(cert.issueDate)}</TableCell>
                      <TableCell>{formatDate(cert.expiryDate)}</TableCell>
                      <TableCell>{getStatusBadge(getExpiryStatus(cert.expiryDate))}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm">
                            <Award className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'programs' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Available Certification Programs</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {certificationPrograms.map((program, index) => (
              <Card key={index}>
                <CardContent className="p-6 space-y-4">
                  <div>
                    <h4 className="font-semibold text-lg">{program.name}</h4>
                    <p className="text-sm text-gray-600">by {program.provider}</p>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-medium">{program.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cost:</span>
                      <span className="font-medium">{program.cost}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Next Session:</span>
                      <span className="font-medium">{formatDate(program.nextSession)}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1">Enroll</Button>
                    <Button variant="outline" size="sm">Details</Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};


import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Line, Bar, CartesianGrid, XAxis, YAxis } from 'recharts';
import { learningROIData } from '../data/analyticsData';

export const LearningROIChart: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Learning ROI Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={{
          trainingCost: { label: "Training Cost ($)", color: "#82ca9d" },
          roi: { label: "ROI %", color: "#8884d8" }
        }} className="h-[300px]">
          <LineChart data={learningROIData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <ChartTooltip 
              content={<ChartTooltipContent />}
              formatter={(value: number, name: string) => [
                name === 'roi' ? `${value}%` : `$${value.toLocaleString()}`,
                name
              ]} 
            />
            <Bar yAxisId="left" dataKey="trainingCost" fill="#82ca9d" name="Training Cost ($)" />
            <Line yAxisId="right" type="monotone" dataKey="roi" stroke="#8884d8" strokeWidth={3} name="ROI %" />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

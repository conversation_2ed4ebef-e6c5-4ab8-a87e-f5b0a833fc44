
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON><PERSON>, Bar, CartesianGrid, XAxis, YAxis } from 'recharts';
import { skillCompetencyData, skillsChartConfig } from '../data/analyticsData';

export const SkillsProficiencyChart: React.FC = () => {
  console.log('Skills Competency Data:', skillCompetencyData);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Skills Proficiency Matrix</CardTitle>
      </CardHeader>
      <CardContent>
        {skillCompetencyData && skillCompetencyData.length > 0 ? (
          <ChartContainer config={skillsChartConfig} className="h-[400px] w-full">
            <BarChart 
              data={skillCompetencyData} 
              layout="horizontal"
              margin={{ top: 20, right: 40, left: 200, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                type="number" 
                domain={[0, 100]} 
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                dataKey="skill" 
                type="category" 
                width={180}
                fontSize={12}
                interval={0}
                tick={{ fontSize: 12 }}
                tickLine={false}
                axisLine={false}
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  `${value}%`,
                  name === 'expert' ? 'Expert' : 
                  name === 'competent' ? 'Competent' : 'Novice'
                ]}
              />
              <Bar 
                dataKey="expert" 
                stackId="a" 
                fill="#0088FE" 
                name="Expert"
                radius={[0, 2, 2, 0]}
              />
              <Bar 
                dataKey="competent" 
                stackId="a" 
                fill="#00C49F" 
                name="Competent"
              />
              <Bar 
                dataKey="novice" 
                stackId="a" 
                fill="#FFBB28" 
                name="Novice"
                radius={[2, 0, 0, 2]}
              />
            </BarChart>
          </ChartContainer>
        ) : (
          <div className="flex items-center justify-center h-[400px] text-gray-500">
            <div className="text-center">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <p>No skills data available</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

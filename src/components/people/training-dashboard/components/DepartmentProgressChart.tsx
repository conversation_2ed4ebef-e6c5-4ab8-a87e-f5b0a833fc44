
import React from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, CartesianGrid, XAxis, YAxis } from 'recharts';
import { departmentProgress } from '../data/analyticsData';

export const DepartmentProgressChart: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Progress by Department</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={{
          rate: { label: "Completion Rate %", color: "#8884d8" }
        }} className="h-[300px]">
          <BarChart data={departmentProgress}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="department" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey="rate" fill="#8884d8" name="Completion Rate %" />
          </Bar<PERSON>hart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

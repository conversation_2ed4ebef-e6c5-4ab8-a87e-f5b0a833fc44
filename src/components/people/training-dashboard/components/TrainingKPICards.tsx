
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TrendingUp, Clock, Users, Target } from 'lucide-react';

export const TrainingKPICards: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-3xl font-bold">85.7%</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="h-4 w-4 mr-1" />
                +12% vs last period
              </p>
            </div>
            <Target className="h-12 w-12 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Session Time</p>
              <p className="text-3xl font-bold">45</p>
              <p className="text-sm text-gray-600">minutes</p>
              <p className="text-sm text-blue-600 flex items-center mt-1">
                <Clock className="h-4 w-4 mr-1" />
                +8 min improvement
              </p>
            </div>
            <Clock className="h-12 w-12 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Learning ROI</p>
              <p className="text-3xl font-bold">124%</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <TrendingUp className="h-4 w-4 mr-1" />
                +18% increase
              </p>
            </div>
            <TrendingUp className="h-12 w-12 text-purple-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Learners</p>
              <p className="text-3xl font-bold">200</p>
              <p className="text-sm text-green-600 flex items-center mt-1">
                <Users className="h-4 w-4 mr-1" />
                +25 new this month
              </p>
            </div>
            <Users className="h-12 w-12 text-orange-500" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};


import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

export const DetailedMetrics: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Detailed Learning Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-3">
            <h4 className="font-semibold">Engagement Metrics</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Daily Active Users:</span>
                <span className="font-medium">45</span>
              </div>
              <div className="flex justify-between">
                <span>Weekly Active Users:</span>
                <span className="font-medium">125</span>
              </div>
              <div className="flex justify-between">
                <span>Average Session Duration:</span>
                <span className="font-medium">45 min</span>
              </div>
              <div className="flex justify-between">
                <span>Course Drop-off Rate:</span>
                <span className="font-medium">15%</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Performance Metrics</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Average Quiz Score:</span>
                <span className="font-medium">87%</span>
              </div>
              <div className="flex justify-between">
                <span>First-time Pass Rate:</span>
                <span className="font-medium">78%</span>
              </div>
              <div className="flex justify-between">
                <span>Time to Competency:</span>
                <span className="font-medium">6.5 weeks</span>
              </div>
              <div className="flex justify-between">
                <span>Knowledge Retention:</span>
                <span className="font-medium">91%</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Business Impact</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Productivity Improvement:</span>
                <span className="font-medium">18%</span>
              </div>
              <div className="flex justify-between">
                <span>Error Reduction:</span>
                <span className="font-medium">25%</span>
              </div>
              <div className="flex justify-between">
                <span>Employee Satisfaction:</span>
                <span className="font-medium">4.2/5</span>
              </div>
              <div className="flex justify-between">
                <span>Training Cost per Employee:</span>
                <span className="font-medium">$190</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

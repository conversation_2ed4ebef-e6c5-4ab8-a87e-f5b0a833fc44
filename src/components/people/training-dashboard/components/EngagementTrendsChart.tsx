
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { AreaChart, Area, CartesianGrid, XAxis, YAxis } from 'recharts';
import { engagementData } from '../data/analyticsData';

export const EngagementTrendsChart: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Learning Engagement Trends</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={{
          coursesCompleted: { label: "Completed", color: "#8884d8" },
          coursesStarted: { label: "Started", color: "#82ca9d" }
        }} className="h-[300px]">
          <AreaChart data={engagementData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area type="monotone" dataKey="coursesCompleted" stackId="1" stroke="#8884d8" fill="#8884d8" />
            <Area type="monotone" dataKey="coursesStarted" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};


import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Plus, User } from "lucide-react";
import { Person } from "@/types/people";
import { AssignTrainingDialog } from "../AssignTrainingDialog";
import { useTraining } from "@/contexts/TrainingContext";

interface TrainingAssignmentCardProps {
  employee: Person;
}

export const TrainingAssignmentCard: React.FC<TrainingAssignmentCardProps> = ({ employee }) => {
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const { employeeTrainings } = useTraining();

  const employeeTrainingCount = employeeTrainings.filter(t => t.employeeId === employee.id);
  const completedCount = employeeTrainingCount.filter(t => t.status === "Completed").length;
  const overdueCount = employeeTrainingCount.filter(t => t.status === "In Progress" && new Date(t.dueDate) < new Date()).length;

  return (
    <>
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarFallback>
                  {employee.firstName?.charAt(0)}{employee.lastName?.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h4 className="font-medium">{employee.name}</h4>
                <p className="text-sm text-gray-600">{employee.jobTitle}</p>
                <p className="text-xs text-gray-500">{employee.department}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-right">
                <div className="flex gap-2 mb-1">
                  <Badge variant="outline" className="text-xs">
                    {employeeTrainingCount.length} Assigned
                  </Badge>
                  <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                    {completedCount} Completed
                  </Badge>
                  {overdueCount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {overdueCount} Overdue
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-500">ID: {employee.id}</p>
              </div>
              
              <Button 
                onClick={() => setShowAssignDialog(true)}
                className="gap-2"
                size="sm"
              >
                <Plus className="h-4 w-4" />
                Assign Training
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AssignTrainingDialog 
        open={showAssignDialog}
        onOpenChange={setShowAssignDialog}
        employeeId={employee.id}
      />
    </>
  );
};

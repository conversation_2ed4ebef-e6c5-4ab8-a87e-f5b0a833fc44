
import React, { useState } from 'react';
import { AnalyticsHeader } from './components/AnalyticsHeader';
import { TrainingKPICards } from './components/TrainingKPICards';
import { EngagementTrendsChart } from './components/EngagementTrendsChart';
import { DepartmentProgressChart } from './components/DepartmentProgressChart';
import { SkillsProficiencyChart } from './components/SkillsProficiencyChart';
import { LearningROIChart } from './components/LearningROIChart';
import { DetailedMetrics } from './components/DetailedMetrics';
import { engagementData, departmentProgress, skillCompetencyData, learningROIData } from './data/analyticsData';

export const TrainingAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('6months');

  const exportAnalytics = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      timeRange,
      engagementData,
      departmentProgress,
      skillCompetencyData,
      learningROIData
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `training-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <AnalyticsHeader 
        timeRange={timeRange}
        onTimeRangeChange={setTimeRange}
        onExport={exportAnalytics}
      />

      {/* Key Performance Indicators */}
      <TrainingKPICards />

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <EngagementTrendsChart />
        <DepartmentProgressChart />
      </div>

      {/* Skills Proficiency Chart - Full Width */}
      <SkillsProficiencyChart />

      {/* Learning ROI Chart */}
      <LearningROIChart />

      {/* Detailed Metrics Table */}
      <DetailedMetrics />
    </div>
  );
};


import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, FileText, Video, Clock, Users, Star, Download } from 'lucide-react';

interface TrainingContent {
  id: string;
  title: string;
  type: string;
  duration: string;
  rating: number;
  enrollments: number;
  completions: number;
  category: string;
  difficulty: string;
  thumbnail: string;
  description: string;
}

interface TrainingContentPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  content: TrainingContent | null;
}

export const TrainingContentPreviewModal: React.FC<TrainingContentPreviewModalProps> = ({
  open,
  onOpenChange,
  content
}) => {
  if (!content) return null;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Video': return <Video className="h-4 w-4" />;
      case 'Interactive': return <Play className="h-4 w-4" />;
      case 'Document': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getTypeIcon(content.type)}
            Training Content Preview
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Content Header */}
          <div className="space-y-4">
            <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
              <img 
                src={content.thumbnail} 
                alt={content.title}
                className="w-full h-full object-cover"
              />
            </div>
            
            <div className="space-y-2">
              <h2 className="text-2xl font-bold">{content.title}</h2>
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline" className="gap-1">
                  {getTypeIcon(content.type)}
                  {content.type}
                </Badge>
                <Badge className={getDifficultyColor(content.difficulty)}>
                  {content.difficulty}
                </Badge>
                <Badge variant="outline">{content.category}</Badge>
              </div>
            </div>
          </div>

          {/* Content Stats */}
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{content.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{content.enrollments} enrolled</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-sm">{content.rating}/5</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-green-600">{content.completions} completed</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Description */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2">Description</h3>
              <p className="text-gray-600">{content.description}</p>
            </CardContent>
          </Card>

          {/* Learning Objectives */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2">Learning Objectives</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• Understand the fundamentals of {content.category.toLowerCase()}</li>
                <li>• Apply best practices in daily work activities</li>
                <li>• Demonstrate compliance with industry standards</li>
                <li>• Complete practical assessments successfully</li>
              </ul>
            </CardContent>
          </Card>

          {/* Content Preview */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2">Content Preview</h3>
              <div className="bg-gray-50 rounded-lg p-8 text-center">
                <div className="mb-4">
                  {getTypeIcon(content.type)}
                </div>
                <p className="text-gray-600">
                  {content.type === 'Video' && 'Video content preview would be displayed here'}
                  {content.type === 'Interactive' && 'Interactive module preview would be displayed here'}
                  {content.type === 'Document' && 'Document preview would be displayed here'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-between pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
            <Button>
              <Play className="h-4 w-4 mr-2" />
              Start Training
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

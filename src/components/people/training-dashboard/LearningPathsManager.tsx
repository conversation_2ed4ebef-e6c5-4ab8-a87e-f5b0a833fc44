import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Plus, Users, BookOpen, Clock, Target, Edit, Trash2 } from 'lucide-react';
import { LearningPathDetailModal } from './LearningPathDetailModal';

const learningPaths = [
  {
    id: '1',
    name: 'New Hire Onboarding',
    description: 'Complete orientation program for new employees covering company policies, safety, and basic job skills',
    courses: 8,
    duration: '40 hours',
    enrolled: 25,
    completed: 18,
    progress: 72,
    difficulty: 'Beginner',
    category: 'Onboarding',
    estimatedWeeks: 4
  },
  {
    id: '2',
    name: 'Quality Specialist Certification',
    description: 'Advanced training path for quality management professionals covering ISO standards, auditing, and CAPA',
    courses: 12,
    duration: '80 hours',
    enrolled: 15,
    completed: 12,
    progress: 80,
    difficulty: 'Advanced',
    category: 'Quality',
    estimatedWeeks: 8
  },
  {
    id: '3',
    name: 'Safety Officer Development',
    description: 'Comprehensive safety training including risk assessment, incident investigation, and emergency response',
    courses: 10,
    duration: '60 hours',
    enrolled: 8,
    completed: 6,
    progress: 75,
    difficulty: 'Intermediate',
    category: 'Safety',
    estimatedWeeks: 6
  },
  {
    id: '4',
    name: 'Leadership Excellence Program',
    description: 'Management and leadership skills development for supervisors and team leaders',
    courses: 15,
    duration: '100 hours',
    enrolled: 12,
    completed: 8,
    progress: 67,
    difficulty: 'Advanced',
    category: 'Leadership',
    estimatedWeeks: 10
  }
];

const courseTemplates = [
  { name: 'Company Introduction', duration: '2 hours', type: 'Video' },
  { name: 'Safety Basics', duration: '3 hours', type: 'Interactive' },
  { name: 'Quality Fundamentals', duration: '4 hours', type: 'Document' },
  { name: 'GMP Training', duration: '5 hours', type: 'Video' },
  { name: 'Leadership Skills', duration: '6 hours', type: 'Interactive' }
];

export const LearningPathsManager: React.FC = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [selectedPathForDetail, setSelectedPathForDetail] = useState<any>(null);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Onboarding': return 'bg-blue-100 text-blue-800';
      case 'Quality': return 'bg-purple-100 text-purple-800';
      case 'Safety': return 'bg-orange-100 text-orange-800';
      case 'Leadership': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewDetails = (path: any) => {
    setSelectedPathForDetail(path);
  };

  if (isCreating) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Create Learning Path</h2>
          <Button variant="outline" onClick={() => setIsCreating(false)}>
            Back to Paths
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Learning Path Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Path Name</label>
                <Input placeholder="Enter learning path name" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Category</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option>Onboarding</option>
                  <option>Quality</option>
                  <option>Safety</option>
                  <option>Leadership</option>
                  <option>Technical</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Difficulty Level</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option>Beginner</option>
                  <option>Intermediate</option>
                  <option>Advanced</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Estimated Duration</label>
                <Input placeholder="e.g., 40 hours" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Estimated Weeks</label>
                <Input placeholder="e.g., 4 weeks" />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <textarea 
                className="w-full px-3 py-2 border border-gray-300 rounded-md h-24"
                placeholder="Describe the learning path objectives and target audience"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Add Courses to Path</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {courseTemplates.map((course, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium">{course.name}</h4>
                    <Button size="sm" variant="outline">
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-3 w-3" />
                    {course.duration}
                  </div>
                  <Badge variant="outline" className="text-xs">{course.type}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-2">
          <Button>Create Learning Path</Button>
          <Button variant="outline" onClick={() => setIsCreating(false)}>Cancel</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Learning Paths</h2>
          <p className="text-gray-600">Structured learning journeys for skill development</p>
        </div>
        <Button onClick={() => setIsCreating(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Learning Path
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {learningPaths.map((path) => (
          <Card key={path.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">{path.name}</h3>
                  <div className="flex gap-2">
                    <Badge className={getDifficultyColor(path.difficulty)}>
                      {path.difficulty}
                    </Badge>
                    <Badge className={getCategoryColor(path.category)}>
                      {path.category}
                    </Badge>
                  </div>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">{path.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-gray-400" />
                  <span>{path.courses} courses</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span>{path.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span>{path.enrolled} enrolled</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-400" />
                  <span>{path.estimatedWeeks} weeks</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress: {path.completed}/{path.enrolled} completed</span>
                  <span>{path.progress}%</span>
                </div>
                <Progress value={path.progress} className="h-2" />
              </div>
              
              <div className="flex gap-2">
                <Button size="sm" className="flex-1" onClick={() => handleViewDetails(path)}>
                  View Details
                </Button>
                <Button variant="outline" size="sm">Assign Users</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <LearningPathDetailModal
        open={!!selectedPathForDetail}
        onOpenChange={() => setSelectedPathForDetail(null)}
        learningPath={selectedPathForDetail}
      />
    </div>
  );
};

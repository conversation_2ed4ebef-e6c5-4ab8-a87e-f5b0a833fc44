
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { ChartContainer } from "@/components/ui/chart";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line, ResponsiveContainer } from "recharts";

// Mock data for completion time by department
const departmentCompletionData = [
  { department: "Engineering", avgDays: 12, target: 14 },
  { department: "Quality", avgDays: 8, target: 10 },
  { department: "Manufacturing", avgDays: 15, target: 14 },
  { department: "HR", avgDays: 6, target: 7 },
  { department: "Sales", avgDays: 10, target: 12 }
];

// Mock data for training completion trends
const completionTrendData = [
  { month: "Jan", completed: 85, target: 90 },
  { month: "Feb", completed: 88, target: 90 },
  { month: "Mar", completed: 92, target: 90 },
  { month: "Apr", completed: 89, target: 90 },
  { month: "May", completed: 94, target: 90 },
  { month: "Jun", completed: 91, target: 90 }
];

export const TrainingPerformanceCharts: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Average Completion Time by Department - Full Width */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Average Completion Time by Department</CardTitle>
          <p className="text-sm text-gray-600">Days to complete training assignments</p>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] w-full">
            <ChartContainer config={{
              avgDays: { color: '#3b82f6' },
              target: { color: '#ef4444' },
            }}>
              <BarChart data={departmentCompletionData} height={400}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="department" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="avgDays" name="Avg Days" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                <Bar dataKey="target" name="Target" fill="#ef4444" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>

      {/* Training Completion Trends - Full Width */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Training Completion Trends</CardTitle>
          <p className="text-sm text-gray-600">Monthly completion rates vs targets</p>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] w-full">
            <ChartContainer config={{
              completed: { color: '#22c55e' },
              target: { color: '#f59e0b' },
            }}>
              <LineChart data={completionTrendData} height={400}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[75, 100]} />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="completed" 
                  name="Completed %" 
                  stroke="#22c55e" 
                  strokeWidth={3}
                  dot={{ r: 5 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="target" 
                  name="Target %" 
                  stroke="#f59e0b" 
                  strokeWidth={3}
                  strokeDasharray="5 5"
                  dot={{ r: 5 }}
                />
              </LineChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Play, FileText, Video, Users, Clock, Star, Upload, Edit } from 'lucide-react';
import { TrainingContentPreviewModal } from './TrainingContentPreviewModal';
import { TrainingContentEditModal } from './TrainingContentEditModal';

const trainingContent = [
  {
    id: '1',
    title: 'ISO 9001:2015 Quality Management Fundamentals',
    type: 'Video',
    duration: '45 min',
    rating: 4.8,
    enrollments: 156,
    completions: 142,
    category: 'Quality Management',
    difficulty: 'Intermediate',
    thumbnail: '/lovable-uploads/143bb4e6-dd7c-41f3-9b80-09442b5622ba.png',
    description: 'Comprehensive overview of ISO 9001:2015 requirements and implementation'
  },
  {
    id: '2',
    title: 'Safety Protocols and Emergency Procedures',
    type: 'Interactive',
    duration: '30 min',
    rating: 4.9,
    enrollments: 200,
    completions: 185,
    category: 'Safety',
    difficulty: 'Beginner',
    thumbnail: '/lovable-uploads/206ab8af-c1cb-4dc3-94a5-bdebe69467c8.png',
    description: 'Essential safety training covering emergency procedures and workplace protocols'
  },
  {
    id: '3',
    title: 'GMP Compliance in Manufacturing',
    type: 'Document',
    duration: '60 min',
    rating: 4.7,
    enrollments: 89,
    completions: 78,
    category: 'Compliance',
    difficulty: 'Advanced',
    thumbnail: '/lovable-uploads/55d3f27c-4c35-47eb-9aea-174ad8fbad1f.png',
    description: 'Good Manufacturing Practices for pharmaceutical and medical device industries'
  }
];

export const TrainingContentLibrary: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [activeTab, setActiveTab] = useState('library');
  const [selectedContentForPreview, setSelectedContentForPreview] = useState<any>(null);
  const [selectedContentForEdit, setSelectedContentForEdit] = useState<any>(null);

  const categories = ['all', 'Quality Management', 'Safety', 'Compliance', 'Leadership', 'Technical'];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Video': return <Video className="h-4 w-4" />;
      case 'Interactive': return <Play className="h-4 w-4" />;
      case 'Document': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredContent = trainingContent.filter(content => {
    const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || content.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handlePreview = (content: any) => {
    setSelectedContentForPreview(content);
  };

  const handleEdit = (content: any) => {
    setSelectedContentForEdit(content);
  };

  // Helper to add staggered animation delay per content tile
  const getTileAnimation = (index: number) =>
    `animate-fade-in will-change-transform relative z-10` +
    ` transition-all duration-400 ease-out delay-${index * 75}`;

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="library">Content Library</TabsTrigger>
            <TabsTrigger value="create">Create Content</TabsTrigger>
            <TabsTrigger value="upload">Upload Content</TabsTrigger>
          </TabsList>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Training
          </Button>
        </div>

        <TabsContent value="library" className="space-y-6">
          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search training content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Content Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContent.map((content, idx) => (
              <Card
                key={content.id}
                className={`modern-card hover-lift shadow-md rounded-xl bg-white/90 border border-slate-200/80 overflow-hidden outline-none focus:ring-2 focus:ring-blue-300 transition-all duration-300 ${getTileAnimation(idx)}`}
                style={{ 
                  boxShadow: "0 4px 20px 0 rgba(29,78,216,.05), 0 1.5px 2.5px 0 rgba(17,24,39,.08)" 
                }}
                tabIndex={0}
              >
                <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
                  <img 
                    src={content.thumbnail} 
                    alt={content.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <h3 className="font-semibold text-sm leading-tight">{content.title}</h3>
                    <div className="flex items-center gap-1 text-yellow-500">
                      <Star className="h-3 w-3 fill-current" />
                      <span className="text-xs">{content.rating}</span>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-600 line-clamp-2">{content.description}</p>
                  
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="text-xs">
                      {getTypeIcon(content.type)}
                      <span className="ml-1">{content.type}</span>
                    </Badge>
                    <Badge className={`text-xs ${getDifficultyColor(content.difficulty)}`}>
                      {content.difficulty}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {content.duration}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {content.enrollments} enrolled
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button size="sm" className="flex-1" onClick={() => handlePreview(content)}>
                      <Play className="h-3 w-3 mr-1" />
                      Preview
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleEdit(content)}>
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create New Training Content</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Content Title</label>
                  <Input placeholder="Enter training title" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Content Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option>Video Course</option>
                    <option>Interactive Module</option>
                    <option>Document/PDF</option>
                    <option>Quiz/Assessment</option>
                    <option>Virtual Simulation</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Category</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option>Quality Management</option>
                    <option>Safety</option>
                    <option>Compliance</option>
                    <option>Leadership</option>
                    <option>Technical</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Difficulty</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                    <option>Beginner</option>
                    <option>Intermediate</option>
                    <option>Advanced</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Estimated Duration</label>
                  <Input placeholder="e.g., 45 min" />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <textarea 
                  className="w-full px-3 py-2 border border-gray-300 rounded-md h-24"
                  placeholder="Describe the training content and learning objectives"
                />
              </div>
              
              <div className="flex gap-2">
                <Button>Create Content</Button>
                <Button variant="outline">Save as Draft</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Upload Training Materials</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">Upload Training Files</h3>
                <p className="text-gray-600 mb-4">
                  Drag and drop your files here, or click to browse
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Supported formats: MP4, PDF, PPTX, DOCX, SCORM packages
                </p>
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  Choose Files
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <TrainingContentPreviewModal
        open={!!selectedContentForPreview}
        onOpenChange={() => setSelectedContentForPreview(null)}
        content={selectedContentForPreview}
      />

      <TrainingContentEditModal
        open={!!selectedContentForEdit}
        onOpenChange={() => setSelectedContentForEdit(null)}
        content={selectedContentForEdit}
      />
    </div>
  );
};


import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookOpen, Users, Clock, Star, Play, CheckCircle, Target } from 'lucide-react';

interface LearningPath {
  id: string;
  name: string;
  description: string;
  courses: number;
  duration: string;
  enrolled: number;
  completed: number;
  progress: number;
  difficulty: string;
  category: string;
  estimatedWeeks: number;
}

interface LearningPathDetailModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  learningPath: LearningPath | null;
}

const mockCourses = [
  { id: '1', name: 'Company Introduction', duration: '2 hours', status: 'completed', score: 95 },
  { id: '2', name: 'Safety Fundamentals', duration: '3 hours', status: 'completed', score: 88 },
  { id: '3', name: 'Quality Basics', duration: '4 hours', status: 'in-progress', score: null },
  { id: '4', name: 'Documentation Standards', duration: '3 hours', status: 'not-started', score: null },
  { id: '5', name: 'Compliance Training', duration: '5 hours', status: 'not-started', score: null }
];

const mockEnrolledUsers = [
  { id: '1', name: 'Sarah Johnson', department: 'Quality', progress: 85, status: 'active' },
  { id: '2', name: 'Mike Chen', department: 'Production', progress: 100, status: 'completed' },
  { id: '3', name: 'Emma Davis', department: 'Quality', progress: 45, status: 'active' },
  { id: '4', name: 'Alex Rodriguez', department: 'Safety', progress: 90, status: 'active' }
];

export const LearningPathDetailModal: React.FC<LearningPathDetailModalProps> = ({
  open,
  onOpenChange,
  learningPath
}) => {
  if (!learningPath) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in-progress': return <Play className="h-4 w-4 text-blue-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'completed': 'bg-green-100 text-green-800',
      'in-progress': 'bg-blue-100 text-blue-800',
      'not-started': 'bg-gray-100 text-gray-800',
      'active': 'bg-blue-100 text-blue-800'
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status.replace('-', ' ')}</Badge>;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {learningPath.name}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Learning Path Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Learning Path Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">{learningPath.description}</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{learningPath.courses} courses</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{learningPath.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{learningPath.enrolled} enrolled</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{learningPath.estimatedWeeks} weeks</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Overall Progress</span>
                  <span>{learningPath.progress}%</span>
                </div>
                <Progress value={learningPath.progress} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Course List */}
          <Card>
            <CardHeader>
              <CardTitle>Courses in this Learning Path</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockCourses.map((course, index) => (
                  <div key={course.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{course.name}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Clock className="h-3 w-3" />
                          {course.duration}
                          {course.score && (
                            <>
                              <Star className="h-3 w-3 text-yellow-500" />
                              {course.score}%
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(course.status)}
                      {getStatusBadge(course.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Enrolled Users */}
          <Card>
            <CardHeader>
              <CardTitle>Enrolled Users ({learningPath.enrolled})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {mockEnrolledUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{user.name}</h4>
                      <p className="text-sm text-gray-600">{user.department}</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <div className="text-sm font-medium">{user.progress}%</div>
                        <Progress value={user.progress} className="h-1 w-20" />
                      </div>
                      {getStatusBadge(user.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button>
            Assign More Users
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

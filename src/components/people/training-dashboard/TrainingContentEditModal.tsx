
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Edit, Save, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TrainingContent {
  id: string;
  title: string;
  type: string;
  duration: string;
  rating: number;
  enrollments: number;
  completions: number;
  category: string;
  difficulty: string;
  thumbnail: string;
  description: string;
}

interface TrainingContentEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  content: TrainingContent | null;
}

export const TrainingContentEditModal: React.FC<TrainingContentEditModalProps> = ({
  open,
  onOpenChange,
  content
}) => {
  const { toast } = useToast();
  const [editedContent, setEditedContent] = useState(content);

  React.useEffect(() => {
    setEditedContent(content);
  }, [content]);

  if (!content || !editedContent) return null;

  const handleSave = () => {
    toast({
      title: "Content Updated",
      description: `"${editedContent.title}" has been updated successfully.`
    });
    onOpenChange(false);
  };

  const categories = ['Quality Management', 'Safety', 'Compliance', 'Leadership', 'Technical'];
  const difficulties = ['Beginner', 'Intermediate', 'Advanced'];
  const types = ['Video Course', 'Interactive Module', 'Document/PDF', 'Quiz/Assessment'];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Training Content
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Content Title</Label>
              <Input
                id="title"
                value={editedContent.title}
                onChange={(e) => setEditedContent({...editedContent, title: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">Content Type</Label>
                <select 
                  id="type"
                  value={editedContent.type}
                  onChange={(e) => setEditedContent({...editedContent, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {types.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  value={editedContent.duration}
                  onChange={(e) => setEditedContent({...editedContent, duration: e.target.value})}
                  placeholder="e.g., 45 min"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <select 
                  id="category"
                  value={editedContent.category}
                  onChange={(e) => setEditedContent({...editedContent, category: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              <div>
                <Label htmlFor="difficulty">Difficulty</Label>
                <select 
                  id="difficulty"
                  value={editedContent.difficulty}
                  onChange={(e) => setEditedContent({...editedContent, difficulty: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {difficulties.map(difficulty => (
                    <option key={difficulty} value={difficulty}>{difficulty}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <textarea 
                id="description"
                value={editedContent.description}
                onChange={(e) => setEditedContent({...editedContent, description: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md h-24"
                placeholder="Describe the training content and learning objectives"
              />
            </div>
          </div>

          {/* Thumbnail Upload */}
          <div className="space-y-2">
            <Label>Thumbnail Image</Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <div className="aspect-video bg-gray-100 rounded mb-2 overflow-hidden">
                <img 
                  src={editedContent.thumbnail} 
                  alt="Thumbnail"
                  className="w-full h-full object-cover"
                />
              </div>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Change Thumbnail
              </Button>
            </div>
          </div>

          {/* Current Stats (Read-only) */}
          <div className="space-y-2">
            <Label>Current Statistics</Label>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline">{editedContent.enrollments} enrollments</Badge>
              <Badge variant="outline">{editedContent.completions} completions</Badge>
              <Badge variant="outline">★ {editedContent.rating} rating</Badge>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

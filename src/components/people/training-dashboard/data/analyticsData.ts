
export const engagementData = [
  { month: 'Jan', logins: 450, coursesStarted: 120, coursesCompleted: 85, avgSessionTime: 35 },
  { month: 'Feb', logins: 520, coursesStarted: 145, coursesCompleted: 102, avgSessionTime: 42 },
  { month: 'Mar', logins: 580, coursesStarted: 160, coursesCompleted: 125, avgSessionTime: 38 },
  { month: 'Apr', logins: 620, coursesStarted: 175, coursesCompleted: 140, avgSessionTime: 45 },
  { month: 'May', logins: 680, coursesStarted: 195, coursesCompleted: 165, avgSessionTime: 48 },
  { month: 'Jun', logins: 720, coursesStarted: 210, coursesCompleted: 180, avgSessionTime: 52 }
];

export const departmentProgress = [
  { department: 'Quality', completed: 92, enrolled: 100, rate: 92 },
  { department: 'Production', completed: 85, enrolled: 120, rate: 71 },
  { department: 'Safety', completed: 98, enrolled: 110, rate: 89 },
  { department: 'Engineering', completed: 76, enrolled: 95, rate: 80 },
  { department: 'Maintenance', completed: 65, enrolled: 80, rate: 81 }
];

export const skillCompetencyData = [
  { skill: 'Quality Management', novice: 15, competent: 45, expert: 40, total: 100 },
  { skill: 'Safety Protocols', novice: 10, competent: 35, expert: 55, total: 100 },
  { skill: 'Technical Skills', novice: 25, competent: 50, expert: 25, total: 100 },
  { skill: 'Leadership', novice: 35, competent: 45, expert: 20, total: 100 },
  { skill: 'Compliance', novice: 20, competent: 55, expert: 25, total: 100 }
];

export const learningROIData = [
  { month: 'Jan', trainingCost: 25000, productivityGain: 45000, roi: 80 },
  { month: 'Feb', trainingCost: 30000, productivityGain: 52000, roi: 73 },
  { month: 'Mar', trainingCost: 28000, productivityGain: 58000, roi: 107 },
  { month: 'Apr', trainingCost: 35000, productivityGain: 68000, roi: 94 },
  { month: 'May', trainingCost: 32000, productivityGain: 72000, roi: 125 },
  { month: 'Jun', trainingCost: 38000, productivityGain: 85000, roi: 124 }
];

export const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const skillsChartConfig = {
  novice: {
    label: "Novice",
    color: "#FFBB28",
  },
  competent: {
    label: "Competent", 
    color: "#00C49F",
  },
  expert: {
    label: "Expert",
    color: "#0088FE",
  },
};

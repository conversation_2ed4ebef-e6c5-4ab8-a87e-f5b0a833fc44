import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { BookOpen, Users, Trophy, TrendingUp, Play, Plus, Download, Calendar, Clock, Star } from 'lucide-react';
import { TrainingContentLibrary } from './TrainingContentLibrary';
import { LearningPathsManager } from './LearningPathsManager';
import { TrainingAnalytics } from './TrainingAnalytics';
import { CertificationManagement } from './CertificationManagement';

const completionData = [
  { month: 'Jan', completed: 45, enrolled: 120, rate: 37.5 },
  { month: 'Feb', completed: 78, enrolled: 145, rate: 53.8 },
  { month: 'Mar', completed: 92, enrolled: 160, rate: 57.5 },
  { month: 'Apr', completed: 105, enrolled: 175, rate: 60.0 },
  { month: 'May', completed: 125, enrolled: 190, rate: 65.8 },
  { month: 'Jun', completed: 140, enrolled: 200, rate: 70.0 }
];

const skillsData = [
  { skill: 'Quality Management', proficient: 85, developing: 15, total: 100 },
  { skill: 'Safety Protocols', proficient: 92, developing: 8, total: 100 },
  { skill: 'GMP Compliance', proficient: 78, developing: 22, total: 100 },
  { skill: 'Documentation', proficient: 88, developing: 12, total: 100 },
  { skill: 'Leadership', proficient: 65, developing: 35, total: 100 }
];

const learningPathsData = [
  { name: 'New Hire Onboarding', completed: 18, enrolled: 25, progress: 72 },
  { name: 'Quality Specialist Certification', completed: 12, enrolled: 15, progress: 80 },
  { name: 'Safety Officer Development', completed: 6, enrolled: 8, progress: 75 },
  { name: 'Leadership Excellence Program', completed: 8, enrolled: 12, progress: 67 }
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

export const TrainingDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const exportTrainingData = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      completionData,
      skillsData,
      learningPathsData,
      totalTrainees: 200,
      completedCourses: 140,
      activeCertifications: 45
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `training-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Training Management Hub</h1>
          <p className="text-gray-600">Comprehensive learning and development platform</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportTrainingData}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Training
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Learners</p>
                <p className="text-3xl font-bold">200</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +15% this month
                </p>
              </div>
              <Users className="h-12 w-12 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Course Completion</p>
                <p className="text-3xl font-bold">70%</p>
                <p className="text-sm text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +8% improvement
                </p>
              </div>
              <BookOpen className="h-12 w-12 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Certifications</p>
                <p className="text-3xl font-bold">45</p>
                <p className="text-sm text-blue-600 flex items-center mt-1">
                  <Trophy className="h-4 w-4 mr-1" />
                  Active certificates
                </p>
              </div>
              <Trophy className="h-12 w-12 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Learning Time</p>
                <p className="text-3xl font-bold">2.5</p>
                <p className="text-sm text-gray-600">hours/week</p>
                <p className="text-sm text-purple-600 flex items-center mt-1">
                  <Clock className="h-4 w-4 mr-1" />
                  Per employee
                </p>
              </div>
              <Clock className="h-12 w-12 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content Library</TabsTrigger>
          <TabsTrigger value="paths">Learning Paths</TabsTrigger>
          <TabsTrigger value="certifications">Certifications</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="skills">Skills Matrix</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Completion Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Training Completion Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={completionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="completed" stroke="#8884d8" strokeWidth={2} name="Completed" />
                    <Line type="monotone" dataKey="enrolled" stroke="#82ca9d" strokeWidth={2} name="Enrolled" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Learning Paths Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Learning Paths Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {learningPathsData.map((path) => (
                  <div key={path.name} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{path.name}</span>
                      <span className="text-sm text-gray-600">{path.completed}/{path.enrolled} completed</span>
                    </div>
                    <Progress value={path.progress} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Training Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { user: 'Sarah Johnson', action: 'completed', course: 'ISO 9001 Fundamentals', time: '2 hours ago' },
                  { user: 'Mike Chen', action: 'started', course: 'Safety Protocols Advanced', time: '4 hours ago' },
                  { user: 'Emma Davis', action: 'earned certificate', course: 'GMP Compliance', time: '1 day ago' },
                  { user: 'Alex Rodriguez', action: 'joined learning path', course: 'Quality Specialist', time: '2 days ago' }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <Play className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.user}</p>
                        <p className="text-sm text-gray-600">{activity.action} "{activity.course}"</p>
                      </div>
                    </div>
                    <span className="text-sm text-gray-500">{activity.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          <TrainingContentLibrary />
        </TabsContent>

        <TabsContent value="paths" className="space-y-6">
          <LearningPathsManager />
        </TabsContent>

        <TabsContent value="certifications" className="space-y-6">
          <CertificationManagement />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <TrainingAnalytics />
        </TabsContent>

        <TabsContent value="skills" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Skills Proficiency Matrix</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={skillsData} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" domain={[0, 100]} />
                    <YAxis dataKey="skill" type="category" width={140} />
                    <Tooltip formatter={(value, name) => [`${value}%`, name === 'proficient' ? 'Proficient' : 'Developing']} />
                    <Bar dataKey="proficient" stackId="a" fill="#22c55e" name="Proficient" />
                    <Bar dataKey="developing" stackId="a" fill="#fbbf24" name="Developing" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Skill Distribution Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <PieChart>
                    <Pie
                      data={skillsData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ skill, proficient }) => `${skill}: ${proficient}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="proficient"
                    >
                      {skillsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Detailed Skills Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {skillsData.map((skill) => (
                  <div key={skill.skill} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{skill.skill}</span>
                      <div className="flex gap-2">
                        <Badge className="bg-green-100 text-green-800">{skill.proficient}% Proficient</Badge>
                        <Badge className="bg-yellow-100 text-yellow-800">{skill.developing}% Developing</Badge>
                      </div>
                    </div>
                    <Progress value={skill.proficient} className="h-3" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

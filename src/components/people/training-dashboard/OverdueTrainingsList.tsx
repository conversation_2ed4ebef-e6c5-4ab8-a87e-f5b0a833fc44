
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Send, AlertTriangle, Clock, Calendar, CheckCircle } from "lucide-react";
import { OverdueTraining } from "@/types/overdueTraining";
import { toast } from "sonner";

interface OverdueTrainingsListProps {
  overdueTrainings: OverdueTraining[];
  onDepartmentChange: (department: string) => void;
  selectedDepartment: string;
  departments: string[];
}

export const OverdueTrainingsList: React.FC<OverdueTrainingsListProps> = ({
  overdueTrainings,
  onDepartmentChange,
  selectedDepartment,
  departments
}) => {
  const getUrgencyBadge = (urgency: string) => {
    switch (urgency) {
      case "critical":
        return <Badge variant="destructive">Critical</Badge>;
      case "high":
        return <Badge className="bg-red-100 text-red-800">High</Badge>;
      case "medium":
        return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case "low":
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">{urgency}</Badge>;
    }
  };

  const sendReminder = (training: OverdueTraining) => {
    toast.success(`Reminder sent to ${training.employeeName} for "${training.trainingTitle}"`);
  };

  const sendBulkReminders = () => {
    toast.success(`Bulk reminders sent to ${overdueTrainings.length} employees`);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Overdue Trainings ({overdueTrainings.length})
          </CardTitle>
          <div className="flex gap-3">
            <Select value={selectedDepartment} onValueChange={onDepartmentChange}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {departments.map(dept => (
                  <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            {overdueTrainings.length > 0 && (
              <Button onClick={sendBulkReminders} className="gap-2">
                <Send className="h-4 w-4" />
                Send All Reminders
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {overdueTrainings.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <p>No overdue trainings found!</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Training</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Days Overdue</TableHead>
                  <TableHead>Urgency</TableHead>
                  <TableHead>Last Reminder</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {overdueTrainings.map((training) => (
                  <TableRow key={training.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{training.employeeName}</div>
                        <div className="text-sm text-gray-500">Manager: {training.managerName}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{training.trainingTitle}</div>
                    </TableCell>
                    <TableCell>{training.department}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {new Date(training.dueDate).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-red-400" />
                        <span className="text-red-600 font-medium">{training.daysOverdue} days</span>
                      </div>
                    </TableCell>
                    <TableCell>{getUrgencyBadge(training.urgencyLevel)}</TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">
                        {training.lastReminderSent === "Never" 
                          ? "Never" 
                          : new Date(training.lastReminderSent).toLocaleDateString()
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => sendReminder(training)}
                        className="gap-1"
                      >
                        <Send className="h-3 w-3" />
                        Send Reminder
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BookOpen, Clock, Users, Award } from "lucide-react";

export const TrainingCatalog: React.FC = () => {
  const trainingCourses = [
    {
      id: "TRN-001",
      title: "ISO 9001:2015 Fundamentals",
      category: "Quality",
      duration: "4 hours",
      level: "Beginner",
      enrollees: 45,
      status: "Active"
    },
    {
      id: "TRN-002", 
      title: "Risk Management Principles",
      category: "Risk",
      duration: "6 hours",
      level: "Intermediate",
      enrollees: 32,
      status: "Active"
    },
    {
      id: "TRN-003",
      title: "Document Control Best Practices",
      category: "Documentation",
      duration: "3 hours",
      level: "Beginner",
      enrollees: 67,
      status: "Active"
    },
    {
      id: "TRN-004",
      title: "Audit Planning and Execution",
      category: "Audit",
      duration: "8 hours", 
      level: "Advanced",
      enrollees: 28,
      status: "Active"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Courses</p>
                <p className="text-2xl font-bold">{trainingCourses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Enrollees</p>
                <p className="text-2xl font-bold">
                  {trainingCourses.reduce((sum, course) => sum + course.enrollees, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Duration</p>
                <p className="text-2xl font-bold">5.25h</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold">87%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Available Training Courses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {trainingCourses.map((course) => (
              <Card key={course.id} className="border">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h3 className="font-semibold">{course.title}</h3>
                      <Badge variant="outline">{course.status}</Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <BookOpen className="h-4 w-4" />
                        <span>{course.category}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{course.duration}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          course.level === 'Beginner' ? 'default' : 
                          course.level === 'Intermediate' ? 'secondary' : 'destructive'
                        }>
                          {course.level}
                        </Badge>
                        <span className="text-sm text-gray-600">{course.enrollees} enrollees</span>
                      </div>
                      <Button size="sm">
                        Enroll
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

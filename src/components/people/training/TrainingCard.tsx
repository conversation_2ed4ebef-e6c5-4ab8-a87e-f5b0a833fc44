
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, User } from "lucide-react";

interface TrainingCardProps {
  training: {
    id: string;
    title: string;
    description: string;
    status: string;
    dueDate: string;
    assignedBy: string;
    estimatedDuration: string;
  };
}

export const TrainingCard: React.FC<TrainingCardProps> = ({ training }) => {
  const getStatusBadge = (status: string) => {
    const variant = status === "Completed" ? "default" : 
                   status === "In Progress" ? "secondary" : "outline";
    return <Badge variant={variant}>{status}</Badge>;
  };

  const getDaysUntilDue = () => {
    const today = new Date();
    const dueDate = new Date(training.dueDate);
    const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff < 0) return { text: "Overdue", color: "text-red-600" };
    if (daysDiff <= 7) return { text: `Due in ${daysDiff} days`, color: "text-yellow-600" };
    return { text: `Due in ${daysDiff} days`, color: "text-gray-600" };
  };

  const dueInfo = getDaysUntilDue();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">{training.title}</h3>
            <p className="text-gray-600 mt-1">{training.description}</p>
            
            <div className="flex items-center gap-4 mt-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span className={dueInfo.color}>{dueInfo.text}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{training.estimatedDuration}</span>
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>Assigned by {training.assignedBy}</span>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col items-end gap-2">
            {getStatusBadge(training.status)}
            {training.status !== "Completed" && (
              <Button size="sm" variant="outline">
                {training.status === "Not Started" ? "Start Training" : "Continue"}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

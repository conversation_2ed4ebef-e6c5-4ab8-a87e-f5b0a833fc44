
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { useTraining } from "@/contexts/TrainingContext";
import { TrainingFilters } from "./training/TrainingFilters";
import { TrainingCard } from "./training/TrainingCard";

interface MyTrainingsTabProps {
  employeeId: string;
}

export const MyTrainingsTab: React.FC<MyTrainingsTabProps> = ({ employeeId }) => {
  const { employeeTrainings } = useTraining();
  const [statusFilter, setStatusFilter] = useState("all");
  const [dueDateFilter, setDueDateFilter] = useState("all");

  const myTrainings = employeeTrainings.filter(training => training.employeeId === employeeId);

  const filteredTrainings = myTrainings.filter(training => {
    if (statusFilter !== "all" && training.status !== statusFilter) return false;
    if (dueDateFilter !== "all") {
      const today = new Date();
      const dueDate = new Date(training.dueDate);
      const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
      
      if (dueDateFilter === "overdue" && daysDiff >= 0) return false;
      if (dueDateFilter === "due-soon" && (daysDiff < 0 || daysDiff > 7)) return false;
      if (dueDateFilter === "due-later" && daysDiff <= 7) return false;
    }
    return true;
  });

  // Map employee training data to the format expected by TrainingCard
  const mappedTrainings = filteredTrainings.map(employeeTraining => ({
    id: employeeTraining.id,
    title: employeeTraining.training.title,
    description: employeeTraining.training.description,
    status: employeeTraining.status,
    dueDate: employeeTraining.dueDate,
    assignedBy: employeeTraining.assignedBy,
    estimatedDuration: employeeTraining.training.estimatedDuration
  }));

  return (
    <div className="space-y-6">
      <TrainingFilters
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        dueDateFilter={dueDateFilter}
        setDueDateFilter={setDueDateFilter}
      />

      <div className="grid gap-4">
        {mappedTrainings.map((training) => (
          <TrainingCard key={training.id} training={training} />
        ))}
      </div>

      {mappedTrainings.length === 0 && (
        <Card>
          <CardContent className="text-center py-8 text-gray-500">
            <p>No trainings found matching your filters.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

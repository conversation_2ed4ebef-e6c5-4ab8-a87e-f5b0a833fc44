
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ChevronUp, ChevronDown } from "lucide-react";
import { SortField, SortDirection } from "@/hooks/usePeopleTableSort";

interface PeopleTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
  isMobile: boolean;
}

export const PeopleTableHeader: React.FC<PeopleTableHeaderProps> = ({ 
  sortField, 
  sortDirection, 
  onSort,
  isMobile 
}) => {
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  const handleSort = (field: SortField) => {
    onSort(field);
  };

  return (
    <TableHeader>
      <TableRow className="bg-gray-50">
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('name')}
        >
          <div className="flex items-center gap-2">
            Name
            {getSortIcon('name')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('jobTitle')}
        >
          <div className="flex items-center gap-2">
            Job Title
            {getSortIcon('jobTitle')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('email')}
        >
          <div className="flex items-center gap-2">
            Email
            {getSortIcon('email')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('department')}
        >
          <div className="flex items-center gap-2">
            Department
            {getSortIcon('department')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('processes')}
        >
          <div className="flex items-center gap-2">
            Processes
            {getSortIcon('processes')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('joiningDate')}
        >
          <div className="flex items-center gap-2">
            Joining Date
            {getSortIcon('joiningDate')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('manager')}
        >
          <div className="flex items-center gap-2">
            Manager
            {getSortIcon('manager')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 transition-colors"
          onClick={() => handleSort('status')}
        >
          <div className="flex items-center gap-2">
            Status
            {getSortIcon('status')}
          </div>
        </TableHead>
        <TableHead className="text-center">Actions</TableHead>
      </TableRow>
    </TableHeader>
  );
};

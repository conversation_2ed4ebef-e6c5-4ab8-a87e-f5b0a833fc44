
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { Plus } from "lucide-react";
import { AddEmployeeDialog } from "./AddEmployeeDialog";

export const AddEmployeeButton = () => {
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <>
      <Button 
        className="bg-teal-600 hover:bg-teal-700"
        onClick={() => setIsOpen(true)}
      >
        <Plus className="h-4 w-4 mr-1" /> 
        {isMobile ? "Add" : "Add Employee"}
      </Button>
      <AddEmployeeDialog open={isOpen} onOpenChange={setIsOpen} />
    </>
  );
};

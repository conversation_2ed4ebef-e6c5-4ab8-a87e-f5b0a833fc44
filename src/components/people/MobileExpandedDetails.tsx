
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Person } from "@/types/people";
import { format } from "date-fns";

interface MobileExpandedDetailsProps {
  person: Person;
  index: number;
}

export const MobileExpandedDetails: React.FC<MobileExpandedDetailsProps> = ({ person, index }) => {
  const formatDate = (dateString: string): string => {
    if (!dateString) return "--";
    try {
      return format(new Date(dateString), "MMM d yyyy");
    } catch (error) {
      return dateString;
    }
  };

  const renderDetailRow = (label: string, value: string) => {
    return (
      <div className="mb-2">
        <span className="text-xs text-gray-500">{label}:</span>
        <span className="ml-2 text-sm font-medium">{value || "--"}</span>
      </div>
    );
  };

  // Handle processes as either string or array
  const processesDisplay = Array.isArray(person.processes) 
    ? person.processes.join(", ") 
    : person.processes;

  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
      <TableCell colSpan={3} className="p-3">
        <div className="px-2 py-2 space-y-1">
          {renderDetailRow("Job Title", person.jobTitle)}
          {renderDetailRow("Email", person.email)}
          {renderDetailRow("Department", person.department)}
          {renderDetailRow("Processes", processesDisplay)}
          {renderDetailRow("Joining Dt", formatDate(person.joiningDate))}
          {renderDetailRow("Leaving Dt", formatDate(person.leavingDate || ""))}
        </div>
      </TableCell>
    </TableRow>
  );
};

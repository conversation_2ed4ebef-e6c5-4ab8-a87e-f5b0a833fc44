import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CalendarIcon, ChevronLeft, ChevronRight, Users, BookOpen, CheckCircle, X } from "lucide-react";
import { format } from "date-fns";
import { mockTrainings } from "@/data/mockTrainingData";
import { Training } from "@/types/training";

interface BulkTrainingAssignmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  people: any[];
}

type AssignmentStep = "trainings" | "people" | "schedule" | "confirm";

export const BulkTrainingAssignmentDialog: React.FC<BulkTrainingAssignmentDialogProps> = ({
  open,
  onOpenChange,
  people
}) => {
  const [currentStep, setCurrentStep] = useState<AssignmentStep>("trainings");
  const [selectedTrainings, setSelectedTrainings] = useState<Training[]>([]);
  const [selectedPeople, setSelectedPeople] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [assignmentType, setAssignmentType] = useState<"individuals" | "role">("individuals");
  const [dueDate, setDueDate] = useState<Date>();

  const roles = ["Quality Manager", "Production Staff", "Lab Technician", "All Employees"];

  const handleTrainingToggle = (training: Training) => {
    setSelectedTrainings(prev => 
      prev.find(t => t.id === training.id)
        ? prev.filter(t => t.id !== training.id)
        : [...prev, training]
    );
  };

  const handlePersonToggle = (personId: string) => {
    setSelectedPeople(prev => 
      prev.includes(personId)
        ? prev.filter(id => id !== personId)
        : [...prev, personId]
    );
  };

  const handleNext = () => {
    const steps: AssignmentStep[] = ["trainings", "people", "schedule", "confirm"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const handleBack = () => {
    const steps: AssignmentStep[] = ["trainings", "people", "schedule", "confirm"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const handleAssign = () => {
    console.log("Assigning trainings:", {
      trainings: selectedTrainings,
      people: assignmentType === "individuals" ? selectedPeople : selectedRole,
      assignmentType,
      dueDate
    });
    onOpenChange(false);
    // Reset form
    setCurrentStep("trainings");
    setSelectedTrainings([]);
    setSelectedPeople([]);
    setSelectedRole("");
    setDueDate(undefined);
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "trainings": return "Select Trainings";
      case "people": return "Select Recipients";
      case "schedule": return "Set Schedule";
      case "confirm": return "Confirm Assignment";
      default: return "";
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case "trainings": return selectedTrainings.length > 0;
      case "people": return assignmentType === "role" ? selectedRole !== "" : selectedPeople.length > 0;
      case "schedule": return dueDate !== undefined;
      case "confirm": return true;
      default: return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-3 text-xl">
              <BookOpen className="h-6 w-6" />
              Bulk Training Assignment - {getStepTitle()}
            </DialogTitle>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="px-6 py-4 border-b bg-gray-50">
          <div className="flex items-center justify-between max-w-md mx-auto">
            {["trainings", "people", "schedule", "confirm"].map((step, index) => (
              <div key={step} className="flex items-center">
                <div className={`rounded-full w-10 h-10 flex items-center justify-center text-sm font-medium border-2 ${
                  currentStep === step ? 'bg-blue-600 text-white border-blue-600' : 
                  ["trainings", "people", "schedule", "confirm"].indexOf(currentStep) > index ? 'bg-green-600 text-white border-green-600' : 'bg-white text-gray-400 border-gray-300'
                }`}>
                  {["trainings", "people", "schedule", "confirm"].indexOf(currentStep) > index ? '✓' : index + 1}
                </div>
                {index < 3 && <div className="w-16 h-px bg-gray-300 mx-2" />}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentStep === "people" && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4">Select Recipients</h3>
                
                <div className="mb-6">
                  <Select value={assignmentType} onValueChange={(value: "individuals" | "role") => setAssignmentType(value)}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Assignment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="individuals">Assign to Individuals</SelectItem>
                      <SelectItem value="role">Assign to Role/Department</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {assignmentType === "role" ? (
                  <div>
                    <Select value={selectedRole} onValueChange={setSelectedRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role or department" />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.map((role) => (
                          <SelectItem key={role} value={role}>{role}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                    {people.map((person) => (
                      <Card key={person.id} className={`cursor-pointer transition-all hover:shadow-sm ${
                        selectedPeople.includes(person.id) ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                      }`}>
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedPeople.includes(person.id)}
                              onCheckedChange={() => handlePersonToggle(person.id)}
                            />
                            <div className="flex-1">
                              <p className="font-medium text-gray-900">{person.name}</p>
                              <p className="text-sm text-gray-600">{person.jobTitle}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === "trainings" && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Select Training Programs</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {mockTrainings.map((training) => (
                  <Card key={training.id} className={`cursor-pointer transition-all ${
                    selectedTrainings.find(t => t.id === training.id) ? 'ring-2 ring-blue-500' : ''
                  }`}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={selectedTrainings.find(t => t.id === training.id) !== undefined}
                          onCheckedChange={() => handleTrainingToggle(training)}
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{training.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{training.description}</p>
                          <div className="flex gap-2 mb-2">
                            <Badge variant="outline">{training.type}</Badge>
                            <Badge variant="outline">{training.category}</Badge>
                          </div>
                          <p className="text-xs text-gray-500">Duration: {training.estimatedDuration}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {currentStep === "schedule" && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Set Due Date</h3>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : "Select due date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={setDueDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          {currentStep === "confirm" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Assignment Summary</h3>
              
              <Card>
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2">Selected Trainings ({selectedTrainings.length})</h4>
                  <div className="space-y-2">
                    {selectedTrainings.map((training) => (
                      <div key={training.id} className="flex justify-between items-center">
                        <span>{training.title}</span>
                        <Badge variant="outline">{training.estimatedDuration}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2">Recipients</h4>
                  {assignmentType === "role" ? (
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>Role: {selectedRole}</span>
                    </div>
                  ) : (
                    <div>
                      <p>{selectedPeople.length} individuals selected</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <h4 className="font-medium mb-2">Schedule</h4>
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="h-4 w-4" />
                    <span>Due Date: {dueDate ? format(dueDate, "PPP") : "Not set"}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Navigation Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <Button 
            variant="ghost" 
            onClick={handleBack}
            disabled={currentStep === "trainings"}
            className="gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Back
          </Button>
          
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            {currentStep === "confirm" ? (
              <Button onClick={handleAssign} className="gap-2">
                <CheckCircle className="h-4 w-4" />
                Assign Training
              </Button>
            ) : (
              <Button onClick={handleNext} disabled={!canProceed()} className="gap-2">
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

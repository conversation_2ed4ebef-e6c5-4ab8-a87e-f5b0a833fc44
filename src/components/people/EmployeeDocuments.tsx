
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { FileText, Upload, Download, Eye, Plus, Calendar, User } from "lucide-react";

interface EmployeeDocument {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  size: string;
  status: "Active" | "Expired" | "Pending";
  category: string;
}

interface EmployeeDocumentsProps {
  employeeId: string;
  employeeName: string;
}

const mockDocuments: EmployeeDocument[] = [
  {
    id: "DOC001",
    name: "Employment Contract",
    type: "PDF",
    uploadDate: "2023-05-15",
    size: "245 KB",
    status: "Active",
    category: "Contract"
  },
  {
    id: "DOC002",
    name: "ID Card Copy",
    type: "PDF",
    uploadDate: "2023-05-15",
    size: "1.2 MB",
    status: "Active",
    category: "Identity"
  },
  {
    id: "DOC003",
    name: "Educational Certificates",
    type: "PDF",
    uploadDate: "2023-05-15",
    size: "3.4 MB",
    status: "Active",
    category: "Education"
  },
  {
    id: "DOC004",
    name: "Medical Certificate",
    type: "PDF",
    uploadDate: "2023-11-20",
    size: "890 KB",
    status: "Expired",
    category: "Medical"
  },
  {
    id: "DOC005",
    name: "Background Check",
    type: "PDF",
    uploadDate: "2023-05-10",
    size: "456 KB",
    status: "Active",
    category: "Verification"
  },
  {
    id: "DOC006",
    name: "Tax Documents",
    type: "PDF",
    uploadDate: "2024-01-15",
    size: "678 KB",
    status: "Pending",
    category: "Tax"
  }
];

export const EmployeeDocuments: React.FC<EmployeeDocumentsProps> = ({ 
  employeeId, 
  employeeName 
}) => {
  const [documents] = useState<EmployeeDocument[]>(mockDocuments);
  const [isUploadOpen, setIsUploadOpen] = useState(false);

  const getStatusBadge = (status: EmployeeDocument['status']) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Expired":
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Contract":
        return <FileText className="h-4 w-4 text-blue-600" />;
      case "Identity":
        return <User className="h-4 w-4 text-green-600" />;
      case "Education":
        return <FileText className="h-4 w-4 text-purple-600" />;
      case "Medical":
        return <FileText className="h-4 w-4 text-red-600" />;
      case "Verification":
        return <FileText className="h-4 w-4 text-orange-600" />;
      case "Tax":
        return <FileText className="h-4 w-4 text-gray-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Document Management</h3>
          <p className="text-sm text-gray-600">Manage {employeeName}'s documents and certifications</p>
        </div>
        <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Upload Document
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload New Document</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="doc-name">Document Name</Label>
                <Input id="doc-name" placeholder="Enter document name..." />
              </div>
              <div className="space-y-2">
                <Label htmlFor="doc-category">Category</Label>
                <select id="doc-category" className="w-full p-2 border rounded">
                  <option value="">Select category...</option>
                  <option value="Contract">Contract</option>
                  <option value="Identity">Identity</option>
                  <option value="Education">Education</option>
                  <option value="Medical">Medical</option>
                  <option value="Verification">Verification</option>
                  <option value="Tax">Tax</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="doc-file">File</Label>
                <Input id="doc-file" type="file" accept=".pdf,.doc,.docx,.jpg,.png" />
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsUploadOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsUploadOpen(false)}>
                  Upload Document
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Document Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{documents.length}</div>
            <div className="text-sm text-gray-600">Total Documents</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {documents.filter(d => d.status === "Active").length}
            </div>
            <div className="text-sm text-gray-600">Active</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {documents.filter(d => d.status === "Expired").length}
            </div>
            <div className="text-sm text-gray-600">Expired</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {documents.filter(d => d.status === "Pending").length}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </CardContent>
        </Card>
      </div>

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {documents.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(doc.category)}
                      <span className="font-medium">{doc.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{doc.category}</Badge>
                  </TableCell>
                  <TableCell>{doc.type}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {new Date(doc.uploadDate).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>{doc.size}</TableCell>
                  <TableCell>{getStatusBadge(doc.status)}</TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button variant="ghost" size="sm" className="gap-1">
                        <Eye className="h-3 w-3" />
                        View
                      </Button>
                      <Button variant="ghost" size="sm" className="gap-1">
                        <Download className="h-3 w-3" />
                        Download
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

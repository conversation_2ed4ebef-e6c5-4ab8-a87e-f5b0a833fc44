
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Users, Plus } from "lucide-react";
import { useTraining } from "@/contexts/TrainingContext";
import { useToast } from "@/hooks/use-toast";

interface Person {
  id: string;
  name: string;
  jobTitle: string;
  department: string;
  status: string;
}

interface BulkAssignmentButtonProps {
  people: Person[];
}

export const BulkAssignmentButton: React.FC<BulkAssignmentButtonProps> = ({ people }) => {
  const { availableTrainings, assignTraining } = useTraining();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedPeople, setSelectedPeople] = useState<string[]>([]);
  const [selectedTraining, setSelectedTraining] = useState<string>("");
  const [dueDate, setDueDate] = useState<string>("");

  const handlePersonToggle = (personId: string) => {
    setSelectedPeople(prev => 
      prev.includes(personId) 
        ? prev.filter(id => id !== personId)
        : [...prev, personId]
    );
  };

  const handleAssign = () => {
    if (!selectedTraining || selectedPeople.length === 0 || !dueDate) {
      toast({
        title: "Missing Information",
        description: "Please select training, people, and due date",
        variant: "destructive"
      });
      return;
    }

    const training = availableTrainings.find(t => t.id === selectedTraining);
    if (!training) return;

    selectedPeople.forEach(personId => {
      assignTraining(personId, training, dueDate);
    });

    toast({
      title: "Training Assigned",
      description: `${training.title} assigned to ${selectedPeople.length} employees`,
    });

    setIsOpen(false);
    setSelectedPeople([]);
    setSelectedTraining("");
    setDueDate("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Bulk Assign Training
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Training Assignment
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          <div className="space-y-2">
            <Label>Select Training</Label>
            <Select value={selectedTraining} onValueChange={setSelectedTraining}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a training..." />
              </SelectTrigger>
              <SelectContent>
                {availableTrainings.map(training => (
                  <SelectItem key={training.id} value={training.id}>
                    {training.title} ({training.category})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Due Date</Label>
            <Input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label>Select Employees ({selectedPeople.length} selected)</Label>
            <div className="max-h-60 overflow-y-auto border rounded-lg p-3 space-y-2">
              {people.filter(p => p.status === "Active").map(person => (
                <div key={person.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={person.id}
                    checked={selectedPeople.includes(person.id)}
                    onCheckedChange={() => handlePersonToggle(person.id)}
                  />
                  <label htmlFor={person.id} className="flex-1 cursor-pointer">
                    <div className="font-medium">{person.name}</div>
                    <div className="text-sm text-gray-500">{person.jobTitle} • {person.department}</div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAssign}>
              Assign Training
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

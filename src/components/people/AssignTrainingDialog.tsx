
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useTraining } from "@/contexts/TrainingContext";

interface AssignTrainingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employeeId: string;
}

export const AssignTrainingDialog: React.FC<AssignTrainingDialogProps> = ({ 
  open, 
  onOpenChange, 
  employeeId 
}) => {
  const { toast } = useToast();
  const { availableTrainings, assignTraining } = useTraining();
  const [selectedTraining, setSelectedTraining] = useState("");
  const [dueDate, setDueDate] = useState("");
  const [notes, setNotes] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTraining || !dueDate) {
      toast({
        title: "Missing Information",
        description: "Please select a training and set a due date.",
        variant: "destructive",
      });
      return;
    }

    const training = availableTrainings.find(t => t.id === selectedTraining);
    if (!training) return;

    assignTraining(employeeId, training, dueDate, notes);

    toast({
      title: "Training Assigned",
      description: `${training.title} has been assigned successfully.`,
    });

    onOpenChange(false);
    setSelectedTraining("");
    setDueDate("");
    setNotes("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Assign Training</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="training">Select Training</Label>
            <Select onValueChange={setSelectedTraining}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a training program" />
              </SelectTrigger>
              <SelectContent>
                {availableTrainings.map((training) => (
                  <SelectItem key={training.id} value={training.id}>
                    {training.title} ({training.category})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="dueDate">Due Date</Label>
            <Input
              id="dueDate"
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Input
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any additional notes or requirements..."
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Assign Training</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

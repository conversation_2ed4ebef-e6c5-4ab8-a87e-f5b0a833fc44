
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Users, BookOpen, Plus, Calendar } from "lucide-react";
import { BulkTrainingAssignmentDialog } from "../BulkTrainingAssignmentDialog";
import { AssignTrainingDialog } from "../AssignTrainingDialog";
import { useTraining } from "@/contexts/TrainingContext";
import { useAccount } from "@/contexts/AccountContext";

export const TrainingManagementHub = () => {
  const [showBulkAssign, setShowBulkAssign] = useState(false);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const { employeeTrainings, availableTrainings } = useTraining();
  const { currentAccount } = useAccount();

  const stats = {
    totalEmployees: currentAccount.people.length,
    totalTrainings: availableTrainings.length,
    activeAssignments: employeeTrainings.filter(t => t.status !== "Completed").length,
    completedTrainings: employeeTrainings.filter(t => t.status === "Completed").length
  };

  const recentAssignments = employeeTrainings
    .sort((a, b) => new Date(b.assignedDate).getTime() - new Date(a.assignedDate).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold">{stats.totalEmployees}</div>
              <div className="text-sm text-gray-600">Total Employees</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <BookOpen className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold">{stats.totalTrainings}</div>
              <div className="text-sm text-gray-600">Available Trainings</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Calendar className="h-8 w-8 mx-auto mb-2 text-orange-600" />
              <div className="text-2xl font-bold">{stats.activeAssignments}</div>
              <div className="text-sm text-gray-600">Active Assignments</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold">{stats.completedTrainings}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button onClick={() => setShowBulkAssign(true)} className="gap-2">
              <Users className="h-4 w-4" />
              Bulk Assign Training
            </Button>
            <Button variant="outline" onClick={() => setShowAssignDialog(true)} className="gap-2">
              <Plus className="h-4 w-4" />
              Assign Individual Training
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Assignments */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Training Assignments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentAssignments.map((assignment) => {
              const employee = currentAccount.people.find(p => p.id === assignment.employeeId);
              return (
                <div key={assignment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{assignment.training.title}</h4>
                    <p className="text-sm text-gray-600">
                      Assigned to: {employee?.name || "Unknown Employee"}
                    </p>
                    <p className="text-xs text-gray-500">
                      Due: {new Date(assignment.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{assignment.progress}%</div>
                    <div className="text-xs text-gray-500">{assignment.status}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <BulkTrainingAssignmentDialog 
        open={showBulkAssign} 
        onOpenChange={setShowBulkAssign}
        people={currentAccount.people}
      />
      
      <AssignTrainingDialog 
        open={showAssignDialog} 
        onOpenChange={setShowAssignDialog}
        employeeId=""
      />
    </div>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Play, Pause, CheckCircle, Clock, Award, BookOpen, Video, FileText } from "lucide-react";
import { EmployeeTraining } from "@/types/training";
import { useTraining } from "@/contexts/TrainingContext";
import { useToast } from "@/hooks/use-toast";

interface TrainingSimulatorModalProps {
  isOpen: boolean;
  onClose: () => void;
  training: EmployeeTraining;
  employeeName: string;
}

export const TrainingSimulatorModal: React.FC<TrainingSimulatorModalProps> = ({
  isOpen,
  onClose,
  training,
  employeeName
}) => {
  const { updateProgress, completeTraining } = useTraining();
  const { toast } = useToast();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(training.progress);
  const [timeSpent, setTimeSpent] = useState(parseInt(training.timeSpent) || 0);

  const handleStart = () => {
    setIsPlaying(true);
    toast({
      title: "Training Started",
      description: `${employeeName} started ${training.training.title}`,
    });

    const interval = setInterval(() => {
      setCurrentProgress(prev => {
        const newProgress = Math.min(prev + 5, 100);
        const newTimeSpent = timeSpent + 1;
        setTimeSpent(newTimeSpent);
        
        updateProgress(training.id, newProgress, `${newTimeSpent} minutes`);
        
        if (newProgress >= 100) {
          setIsPlaying(false);
          completeTraining(training.id, 95);
          toast({
            title: "Training Completed!",
            description: `${employeeName} completed ${training.training.title}`,
          });
          clearInterval(interval);
        }
        
        return newProgress;
      });
    }, 1000);

    setTimeout(() => {
      if (isPlaying) {
        setIsPlaying(false);
        clearInterval(interval);
      }
    }, 10000);
  };

  const handlePause = () => {
    setIsPlaying(false);
    toast({
      title: "Training Paused",
      description: `${employeeName}'s training paused`,
    });
  };

  const handleComplete = () => {
    setCurrentProgress(100);
    completeTraining(training.id, 95);
    toast({
      title: "Training Completed!",
      description: `${employeeName} completed ${training.training.title}`,
    });
  };

  const getTrainingIcon = () => {
    switch (training.training.type) {
      case "Video": return <Video className="h-4 w-4" />;
      case "PDF": return <FileText className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getTrainingIcon()}
            Training Simulator - {training.training.title}
          </DialogTitle>
          <p className="text-sm text-gray-600">Simulating training for {employeeName}</p>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Training Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Type:</span>
                    <Badge variant="outline">{training.training.type}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Category:</span>
                    <span className="text-sm font-medium">{training.training.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <span className="text-sm font-medium">{training.training.estimatedDuration} min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Due Date:</span>
                    <span className="text-sm font-medium">{new Date(training.dueDate).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Current Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Progress:</span>
                    <span className="text-lg font-bold">{currentProgress}%</span>
                  </div>
                  <Progress value={currentProgress} className="h-3" />
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Time Spent:</span>
                    <span className="text-sm font-medium">{timeSpent} minutes</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Status:</span>
                    <Badge variant={training.status === "Completed" ? "default" : "outline"}>
                      {training.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Training Controls</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 justify-center">
                  {training.status !== "Completed" && (
                    <>
                      {!isPlaying ? (
                        <Button onClick={handleStart} className="gap-2">
                          <Play className="h-4 w-4" />
                          {currentProgress > 0 ? "Continue Training" : "Start Training"}
                        </Button>
                      ) : (
                        <Button onClick={handlePause} variant="outline" className="gap-2">
                          <Pause className="h-4 w-4" />
                          Pause Training
                        </Button>
                      )}
                      
                      {currentProgress >= 80 && (
                        <Button onClick={handleComplete} variant="outline" className="gap-2">
                          <CheckCircle className="h-4 w-4" />
                          Mark Complete
                        </Button>
                      )}
                    </>
                  )}
                  
                  {training.status === "Completed" && (
                    <div className="flex items-center gap-2 text-green-600">
                      <Award className="h-5 w-5" />
                      <span className="font-medium">Training Completed Successfully!</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Training Content</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Description</h3>
                    <p className="text-gray-600">{training.training.description}</p>
                  </div>
                  
                  {training.training.type === "Video" && (
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <Video className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600">Video content would be displayed here</p>
                      <p className="text-sm text-gray-500">Duration: {training.training.estimatedDuration} minutes</p>
                    </div>
                  )}
                  
                  {training.training.type === "PDF" && (
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <FileText className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600">PDF document would be displayed here</p>
                    </div>
                  )}
                  
                  {training.training.type === "Interactive" && (
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <BookOpen className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600">Interactive training module would be displayed here</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="progress" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Progress Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <Clock className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                      <div className="text-2xl font-bold text-blue-600">{timeSpent}</div>
                      <div className="text-sm text-gray-600">Minutes Spent</div>
                    </div>
                    
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-600" />
                      <div className="text-2xl font-bold text-green-600">{currentProgress}%</div>
                      <div className="text-sm text-gray-600">Completed</div>
                    </div>
                    
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <Award className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                      <div className="text-2xl font-bold text-purple-600">
                        {training.quizScore || 'N/A'}
                      </div>
                      <div className="text-sm text-gray-600">Quiz Score</div>
                    </div>
                  </div>
                  
                  {isPlaying && (
                    <div className="flex items-center justify-center gap-2 text-blue-600 text-sm bg-blue-50 p-3 rounded-lg">
                      <Clock className="h-4 w-4 animate-spin" />
                      <span>Training simulation in progress...</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Close Simulator
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

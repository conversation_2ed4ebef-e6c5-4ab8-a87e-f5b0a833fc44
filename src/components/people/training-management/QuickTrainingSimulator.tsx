
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, Square, Award, ExternalLink } from "lucide-react";
import { EmployeeTraining } from "@/types/training";
import { useTraining } from "@/contexts/TrainingContext";
import { useToast } from "@/hooks/use-toast";
import { TrainingSimulatorModal } from "./TrainingSimulatorModal";

interface QuickTrainingSimulatorProps {
  training: EmployeeTraining;
  employeeName: string;
}

export const QuickTrainingSimulator: React.FC<QuickTrainingSimulatorProps> = ({ 
  training, 
  employeeName 
}) => {
  const { updateProgress, completeTraining } = useTraining();
  const { toast } = useToast();
  const [isSimulatorOpen, setIsSimulatorOpen] = useState(false);
  const [isQuickPlaying, setIsQuickPlaying] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(training.progress);

  const handleQuickStart = () => {
    setIsQuickPlaying(true);
    toast({
      title: "Quick Training Started",
      description: `${employeeName} started ${training.training.title}`,
    });

    // Quick simulation for demo
    const interval = setInterval(() => {
      setCurrentProgress(prev => {
        const newProgress = Math.min(prev + 10, 100);
        updateProgress(training.id, newProgress, `${Math.floor(newProgress/10)} minutes`);
        
        if (newProgress >= 100) {
          setIsQuickPlaying(false);
          completeTraining(training.id, 95);
          toast({
            title: "Training Completed!",
            description: `${employeeName} completed ${training.training.title}`,
          });
          clearInterval(interval);
        }
        
        return newProgress;
      });
    }, 500);

    // Auto-stop after 5 seconds
    setTimeout(() => {
      if (isQuickPlaying) {
        setIsQuickPlaying(false);
        clearInterval(interval);
      }
    }, 5000);
  };

  const handleOpenSimulator = () => {
    setIsSimulatorOpen(true);
  };

  const handlePause = () => {
    setIsQuickPlaying(false);
    toast({
      title: "Training Paused",
      description: `${employeeName}'s training paused`,
    });
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium">{training.training.title}</CardTitle>
              <p className="text-xs text-gray-500">{employeeName}</p>
            </div>
            <Badge variant={training.status === "Completed" ? "default" : "outline"}>
              {training.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Progress</span>
              <span>{currentProgress}%</span>
            </div>
            <Progress value={currentProgress} className="h-1" />
          </div>

          <div className="flex gap-1">
            {training.status !== "Completed" && (
              <>
                {!isQuickPlaying ? (
                  <Button size="sm" onClick={handleQuickStart} className="gap-1 text-xs h-7">
                    <Play className="h-3 w-3" />
                    Quick {currentProgress > 0 ? "Continue" : "Start"}
                  </Button>
                ) : (
                  <Button size="sm" onClick={handlePause} variant="outline" className="gap-1 text-xs h-7">
                    <Pause className="h-3 w-3" />
                    Pause
                  </Button>
                )}
                
                <Button 
                  size="sm" 
                  onClick={handleOpenSimulator} 
                  variant="outline" 
                  className="gap-1 text-xs h-7"
                >
                  <ExternalLink className="h-3 w-3" />
                  Full Simulator
                </Button>
              </>
            )}
            
            {training.status === "Completed" && (
              <div className="flex items-center gap-1 text-green-600 text-xs">
                <Award className="h-3 w-3" />
                <span>Completed</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <TrainingSimulatorModal
        isOpen={isSimulatorOpen}
        onClose={() => setIsSimulatorOpen(false)}
        training={training}
        employeeName={employeeName}
      />
    </>
  );
};

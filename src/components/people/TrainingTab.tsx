
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BookOpen, Clock, Calendar, Plus, ExternalLink, Award, Play, User, Target, TrendingUp, RotateCcw, ArrowLeft } from "lucide-react";
import { EmployeeTraining } from "@/types/training";
import { AssignTrainingDialog } from "./AssignTrainingDialog";
import { MyTrainingsTab } from "./MyTrainingsTab";
import { CertificateViewer } from "../certificates/CertificateViewer";
import { TrainingSimulator } from "../training/TrainingSimulator";
import { TrainingVideoPlayer } from "../training/TrainingVideoPlayer";
import { useTraining } from "@/contexts/TrainingContext";
import { useNavigate } from "react-router-dom";

interface TrainingTabProps {
  employeeId: string;
  employeeName?: string;
}

export const TrainingTab: React.FC<TrainingTabProps> = ({ employeeId, employeeName = "Employee" }) => {
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [activeTraining, setActiveTraining] = useState<EmployeeTraining | null>(null);
  const { employeeTrainings, completeTraining } = useTraining();
  const navigate = useNavigate();
  
  // Filter trainings for the specific employee
  const filteredTrainings = employeeTrainings.filter(
    training => training.employeeId === employeeId
  );

  // Calculate enhanced statistics
  const completedCount = filteredTrainings.filter(t => t.status === "Completed").length;
  const inProgressCount = filteredTrainings.filter(t => t.status === "In Progress").length;
  const notStartedCount = filteredTrainings.filter(t => t.status === "Not Started").length;
  const overdueCount = filteredTrainings.filter(t => 
    t.status !== "Completed" && new Date(t.dueDate) < new Date()
  ).length;
  
  const totalTimeSpent = filteredTrainings.reduce((total, training) => {
    const minutes = parseInt(training.timeSpent.split(' ')[0]) || 0;
    return total + minutes;
  }, 0);

  const totalAttempts = filteredTrainings.reduce((total, training) => {
    return total + (training.attempts || 0);
  }, 0);

  const averageScore = filteredTrainings
    .filter(t => t.quizScore !== null)
    .reduce((sum, t, _, arr) => sum + (t.quizScore || 0) / arr.length, 0);

  const getStatusBadge = (status: EmployeeTraining['status']) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Not Started":
        return <Badge className="bg-gray-100 text-gray-800">Not Started</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    const colors = {
      Video: "bg-purple-100 text-purple-800",
      PDF: "bg-orange-100 text-orange-800",
      Quiz: "bg-yellow-100 text-yellow-800",
      Interactive: "bg-blue-100 text-blue-800"
    };
    return <Badge className={colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800"}>{type}</Badge>;
  };

  const handleStartTraining = (training: EmployeeTraining) => {
    setActiveTraining(training);
  };

  const handleContinueTraining = (training: EmployeeTraining) => {
    setActiveTraining(training);
  };

  const handleViewTraining = (training: EmployeeTraining) => {
    if (training.status === 'Completed') {
      navigate(`/training-detail/${training.id}?mode=review`);
    } else {
      setActiveTraining(training);
    }
  };

  const handleTrainingComplete = () => {
    if (activeTraining) {
      completeTraining(activeTraining.id, 95);
      setActiveTraining(null);
    }
  };

  const handleExitTraining = () => {
    setActiveTraining(null);
  };

  // If a training is active, show the video player
  if (activeTraining) {
    return (
      <div className="w-full min-w-0 space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleExitTraining} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Training List
          </Button>
          <div>
            <h3 className="text-lg font-semibold">{activeTraining.training.title}</h3>
            <p className="text-sm text-gray-600">Training for {employeeName}</p>
          </div>
        </div>
        
        <TrainingVideoPlayer
          trainingId={activeTraining.id}
          trainingTitle={activeTraining.training.title}
          onSaveAndExit={handleExitTraining}
          onComplete={handleTrainingComplete}
        />
      </div>
    );
  }

  return (
    <div className="w-full min-w-0 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-semibold">Training Management</h3>
          <p className="text-sm text-gray-600">Track training progress, completion status, and certificates</p>
        </div>
        <Button onClick={() => setShowAssignDialog(true)} className="gap-2 shrink-0">
          <Plus className="h-4 w-4" />
          Assign New Training
        </Button>
      </div>

      {/* Enhanced Statistics Dashboard */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">{completedCount}</div>
            <div className="text-xs text-gray-600">Completed</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Play className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">{inProgressCount}</div>
            <div className="text-xs text-gray-600">In Progress</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-6 w-6 text-gray-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-600">{notStartedCount}</div>
            <div className="text-xs text-gray-600">Not Started</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="h-6 w-6 text-red-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-red-600">{overdueCount}</div>
            <div className="text-xs text-gray-600">Overdue</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">{totalTimeSpent}</div>
            <div className="text-xs text-gray-600">Minutes</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <RotateCcw className="h-6 w-6 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-600">{totalAttempts}</div>
            <div className="text-xs text-gray-600">Attempts</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="my-trainings" className="w-full min-w-0">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="my-trainings" className="gap-2">
            <User className="h-4 w-4" />
            <span className="hidden sm:inline">My Trainings</span>
            <span className="sm:hidden">My Training</span>
          </TabsTrigger>
          <TabsTrigger value="assignments" className="gap-2">
            <BookOpen className="h-4 w-4" />
            <span className="hidden sm:inline">Assignments</span>
            <span className="sm:hidden">Assigned</span>
          </TabsTrigger>
          <TabsTrigger value="simulator" className="gap-2">
            <Play className="h-4 w-4" />
            <span className="hidden sm:inline">Simulator</span>
            <span className="sm:hidden">Sim</span>
          </TabsTrigger>
          <TabsTrigger value="certificates" className="gap-2">
            <Award className="h-4 w-4" />
            <span className="hidden sm:inline">Certificates</span>
            <span className="sm:hidden">Certs</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="my-trainings" className="space-y-6 w-full min-w-0">
          <MyTrainingsTab employeeId={employeeId} />
        </TabsContent>

        <TabsContent value="assignments" className="space-y-6 w-full min-w-0">
          <Card className="w-full min-w-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-blue-600" />
                Training Assignments Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="w-full min-w-0">
              <div className="w-full min-w-0 overflow-x-auto">
                <Table className="w-full min-w-[1200px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Training</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Time Spent</TableHead>
                      <TableHead>Attempts</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTrainings.map((training) => (
                      <TableRow key={training.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{training.training.title}</div>
                            <div className="text-sm text-gray-500">{training.training.category}</div>
                          </div>
                        </TableCell>
                        <TableCell>{getTypeBadge(training.training.type)}</TableCell>
                        <TableCell>{getStatusBadge(training.status)}</TableCell>
                        <TableCell>
                          <div className="w-full max-w-[100px]">
                            <Progress value={training.progress} className="h-2" />
                            <div className="text-xs text-gray-500 mt-1">{training.progress}%</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            {new Date(training.dueDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Clock className="h-4 w-4 text-gray-400" />
                            {training.timeSpent}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <RotateCcw className="h-4 w-4 text-gray-400" />
                            {training.attempts || 0}
                          </div>
                        </TableCell>
                        <TableCell>
                          {training.quizScore ? (
                            <div className="flex items-center gap-1 text-sm">
                              <Award className="h-4 w-4 text-yellow-500" />
                              <span className="font-medium">{training.quizScore}%</span>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">N/A</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            {training.status === 'In Progress' && (
                              <Button 
                                variant="default" 
                                size="sm" 
                                className="gap-1"
                                onClick={() => handleContinueTraining(training)}
                              >
                                <Play className="h-3 w-3" />
                                Continue
                              </Button>
                            )}
                            {training.status === 'Not Started' && (
                              <Button 
                                variant="default" 
                                size="sm" 
                                className="gap-1"
                                onClick={() => handleStartTraining(training)}
                              >
                                <Play className="h-3 w-3" />
                                Start
                              </Button>
                            )}
                            {training.status === 'Completed' && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="gap-1"
                                onClick={() => handleViewTraining(training)}
                              >
                                <ExternalLink className="h-3 w-3" />
                                Review
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {filteredTrainings.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No training assignments found for this employee.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="simulator" className="space-y-4 w-full min-w-0">
          <div>
            <h3 className="text-lg font-semibold mb-2">Training Simulator</h3>
            <p className="text-sm text-gray-600 mb-4">
              Interactive training experience - Start, progress through, and complete assigned trainings
            </p>
          </div>
          
          {filteredTrainings.length > 0 ? (
            <div className="space-y-4 w-full min-w-0">
              {filteredTrainings.map((training) => (
                <TrainingSimulator key={training.id} training={training} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8 text-gray-500">
                <Play className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No training assignments to simulate.</p>
                <p className="text-sm">Assign some trainings first to see the simulator in action.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="certificates" className="w-full min-w-0">
          <CertificateViewer employeeId={employeeId} employeeName={employeeName} />
        </TabsContent>
      </Tabs>

      <AssignTrainingDialog 
        open={showAssignDialog} 
        onOpenChange={setShowAssignDialog}
        employeeId={employeeId}
      />
    </div>
  );
};

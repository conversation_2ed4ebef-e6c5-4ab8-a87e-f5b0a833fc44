
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Person } from "@/types/people";
import { StatusBadge } from "./StatusBadge";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";

interface PeopleTableRowProps {
  person: Person;
  index: number;
  onClick: () => void;
  isSelected: boolean;
}

export const PeopleTableRow: React.FC<PeopleTableRowProps> = ({ 
  person, 
  index, 
  onClick,
  isSelected 
}) => {
  return (
    <TableRow 
      className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 cursor-pointer transition-colors ${isSelected ? 'bg-blue-100' : ''}`}
      onClick={onClick}
    >
      <TableCell className="font-medium">{person.name}</TableCell>
      <TableCell>{person.jobTitle}</TableCell>
      <TableCell>{person.email}</TableCell>
      <TableCell>{person.department}</TableCell>
      <TableCell>
        <div className="flex flex-wrap gap-1">
          {person.processes.slice(0, 2).map((process, idx) => (
            <span key={idx} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
              {process}
            </span>
          ))}
          {person.processes.length > 2 && (
            <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
              +{person.processes.length - 2}
            </span>
          )}
        </div>
      </TableCell>
      <TableCell>{new Date(person.joiningDate).toLocaleDateString()}</TableCell>
      <TableCell>{person.manager || '--'}</TableCell>
      <TableCell>
        <StatusBadge status={person.status} />
      </TableCell>
      <TableCell className="text-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            onClick();
          }}
        >
          <Eye className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Settings } from "lucide-react";
import { PeopleSearch } from "./PeopleSearch";
import { PeopleTable } from "./PeopleTable";
import { TrainingDashboard } from "./training-dashboard/TrainingDashboard";
import { MyTrainingsTab } from "./MyTrainingsTab";
import { PeopleSettingsModal } from "./PeopleSettingsModal";
import { usePeopleData } from "@/hooks/usePeopleData";

export const PeopleAdminView = () => {
  const { filteredPeople, searchTerm, setSearchTerm } = usePeopleData();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Mock current user ID for My Trainings tab
  const currentEmployeeId = "emp001";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div></div>
        <Button 
          variant="outline" 
          onClick={() => setIsSettingsOpen(true)}
          className="gap-2"
        >
          <Settings className="h-4 w-4" />
          Settings
        </Button>
      </div>

      <Tabs defaultValue="people" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="people">People</TabsTrigger>
          <TabsTrigger value="training">Training Dashboard</TabsTrigger>
          <TabsTrigger value="my-trainings">My Trainings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="people" className="space-y-6">
          <PeopleSearch value={searchTerm} onChange={setSearchTerm} />
          <PeopleTable people={filteredPeople} searchTerm={searchTerm} />
        </TabsContent>
        
        <TabsContent value="training" className="space-y-6">
          <TrainingDashboard />
        </TabsContent>
        
        <TabsContent value="my-trainings" className="space-y-6">
          <MyTrainingsTab employeeId={currentEmployeeId} />
        </TabsContent>
      </Tabs>

      <PeopleSettingsModal 
        open={isSettingsOpen} 
        onOpenChange={setIsSettingsOpen} 
      />
    </div>
  );
};

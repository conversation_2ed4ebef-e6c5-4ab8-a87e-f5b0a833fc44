
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { User } from "lucide-react";
import { Person } from "@/types/people";

interface EmployeeHeaderProps {
  employee: Person;
}

export const EmployeeHeader: React.FC<EmployeeHeaderProps> = ({ employee }) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="h-8 w-8 text-blue-600" />
          </div>
          <div className="flex-1">
            <h2 className="text-2xl font-bold">{employee.name}</h2>
            <p className="text-lg text-gray-600 mb-2">{employee.jobTitle}</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-500">Email:</span>
                <p>{employee.email}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Department:</span>
                <p>{employee.department}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Processes:</span>
                <p>{employee.processes}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Joining Date:</span>
                <p>{new Date(employee.joiningDate).toLocaleDateString()}</p>
              </div>
              <div>
                <span className="font-medium text-gray-500">Status:</span>
                <p>{employee.status}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

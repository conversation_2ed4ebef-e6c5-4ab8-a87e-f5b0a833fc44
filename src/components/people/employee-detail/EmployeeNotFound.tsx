
import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";

export const EmployeeNotFound: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="p-4 md:p-6 flex-1 overflow-auto w-full">
      <div className="mb-4">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => navigate('/people')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to People
        </Button>
        <h1 className="text-2xl font-bold mt-2">Employee Not Found</h1>
      </div>
      
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-600">The employee you're looking for could not be found.</p>
          <Button 
            onClick={() => navigate('/people')} 
            className="mt-4"
          >
            Return to People List
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};


import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Person } from "@/types/people";

interface EmployeeProfileProps {
  employee: Person;
}

export const EmployeeProfile: React.FC<EmployeeProfileProps> = ({ employee }) => {
  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold mb-4">Employee Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-sm mt-1">{employee.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Employee ID</label>
              <p className="text-sm mt-1">{employee.id}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Job Title</label>
              <p className="text-sm mt-1">{employee.jobTitle}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email Address</label>
              <p className="text-sm mt-1">{employee.email}</p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Department</label>
              <p className="text-sm mt-1">{employee.department}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Processes</label>
              <p className="text-sm mt-1">{employee.processes}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Start Date</label>
              <p className="text-sm mt-1">{new Date(employee.joiningDate).toLocaleDateString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Employment Status</label>
              <p className="text-sm mt-1">{employee.status}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Users, BookOpen, Calendar, Award, BarChart, CheckCircle } from "lucide-react";

interface PeopleTrainingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PeopleTrainingModal: React.FC<PeopleTrainingModalProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Users className="h-6 w-6 text-blue-600" />
            People & Training Management
          </DialogTitle>
          <DialogDescription>
            Learn how to effectively manage employees and their training requirements
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* People Management Section */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Users className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">People Management</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Employee Profiles</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• View detailed employee information</li>
                    <li>• Track roles and responsibilities</li>
                    <li>• Monitor training progress</li>
                    <li>• Access contact details</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Key Features</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Search and filter employees</li>
                    <li>• Quick access to training records</li>
                    <li>• Performance tracking</li>
                    <li>• Department organization</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Training Management Section */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <BookOpen className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold">Training Management</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-orange-500 mt-1" />
                  <div>
                    <h4 className="font-medium">Assignment & Scheduling</h4>
                    <p className="text-sm text-gray-600">Assign trainings to employees with due dates and track progress</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <BarChart className="h-5 w-5 text-purple-500 mt-1" />
                  <div>
                    <h4 className="font-medium">Progress Tracking</h4>
                    <p className="text-sm text-gray-600">Monitor completion rates, quiz scores, and time spent</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Award className="h-5 w-5 text-yellow-500 mt-1" />
                  <div>
                    <h4 className="font-medium">Compliance</h4>
                    <p className="text-sm text-gray-600">Ensure regulatory compliance and certification requirements</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Training Status Guide */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Training Status Guide</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                  <span className="text-sm">Not Started - Training assigned but not begun</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">In Progress - Currently working through content</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Completed - Finished with passing score</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">For Employees</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Click "My Trainings" tab to view assigned training</li>
                    <li>• Use "Start" to begin new training</li>
                    <li>• Use "Continue" to resume in-progress training</li>
                    <li>• Use "Review" to revisit completed training</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">For Managers</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Filter by status to find overdue trainings</li>
                    <li>• Monitor quiz scores and completion rates</li>
                    <li>• Track compliance requirements</li>
                    <li>• Generate training reports</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={onClose} className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Got it!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

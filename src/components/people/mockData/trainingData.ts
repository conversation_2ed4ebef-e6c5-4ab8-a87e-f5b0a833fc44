
import { EmployeeTraining } from '@/types/training';

export const mockTrainingData: EmployeeTraining[] = [
  // <PERSON> (EMPID12345) - Puducherry
  {
    id: "training-1",
    trainingId: "t-001",
    employeeId: "EMPID12345",
    training: {
      id: "t-001",
      title: "ISO 9001:2015 Quality Management Fundamentals",
      type: "Video",
      description: "Comprehensive overview of ISO 9001:2015 requirements and implementation",
      estimatedDuration: "45",
      category: "Quality Management",
      language: "English",
      roleApplicability: ["Quality Engineer", "Manager"],
      lastUpdated: "2024-01-01",
      createdBy: "Training Admin",
      tags: ["ISO", "Quality", "Management"],
      difficulty: "Intermediate",
      requirements: ["Basic understanding of quality concepts"],
      learningObjectives: [
        "Understand ISO 9001:2015 structure",
        "Learn process approach",
        "Apply risk-based thinking"
      ]
    },
    status: "Completed",
    progress: 100,
    assignedDate: "2024-01-15",
    dueDate: "2024-02-15",
    timeSpent: "48 minutes",
    completedDate: "2024-02-10",
    quizScore: 92,
    assignedBy: "Training Admin",
    attempts: 2,
    lastAccessed: "2024-02-10",
    certificateId: "CERT-001"
  },
  {
    id: "training-2",
    trainingId: "t-002",
    employeeId: "EMPID12345",
    training: {
      id: "t-002",
      title: "Safety Protocols and Procedures",
      type: "Interactive",
      description: "Essential safety training for all employees",
      estimatedDuration: "30",
      category: "Safety",
      language: "English",
      roleApplicability: ["All Employees"],
      lastUpdated: "2024-01-01",
      createdBy: "Safety Officer",
      tags: ["Safety", "Protocols", "Procedures"],
      difficulty: "Beginner",
      requirements: ["None"],
      learningObjectives: [
        "Identify workplace hazards",
        "Follow safety procedures",
        "Use protective equipment"
      ]
    },
    status: "In Progress",
    progress: 65,
    assignedDate: "2024-01-10",
    dueDate: "2024-02-10",
    timeSpent: "20 minutes",
    completedDate: null,
    quizScore: null,
    assignedBy: "Safety Officer",
    attempts: 1,
    lastAccessed: "2024-01-25"
  },
  // Sarah Johnson (EMPID12346) - Puducherry
  {
    id: "training-3",
    trainingId: "t-003",
    employeeId: "EMPID12346",
    training: {
      id: "t-003",
      title: "Document Control and Management",
      type: "Video",
      description: "Learn how to manage and control documents effectively",
      estimatedDuration: "35",
      category: "Documentation",
      language: "English",
      roleApplicability: ["Document Controller", "Quality Engineer"],
      lastUpdated: "2024-01-01",
      createdBy: "Compliance Manager",
      tags: ["Documentation", "Control", "Management"],
      difficulty: "Intermediate",
      requirements: ["Basic computer skills"],
      learningObjectives: [
        "Understand document lifecycle",
        "Implement version control",
        "Ensure document integrity"
      ]
    },
    status: "Completed",
    progress: 100,
    assignedDate: "2024-01-05",
    dueDate: "2024-02-05",
    timeSpent: "35 minutes",
    completedDate: "2024-01-25",
    quizScore: 88,
    assignedBy: "Compliance Manager",
    attempts: 1,
    lastAccessed: "2024-01-25",
    certificateId: "CERT-002"
  },
  {
    id: "training-4",
    trainingId: "t-001",
    employeeId: "EMPID12346",
    training: {
      id: "t-001",
      title: "ISO 9001:2015 Quality Management Fundamentals",
      type: "Video",
      description: "Comprehensive overview of ISO 9001:2015 requirements and implementation",
      estimatedDuration: "45",
      category: "Quality Management",
      language: "English",
      roleApplicability: ["Quality Engineer", "Manager"],
      lastUpdated: "2024-01-01",
      createdBy: "Training Admin",
      tags: ["ISO", "Quality", "Management"],
      difficulty: "Intermediate",
      requirements: ["Basic understanding of quality concepts"],
      learningObjectives: [
        "Understand ISO 9001:2015 structure",
        "Learn process approach",
        "Apply risk-based thinking"
      ]
    },
    status: "In Progress",
    progress: 30,
    assignedDate: "2024-02-01",
    dueDate: "2024-03-01",
    timeSpent: "15 minutes",
    completedDate: null,
    quizScore: null,
    assignedBy: "Training Admin",
    attempts: 1,
    lastAccessed: "2024-02-05"
  },
  // Raj Patel (EMPID12347) - Puducherry
  {
    id: "training-5",
    trainingId: "t-004",
    employeeId: "EMPID12347",
    training: {
      id: "t-004",
      title: "Supplier Quality Management",
      type: "Interactive",
      description: "Advanced training on managing supplier quality relationships",
      estimatedDuration: "60",
      category: "Supplier Management",
      language: "English",
      roleApplicability: ["Supplier Quality Engineer", "Manager"],
      lastUpdated: "2024-01-01",
      createdBy: "Quality Director",
      tags: ["Supplier", "Quality", "Management"],
      difficulty: "Advanced",
      requirements: ["ISO 9001 fundamentals"],
      learningObjectives: [
        "Assess supplier capabilities",
        "Implement supplier audits",
        "Manage supplier performance"
      ]
    },
    status: "Not Started",
    progress: 0,
    assignedDate: "2024-02-01",
    dueDate: "2024-03-01",
    timeSpent: "0 minutes",
    completedDate: null,
    quizScore: null,
    assignedBy: "Quality Director",
    attempts: 0,
    lastAccessed: null
  },
  {
    id: "training-6",
    trainingId: "t-002",
    employeeId: "EMPID12347",
    training: {
      id: "t-002",
      title: "Safety Protocols and Procedures",
      type: "Interactive",
      description: "Essential safety training for all employees",
      estimatedDuration: "30",
      category: "Safety",
      language: "English",
      roleApplicability: ["All Employees"],
      lastUpdated: "2024-01-01",
      createdBy: "Safety Officer",
      tags: ["Safety", "Protocols", "Procedures"],
      difficulty: "Beginner",
      requirements: ["None"],
      learningObjectives: [
        "Identify workplace hazards",
        "Follow safety procedures",
        "Use protective equipment"
      ]
    },
    status: "Completed",
    progress: 100,
    assignedDate: "2024-01-08",
    dueDate: "2024-02-08",
    timeSpent: "32 minutes",
    completedDate: "2024-01-20",
    quizScore: 95,
    assignedBy: "Safety Officer",
    attempts: 1,
    lastAccessed: "2024-01-20",
    certificateId: "CERT-003"
  },
  // Maria Garcia (EMPID12348) - Puducherry
  {
    id: "training-7",
    trainingId: "t-003",
    employeeId: "EMPID12348",
    training: {
      id: "t-003",
      title: "Document Control and Management",
      type: "Video",
      description: "Learn how to manage and control documents effectively",
      estimatedDuration: "35",
      category: "Documentation",
      language: "English",
      roleApplicability: ["Document Controller", "Quality Engineer"],
      lastUpdated: "2024-01-01",
      createdBy: "Compliance Manager",
      tags: ["Documentation", "Control", "Management"],
      difficulty: "Intermediate",
      requirements: ["Basic computer skills"],
      learningObjectives: [
        "Understand document lifecycle",
        "Implement version control",
        "Ensure document integrity"
      ]
    },
    status: "In Progress",
    progress: 75,
    assignedDate: "2024-01-20",
    dueDate: "2024-02-20",
    timeSpent: "28 minutes",
    completedDate: null,
    quizScore: null,
    assignedBy: "Compliance Manager",
    attempts: 2,
    lastAccessed: "2024-02-01"
  },
  // Emily Rodriguez (SJ001) - San Jose
  {
    id: "training-8",
    trainingId: "t-001",
    employeeId: "SJ001",
    training: {
      id: "t-001",
      title: "ISO 9001:2015 Quality Management Fundamentals",
      type: "Video",
      description: "Comprehensive overview of ISO 9001:2015 requirements and implementation",
      estimatedDuration: "45",
      category: "Quality Management",
      language: "English",
      roleApplicability: ["Quality Engineer", "Manager"],
      lastUpdated: "2024-01-01",
      createdBy: "Training Admin",
      tags: ["ISO", "Quality", "Management"],
      difficulty: "Intermediate",
      requirements: ["Basic understanding of quality concepts"],
      learningObjectives: [
        "Understand ISO 9001:2015 structure",
        "Learn process approach",
        "Apply risk-based thinking"
      ]
    },
    status: "Completed",
    progress: 100,
    assignedDate: "2024-01-12",
    dueDate: "2024-02-12",
    timeSpent: "50 minutes",
    completedDate: "2024-02-05",
    quizScore: 96,
    assignedBy: "Training Admin",
    attempts: 1,
    lastAccessed: "2024-02-05",
    certificateId: "CERT-004"
  },
  {
    id: "training-9",
    trainingId: "t-004",
    employeeId: "SJ001",
    training: {
      id: "t-004",
      title: "Supplier Quality Management",
      type: "Interactive",
      description: "Advanced training on managing supplier quality relationships",
      estimatedDuration: "60",
      category: "Supplier Management",
      language: "English",
      roleApplicability: ["Supplier Quality Engineer", "Manager"],
      lastUpdated: "2024-01-01",
      createdBy: "Quality Director",
      tags: ["Supplier", "Quality", "Management"],
      difficulty: "Advanced",
      requirements: ["ISO 9001 fundamentals"],
      learningObjectives: [
        "Assess supplier capabilities",
        "Implement supplier audits",
        "Manage supplier performance"
      ]
    },
    status: "In Progress",
    progress: 40,
    assignedDate: "2024-02-05",
    dueDate: "2024-03-05",
    timeSpent: "25 minutes",
    completedDate: null,
    quizScore: null,
    assignedBy: "Quality Director",
    attempts: 1,
    lastAccessed: "2024-02-08"
  }
];

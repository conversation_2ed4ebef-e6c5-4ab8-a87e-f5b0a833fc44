
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Star, FileText, MapPin, MessageSquare } from "lucide-react";

interface VendorInfoCardsProps {
  vendor: {
    rating: number;
    contracts: number;
    location: string;
    lastOrder: string;
  };
}

export const VendorInfoCards: React.FC<VendorInfoCardsProps> = ({ vendor }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            <div>
              <p className="text-sm text-gray-600">Rating</p>
              <p className="text-xl font-bold">{vendor.rating}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm text-gray-600">Contracts</p>
              <p className="text-xl font-bold">{vendor.contracts}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">Location</p>
              <p className="text-sm font-medium">{vendor.location}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-purple-600" />
            <div>
              <p className="text-sm text-gray-600">Last Order</p>
              <p className="text-sm font-medium">{vendor.lastOrder}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

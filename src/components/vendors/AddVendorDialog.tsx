
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { AddVendorForm } from "./AddVendorForm";
import { toast } from "sonner";

interface AddVendorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const AddVendorDialog: React.FC<AddVendorDialogProps> = ({ open, onOpenChange }) => {
  const handleSubmit = (data: any) => {
    // Would normally submit data to server/API
    console.log("Submitted vendor data:", data);
    toast.success(`Vendor ${data.vendorName} added successfully`);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] p-0 max-h-[85vh] overflow-y-auto">
        <DialogHeader className="bg-gray-50 p-6 border-b border-gray-200">
          <DialogTitle className="text-xl font-semibold text-gray-800">Create Vendor</DialogTitle>
          <DialogDescription className="text-gray-600">
            Add a new vendor to your vendor management system. Fill in the details below.
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6">
          <AddVendorForm onSubmit={handleSubmit} onCancel={handleCancel} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

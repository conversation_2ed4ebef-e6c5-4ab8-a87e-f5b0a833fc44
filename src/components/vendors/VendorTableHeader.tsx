
import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ChevronDown, ChevronUp, ArrowUpDown } from "lucide-react";

interface VendorTableHeaderProps {
  sortField: string | null;
  sortDirection: "asc" | "desc";
  onSort: (field: string) => void;
  isMobile: boolean;
}

export const VendorTableHeader: React.FC<VendorTableHeaderProps> = ({
  sortField,
  sortDirection,
  onSort,
  isMobile
}) => {
  const getSortIcon = (field: string) => {
    if (sortField !== field) return <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />;
    return sortDirection === 'asc' 
      ? <ChevronUp className="ml-1 h-4 w-4" />
      : <ChevronDown className="ml-1 h-4 w-4" />;
  };

  if (isMobile) {
    return (
      <TableHeader>
        <TableRow className="bg-gray-50">
          <TableHead 
            className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
            onClick={() => onSort('name')}
          >
            <div className="flex items-center">
              Vendor
              {getSortIcon('name')}
            </div>
          </TableHead>
          <TableHead 
            className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
            onClick={() => onSort('status')}
          >
            <div className="flex items-center">
              Status
              {getSortIcon('status')}
            </div>
          </TableHead>
          <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider">
            Actions
          </TableHead>
        </TableRow>
      </TableHeader>
    );
  }

  return (
    <TableHeader>
      <TableRow className="bg-gray-50">
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('name')}
        >
          <div className="flex items-center">
            Vendor Name
            {getSortIcon('name')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('productService')}
        >
          <div className="flex items-center">
            Product/Service
            {getSortIcon('productService')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('assignee')}
        >
          <div className="flex items-center">
            Assignee
            {getSortIcon('assignee')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('status')}
        >
          <div className="flex items-center">
            Status
            {getSortIcon('status')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('lastEvaluationDate')}
        >
          <div className="flex items-center">
            Last Evaluation
            {getSortIcon('lastEvaluationDate')}
          </div>
        </TableHead>
        <TableHead 
          className="cursor-pointer hover:bg-gray-100 text-xs font-semibold text-gray-600 uppercase tracking-wider"
          onClick={() => onSort('reviewPeriod')}
        >
          <div className="flex items-center">
            Review Period
            {getSortIcon('reviewPeriod')}
          </div>
        </TableHead>
        <TableHead className="text-xs font-semibold text-gray-600 uppercase tracking-wider">
          Actions
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};


import React from "react";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>Down, <PERSON>U<PERSON>, ArrowUpDown } from "lucide-react";
import { SortField, SortDirection } from "@/hooks/useVendorTableSort";

interface MobileVendorTableHeaderProps {
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
}

export const MobileVendorTableHeader: React.FC<MobileVendorTableHeaderProps> = ({ 
  sortField, 
  sortDirection, 
  onSort 
}) => {
  const renderSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHeader>
      <TableRow>
        <TableHead className="py-3 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider w-10">
          <span className="sr-only">Expand</span>
        </TableHead>
        <TableHead 
          onClick={() => onSort("name")}
          className="py-3 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 group cursor-pointer"
        >
          <div className="flex items-center">
            Vendor
            {renderSortIcon("name")}
          </div>
        </TableHead>
        <TableHead 
          onClick={() => onSort("status")}
          className="py-3 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 group cursor-pointer w-[100px]"
        >
          <div className="flex items-center">
            Status
            {renderSortIcon("status")}
          </div>
        </TableHead>
      </TableRow>
    </TableHeader>
  );
};

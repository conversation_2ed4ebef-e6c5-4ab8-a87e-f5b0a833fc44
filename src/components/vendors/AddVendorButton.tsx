
import React from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

interface AddVendorButtonProps {
  onClick: () => void;
}

export const AddVendorButton: React.FC<AddVendorButtonProps> = ({ onClick }) => {
  const isMobile = useIsMobile();

  return (
    <Button 
      onClick={onClick} 
      className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2"
    >
      <Plus className="h-4 w-4" />
      {!isMobile && "Add Vendor"}
      {isMobile && "Add"}
    </Button>
  );
};

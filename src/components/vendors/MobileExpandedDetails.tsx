
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Vendor } from "@/types/vendors";
import { format } from "date-fns";

interface MobileExpandedDetailsProps {
  vendor: Vendor;
  index: number;
}

export const MobileExpandedDetails: React.FC<MobileExpandedDetailsProps> = ({ vendor, index }) => {
  const formatDate = (dateString: string): string => {
    if (!dateString) return "--";
    try {
      const date = new Date(dateString);
      return format(date, "MMM d yyyy");
    } catch (error) {
      return dateString;
    }
  };

  const renderDetailRow = (label: string, value: string) => {
    return (
      <div className="mb-2">
        <span className="text-xs text-gray-500">{label}:</span>
        <span className="ml-2 text-sm font-medium">{value || "--"}</span>
      </div>
    );
  };

  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
      <TableCell colSpan={3} className="p-3">
        <div className="px-2 py-2 space-y-1">
          {renderDetailRow("Product/Service", vendor.productService)}
          {renderDetailRow("Contact", vendor.contactName || "")}
          {renderDetailRow("Email", vendor.contactEmail || "")}
          {renderDetailRow("Phone", vendor.contactPhone || "")}
          {renderDetailRow("Assignee", vendor.assignee || "")}
          {renderDetailRow("Last Eval.", formatDate(vendor.lastEvaluationDate || ""))}
          {renderDetailRow("Next Eval.", formatDate(vendor.nextEvaluationDate || ""))}
          {renderDetailRow("Review Period", vendor.reviewPeriod || "")}
        </div>
      </TableCell>
    </TableRow>
  );
};

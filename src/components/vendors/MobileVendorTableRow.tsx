
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Vendor } from "@/types/vendors";
import { ChevronDown, ChevronUp, Edit, Trash2, Eye } from "lucide-react";
import { MobileExpandedDetails } from "./MobileExpandedDetails";
import { StatusBadge } from "./StatusBadge";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface MobileVendorTableRowProps {
  vendor: Vendor;
  index: number;
}

export const MobileVendorTableRow: React.FC<MobileVendorTableRowProps> = ({ vendor, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleDelete = () => {
    toast.success(`Deleting vendor: ${vendor.name}`);
  };

  const handleEdit = () => {
    toast.success(`Editing vendor: ${vendor.name}`);
  };

  const handleView = () => {
    toast.success(`Viewing vendor: ${vendor.name}`);
  };

  return (
    <>
      <TableRow className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
        <TableCell className="py-2 px-2 w-10">
          <Button 
            variant="ghost" 
            size="sm" 
            className="p-0 h-8 w-8"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </Button>
        </TableCell>
        <TableCell className="py-3 px-2">
          <div className="flex flex-col">
            <span className="text-blue-600 font-medium">{vendor.name}</span>
            <span className="text-xs text-gray-500 mt-0.5">ID: {vendor.id}</span>
          </div>
        </TableCell>
        <TableCell className="py-3 px-2 text-right">
          <StatusBadge status={vendor.status} />
        </TableCell>
      </TableRow>

      {isExpanded && (
        <>
          {/* Action buttons row - moved to the top when expanded */}
          <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
            <TableCell colSpan={3} className="px-2 py-1">
              <div className="flex items-center justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={handleView}
                      >
                        <Eye className="h-4 w-4 text-blue-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={handleEdit}
                      >
                        <Edit className="h-4 w-4 text-amber-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={handleDelete}
                      >
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableCell>
          </TableRow>

          <MobileExpandedDetails vendor={vendor} index={index} />
        </>
      )}
    </>
  );
};


import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface VendorDetailsTabsProps {
  vendor: {
    id: string;
    name: string;
    category: string;
    status: string;
    contact: string;
    phone: string;
    description: string;
    paymentTerms: string;
    deliveryTerms: string;
  };
}

export const VendorDetailsTabs: React.FC<VendorDetailsTabsProps> = ({ vendor }) => {
  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="contacts">Contacts</TabsTrigger>
        <TabsTrigger value="documents">Documents</TabsTrigger>
        <TabsTrigger value="performance">Performance</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Vendor Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900">Description</h4>
              <p className="text-gray-600">{vendor.description}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900">Payment Terms</h4>
                <p className="text-gray-600">{vendor.paymentTerms}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Delivery Terms</h4>
                <p className="text-gray-600">{vendor.deliveryTerms}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="contacts" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900">Primary Contact</h4>
              <p className="text-gray-600">{vendor.contact}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Phone</h4>
              <p className="text-gray-600">{vendor.phone}</p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="documents" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">No documents available for this vendor.</p>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="performance" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">Performance data will be displayed here.</p>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};


import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";

interface EmptyVendorRowProps {
  colSpan: number;
}

export const EmptyVendorRow: React.FC<EmptyVendorRowProps> = ({ colSpan }) => {
  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="h-24 text-center">
        <div className="flex flex-col items-center justify-center text-gray-500">
          <p className="font-medium">No vendors found</p>
          <p className="text-sm mt-1">Add a new vendor to get started</p>
        </div>
      </TableCell>
    </TableRow>
  );
};


import React, { useEffect, useRef } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";

interface VendorSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export const VendorSearchModal: React.FC<VendorSearchModalProps> = ({
  isOpen,
  onClose,
  searchTerm,
  onSearchChange,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleClear = () => {
    onSearchChange("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="p-0 gap-0 max-w-[100vw] w-full top-0 translate-y-0 rounded-none border-0">
        <div className="flex items-center p-4 w-full bg-white border-b">
          <Search className="h-5 w-5 text-gray-400 mr-2" />
          <Input
            ref={inputRef}
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="flex-1 border-none text-base h-10 focus-visible:ring-0 pl-0 shadow-none"
            placeholder="Search vendors..."
            autoComplete="off"
          />
          {searchTerm ? (
            <button
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          ) : (
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

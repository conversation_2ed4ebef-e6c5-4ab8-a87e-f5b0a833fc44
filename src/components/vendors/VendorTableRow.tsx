
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Vendor } from "@/types/vendors";

interface VendorTableRowProps {
  vendor: Vendor;
  index: number;
  isMobile: boolean;
}

export const VendorTableRow: React.FC<VendorTableRowProps> = ({
  vendor,
  index,
  isMobile
}) => {
  const navigate = useNavigate();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Approved":
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case "Under evaluation":
        return <Badge className="bg-orange-100 text-orange-800">Under evaluation</Badge>;
      case "Rejected":
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleViewVendor = () => {
    navigate(`/vendors/${vendor.id}`);
  };

  if (isMobile) {
    return (
      <TableRow className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
        <TableCell>
          <div>
            <div className="font-medium text-blue-600">{vendor.name}</div>
            <div className="text-sm text-gray-500">{vendor.productService}</div>
            <div className="text-xs text-gray-500">ID: {vendor.id}</div>
          </div>
        </TableCell>
        <TableCell>{getStatusBadge(vendor.status)}</TableCell>
        <TableCell>
          <Button size="sm" variant="outline" onClick={handleViewVendor}>
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
        </TableCell>
      </TableRow>
    );
  }

  return (
    <TableRow className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
      <TableCell>
        <div className="font-medium text-blue-600">{vendor.name}</div>
        <div className="text-xs text-gray-500">ID: {vendor.id}</div>
      </TableCell>
      <TableCell>{vendor.productService}</TableCell>
      <TableCell>{vendor.assignee || "Not assigned"}</TableCell>
      <TableCell>{getStatusBadge(vendor.status)}</TableCell>
      <TableCell>{vendor.lastEvaluationDate || "N/A"}</TableCell>
      <TableCell>{vendor.reviewPeriod}</TableCell>
      <TableCell>
        <Button size="sm" variant="outline" onClick={handleViewVendor}>
          <Eye className="h-4 w-4 mr-1" />
          View
        </Button>
      </TableCell>
    </TableRow>
  );
};

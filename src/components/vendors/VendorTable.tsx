
import React, { useState } from "react";
import { Vendor } from "@/types/vendors";
import { useIsMobile } from "@/hooks/use-mobile";
import { useVendorPagination } from "@/hooks/useVendorPagination";
import { useVendorTableSort } from "@/hooks/useVendorTableSort";
import { AddVendorButton } from "./AddVendorButton";
import { AddVendorDialog } from "./AddVendorDialog";
import { StatusBadge } from "./StatusBadge";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { GenericTable, GenericTableColumn } from "@/components/shared/table/GenericTable";

interface VendorTableProps {
  vendors: Vendor[];
}

export const VendorTable: React.FC<VendorTableProps> = ({ vendors }) => {
  const isMobile = useIsMobile();
  const [dialogOpen, setDialogOpen] = useState(false);

  // Pagination
  const {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData
  } = useVendorPagination({
    totalCount: vendors.length,
    initialPageSize: 10,
    initialPage: 1
  });

  // Sort
  const { sortField, sortDirection, handleSort, sortedItems } = useVendorTableSort(getPaginatedData(vendors));

  const handleOpenDialog = () => setDialogOpen(true);

  // Columns definition (domain-specific, customize here)
  const columns: GenericTableColumn<Vendor>[] = [
    {
      field: "name",
      header: "Name",
      sortable: true,
      cellRenderer: (item) => (
        <span className="font-medium text-blue-700">{item.name}</span>
      ),
    },
    {
      field: "location",
      header: "Location",
      sortable: true,
    },
    {
      field: "status",
      header: "Status",
      sortable: true,
      cellRenderer: (item) => <StatusBadge status={item.status} />,
    },
  ];

  // Optional actions column logic
  const renderActions = (item: Vendor) => (
    <div className="flex gap-2 justify-end">
      <Button variant="ghost" size="icon">
        <Edit className="h-4 w-4 text-blue-500" />
      </Button>
      <Button variant="ghost" size="icon">
        <Trash2 className="h-4 w-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <div className="space-y-4 w-full">
      <div className="flex justify-end">
        <AddVendorButton onClick={handleOpenDialog} />
      </div>

      {/* FIX: Remove <Vendor> here */}
      <GenericTable
        columns={columns}
        data={sortedItems}
        totalCount={vendors.length}
        currentPage={currentPage}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        isMobile={isMobile}
        renderActions={renderActions}
        emptyMessage="No vendors found"
      />

      <AddVendorDialog open={dialogOpen} onOpenChange={setDialogOpen} />
    </div>
  );
};

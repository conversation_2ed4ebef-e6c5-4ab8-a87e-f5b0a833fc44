
import React, { useState } from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { VendorSearchModal } from "./VendorSearchModal";

interface VendorSearchProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  className?: string;
}

export const VendorSearch: React.FC<VendorSearchProps> = ({
  searchTerm,
  onSearchChange,
  className
}) => {
  const isMobile = useIsMobile();
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const handleClear = () => {
    onSearchChange("");
  };
  
  if (isMobile) {
    return (
      <>
        <div 
          className={cn("relative w-full", className)} 
          onClick={() => setIsModalOpen(true)}
        >
          <div className="flex items-center border border-gray-300 rounded-md h-11 px-3">
            <Search className="text-gray-400 h-4 w-4 flex-shrink-0" />
            <div className="ml-2 text-gray-500 truncate flex-1">
              {searchTerm || "Search vendors..."}
            </div>
          </div>
        </div>
        
        <VendorSearchModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
        />
      </>
    );
  }
  
  return (
    <div className={cn("relative w-full", className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
      <Input
        type="text"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        className={cn(
          "pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
          "text-sm"
        )}
        placeholder="Search by name, service, contact details or status"
      />
      {searchTerm.length > 0 && (
        <button 
          onClick={handleClear}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

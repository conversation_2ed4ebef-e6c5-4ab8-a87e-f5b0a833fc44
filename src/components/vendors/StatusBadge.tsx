
import React from "react";

interface StatusBadgeProps {
  status: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  let bgColor = "bg-gray-100";
  let textColor = "text-gray-800";
  
  switch (status.toLowerCase()) {
    case "approved":
      bgColor = "bg-green-100";
      textColor = "text-green-800";
      break;
    case "under evaluation":
      bgColor = "bg-yellow-100";
      textColor = "text-yellow-800";
      break;
    case "rejected":
      bgColor = "bg-red-100";
      textColor = "text-red-800";
      break;
    case "pending":
      bgColor = "bg-blue-100";
      textColor = "text-blue-800";
      break;
    default:
      break;
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
      {status}
    </span>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronRight, Package, Factory, Truck } from "lucide-react";

interface TraceNode {
  id: string;
  name: string;
  type: "supplier" | "material" | "process" | "product";
  status: "good" | "warning" | "critical";
  children?: TraceNode[];
  details: {
    location?: string;
    date?: string;
    quantity?: string;
    batchNumber?: string;
  };
}

const mockTraceData: TraceNode = {
  id: "final-product",
  name: "Automotive Part #AP-2024-001",
  type: "product",
  status: "good",
  details: {
    location: "Assembly Line 3",
    date: "2024-01-15",
    quantity: "500 units"
  },
  children: [
    {
      id: "material-steel",
      name: "Steel Grade A572",
      type: "material",
      status: "good",
      details: {
        location: "Warehouse B",
        date: "2024-01-10",
        quantity: "2000 kg",
        batchNumber: "ST-240110-001"
      },
      children: [
        {
          id: "supplier-steel",
          name: "Steel Corp Ltd",
          type: "supplier",
          status: "good",
          details: {
            location: "Pittsburgh, PA",
            date: "2024-01-08"
          }
        }
      ]
    },
    {
      id: "material-rubber",
      name: "Rubber Compound RX-500",
      type: "material",
      status: "warning",
      details: {
        location: "Warehouse A",
        date: "2024-01-12",
        quantity: "150 kg",
        batchNumber: "RX-240112-003"
      },
      children: [
        {
          id: "supplier-rubber",
          name: "Polymer Solutions Inc",
          type: "supplier",
          status: "warning",
          details: {
            location: "Houston, TX",
            date: "2024-01-09"
          }
        }
      ]
    }
  ]
};

export const TraceTreeVisualization: React.FC = () => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['final-product']));

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good": return "bg-green-100 text-green-800";
      case "warning": return "bg-orange-100 text-orange-800";
      case "critical": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "supplier": return <Factory className="h-4 w-4" />;
      case "material": return <Package className="h-4 w-4" />;
      case "process": return <Truck className="h-4 w-4" />;
      case "product": return <Package className="h-4 w-4 text-blue-600" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const renderNode = (node: TraceNode, level: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div key={node.id} className="space-y-2">
        <Card className={`ml-${level * 6} ${level === 0 ? 'border-2 border-blue-200' : ''}`}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {hasChildren && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleNode(node.id)}
                    className="p-1 h-6 w-6"
                  >
                    {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </Button>
                )}
                {!hasChildren && <div className="w-6" />}
                
                {getTypeIcon(node.type)}
                
                <div>
                  <h4 className="font-medium">{node.name}</h4>
                  <div className="flex gap-2 mt-1">
                    {node.details.location && (
                      <span className="text-xs text-gray-600">📍 {node.details.location}</span>
                    )}
                    {node.details.date && (
                      <span className="text-xs text-gray-600">📅 {node.details.date}</span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {node.details.quantity && (
                  <Badge variant="outline" className="text-xs">
                    {node.details.quantity}
                  </Badge>
                )}
                {node.details.batchNumber && (
                  <Badge variant="outline" className="text-xs">
                    #{node.details.batchNumber}
                  </Badge>
                )}
                <Badge className={getStatusColor(node.status)}>
                  {node.status}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {isExpanded && hasChildren && (
          <div className="space-y-2">
            {node.children!.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Material Trace Tree
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4 mb-4">
          <Button variant="outline" size="sm" onClick={() => setExpandedNodes(new Set(['final-product', 'material-steel', 'material-rubber']))}>
            Expand All
          </Button>
          <Button variant="outline" size="sm" onClick={() => setExpandedNodes(new Set(['final-product']))}>
            Collapse All
          </Button>
        </div>
        
        {renderNode(mockTraceData)}
      </CardContent>
    </Card>
  );
};

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Search, UserPlus, Phone, Mail, MapPin, Wrench, Calendar, Users } from 'lucide-react';

interface Technician {
  id: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  specialization: string[];
  status: 'Available' | 'Busy' | 'On Leave';
  activeWorkOrders: number;
  completedWorkOrders: number;
  lastActivity: string;
  shift: 'Day' | 'Night' | 'Rotating';
}

const mockTechnicians: Technician[] = [
  {
    id: 'TECH-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Mechanical',
    specialization: ['Pumps', 'Motors', 'Conveyors'],
    status: 'Available',
    activeWorkOrders: 3,
    completedWorkOrders: 45,
    lastActivity: '2025-06-04 09:30',
    shift: 'Day'
  },
  {
    id: 'TECH-002',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Electrical',
    specialization: ['PLCs', 'Motor Controls', 'Instrumentation'],
    status: 'Busy',
    activeWorkOrders: 5,
    completedWorkOrders: 67,
    lastActivity: '2025-06-04 10:15',
    shift: 'Day'
  },
  {
    id: 'TECH-003',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'HVAC',
    specialization: ['Air Handling', 'Chillers', 'Boilers'],
    status: 'Available',
    activeWorkOrders: 2,
    completedWorkOrders: 32,
    lastActivity: '2025-06-04 08:45',
    shift: 'Rotating'
  },
  {
    id: 'TECH-004',
    name: 'Lisa Chen',
    email: '<EMAIL>',
    phone: '+****************',
    department: 'Mechanical',
    specialization: ['Packaging Equipment', 'Pneumatics'],
    status: 'On Leave',
    activeWorkOrders: 0,
    completedWorkOrders: 28,
    lastActivity: '2025-06-02 16:30',
    shift: 'Day'
  }
];

export const TechniciansManagement: React.FC = () => {
  const [technicians, setTechnicians] = useState(mockTechnicians);
  const [searchQuery, setSearchQuery] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null);

  const filteredTechnicians = technicians.filter(tech => {
    const matchesSearch = tech.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tech.specialization.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesDepartment = departmentFilter === 'All' || tech.department === departmentFilter;
    const matchesStatus = statusFilter === 'All' || tech.status === statusFilter;

    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Available': return 'bg-green-100 text-green-800';
      case 'Busy': return 'bg-yellow-100 text-yellow-800';
      case 'On Leave': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (selectedTechnician) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => setSelectedTechnician(null)}>
            ← Back to Technicians
          </Button>
          <h2 className="text-2xl font-bold">{selectedTechnician.name}</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Technician Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Employee ID</p>
                    <p className="font-medium">{selectedTechnician.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Department</p>
                    <p className="font-medium">{selectedTechnician.department}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{selectedTechnician.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{selectedTechnician.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Shift</p>
                    <p className="font-medium">{selectedTechnician.shift}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <Badge className={getStatusColor(selectedTechnician.status)}>
                      {selectedTechnician.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-2">Specializations</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedTechnician.specialization.map((spec) => (
                      <Badge key={spec} variant="outline">
                        {spec}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Current Work Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">WO-2025-008</p>
                      <p className="text-sm text-gray-600">Pump Maintenance - Tank A-101</p>
                    </div>
                    <Badge variant="outline">In Progress</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">WO-2025-009</p>
                      <p className="text-sm text-gray-600">Motor Inspection - Line B</p>
                    </div>
                    <Badge variant="outline">Assigned</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500">Active Work Orders</p>
                    <p className="text-2xl font-bold text-blue-600">{selectedTechnician.activeWorkOrders}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Completed This Month</p>
                    <p className="text-2xl font-bold text-green-600">{selectedTechnician.completedWorkOrders}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Last Activity</p>
                    <p className="font-medium">{selectedTechnician.lastActivity}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full gap-2">
                  <Wrench className="h-4 w-4" />
                  Assign Work Order
                </Button>
                <Button variant="outline" className="w-full gap-2">
                  <Calendar className="h-4 w-4" />
                  View Schedule
                </Button>
                <Button variant="outline" className="w-full gap-2">
                  <Mail className="h-4 w-4" />
                  Send Message
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Technicians Management</h2>
          <p className="text-gray-600">Manage maintenance technicians and their assignments</p>
        </div>
        <Button className="gap-2">
          <UserPlus className="h-4 w-4" />
          Add Technician
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input
                placeholder="Search technicians..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Department" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Departments</SelectItem>
                <SelectItem value="Mechanical">Mechanical</SelectItem>
                <SelectItem value="Electrical">Electrical</SelectItem>
                <SelectItem value="HVAC">HVAC</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Status</SelectItem>
                <SelectItem value="Available">Available</SelectItem>
                <SelectItem value="Busy">Busy</SelectItem>
                <SelectItem value="On Leave">On Leave</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Technicians Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {filteredTechnicians.map((technician) => (
          <Card key={technician.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => setSelectedTechnician(technician)}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">{technician.name}</h3>
                  <p className="text-sm text-gray-600">{technician.department}</p>
                </div>
                <Badge className={getStatusColor(technician.status)}>
                  {technician.status}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Mail className="h-4 w-4" />
                {technician.email}
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Phone className="h-4 w-4" />
                {technician.phone}
              </div>
              
              <div>
                <p className="text-sm text-gray-500 mb-2">Specializations</p>
                <div className="flex flex-wrap gap-1">
                  {technician.specialization.slice(0, 2).map((spec) => (
                    <Badge key={spec} variant="outline" className="text-xs">
                      {spec}
                    </Badge>
                  ))}
                  {technician.specialization.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{technician.specialization.length - 2} more
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center pt-2 border-t text-sm">
                <span className="text-gray-500">Active: {technician.activeWorkOrders}</span>
                <span className="text-gray-500">Completed: {technician.completedWorkOrders}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTechnicians.length === 0 && (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No technicians found</h3>
          <p className="text-gray-500 mb-4">
            No technicians match your current filters.
          </p>
          <Button variant="outline">Clear Filters</Button>
        </div>
      )}
    </div>
  );
};

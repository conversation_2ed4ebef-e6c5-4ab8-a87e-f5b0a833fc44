
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Wrench, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Plus,
  Calendar,
  Users,
  Package
} from 'lucide-react';
import { ScheduleMaintenanceModal } from './ScheduleMaintenanceModal';

interface CMMSDashboardProps {
  onViewWorkOrder?: (id: string) => void;
  onManageAssets?: () => void;
}

export const CMMSDashboard: React.FC<CMMSDashboardProps> = ({ 
  onViewWorkOrder, 
  onManageAssets 
}) => {
  const [showScheduleModal, setShowScheduleModal] = useState(false);

  const stats = [
    { title: 'Open Work Orders', value: '12', icon: <Wrench className="h-6 w-6" />, color: 'text-blue-600' },
    { title: 'Overdue Tasks', value: '3', icon: <AlertTriangle className="h-6 w-6" />, color: 'text-red-600' },
    { title: 'Completed Today', value: '8', icon: <CheckCircle className="h-6 w-6" />, color: 'text-green-600' },
    { title: 'Pending Approval', value: '5', icon: <Clock className="h-6 w-6" />, color: 'text-yellow-600' }
  ];

  const recentWorkOrders = [
    { id: 'WO-001', equipment: 'Mixer Tank A', type: 'Preventive', priority: 'High', status: 'In Progress' },
    { id: 'WO-002', equipment: 'Conveyor Belt 3', type: 'Corrective', priority: 'Medium', status: 'Pending' },
    { id: 'WO-003', equipment: 'Packaging Machine 2', type: 'Preventive', priority: 'Low', status: 'Completed' }
  ];

  const getPriorityBadge = (priority: string) => {
    const variants = {
      'High': 'destructive',
      'Medium': 'secondary',
      'Low': 'outline'
    } as const;
    return <Badge variant={variants[priority as keyof typeof variants]}>{priority}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      'In Progress': 'secondary',
      'Pending': 'outline',
      'Completed': 'default'
    } as const;
    return <Badge variant={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const handleScheduleMaintenance = (event: any) => {
    console.log('Maintenance scheduled:', event);
    setShowScheduleModal(false);
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">CMMS Dashboard</h1>
        <div className="flex gap-2">
          <Button onClick={() => setShowScheduleModal(true)} className="gap-2">
            <Plus className="h-4 w-4" />
            Schedule Maintenance
          </Button>
          {onManageAssets && (
            <Button variant="outline" onClick={onManageAssets} className="gap-2">
              <Package className="h-4 w-4" />
              Manage Assets
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={stat.color}>{stat.icon}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Work Orders */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Work Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentWorkOrders.map((wo) => (
              <div key={wo.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-4">
                    <span className="font-medium">{wo.id}</span>
                    <span className="text-gray-600">{wo.equipment}</span>
                    <Badge variant="outline">{wo.type}</Badge>
                    {getPriorityBadge(wo.priority)}
                    {getStatusBadge(wo.status)}
                  </div>
                </div>
                {onViewWorkOrder && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => onViewWorkOrder(wo.id)}
                  >
                    View Details
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <ScheduleMaintenanceModal 
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        onSchedule={handleScheduleMaintenance}
      />
    </div>
  );
};


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Camera, FileText, User, Calendar, Package, AlertTriangle } from 'lucide-react';

interface WorkOrderDetailProps {
  workOrderId: string;
  onBack: () => void;
}

interface WorkOrderData {
  id: string;
  assetName: string;
  assetId: string;
  taskType: 'Routine' | 'Emergency' | 'Inspection';
  assignedTechnician: string;
  dueDate: string;
  status: 'Open' | 'In Progress' | 'Completed';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  description: string;
  notes: string[];
  attachments: string[];
  createdDate: string;
  estimatedHours: number;
}

const mockWorkOrderData: WorkOrderData = {
  id: 'WO-2025-001',
  assetName: 'Mixing Tank A-101',
  assetId: 'AST-001',
  taskType: 'Routine',
  assignedTechnician: '<PERSON>',
  dueDate: '2025-06-05',
  status: 'In Progress',
  priority: 'Medium',
  description: 'Monthly preventive maintenance of mixing tank including lubrication, inspection of seals, and cleaning.',
  notes: [
    'Started maintenance at 09:00 AM',
    'Lubrication completed - all points checked',
    'Found minor wear on seal #3 - replacement needed'
  ],
  attachments: ['checklist.pdf', 'before_photo.jpg'],
  createdDate: '2025-06-01',
  estimatedHours: 4
};

export const WorkOrderDetail: React.FC<WorkOrderDetailProps> = ({ workOrderId, onBack }) => {
  const [workOrder, setWorkOrder] = useState(mockWorkOrderData);
  const [newNote, setNewNote] = useState('');
  const [showStatusUpdate, setShowStatusUpdate] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Open': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleStatusUpdate = (newStatus: 'Open' | 'In Progress' | 'Completed') => {
    setWorkOrder(prev => ({ ...prev, status: newStatus }));
    setShowStatusUpdate(false);
  };

  const handleAddNote = () => {
    if (newNote.trim()) {
      setWorkOrder(prev => ({
        ...prev,
        notes: [...prev.notes, `${new Date().toLocaleTimeString()}: ${newNote}`]
      }));
      setNewNote('');
    }
  };

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4 mb-4">
          <Button variant="ghost" size="icon" onClick={onBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-xl font-bold">{workOrder.id}</h1>
              <Badge className={getPriorityColor(workOrder.priority)} variant="outline">
                {workOrder.priority}
              </Badge>
              <Badge variant="outline">{workOrder.taskType}</Badge>
            </div>
            <p className="text-sm text-gray-600">{workOrder.assetName}</p>
          </div>
          <Badge className={getStatusColor(workOrder.status)} variant="default">
            {workOrder.status}
          </Badge>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Work Order Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Work Order Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Package className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Linked Asset</p>
                  <p className="font-medium">{workOrder.assetName} ({workOrder.assetId})</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Assigned Technician</p>
                  <p className="font-medium">{workOrder.assignedTechnician}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Due Date</p>
                  <p className="font-medium">{workOrder.dueDate}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Estimated Hours</p>
                  <p className="font-medium">{workOrder.estimatedHours}h</p>
                </div>
              </div>
            </div>
            
            <div>
              <p className="text-sm text-gray-500 mb-2">Description</p>
              <p className="text-sm">{workOrder.description}</p>
            </div>
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Notes & Updates</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {workOrder.notes.map((note, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm">{note}</p>
                </div>
              ))}
            </div>
            
            <div className="space-y-3">
              <Textarea
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                placeholder="Add a note or update..."
                rows={3}
              />
              <Button onClick={handleAddNote} disabled={!newNote.trim()}>
                Add Note
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Attachments */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Attachments</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              {workOrder.attachments.map((attachment, index) => (
                <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-sm truncate">{attachment}</span>
                </div>
              ))}
            </div>
            
            <div className="flex gap-3">
              <Button variant="outline" className="flex-1 gap-2">
                <Camera className="h-4 w-4" />
                Take Photo
              </Button>
              <Button variant="outline" className="flex-1 gap-2">
                <FileText className="h-4 w-4" />
                Add File
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Actions */}
      <div className="bg-white border-t border-gray-200 p-4">
        {!showStatusUpdate ? (
          <Button 
            className="w-full gap-2" 
            size="lg"
            onClick={() => setShowStatusUpdate(true)}
          >
            Update Status
          </Button>
        ) : (
          <div className="grid grid-cols-3 gap-3">
            <Button 
              variant={workOrder.status === 'Open' ? 'default' : 'outline'}
              onClick={() => handleStatusUpdate('Open')}
            >
              Open
            </Button>
            <Button 
              variant={workOrder.status === 'In Progress' ? 'default' : 'outline'}
              onClick={() => handleStatusUpdate('In Progress')}
            >
              In Progress
            </Button>
            <Button 
              variant={workOrder.status === 'Completed' ? 'default' : 'outline'}
              onClick={() => handleStatusUpdate('Completed')}
            >
              Complete
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

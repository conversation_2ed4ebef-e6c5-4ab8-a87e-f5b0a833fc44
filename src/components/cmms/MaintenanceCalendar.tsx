
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, Calendar, Clock, User, Wrench, Plus } from 'lucide-react';

interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  time: string;
  type: 'Preventive' | 'Corrective' | 'Inspection' | 'Emergency';
  technician: string;
  asset: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Overdue';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
}

const mockEvents: CalendarEvent[] = [
  {
    id: 'CAL-001',
    title: 'Monthly Pump Maintenance',
    date: '2025-06-05',
    time: '09:00',
    type: 'Preventive',
    technician: '<PERSON>',
    asset: 'Mixing Tank A-101',
    status: 'Scheduled',
    priority: 'Medium'
  },
  {
    id: 'CAL-002',
    title: 'Conveyor Belt Inspection',
    date: '2025-06-06',
    time: '14:30',
    type: 'Inspection',
    technician: 'Sarah Johnson',
    asset: 'Conveyor Belt B-203',
    status: 'Scheduled',
    priority: 'High'
  },
  {
    id: 'CAL-003',
    title: 'Emergency Motor Repair',
    date: '2025-06-04',
    time: '08:00',
    type: 'Emergency',
    technician: 'Mike Wilson',
    asset: 'Motor C-105',
    status: 'In Progress',
    priority: 'Critical'
  },
  {
    id: 'CAL-004',
    title: 'HVAC System Service',
    date: '2025-06-07',
    time: '11:00',
    type: 'Preventive',
    technician: 'Lisa Chen',
    asset: 'HVAC Unit D-201',
    status: 'Scheduled',
    priority: 'Low'
  },
  {
    id: 'CAL-005',
    title: 'Packaging Machine Calibration',
    date: '2025-06-08',
    time: '10:15',
    type: 'Preventive',
    technician: 'John Smith',
    asset: 'Packaging Machine E-301',
    status: 'Scheduled',
    priority: 'Medium'
  },
  {
    id: 'CAL-006',
    title: 'Safety Valve Testing',
    date: '2025-06-03',
    time: '16:00',
    type: 'Inspection',
    technician: 'Sarah Johnson',
    asset: 'Safety Valve F-102',
    status: 'Overdue',
    priority: 'High'
  }
];

export const MaintenanceCalendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2025, 5, 4)); // June 4, 2025
  const [view, setView] = useState<'month' | 'week' | 'day'>('month');
  const [events, setEvents] = useState(mockEvents);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Preventive': return 'bg-blue-100 text-blue-800';
      case 'Corrective': return 'bg-orange-100 text-orange-800';
      case 'Inspection': return 'bg-green-100 text-green-800';
      case 'Emergency': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'In Progress': return 'bg-yellow-100 text-yellow-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    });
  };

  const getEventsForDate = (day: number) => {
    const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return events.filter(event => event.date === dateStr);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  if (selectedEvent) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => setSelectedEvent(null)}>
            ← Back to Calendar
          </Button>
          <h2 className="text-2xl font-bold">{selectedEvent.title}</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Event Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Event ID</p>
                    <p className="font-medium">{selectedEvent.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Type</p>
                    <Badge className={getTypeColor(selectedEvent.type)}>
                      {selectedEvent.type}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Date & Time</p>
                    <p className="font-medium">{selectedEvent.date} at {selectedEvent.time}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Asset</p>
                    <p className="font-medium">{selectedEvent.asset}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Assigned Technician</p>
                    <p className="font-medium">{selectedEvent.technician}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Priority</p>
                    <Badge className={getPriorityColor(selectedEvent.priority)} variant="outline">
                      {selectedEvent.priority}
                    </Badge>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <Badge className={getStatusColor(selectedEvent.status)}>
                    {selectedEvent.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full">Start Work Order</Button>
                <Button variant="outline" className="w-full">Reschedule</Button>
                <Button variant="outline" className="w-full">Assign Technician</Button>
                <Button variant="outline" className="w-full">View Asset Details</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Maintenance Calendar</h2>
          <p className="text-gray-600">Schedule and track maintenance activities</p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Schedule Maintenance
        </Button>
      </div>

      {/* Calendar Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => navigateMonth('prev')}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <h3 className="text-lg font-semibold min-w-[200px] text-center">
                  {formatDate(currentDate)}
                </h3>
                <Button variant="outline" size="sm" onClick={() => navigateMonth('next')}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Select value={view} onValueChange={(value: 'month' | 'week' | 'day') => setView(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="month">Month</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                  <SelectItem value="day">Day</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                Today
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-7 gap-2 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-center font-medium text-gray-500 text-sm">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-2">
            {/* Empty cells for days before month starts */}
            {Array.from({ length: getFirstDayOfMonth(currentDate) }).map((_, index) => (
              <div key={`empty-${index}`} className="h-24 border border-gray-100"></div>
            ))}
            
            {/* Days of the month */}
            {Array.from({ length: getDaysInMonth(currentDate) }).map((_, index) => {
              const day = index + 1;
              const dayEvents = getEventsForDate(day);
              const isToday = day === 4; // Simulating June 4, 2025 as today
              
              return (
                <div 
                  key={day} 
                  className={`h-24 border border-gray-100 p-1 ${isToday ? 'bg-blue-50 border-blue-200' : ''}`}
                >
                  <div className={`text-sm font-medium mb-1 ${isToday ? 'text-blue-600' : ''}`}>
                    {day}
                  </div>
                  <div className="space-y-1">
                    {dayEvents.slice(0, 2).map(event => (
                      <div 
                        key={event.id}
                        className="text-xs p-1 rounded cursor-pointer hover:shadow-sm"
                        style={{
                          backgroundColor: event.type === 'Emergency' ? '#fee2e2' :
                                         event.type === 'Preventive' ? '#dbeafe' :
                                         event.type === 'Inspection' ? '#dcfce7' : '#fed7d7'
                        }}
                        onClick={() => setSelectedEvent(event)}
                      >
                        <div className="font-medium truncate">{event.time}</div>
                        <div className="truncate">{event.title}</div>
                      </div>
                    ))}
                    {dayEvents.length > 2 && (
                      <div className="text-xs text-gray-500 font-medium">
                        +{dayEvents.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Upcoming Events */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Events</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {events.filter(event => new Date(event.date) >= new Date('2025-06-04')).slice(0, 5).map(event => (
              <div 
                key={event.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:shadow-sm cursor-pointer"
                onClick={() => setSelectedEvent(event)}
              >
                <div className="flex items-center gap-3">
                  <div className="flex flex-col items-center">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-xs text-gray-500">{event.date.split('-')[2]}</span>
                  </div>
                  <div>
                    <p className="font-medium">{event.title}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-3 w-3" />
                      {event.time}
                      <User className="h-3 w-3 ml-2" />
                      {event.technician}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getTypeColor(event.type)} variant="outline">
                    {event.type}
                  </Badge>
                  <Badge className={getStatusColor(event.status)}>
                    {event.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

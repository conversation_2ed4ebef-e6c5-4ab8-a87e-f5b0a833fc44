
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { History, User, Calendar, FileText } from "lucide-react";
import { TemplateVersion } from "@/types/auditTemplates";

interface TemplateVersionHistoryProps {
  templateId: string;
  isOpen: boolean;
  onClose: () => void;
}

const mockVersionHistory: TemplateVersion[] = [
  {
    id: 'v3',
    templateId: 'template-1',
    version: '1.2',
    changes: [
      'Added new question to Assembly section',
      'Updated scoring weights for Final Inspection',
      'Fixed typo in Receiving Inspection question'
    ],
    createdBy: '<PERSON>',
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'v2',
    templateId: 'template-1',
    version: '1.1',
    changes: [
      'Added evidence requirement flags',
      'Updated supplier category mappings',
      'Enhanced question descriptions'
    ],
    createdBy: '<PERSON>',
    createdAt: '2024-01-10T14:15:00Z'
  },
  {
    id: 'v1',
    templateId: 'template-1',
    version: '1.0',
    changes: [
      'Initial template creation',
      'Added all base sections',
      'Configured default scoring system'
    ],
    createdBy: 'Alex Rodriguez',
    createdAt: '2024-01-05T09:00:00Z'
  }
];

export const TemplateVersionHistory: React.FC<TemplateVersionHistoryProps> = ({
  templateId,
  isOpen,
  onClose
}) => {
  const versions = mockVersionHistory.filter(v => v.templateId === templateId);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Version History
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {versions.map((version, index) => (
            <div key={version.id} className="relative">
              {index < versions.length - 1 && (
                <div className="absolute left-4 top-12 bottom-0 w-px bg-gray-200" />
              )}
              
              <div className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <FileText className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                
                <div className="flex-1 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge variant={index === 0 ? "default" : "outline"}>
                        Version {version.version}
                      </Badge>
                      {index === 0 && (
                        <Badge variant="secondary" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      {index > 0 && (
                        <Button variant="outline" size="sm">
                          Restore
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {version.createdBy}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(version.createdAt).toLocaleDateString()} at{' '}
                        {new Date(version.createdAt).toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Changes:</p>
                      <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                        {version.changes.map((change, changeIndex) => (
                          <li key={changeIndex}>{change}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {versions.length === 0 && (
          <div className="text-center py-8">
            <History className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">No version history available</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

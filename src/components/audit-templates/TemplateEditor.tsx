
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Trash2, 
  GripVertical, 
  Edit, 
  Save, 
  Eye,
  Copy,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { AuditTemplate, TemplateSection, TemplateQuestion } from '@/types/auditTemplates';

interface TemplateEditorProps {
  template: AuditTemplate;
  onSave: (template: AuditTemplate) => void;
  onCancel: () => void;
}

export const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  onSave,
  onCancel
}) => {
  const [editingTemplate, setEditingTemplate] = useState<AuditTemplate>(template);
  const [previewMode, setPreviewMode] = useState(false);

  const updateTemplate = (updates: Partial<AuditTemplate>) => {
    setEditingTemplate(prev => ({
      ...prev,
      ...updates,
      updatedAt: new Date().toISOString()
    }));
  };

  const addSection = () => {
    const newSection: TemplateSection = {
      id: `section-${Date.now()}`,
      name: 'New Section',
      description: '',
      questions: [],
      order: editingTemplate.sections.length,
      weight: 1
    };
    
    updateTemplate({
      sections: [...editingTemplate.sections, newSection]
    });
  };

  const updateSection = (sectionId: string, updates: Partial<TemplateSection>) => {
    updateTemplate({
      sections: editingTemplate.sections.map(section =>
        section.id === sectionId ? { ...section, ...updates } : section
      )
    });
  };

  const deleteSection = (sectionId: string) => {
    updateTemplate({
      sections: editingTemplate.sections.filter(section => section.id !== sectionId)
    });
  };

  const addQuestion = (sectionId: string) => {
    const newQuestion: TemplateQuestion = {
      id: `question-${Date.now()}`,
      question: 'New Question',
      description: '',
      scoring: 'binary',
      weight: 1,
      category: 'General',
      required: false,
      evidenceRequired: false,
      order: 0
    };

    updateSection(sectionId, {
      questions: [...(editingTemplate.sections.find(s => s.id === sectionId)?.questions || []), newQuestion]
    });
  };

  const updateQuestion = (sectionId: string, questionId: string, updates: Partial<TemplateQuestion>) => {
    updateSection(sectionId, {
      questions: editingTemplate.sections
        .find(s => s.id === sectionId)?.questions
        .map(q => q.id === questionId ? { ...q, ...updates } : q) || []
    });
  };

  const deleteQuestion = (sectionId: string, questionId: string) => {
    updateSection(sectionId, {
      questions: editingTemplate.sections
        .find(s => s.id === sectionId)?.questions
        .filter(q => q.id !== questionId) || []
    });
  };

  const moveSectionUp = (index: number) => {
    if (index > 0) {
      const newSections = [...editingTemplate.sections];
      [newSections[index], newSections[index - 1]] = [newSections[index - 1], newSections[index]];
      updateTemplate({ sections: newSections });
    }
  };

  const moveSectionDown = (index: number) => {
    if (index < editingTemplate.sections.length - 1) {
      const newSections = [...editingTemplate.sections];
      [newSections[index], newSections[index + 1]] = [newSections[index + 1], newSections[index]];
      updateTemplate({ sections: newSections });
    }
  };

  const handleSave = () => {
    const totalQuestions = editingTemplate.sections.reduce((total, section) => 
      total + section.questions.length, 0
    );
    
    onSave({
      ...editingTemplate,
      totalQuestions
    });
  };

  if (previewMode) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Template Preview</h2>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setPreviewMode(false)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Template
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{editingTemplate.name}</CardTitle>
            {editingTemplate.description && (
              <p className="text-gray-600">{editingTemplate.description}</p>
            )}
            <div className="flex gap-2">
              <Badge>Version {editingTemplate.version}</Badge>
              <Badge variant="outline">{editingTemplate.totalQuestions} Questions</Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {editingTemplate.sections.map((section, sectionIndex) => (
              <Card key={section.id}>
                <CardHeader>
                  <CardTitle className="text-lg">{section.name}</CardTitle>
                  {section.description && (
                    <p className="text-sm text-gray-600">{section.description}</p>
                  )}
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.questions.map((question, questionIndex) => (
                    <div key={question.id} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="font-medium">
                            {sectionIndex + 1}.{questionIndex + 1} {question.question}
                          </p>
                          {question.description && (
                            <p className="text-sm text-gray-600 mt-1">{question.description}</p>
                          )}
                          <div className="flex gap-2 mt-2">
                            <Badge variant="outline">{question.category}</Badge>
                            {question.required && <Badge variant="destructive">Required</Badge>}
                            {question.evidenceRequired && <Badge>Evidence Required</Badge>}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Edit Template</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setPreviewMode(true)}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Template
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Template Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Template Name</Label>
              <Input
                value={editingTemplate.name}
                onChange={(e) => updateTemplate({ name: e.target.value })}
              />
            </div>
            <div>
              <Label>Version</Label>
              <Input
                value={editingTemplate.version}
                onChange={(e) => updateTemplate({ version: e.target.value })}
              />
            </div>
          </div>
          
          <div>
            <Label>Description</Label>
            <Textarea
              value={editingTemplate.description || ''}
              onChange={(e) => updateTemplate({ description: e.target.value })}
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={editingTemplate.isActive}
              onCheckedChange={(checked) => updateTemplate({ isActive: checked })}
            />
            <Label>Active Template</Label>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Sections</h3>
          <Button onClick={addSection} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Section
          </Button>
        </div>

        {editingTemplate.sections.map((section, index) => (
          <Card key={section.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GripVertical className="h-4 w-4 text-gray-400" />
                  <Input
                    value={section.name}
                    onChange={(e) => updateSection(section.id, { name: e.target.value })}
                    className="font-medium"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => moveSectionUp(index)}
                    disabled={index === 0}
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => moveSectionDown(index)}
                    disabled={index === editingTemplate.sections.length - 1}
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteSection(section.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <Textarea
                value={section.description || ''}
                onChange={(e) => updateSection(section.id, { description: e.target.value })}
                placeholder="Section description..."
                rows={2}
              />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Questions ({section.questions.length})</span>
                  <Button
                    onClick={() => addQuestion(section.id)}
                    size="sm"
                    variant="outline"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Question
                  </Button>
                </div>

                {section.questions.map((question, qIndex) => (
                  <div key={question.id} className="p-4 border rounded-lg space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <Input
                          value={question.question}
                          onChange={(e) => updateQuestion(section.id, question.id, { question: e.target.value })}
                          placeholder="Question text..."
                        />
                        <Textarea
                          value={question.description || ''}
                          onChange={(e) => updateQuestion(section.id, question.id, { description: e.target.value })}
                          placeholder="Question description..."
                          rows={2}
                        />
                        
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <Label>Category</Label>
                            <Input
                              value={question.category}
                              onChange={(e) => updateQuestion(section.id, question.id, { category: e.target.value })}
                            />
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={question.required}
                              onCheckedChange={(checked) => updateQuestion(section.id, question.id, { required: checked })}
                            />
                            <Label>Required</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={question.evidenceRequired}
                              onCheckedChange={(checked) => updateQuestion(section.id, question.id, { evidenceRequired: checked })}
                            />
                            <Label>Evidence Required</Label>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteQuestion(section.id, question.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

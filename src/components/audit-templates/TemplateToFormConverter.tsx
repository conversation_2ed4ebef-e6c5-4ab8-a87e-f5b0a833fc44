
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FileText, 
  ArrowRight, 
  CheckCircle, 
  Users,
  Calendar
} from 'lucide-react';
import { AuditTemplate } from '@/types/auditTemplates';
import { SupplierForm, FormSection, FormQuestion } from '@/types/supplierForms';

interface TemplateToFormConverterProps {
  template: AuditTemplate;
  isOpen: boolean;
  onClose: () => void;
  onConvert: (form: Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

export const TemplateToFormConverter: React.FC<TemplateToFormConverterProps> = ({
  template,
  isOpen,
  onClose,
  onConvert
}) => {
  const [formData, setFormData] = useState({
    name: `${template.name} - Supplier Questionnaire`,
    description: `Supplier questionnaire based on ${template.name}`,
    dueDate: '',
    selectedSections: template.sections.map(s => s.id)
  });

  const handleSectionToggle = (sectionId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedSections: prev.selectedSections.includes(sectionId)
        ? prev.selectedSections.filter(id => id !== sectionId)
        : [...prev.selectedSections, sectionId]
    }));
  };

  const convertTemplateQuestion = (templateQuestion: any): FormQuestion => {
    let questionType: FormQuestion['type'] = 'yesno';
    
    // Determine question type based on template question properties
    if (templateQuestion.evidenceRequired) {
      questionType = 'file';
    } else if (templateQuestion.description && templateQuestion.description.length > 100) {
      questionType = 'textarea';
    } else if (templateQuestion.scoring === 'weighted') {
      questionType = 'select';
    }

    return {
      id: `q-${templateQuestion.id}`,
      type: questionType,
      text: templateQuestion.question,
      required: templateQuestion.required,
      options: questionType === 'select' ? ['1', '2', '3', '4', '5'] : 
               questionType === 'yesno' ? ['Yes', 'No'] : undefined
    };
  };

  const handleConvert = () => {
    const selectedTemplateSections = template.sections.filter(section => 
      formData.selectedSections.includes(section.id)
    );

    const formSections: FormSection[] = selectedTemplateSections.map(section => ({
      id: `section-${section.id}`,
      title: section.name,
      description: section.description,
      questions: section.questions.map(convertTemplateQuestion)
    }));

    const supplierForm: Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'> = {
      name: formData.name,
      description: formData.description,
      dueDate: formData.dueDate || undefined,
      sections: formSections,
      recipientCount: 0,
      status: {
        completed: 0,
        pending: 0,
        overdue: 0
      }
    };

    onConvert(supplierForm);
    onClose();
  };

  const totalSelectedQuestions = template.sections
    .filter(section => formData.selectedSections.includes(section.id))
    .reduce((total, section) => total + section.questions.length, 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Convert Template to Supplier Form
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Conversion Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Conversion Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <p className="text-sm font-medium">Audit Template</p>
                    <p className="text-xs text-gray-600">{template.sections.length} sections</p>
                  </div>
                  <ArrowRight className="h-8 w-8 text-blue-500" />
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                      <Users className="h-6 w-6 text-green-600" />
                    </div>
                    <p className="text-sm font-medium">Supplier Form</p>
                    <p className="text-xs text-gray-600">{formData.selectedSections.length} sections</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className="mb-2">{totalSelectedQuestions} Questions</Badge>
                  <p className="text-xs text-gray-600">Will be converted</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Form Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Form Name</Label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              
              <div>
                <Label>Description</Label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>
              
              <div>
                <Label>Due Date (Optional)</Label>
                <Input
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                />
              </div>
            </CardContent>
          </Card>

          {/* Section Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Select Sections to Include</CardTitle>
              <p className="text-sm text-gray-600">
                Choose which sections from the audit template should be included in the supplier form
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {template.sections.map((section) => (
                <div key={section.id} className="flex items-start space-x-3 p-4 border rounded-lg">
                  <Checkbox
                    checked={formData.selectedSections.includes(section.id)}
                    onCheckedChange={() => handleSectionToggle(section.id)}
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{section.name}</h4>
                    {section.description && (
                      <p className="text-sm text-gray-600 mt-1">{section.description}</p>
                    )}
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline">{section.questions.length} questions</Badge>
                      {section.questions.some(q => q.required) && (
                        <Badge variant="destructive" className="text-xs">Has required questions</Badge>
                      )}
                      {section.questions.some(q => q.evidenceRequired) && (
                        <Badge className="text-xs">Evidence required</Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Conversion Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Conversion Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{formData.selectedSections.length}</div>
                    <div className="text-sm text-gray-600">Sections</div>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{totalSelectedQuestions}</div>
                    <div className="text-sm text-gray-600">Questions</div>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {template.sections
                        .filter(s => formData.selectedSections.includes(s.id))
                        .flatMap(s => s.questions)
                        .filter(q => q.required).length}
                    </div>
                    <div className="text-sm text-gray-600">Required</div>
                  </div>
                </div>

                <div className="text-sm text-gray-600">
                  <p><CheckCircle className="h-4 w-4 inline mr-1 text-green-500" />
                    Binary scoring questions will be converted to Yes/No questions</p>
                  <p><CheckCircle className="h-4 w-4 inline mr-1 text-green-500" />
                    Evidence-required questions will become file upload fields</p>
                  <p><CheckCircle className="h-4 w-4 inline mr-1 text-green-500" />
                    Weighted scoring questions will become rating scales</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleConvert}
              disabled={formData.selectedSections.length === 0 || !formData.name.trim()}
            >
              Convert to Form
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

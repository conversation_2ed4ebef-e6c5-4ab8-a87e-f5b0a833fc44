
import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Users, FileText, Target } from 'lucide-react';

interface ApplyTemplateDialogProps {
  template: any;
  isOpen: boolean;
  onClose: () => void;
}

export const ApplyTemplateDialog: React.FC<ApplyTemplateDialogProps> = ({
  template,
  isOpen,
  onClose
}) => {
  const [formData, setFormData] = useState({
    supplierName: '',
    auditDate: '',
    auditorName: '',
    auditType: 'scheduled'
  });

  const handleApply = () => {
    console.log('Applying template:', template, 'with data:', formData);
    // In real app, this would create an audit from the template
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Apply Audit Template
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Template: {template?.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{template?.totalQuestions} Questions</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{template?.sections?.length} Sections</span>
                </div>
              </div>
              {template?.tags && (
                <div className="flex gap-1 mt-3">
                  {template.tags.map((tag: string, index: number) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Audit Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Audit Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Supplier Name</Label>
                <Input
                  value={formData.supplierName}
                  onChange={(e) => setFormData(prev => ({ ...prev, supplierName: e.target.value }))}
                  placeholder="Enter supplier name"
                />
              </div>
              
              <div>
                <Label>Audit Date</Label>
                <Input
                  type="date"
                  value={formData.auditDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, auditDate: e.target.value }))}
                />
              </div>
              
              <div>
                <Label>Assigned Auditor</Label>
                <Select
                  value={formData.auditorName}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, auditorName: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select auditor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sarah-johnson">Sarah Johnson</SelectItem>
                    <SelectItem value="mike-chen">Mike Chen</SelectItem>
                    <SelectItem value="alex-rodriguez">Alex Rodriguez</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Audit Type</Label>
                <Select
                  value={formData.auditType}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, auditType: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="scheduled">Scheduled Audit</SelectItem>
                    <SelectItem value="follow-up">Follow-up Audit</SelectItem>
                    <SelectItem value="surveillance">Surveillance Audit</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleApply}
              disabled={!formData.supplierName || !formData.auditDate || !formData.auditorName}
            >
              Create Audit
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};


import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Plus, X } from "lucide-react";
import { AuditTemplate, ScoringType, SupplierCategory } from "@/types/auditTemplates";
import { useToast } from "@/hooks/use-toast";

interface CreateTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTemplate: (template: Omit<AuditTemplate, 'id' | 'createdAt' | 'updatedAt'>) => void;
}

export const CreateTemplateDialog: React.FC<CreateTemplateDialogProps> = ({
  isO<PERSON>,
  onClose,
  onCreateTemplate
}) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    version: "1.0",
    defaultScoring: "binary" as ScoringType,
    supplierCategory: [] as SupplierCategory[],
    tags: [] as string[],
    isActive: true
  });
  
  const [newTag, setNewTag] = useState("");

  const supplierCategories: SupplierCategory[] = [
    'Electronics', 'Mechanical', 'Chemical', 'Software', 'General'
  ];

  const handleCategoryChange = (category: SupplierCategory, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      supplierCategory: checked 
        ? [...prev.supplierCategory, category]
        : prev.supplierCategory.filter(c => c !== category)
    }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Template name is required.",
        variant: "destructive"
      });
      return;
    }

    if (formData.supplierCategory.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one supplier category.",
        variant: "destructive"
      });
      return;
    }

    const newTemplate: Omit<AuditTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
      name: formData.name,
      description: formData.description || undefined,
      version: formData.version,
      sections: [
        {
          id: 'receiving',
          name: 'Receiving Inspection',
          questions: [
            {
              id: 'q1',
              question: 'Are materials stored per specification?',
              scoring: formData.defaultScoring,
              weight: formData.defaultScoring === 'weighted' ? 10 : undefined,
              category: 'Storage',
              required: true,
              evidenceRequired: true,
              order: 1
            }
          ],
          order: 1,
          weight: formData.defaultScoring === 'weighted' ? 25 : undefined
        },
        {
          id: 'assembly',
          name: 'Assembly',
          questions: [
            {
              id: 'q2',
              question: 'Are assembly procedures followed correctly?',
              scoring: formData.defaultScoring,
              weight: formData.defaultScoring === 'weighted' ? 10 : undefined,
              category: 'Process',
              required: true,
              evidenceRequired: false,
              order: 1
            }
          ],
          order: 2,
          weight: formData.defaultScoring === 'weighted' ? 35 : undefined
        },
        {
          id: 'final-inspection',
          name: 'Final Inspection',
          questions: [
            {
              id: 'q3',
              question: 'Are final products inspected according to specifications?',
              scoring: formData.defaultScoring,
              weight: formData.defaultScoring === 'weighted' ? 10 : undefined,
              category: 'Quality',
              required: true,
              evidenceRequired: true,
              order: 1
            }
          ],
          order: 3,
          weight: formData.defaultScoring === 'weighted' ? 40 : undefined
        }
      ],
      totalQuestions: 3,
      defaultScoring: formData.defaultScoring,
      supplierCategory: formData.supplierCategory,
      createdBy: "Current User",
      isActive: formData.isActive,
      tags: formData.tags
    };

    onCreateTemplate(newTemplate);
    
    // Reset form
    setFormData({
      name: "",
      description: "",
      version: "1.0",
      defaultScoring: "binary",
      supplierCategory: [],
      tags: [],
      isActive: true
    });
    
    toast({
      title: "Template Created",
      description: `${formData.name} has been created successfully.`
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Audit Template</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Template Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., System Quality Process Audit"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="version">Version</Label>
              <Input
                id="version"
                value={formData.version}
                onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
                placeholder="1.0"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe the purpose and scope of this audit template..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Default Scoring Type</Label>
            <div className="flex gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="scoring"
                  value="binary"
                  checked={formData.defaultScoring === "binary"}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    defaultScoring: e.target.value as ScoringType 
                  }))}
                />
                <span>Binary (0/1)</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="scoring"
                  value="weighted"
                  checked={formData.defaultScoring === "weighted"}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    defaultScoring: e.target.value as ScoringType 
                  }))}
                />
                <span>Weighted</span>
              </label>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Supplier Categories *</Label>
            <div className="grid grid-cols-2 gap-2">
              {supplierCategories.map((category) => (
                <label key={category} className="flex items-center gap-2">
                  <Checkbox
                    checked={formData.supplierCategory.includes(category)}
                    onCheckedChange={(checked) => 
                      handleCategoryChange(category, checked as boolean)
                    }
                  />
                  <span className="text-sm">{category}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {formData.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Create Template
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

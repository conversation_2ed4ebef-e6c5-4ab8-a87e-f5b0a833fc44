
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Edit, Copy, Download, Users } from 'lucide-react';
import { AuditTemplate } from '@/types/auditTemplates';
import { TemplateEditor } from './TemplateEditor';
import { TemplateVersionHistory } from './TemplateVersionHistory';
import { ApplyTemplateDialog } from './ApplyTemplateDialog';
import { TemplateToFormConverter } from './TemplateToFormConverter';

interface TemplateDetailProps {
  template: AuditTemplate;
  onBack: () => void;
  onUpdate: (template: AuditTemplate) => void;
}

export const TemplateDetail: React.FC<TemplateDetailProps> = ({ template, onBack, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [showApplyDialog, setShowApplyDialog] = useState(false);
  const [showConvertDialog, setShowConvertDialog] = useState(false);

  const handleShowVersionHistory = () => {
    setShowVersionHistory(true);
  };

  const handleCloseVersionHistory = () => {
    setShowVersionHistory(false);
  };

  const handleApplyTemplate = () => {
    setShowApplyDialog(true);
  };

  const handleCloseApplyDialog = () => {
    setShowApplyDialog(false);
  };

  const handleConvertToForm = (form: any) => {
    console.log('Converting template to form:', form);
    // In real app, this would navigate to forms section or create the form
  };

  if (isEditing) {
    return (
      <TemplateEditor
        template={template}
        onSave={(updatedTemplate) => {
          onUpdate(updatedTemplate);
          setIsEditing(false);
        }}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Template Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack} className="gap-2">
            <ChevronLeft className="h-4 w-4" />
            Back to Templates
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{template.name}</h1>
            <p className="text-gray-600">Version {template.version}</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowConvertDialog(true)} className="gap-2">
            <Users className="h-4 w-4" />
            Convert to Form
          </Button>
          <Button variant="outline" onClick={() => setIsEditing(true)} className="gap-2">
            <Edit className="h-4 w-4" />
            Edit Template
          </Button>
          <Button variant="outline" onClick={handleShowVersionHistory} className="gap-2">
            <Copy className="h-4 w-4" />
            Version History
          </Button>
          <Button variant="outline" onClick={handleApplyTemplate} className="gap-2">
            <Download className="h-4 w-4" />
            Apply Template
          </Button>
        </div>
      </div>

      {/* Template Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Template Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p>Description: {template.description || 'No description provided.'}</p>
          <p>Total Questions: {template.totalQuestions}</p>
          <p>Default Scoring: {template.defaultScoring}</p>
          <p>Created By: {template.createdBy}</p>
        </CardContent>
      </Card>

      {/* Template Sections */}
      <Card>
        <CardHeader>
          <CardTitle>Template Sections</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {template.sections.map((section) => (
              <div key={section.id} className="border rounded-md p-4">
                <h3 className="text-lg font-semibold">{section.name}</h3>
                <p className="text-gray-500">{section.description}</p>
                <p>Questions: {section.questions.length}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Convert to Form Dialog */}
      <TemplateToFormConverter
        template={template}
        isOpen={showConvertDialog}
        onClose={() => setShowConvertDialog(false)}
        onConvert={handleConvertToForm}
      />

      {/* Version History Dialog */}
      <TemplateVersionHistory
        templateId={template.id}
        isOpen={showVersionHistory}
        onClose={handleCloseVersionHistory}
      />

      {/* Apply Template Dialog */}
      <ApplyTemplateDialog
        template={template}
        isOpen={showApplyDialog}
        onClose={handleCloseApplyDialog}
      />
    </div>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Clock, Star, Play, BookOpen, Video, FileText } from "lucide-react";

interface TrainingHeaderProps {
  training: any;
  currentProgress: number;
  completedModules: number;
  totalModules: number;
}

export const TrainingHeader: React.FC<TrainingHeaderProps> = ({
  training,
  currentProgress,
  completedModules,
  totalModules
}) => {
  const getFormatIcon = (format: string) => {
    switch (format) {
      case "Video": return <Video className="h-4 w-4" />;
      case "PDF": return <FileText className="h-4 w-4" />;
      case "Interactive": return <BookOpen className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-wrap items-start justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-2xl font-bold mb-2">{training.title}</h2>
            <div className="flex flex-wrap items-center gap-4 mb-4">
              <Badge className="gap-1">
                {getFormatIcon(training.format)}
                {training.format}
              </Badge>
              <Badge variant="outline">{training.category}</Badge>
              <Badge variant="outline">{training.difficulty}</Badge>
              <Badge variant="outline">{training.language}</Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{training.estimatedDuration}</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>4.8 (234 reviews)</span>
              </div>
            </div>
          </div>
          <Button size="lg" className="gap-2 shrink-0">
            <Play className="h-4 w-4" />
            {currentProgress > 0 ? "Continue Training" : "Start Training"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-gray-600">{completedModules}/{totalModules} modules completed</span>
            </div>
            <Progress value={currentProgress} className="h-2" />
            <p className="text-sm text-gray-600 mt-1">{currentProgress}% complete</p>
          </div>
          
          <Separator />
          
          <div>
            <h3 className="font-semibold mb-2">Description</h3>
            <p className="text-gray-600 leading-relaxed">{training.description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

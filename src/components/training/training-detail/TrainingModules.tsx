
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Play, Clock, Lock } from "lucide-react";

interface TrainingModule {
  name: string;
  duration: string;
  completed: boolean;
}

interface TrainingModulesProps {
  modules: TrainingModule[];
}

export const TrainingModules: React.FC<TrainingModulesProps> = ({ modules }) => {
  const completedModules = modules.filter(m => m.completed).length;
  const totalModules = modules.length;
  const progressPercentage = (completedModules / totalModules) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Training Modules</span>
          <Badge variant="outline">
            {completedModules}/{totalModules} Completed
          </Badge>
        </CardTitle>
        <Progress value={progressPercentage} className="w-full" />
      </Card<PERSON>eader>
      <CardContent className="space-y-4">
        {modules.map((module, index) => (
          <div 
            key={index}
            className={`flex items-center justify-between p-4 rounded-lg border ${
              module.completed ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
            }`}
          >
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                module.completed 
                  ? 'bg-green-500 text-white' 
                  : index === 0 || modules[index - 1]?.completed
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-300 text-gray-500'
              }`}>
                {module.completed ? (
                  <CheckCircle className="h-4 w-4" />
                ) : index === 0 || modules[index - 1]?.completed ? (
                  <Play className="h-4 w-4" />
                ) : (
                  <Lock className="h-4 w-4" />
                )}
              </div>
              
              <div>
                <h4 className="font-medium">{module.name}</h4>
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Clock className="h-3 w-3" />
                  {module.duration}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {module.completed ? (
                <Badge className="bg-green-100 text-green-800">Completed</Badge>
              ) : index === 0 || modules[index - 1]?.completed ? (
                <Button size="sm" variant="outline">
                  <Play className="h-3 w-3 mr-1" />
                  Start
                </Button>
              ) : (
                <Badge variant="secondary">Locked</Badge>
              )}
            </div>
          </div>
        ))}
        
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Module Requirements</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Complete modules in sequential order</li>
            <li>• Each module must be completed before proceeding to the next</li>
            <li>• Progress is automatically saved</li>
            <li>• Final assessment will be unlocked after all modules are completed</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};


import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Target, User, History, CheckCircle } from "lucide-react";

interface TrainingSidePanelProps {
  training: any;
}

export const TrainingSidePanel: React.FC<TrainingSidePanelProps> = ({ training }) => {
  return (
    <div className="space-y-6">
      {/* Training Objectives */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            Learning Objectives
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {training.objectives.map((objective: string, index: number) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 shrink-0" />
                <span>{objective}</span>
              </li>
            ))}
          </ul>
        </Card<PERSON>ontent>
      </Card>

      {/* Instructor Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Instructor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold">{training.instructor.name}</h4>
              <p className="text-sm text-gray-600">{training.instructor.title}</p>
              <p className="text-xs text-gray-500 mt-1">{training.instructor.experience}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Training History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5 text-blue-600" />
            Activity History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {training.history.map((item: any, index: number) => (
              <div key={index} className="flex items-start gap-3 text-sm">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 shrink-0"></div>
                <div>
                  <p className="font-medium">{item.action}</p>
                  <p className="text-gray-500">{item.date} at {item.time}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

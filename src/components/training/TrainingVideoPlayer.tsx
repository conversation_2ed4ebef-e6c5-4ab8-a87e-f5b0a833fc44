
import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Play, Pause, Save, LogOut, Clock, Eye } from "lucide-react";
import { useTraining } from "@/contexts/TrainingContext";

interface TrainingVideoPlayerProps {
  trainingId: string;
  trainingTitle: string;
  videoUrl?: string;
  onSaveAndExit: () => void;
  onComplete: () => void;
}

export const TrainingVideoPlayer: React.FC<TrainingVideoPlayerProps> = ({
  trainingId,
  trainingTitle,
  videoUrl = "/placeholder-video.mp4",
  onSaveAndExit,
  onComplete
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [watchPercentage, setWatchPercentage] = useState(0);
  const [timeWatched, setTimeWatched] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const { updateProgress } = useTraining();

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateTime = () => {
      setCurrentTime(video.currentTime);
      const percentage = (video.currentTime / video.duration) * 100;
      setWatchPercentage(percentage);
      setTimeWatched(video.currentTime);
      
      // Update training progress
      updateProgress(trainingId, percentage, `${Math.floor(video.currentTime / 60)} minutes`);
    };

    const setVideoDuration = () => {
      setDuration(video.duration);
    };

    video.addEventListener('timeupdate', updateTime);
    video.addEventListener('loadedmetadata', setVideoDuration);

    return () => {
      video.removeEventListener('timeupdate', updateTime);
      video.removeEventListener('loadedmetadata', setVideoDuration);
    };
  }, [trainingId, updateProgress]);

  const togglePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleSaveAndExit = () => {
    const video = videoRef.current;
    if (video) {
      video.pause();
      setIsPlaying(false);
    }
    onSaveAndExit();
  };

  const handleVideoEnd = () => {
    setWatchPercentage(100);
    updateProgress(trainingId, 100, `${Math.floor(duration / 60)} minutes`);
    onComplete();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5 text-blue-600" />
            {trainingTitle}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Video Player */}
          <div className="relative bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-64 md:h-96"
              onEnded={handleVideoEnd}
              controls={false}
            >
              <source src={videoUrl} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            
            {/* Custom Controls Overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={togglePlayPause}
                  className="text-white hover:bg-white/20"
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                <div className="flex-1">
                  <Progress value={(currentTime / duration) * 100} className="h-2 bg-white/20" />
                </div>
                
                <span className="text-white text-sm">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </span>
              </div>
            </div>
          </div>

          {/* Progress Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Eye className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Watch Progress</span>
                </div>
                <div className="text-2xl font-bold text-blue-600">{Math.round(watchPercentage)}%</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Time Watched</span>
                </div>
                <div className="text-2xl font-bold text-green-600">{formatTime(timeWatched)}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-gray-600" />
                  <span className="text-sm font-medium">Total Duration</span>
                </div>
                <div className="text-2xl font-bold text-gray-600">{formatTime(duration)}</div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button onClick={handleSaveAndExit} variant="outline" className="gap-2">
              <Save className="h-4 w-4" />
              Save & Exit
            </Button>
            
            {watchPercentage >= 100 && (
              <Button onClick={onComplete} className="gap-2">
                Complete Training
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

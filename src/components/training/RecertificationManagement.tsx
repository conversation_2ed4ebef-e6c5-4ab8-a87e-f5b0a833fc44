
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  RefreshCw, 
  Calendar, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Plus,
  Settings,
  Bell,
  FileText
} from "lucide-react";
import { mockRecurringTrainings, mockRecertificationSchedules } from "@/data/mockRecertificationData";
import { RecurringTraining, RecertificationSchedule } from "@/types/recertification";
import { CreateRecurringTrainingDialog } from "./CreateRecurringTrainingDialog";

export const RecertificationManagement: React.FC = () => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedTraining, setSelectedTraining] = useState<string>("all");

  // Filter schedules based on selected filters
  const filteredSchedules = mockRecertificationSchedules.filter(schedule => {
    if (statusFilter !== "all" && schedule.status !== statusFilter) return false;
    if (selectedTraining !== "all" && schedule.recurringTrainingId !== selectedTraining) return false;
    return true;
  });

  const getStatusBadge = (status: RecertificationSchedule['status']) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "Assigned":
        return <Badge className="bg-blue-100 text-blue-800">Assigned</Badge>;
      case "Upcoming":
        return <Badge className="bg-gray-100 text-gray-800">Upcoming</Badge>;
      case "Overdue":
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getRecurrenceBadge = (training: RecurringTraining) => {
    return (
      <Badge variant="outline" className="gap-1">
        <RefreshCw className="h-3 w-3" />
        {training.recurrenceRule.description}
      </Badge>
    );
  };

  // Calculate summary stats
  const totalSchedules = mockRecertificationSchedules.length;
  const completed = mockRecertificationSchedules.filter(s => s.status === "Completed").length;
  const overdue = mockRecertificationSchedules.filter(s => s.status === "Overdue").length;
  const upcoming = mockRecertificationSchedules.filter(s => s.status === "Upcoming").length;

  return (
    <div className="space-y-6 w-full">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Recertification Management</h3>
          <p className="text-sm text-gray-600">Manage recurring training schedules and automatic renewals</p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Recurring Training
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Schedules</p>
                <p className="text-2xl font-bold">{totalSchedules}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{overdue}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold text-blue-600">{upcoming}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="schedules" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="schedules" className="gap-2">
            <Calendar className="h-4 w-4" />
            Recertification Schedules
          </TabsTrigger>
          <TabsTrigger value="recurring" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Recurring Trainings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="schedules" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                <span className="text-sm font-medium">Filters:</span>
                
                <div className="flex flex-col md:flex-row gap-4 flex-1">
                  <div>
                    <label className="text-sm font-medium mb-2 block">Status</label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="Upcoming">Upcoming</SelectItem>
                        <SelectItem value="Assigned">Assigned</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">Training</label>
                    <Select value={selectedTraining} onValueChange={setSelectedTraining}>
                      <SelectTrigger className="w-64">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Trainings</SelectItem>
                        {mockRecurringTrainings.map((training) => (
                          <SelectItem key={training.id} value={training.id}>
                            {training.trainingTitle}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedules Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Recertification Schedules ({filteredSchedules.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Training</TableHead>
                      <TableHead>Current Cycle</TableHead>
                      <TableHead>Due Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Next Cycle</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSchedules.map((schedule) => {
                      const training = mockRecurringTrainings.find(t => t.id === schedule.recurringTrainingId);
                      return (
                        <TableRow key={schedule.id}>
                          <TableCell>
                            <div className="font-medium">{schedule.employeeName}</div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{training?.trainingTitle}</div>
                              <div className="text-sm text-gray-500">
                                {getRecurrenceBadge(training!)}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(schedule.currentCycleStart).toLocaleDateString()} - 
                              {new Date(schedule.currentCycleDue).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              {new Date(schedule.currentCycleDue).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(schedule.status)}</TableCell>
                          <TableCell>
                            {schedule.nextCycleDue ? (
                              <div className="text-sm text-gray-600">
                                {new Date(schedule.nextCycleDue).toLocaleDateString()}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-sm">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" className="gap-1">
                                <Bell className="h-3 w-3" />
                                Remind
                              </Button>
                              <Button variant="outline" size="sm" className="gap-1">
                                <Settings className="h-3 w-3" />
                                Manage
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {filteredSchedules.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No recertification schedules found matching the current filters.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recurring" className="space-y-4">
          {/* Recurring Trainings Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-blue-600" />
                Recurring Training Rules ({mockRecurringTrainings.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Training</TableHead>
                      <TableHead>Recurrence</TableHead>
                      <TableHead>Auto-Reassign</TableHead>
                      <TableHead>Assigned To</TableHead>
                      <TableHead>Next Due</TableHead>
                      <TableHead>Reminders</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockRecurringTrainings.map((training) => (
                      <TableRow key={training.id}>
                        <TableCell>
                          <div className="font-medium">{training.trainingTitle}</div>
                        </TableCell>
                        <TableCell>{getRecurrenceBadge(training)}</TableCell>
                        <TableCell>
                          <Badge variant={training.autoReassign ? "default" : "secondary"}>
                            {training.autoReassign ? "Enabled" : "Manual"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">{training.assignedRoles.join(", ")}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            {new Date(training.nextDue).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {training.reminderSettings.enabled ? (
                              <div className="flex items-center gap-1">
                                <Bell className="h-3 w-3 text-green-600" />
                                <span>{training.reminderSettings.reminderDays.length} alerts</span>
                              </div>
                            ) : (
                              <span className="text-gray-400">Disabled</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={training.isActive ? "default" : "secondary"}>
                            {training.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" className="gap-1">
                              <Settings className="h-3 w-3" />
                              Edit
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CreateRecurringTrainingDialog 
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  );
};

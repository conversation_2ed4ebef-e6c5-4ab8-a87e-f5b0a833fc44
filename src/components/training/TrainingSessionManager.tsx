
import React, { useState } from "react";
import { TrainingVideoPlayer } from "./TrainingVideoPlayer";
import { TrainingSimulator } from "./TrainingSimulator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Play, Book, ArrowLeft } from "lucide-react";
import { EmployeeTraining } from "@/types/training";
import { useTraining } from "@/contexts/TrainingContext";

interface TrainingSessionManagerProps {
  training: EmployeeTraining;
  onComplete: () => void;
  onExit: () => void;
}

export const TrainingSessionManager: React.FC<TrainingSessionManagerProps> = ({
  training,
  onComplete,
  onExit
}) => {
  const [sessionMode, setSessionMode] = useState<'overview' | 'video' | 'interactive'>('overview');
  const { updateTrainingProgress } = useTraining();

  const handleStartSession = () => {
    if (training.training.type === "Video") {
      setSessionMode('video');
    } else {
      setSessionMode('interactive');
    }
  };

  const handleSessionComplete = () => {
    updateTrainingProgress(training.id, 100);
    onComplete();
  };

  if (sessionMode === 'video') {
    return (
      <TrainingVideoPlayer
        trainingId={training.id}
        trainingTitle={training.training.title}
        videoUrl="/api/placeholder/800/450" // Mock video URL
        onSaveAndExit={onExit}
        onComplete={handleSessionComplete}
      />
    );
  }

  if (sessionMode === 'interactive') {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onExit}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Exit Training
          </Button>
          <h2 className="text-xl font-semibold">{training.training.title}</h2>
        </div>
        <TrainingSimulator training={training} />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Book className="h-5 w-5" />
            {training.training.title}
          </CardTitle>
          <Button variant="outline" onClick={onExit}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600">{training.training.description}</p>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span>
            <p>{training.training.type}</p>
          </div>
          <div>
            <span className="font-medium">Duration:</span>
            <p>{training.training.estimatedDuration}</p>
          </div>
        </div>

        <Button onClick={handleStartSession} className="w-full gap-2">
          <Play className="h-4 w-4" />
          {training.status === "Not Started" ? "Start Training" : "Continue Training"}
        </Button>
      </CardContent>
    </Card>
  );
};


import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, Square, CheckCircle, Clock } from "lucide-react";
import { EmployeeTraining } from "@/types/training";
import { useTraining } from "@/contexts/TrainingContext";
import { useToast } from "@/hooks/use-toast";

interface TrainingSimulatorProps {
  training: EmployeeTraining;
}

export const TrainingSimulator: React.FC<TrainingSimulatorProps> = ({ training }) => {
  const { updateTrainingProgress, completeTraining } = useTraining();
  const { toast } = useToast();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(training.progress);

  const handleStart = () => {
    setIsPlaying(true);
    toast({
      title: "Training Started",
      description: `Started ${training.training.title}`,
    });

    // Simulate progress updates
    const interval = setInterval(() => {
      setCurrentProgress(prev => {
        const newProgress = Math.min(prev + 5, 100);
        updateTrainingProgress(training.id, newProgress);
        
        if (newProgress >= 100) {
          setIsPlaying(false);
          completeTraining(training.id, 95);
          toast({
            title: "Training Completed!",
            description: `Congratulations! You've completed ${training.training.title}`,
          });
          clearInterval(interval);
        }
        
        return newProgress;
      });
    }, 1000);

    // Store interval for cleanup
    setTimeout(() => {
      if (isPlaying) {
        setIsPlaying(false);
        clearInterval(interval);
      }
    }, 10000);
  };

  const handlePause = () => {
    setIsPlaying(false);
    toast({
      title: "Training Paused",
      description: `Paused ${training.training.title}`,
    });
  };

  const handleStop = () => {
    setIsPlaying(false);
    toast({
      title: "Training Stopped",
      description: `Stopped ${training.training.title}`,
    });
  };

  const getStatusColor = () => {
    switch (training.status) {
      case "Completed": return "bg-green-500";
      case "In Progress": return "bg-blue-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{training.training.title}</CardTitle>
          <Badge className={getStatusColor()}>{training.status}</Badge>
        </div>
        <p className="text-sm text-gray-600">{training.training.description}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium">Type:</span>
            <p>{training.training.type}</p>
          </div>
          <div>
            <span className="font-medium">Duration:</span>
            <p>{training.training.estimatedDuration} min</p>
          </div>
          <div>
            <span className="font-medium">Due Date:</span>
            <p>{new Date(training.dueDate).toLocaleDateString()}</p>
          </div>
          <div>
            <span className="font-medium">Time Spent:</span>
            <p>{training.timeSpent} min</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{currentProgress}%</span>
          </div>
          <Progress value={currentProgress} className="h-2" />
        </div>

        <div className="flex gap-2">
          {training.status !== "Completed" && (
            <>
              {!isPlaying ? (
                <Button onClick={handleStart} className="gap-2">
                  <Play className="h-4 w-4" />
                  {currentProgress > 0 ? "Continue" : "Start"}
                </Button>
              ) : (
                <Button onClick={handlePause} variant="outline" className="gap-2">
                  <Pause className="h-4 w-4" />
                  Pause
                </Button>
              )}
              <Button onClick={handleStop} variant="outline" className="gap-2">
                <Square className="h-4 w-4" />
                Stop
              </Button>
            </>
          )}
          
          {training.status === "Completed" && (
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Training Completed</span>
              {training.quizScore && (
                <Badge variant="outline">Score: {training.quizScore}%</Badge>
              )}
            </div>
          )}
        </div>

        {isPlaying && (
          <div className="flex items-center gap-2 text-blue-600 text-sm">
            <Clock className="h-4 w-4 animate-spin" />
            <span>Training in progress...</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

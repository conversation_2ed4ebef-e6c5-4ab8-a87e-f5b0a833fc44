
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RefreshCw, Calendar, Bell, Users, CheckCircle } from "lucide-react";
import { mockTrainings } from "@/data/mockTrainingData";

interface CreateRecurringTrainingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateRecurringTrainingDialog: React.FC<CreateRecurringTrainingDialogProps> = ({
  open,
  onOpenChange
}) => {
  const [selectedTraining, setSelectedTraining] = useState("");
  const [recurrenceType, setRecurrenceType] = useState<"months" | "years">("years");
  const [recurrenceInterval, setRecurrenceInterval] = useState("1");
  const [autoReassign, setAutoReassign] = useState(true);
  const [reminderEnabled, setReminderEnabled] = useState(true);
  const [reminderDays, setReminderDays] = useState<number[]>([30, 14, 7, 1]);
  const [escalationDays, setEscalationDays] = useState("3");
  const [assignmentType, setAssignmentType] = useState<"roles" | "individuals">("roles");
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  const roles = ["Quality Manager", "Production Staff", "Lab Technician", "All Employees", "Quality Control", "Warehouse"];

  const handleRoleToggle = (role: string) => {
    setSelectedRoles(prev => 
      prev.includes(role)
        ? prev.filter(r => r !== role)
        : [...prev, role]
    );
  };

  const handleReminderDayToggle = (day: number) => {
    setReminderDays(prev => 
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day].sort((a, b) => b - a)
    );
  };

  const handleCreate = () => {
    console.log("Creating recurring training:", {
      trainingId: selectedTraining,
      recurrenceType,
      recurrenceInterval: parseInt(recurrenceInterval),
      autoReassign,
      reminderSettings: {
        enabled: reminderEnabled,
        reminderDays,
        escalationDays: parseInt(escalationDays)
      },
      assignmentType,
      selectedRoles
    });
    onOpenChange(false);
    // Reset form
    setSelectedTraining("");
    setRecurrenceType("years");
    setRecurrenceInterval("1");
    setAutoReassign(true);
    setReminderEnabled(true);
    setReminderDays([30, 14, 7, 1]);
    setEscalationDays("3");
    setSelectedRoles([]);
  };

  const selectedTrainingData = mockTrainings.find(t => t.id === selectedTraining);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5 text-blue-600" />
            Create Recurring Training
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Training Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Select Training</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Training Program</Label>
                <Select value={selectedTraining} onValueChange={setSelectedTraining}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a training program" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockTrainings.map((training) => (
                      <SelectItem key={training.id} value={training.id}>
                        {training.title} ({training.category})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedTrainingData && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900">{selectedTrainingData.title}</h4>
                  <p className="text-sm text-blue-700 mt-1">{selectedTrainingData.description}</p>
                  <p className="text-xs text-blue-600 mt-2">
                    Duration: {selectedTrainingData.estimatedDuration} | Type: {selectedTrainingData.type}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recurrence Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Recurrence Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Recurrence Type</Label>
                  <Select value={recurrenceType} onValueChange={(value: "months" | "years") => setRecurrenceType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="months">Months</SelectItem>
                      <SelectItem value="years">Years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Interval</Label>
                  <Input
                    type="number"
                    value={recurrenceInterval}
                    onChange={(e) => setRecurrenceInterval(e.target.value)}
                    min="1"
                    max={recurrenceType === "months" ? "24" : "5"}
                  />
                </div>
              </div>
              
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm">
                  <strong>Recurrence:</strong> Every {recurrenceInterval} {recurrenceType}
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-reassign when due</Label>
                  <p className="text-sm text-gray-600">Automatically create new assignments</p>
                </div>
                <Switch checked={autoReassign} onCheckedChange={setAutoReassign} />
              </div>
            </CardContent>
          </Card>

          {/* Assignment Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Users className="h-4 w-4" />
                Assignment Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Assignment Type</Label>
                <Select value={assignmentType} onValueChange={(value: "roles" | "individuals") => setAssignmentType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="roles">Assign to Roles/Departments</SelectItem>
                    <SelectItem value="individuals">Assign to Individuals</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {assignmentType === "roles" && (
                <div>
                  <Label className="mb-3 block">Select Roles</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {roles.map((role) => (
                      <div key={role} className="flex items-center space-x-2">
                        <Checkbox
                          checked={selectedRoles.includes(role)}
                          onCheckedChange={() => handleRoleToggle(role)}
                        />
                        <span className="text-sm">{role}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reminder Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Reminder Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Enable reminders</Label>
                  <p className="text-sm text-gray-600">Send automatic reminders before due dates</p>
                </div>
                <Switch checked={reminderEnabled} onCheckedChange={setReminderEnabled} />
              </div>

              {reminderEnabled && (
                <>
                  <div>
                    <Label className="mb-3 block">Reminder Schedule (days before due)</Label>
                    <div className="flex gap-2 flex-wrap">
                      {[60, 30, 14, 7, 3, 1].map((day) => (
                        <div key={day} className="flex items-center space-x-2">
                          <Checkbox
                            checked={reminderDays.includes(day)}
                            onCheckedChange={() => handleReminderDayToggle(day)}
                          />
                          <span className="text-sm">{day} days</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label>Escalation after overdue (days)</Label>
                    <Input
                      type="number"
                      value={escalationDays}
                      onChange={(e) => setEscalationDays(e.target.value)}
                      min="1"
                      max="30"
                      className="w-32"
                    />
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Actions */}
        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          
          <Button 
            onClick={handleCreate} 
            disabled={!selectedTraining || selectedRoles.length === 0}
            className="gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            Create Recurring Training
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

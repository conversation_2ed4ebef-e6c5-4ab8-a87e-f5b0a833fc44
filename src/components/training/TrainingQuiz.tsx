
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, XCircle } from "lucide-react";

interface TrainingQuizProps {
  trainingId: string;
  onComplete: (score: number) => void;
}

export const TrainingQuiz: React.FC<TrainingQuizProps> = ({ trainingId, onComplete }) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [showResults, setShowResults] = useState(false);

  const questions = [
    {
      question: "What is the primary focus of ISO 9001:2015?",
      options: [
        "Environmental management",
        "Quality management systems", 
        "Safety management",
        "Financial management"
      ],
      correct: 1
    },
    {
      question: "Which principle emphasizes customer satisfaction?",
      options: [
        "Process approach",
        "Customer focus",
        "Leadership",
        "Continuous improvement"
      ],
      correct: 1
    },
    {
      question: "What does PDCA stand for?",
      options: [
        "Plan-Do-Check-Act",
        "Prevent-Detect-Correct-Analyze",
        "Process-Design-Control-Audit",
        "Plan-Design-Create-Assess"
      ],
      correct: 0
    }
  ];

  const handleAnswer = (answerIndex: number) => {
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = answerIndex;
    setAnswers(newAnswers);

    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Calculate score
      const correctAnswers = newAnswers.filter((answer, index) => answer === questions[index].correct).length;
      const score = Math.round((correctAnswers / questions.length) * 100);
      setShowResults(true);
      onComplete(score);
    }
  };

  const correctAnswers = answers.filter((answer, index) => answer === questions[index].correct).length;
  const score = Math.round((correctAnswers / questions.length) * 100);

  if (showResults) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className={`mb-4 ${score >= 70 ? 'text-green-600' : 'text-red-600'}`}>
            {score >= 70 ? (
              <CheckCircle className="h-16 w-16 mx-auto mb-2" />
            ) : (
              <XCircle className="h-16 w-16 mx-auto mb-2" />
            )}
            <h3 className="text-xl font-bold">
              {score >= 70 ? 'Congratulations!' : 'Quiz Failed'}
            </h3>
          </div>
          <p className="text-lg mb-4">Your Score: {score}%</p>
          <p className="text-gray-600 mb-4">
            You answered {correctAnswers} out of {questions.length} questions correctly.
          </p>
          {score < 70 && (
            <p className="text-sm text-red-600">
              You need at least 70% to pass. Please review the material and try again.
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">
          Question {currentQuestion + 1} of {questions.length}
        </h3>
        <div className="text-sm text-gray-600">
          Progress: {Math.round(((currentQuestion) / questions.length) * 100)}%
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <h4 className="text-lg font-medium mb-4">
            {questions[currentQuestion].question}
          </h4>
          <div className="space-y-3">
            {questions[currentQuestion].options.map((option, index) => (
              <Button
                key={index}
                variant="outline"
                className="w-full text-left justify-start p-4 h-auto"
                onClick={() => handleAnswer(index)}
              >
                <span className="mr-3 font-bold">{String.fromCharCode(65 + index)}.</span>
                {option}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

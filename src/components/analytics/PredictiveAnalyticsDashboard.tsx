
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, <PERSON>atter<PERSON><PERSON>, Scatter } from 'recharts';
import { TrendingUp, AlertTriangle, Brain, Target, Zap, Activity, BarChart3 } from 'lucide-react';

const predictiveData = [
  { month: 'Jan', predicted: 92, actual: 90, confidence: 85 },
  { month: 'Feb', predicted: 88, actual: 89, confidence: 82 },
  { month: 'Mar', predicted: 94, actual: 92, confidence: 88 },
  { month: 'Apr', predicted: 89, actual: 87, confidence: 90 },
  { month: 'May', predicted: 91, actual: null, confidence: 85 },
  { month: 'Jun', predicted: 93, actual: null, confidence: 88 }
];

const riskFactors = [
  { factor: 'Supplier Performance', impact: 85, trend: 'increasing', status: 'high' },
  { factor: 'Equipment Maintenance', impact: 72, trend: 'stable', status: 'medium' },
  { factor: 'Environmental Conditions', impact: 45, trend: 'decreasing', status: 'low' },
  { factor: 'Process Variations', impact: 68, trend: 'increasing', status: 'medium' },
  { factor: 'Human Factors', impact: 55, trend: 'stable', status: 'low' }
];

const anomalyData = [
  { id: 1, type: 'Quality Deviation', severity: 'High', confidence: 92, location: 'Line A', timestamp: '2 hours ago' },
  { id: 2, type: 'Process Drift', severity: 'Medium', confidence: 78, location: 'Line B', timestamp: '4 hours ago' },
  { id: 3, type: 'Equipment Anomaly', severity: 'Low', confidence: 65, location: 'Station 3', timestamp: '6 hours ago' }
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export const PredictiveAnalyticsDashboard: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('6months');

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'decreasing': return <TrendingUp className="h-4 w-4 text-green-500 rotate-180" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Brain className="h-8 w-8 text-purple-600" />
            AI-Powered Predictive Analytics
          </h1>
          <p className="text-gray-600">Advanced insights and predictions for quality management</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setSelectedTimeframe('3months')}>
            3 Months
          </Button>
          <Button variant="outline" onClick={() => setSelectedTimeframe('6months')}>
            6 Months
          </Button>
          <Button variant="outline" onClick={() => setSelectedTimeframe('1year')}>
            1 Year
          </Button>
        </div>
      </div>

      {/* AI Insights Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Predicted Quality Score</p>
                <p className="text-3xl font-bold text-green-600">91%</p>
                <p className="text-sm text-gray-500 flex items-center mt-1">
                  <Target className="h-4 w-4 mr-1" />
                  85% confidence
                </p>
              </div>
              <Brain className="h-12 w-12 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Risk Level</p>
                <p className="text-3xl font-bold text-yellow-600">Medium</p>
                <p className="text-sm text-gray-500 flex items-center mt-1">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  3 factors monitored
                </p>
              </div>
              <AlertTriangle className="h-12 w-12 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Anomalies Detected</p>
                <p className="text-3xl font-bold text-red-600">3</p>
                <p className="text-sm text-gray-500 flex items-center mt-1">
                  <Zap className="h-4 w-4 mr-1" />
                  Last 24 hours
                </p>
              </div>
              <Zap className="h-12 w-12 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="predictions" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="risk-factors">Risk Factors</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          <TabsTrigger value="insights">AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="predictions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quality Performance Predictions</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={predictiveData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[80, 100]} />
                  <Tooltip />
                  <Line type="monotone" dataKey="actual" stroke="#22c55e" strokeWidth={2} name="Actual" />
                  <Line type="monotone" dataKey="predicted" stroke="#3b82f6" strokeWidth={2} strokeDasharray="5 5" name="Predicted" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="risk-factors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Risk Factor Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {riskFactors.map((factor, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium">{factor.factor}</h4>
                        {getTrendIcon(factor.trend)}
                        <Badge className={getSeverityColor(factor.status)}>
                          {factor.status}
                        </Badge>
                      </div>
                      <Progress value={factor.impact} className="h-2" />
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-lg font-bold">{factor.impact}%</p>
                      <p className="text-sm text-gray-500">Impact</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anomalies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Real-time Anomaly Detection</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {anomalyData.map((anomaly) => (
                  <div key={anomaly.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500" />
                      <div>
                        <h4 className="font-medium">{anomaly.type}</h4>
                        <p className="text-sm text-gray-600">{anomaly.location} • {anomaly.timestamp}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <p className="text-sm font-medium">{anomaly.confidence}% confidence</p>
                        <Progress value={anomaly.confidence} className="h-1 w-20" />
                      </div>
                      <Badge className={getSeverityColor(anomaly.severity)}>
                        {anomaly.severity}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>AI-Generated Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Process Optimization Opportunity</h4>
                  <p className="text-blue-800 text-sm">Line A shows 15% improvement potential by adjusting temperature parameters during peak hours.</p>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-medium text-yellow-900 mb-2">Preventive Maintenance Alert</h4>
                  <p className="text-yellow-800 text-sm">Equipment in Station 3 predicted to require maintenance within 2 weeks based on performance patterns.</p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-medium text-green-900 mb-2">Quality Trend</h4>
                  <p className="text-green-800 text-sm">Overall quality metrics show consistent improvement over the last 3 months with 94% reliability.</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommended Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Target className="h-4 w-4 mr-2" />
                  Optimize Line A Parameters
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Schedule Station 3 Maintenance
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Review Supplier Performance
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Brain className="h-4 w-4 mr-2" />
                  Update ML Model Training
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

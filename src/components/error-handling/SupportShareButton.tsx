
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Share2, Mail, Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ErrorDetails {
  error?: Error | null;
  errorInfo?: any;
  userAgent?: string;
  timestamp?: string;
  url?: string;
  statusCode?: number;
  additionalContext?: string;
}

interface SupportShareButtonProps {
  errorDetails?: ErrorDetails;
  title?: string;
  description?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
}

export const SupportShareButton: React.FC<SupportShareButtonProps> = ({
  errorDetails = {},
  title = "Share with Support",
  description = "Help us fix this issue by sharing error details",
  variant = "outline",
  size = "default"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [userDescription, setUserDescription] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const { toast } = useToast();

  const formatErrorReport = () => {
    const report = {
      timestamp: errorDetails.timestamp || new Date().toISOString(),
      url: errorDetails.url || window.location.href,
      userAgent: errorDetails.userAgent || navigator.userAgent,
      error: errorDetails.error?.message || 'Unknown error',
      stack: errorDetails.error?.stack || 'No stack trace available',
      statusCode: errorDetails.statusCode,
      userDescription,
      userEmail,
      additionalContext: errorDetails.additionalContext
    };

    return JSON.stringify(report, null, 2);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(formatErrorReport());
      toast({
        title: "Copied to clipboard",
        description: "Error details have been copied. You can paste them in your support ticket.",
      });
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please manually copy the error details below.",
        variant: "destructive"
      });
    }
  };

  const sendEmail = () => {
    const subject = encodeURIComponent(`Error Report - ${errorDetails.error?.message || 'Application Error'}`);
    const body = encodeURIComponent(`
Dear Support Team,

I encountered an error while using the application. Please find the details below:

User Description:
${userDescription}

Technical Details:
${formatErrorReport()}

Best regards,
${userEmail || 'User'}
    `);

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setIsOpen(true)}
        className="gap-2"
      >
        <Share2 className="h-4 w-4" />
        {title}
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Share Error Details with Support</DialogTitle>
            <DialogDescription>
              {description}. This information helps our team identify and fix the issue quickly.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="user-email">Your Email (Optional)</Label>
              <Input
                id="user-email"
                type="email"
                placeholder="<EMAIL>"
                value={userEmail}
                onChange={(e) => setUserEmail(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="user-description">Describe what you were doing when this error occurred</Label>
              <Textarea
                id="user-description"
                placeholder="I was trying to upload a document when the page suddenly crashed..."
                value={userDescription}
                onChange={(e) => setUserDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div>
              <Label>Error Details (Technical Information)</Label>
              <Textarea
                value={formatErrorReport()}
                readOnly
                rows={8}
                className="font-mono text-xs bg-gray-50"
              />
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={copyToClipboard}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Details
              </Button>
              <Button onClick={sendEmail}>
                <Mail className="h-4 w-4 mr-2" />
                Send Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

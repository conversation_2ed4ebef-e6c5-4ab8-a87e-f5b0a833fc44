
import React, { useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Wifi, WifiOff, AlertTriangle, X, RefreshCw } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';

export const NetworkStatusBanner: React.FC = () => {
  const { networkStatus, retry } = useNetworkStatus();
  const [isDismissed, setIsDismissed] = useState(false);

  if (networkStatus.isOnline && !networkStatus.isSlowConnection) {
    return null;
  }

  if (isDismissed) {
    return null;
  }

  const getIcon = () => {
    if (!networkStatus.isOnline) return <WifiOff className="h-4 w-4" />;
    if (networkStatus.isSlowConnection) return <AlertTriangle className="h-4 w-4" />;
    return <Wifi className="h-4 w-4" />;
  };

  const getMessage = () => {
    if (!networkStatus.isOnline) {
      return 'You are currently offline. Some features may not be available.';
    }
    if (networkStatus.isSlowConnection) {
      return 'Your internet connection appears to be slow. This may affect performance.';
    }
    return '';
  };

  const getVariant = () => {
    if (!networkStatus.isOnline) return 'destructive';
    return 'default';
  };

  return (
    <Alert variant={getVariant()} className="fixed top-0 left-0 right-0 z-50 rounded-none border-x-0 border-t-0">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          {getIcon()}
          <AlertDescription className="font-medium">
            {getMessage()}
            {networkStatus.lastDisconnected && (
              <span className="text-sm text-muted-foreground ml-2">
                Last disconnected: {networkStatus.lastDisconnected.toLocaleTimeString()}
              </span>
            )}
          </AlertDescription>
        </div>
        <div className="flex items-center gap-2">
          {!networkStatus.isOnline && (
            <Button
              variant="outline"
              size="sm"
              onClick={retry}
              className="h-7"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry ({networkStatus.retryCount})
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsDismissed(true)}
            className="h-7 w-7 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </Alert>
  );
};


import React from "react";
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Download, Award, Calendar, Hash, Building } from "lucide-react";
import { TrainingCertificate } from "@/types/certificates";
import { CertificateStatusBadge } from "./CertificateStatusBadge";

interface CertificateViewerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  certificate: TrainingCertificate | null;
}

export const CertificateViewerDialog: React.FC<CertificateViewerDialogProps> = ({
  open,
  onOpenChange,
  certificate
}) => {
  if (!certificate) return null;

  const handleDownload = () => {
    console.log("Downloading certificate:", certificate.certificateId);
    // Implementation for downloading certificate
  };

  const handleViewBadge = () => {
    console.log("Viewing digital badge:", certificate.certificateId);
    // Implementation for viewing digital badge
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-blue-600" />
            Certificate Details - {certificate.trainingName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Certificate Information */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-semibold text-lg mb-4">{certificate.trainingName}</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        <strong>Completion Date:</strong> {new Date(certificate.completionDate).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        <strong>Certificate ID:</strong> {certificate.certificateId}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        <strong>Issued by:</strong> {certificate.issuer}
                      </span>
                    </div>
                    {certificate.validUntil && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          <strong>Valid Until:</strong> {new Date(certificate.validUntil).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <strong className="text-sm">Status:</strong>
                    <div className="mt-1">
                      <CertificateStatusBadge status={certificate.status} />
                    </div>
                  </div>
                  <div>
                    <strong className="text-sm">Type:</strong>
                    <div className="mt-1">
                      <Badge variant="outline" className="gap-1">
                        {certificate.certificateType === "PDF" ? "📄" : "🏆"}
                        {certificate.certificateType}
                      </Badge>
                    </div>
                  </div>
                  {certificate.credentialHash && (
                    <div>
                      <strong className="text-sm">Credential Hash:</strong>
                      <div className="mt-1 font-mono text-xs bg-gray-100 p-2 rounded">
                        {certificate.credentialHash}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Certificate Preview */}
          <Card>
            <CardContent className="p-6">
              <h4 className="font-semibold mb-4">Certificate Preview</h4>
              
              {certificate.certificateType === "PDF" ? (
                <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <div className="space-y-4">
                    <div className="text-6xl">📄</div>
                    <div>
                      <h5 className="font-semibold">PDF Certificate</h5>
                      <p className="text-sm text-gray-600">Click download to view the full certificate</p>
                    </div>
                    <Button onClick={handleDownload} className="gap-2">
                      <Download className="h-4 w-4" />
                      Download PDF Certificate
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg p-8 text-center">
                  <div className="space-y-4">
                    <div className="text-6xl">🏆</div>
                    <div>
                      <h5 className="font-semibold text-blue-800">Digital Badge</h5>
                      <p className="text-sm text-blue-600">Verified digital credential</p>
                    </div>
                    <div className="bg-white rounded-lg p-4 max-w-sm mx-auto border">
                      <div className="space-y-2">
                        <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto flex items-center justify-center text-white text-2xl">
                          🎓
                        </div>
                        <h6 className="font-semibold text-sm">{certificate.trainingName}</h6>
                        <p className="text-xs text-gray-600">Issued by {certificate.issuer}</p>
                        <p className="text-xs text-gray-500">{new Date(certificate.completionDate).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <Button variant="outline" onClick={handleViewBadge} className="gap-2">
                      <Award className="h-4 w-4" />
                      View Full Badge
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

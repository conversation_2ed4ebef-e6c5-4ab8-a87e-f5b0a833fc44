
import React from "react";
import { Badge } from "@/components/ui/badge";
import { TrainingCertificate } from "@/types/certificates";

interface CertificateStatusBadgeProps {
  status: TrainingCertificate['status'];
}

export const CertificateStatusBadge: React.FC<CertificateStatusBadgeProps> = ({ status }) => {
  const getStatusConfig = (status: TrainingCertificate['status']) => {
    switch (status) {
      case "Active":
        return { variant: "default" as const, className: "bg-green-100 text-green-800" };
      case "Expiring Soon":
        return { variant: "default" as const, className: "bg-yellow-100 text-yellow-800" };
      case "Expired":
        return { variant: "destructive" as const, className: "bg-red-100 text-red-800" };
      default:
        return { variant: "secondary" as const, className: "bg-gray-100 text-gray-800" };
    }
  };

  const config = getStatusConfig(status);
  
  return (
    <Badge variant={config.variant} className={config.className}>
      {status}
    </Badge>
  );
};


import React from "react";
import { Award, Calendar, Shield } from "lucide-react";

interface CertificatePreviewProps {
  employeeName: string;
  trainingTitle: string;
  completionDate: string;
  certificateId: string;
  score: number;
}

export const CertificatePreview: React.FC<CertificatePreviewProps> = ({
  employeeName,
  trainingTitle,
  completionDate,
  certificateId,
  score
}) => {
  return (
    <div className="bg-white p-8 border-2 border-blue-200 rounded-lg shadow-lg max-w-4xl mx-auto">
      {/* Certificate Header */}
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="p-4 bg-blue-100 rounded-full">
            <Award className="h-12 w-12 text-blue-600" />
          </div>
        </div>
        <h1 className="text-4xl font-bold text-blue-800 mb-2">Certificate of Completion</h1>
        <div className="w-32 h-1 bg-blue-600 mx-auto"></div>
      </div>

      {/* Certificate Body */}
      <div className="text-center mb-8">
        <p className="text-lg text-gray-600 mb-6">This is to certify that</p>
        
        <h2 className="text-3xl font-bold text-gray-800 mb-6 border-b-2 border-blue-200 pb-2 inline-block">
          {employeeName}
        </h2>
        
        <p className="text-lg text-gray-600 mb-4">has successfully completed the training course</p>
        
        <h3 className="text-2xl font-semibold text-blue-700 mb-6">
          {trainingTitle}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Completion Date</p>
            <p className="font-semibold">{new Date(completionDate).toLocaleDateString()}</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Award className="h-6 w-6 text-green-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Score Achieved</p>
            <p className="font-semibold text-green-600">{score}%</p>
          </div>
          
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <Shield className="h-6 w-6 text-purple-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600">Certificate ID</p>
            <p className="font-semibold text-xs">{certificateId}</p>
          </div>
        </div>
      </div>

      {/* Certificate Footer */}
      <div className="border-t-2 border-blue-200 pt-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="text-center">
            <div className="w-48 border-b border-gray-400 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Training Administrator</p>
            <p className="font-medium">Quality Training Institute</p>
          </div>
          
          <div className="text-center">
            <div className="w-48 border-b border-gray-400 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Issue Date</p>
            <p className="font-medium">{new Date().toLocaleDateString()}</p>
          </div>
        </div>
        
        <div className="text-center mt-6">
          <p className="text-xs text-gray-500">
            This certificate is valid for 2 years from the date of completion.
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Certificate verification: {certificateId}
          </p>
        </div>
      </div>
    </div>
  );
};

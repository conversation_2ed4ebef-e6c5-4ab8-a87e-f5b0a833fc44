
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Award, Download, Eye, Calendar, Shield, FileText } from "lucide-react";
import { useTraining } from "@/contexts/TrainingContext";
import { CertificatePreview } from "./CertificatePreview";

interface CertificateViewerProps {
  employeeId: string;
  employeeName: string;
}

export const CertificateViewer: React.FC<CertificateViewerProps> = ({ 
  employeeId, 
  employeeName 
}) => {
  const { employeeTrainings } = useTraining();
  const [selectedCertificate, setSelectedCertificate] = useState<string | null>(null);

  const completedTrainings = employeeTrainings.filter(
    training => training.employeeId === employeeId && training.status === "Completed"
  );

  const handleDownloadCertificate = (trainingId: string, trainingTitle: string) => {
    // Mock download functionality
    const link = document.createElement('a');
    link.href = `data:text/plain;charset=utf-8,Certificate for ${trainingTitle} - ${employeeName}`;
    link.download = `${trainingTitle.replace(/[^a-z0-9]/gi, '_')}_Certificate.pdf`;
    link.click();
  };

  const handleViewCertificate = (certificateId: string) => {
    setSelectedCertificate(certificateId);
  };

  const getValidityStatus = (completedDate: string | null) => {
    if (!completedDate) return "Invalid";
    
    const completed = new Date(completedDate);
    const validUntil = new Date(completed);
    validUntil.setFullYear(validUntil.getFullYear() + 2); // 2 years validity
    
    const today = new Date();
    const daysUntilExpiry = Math.ceil((validUntil.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry < 0) return "Expired";
    if (daysUntilExpiry <= 30) return "Expiring Soon";
    return "Valid";
  };

  const getValidityBadge = (status: string) => {
    switch (status) {
      case "Valid":
        return <Badge className="bg-green-100 text-green-800">Valid</Badge>;
      case "Expiring Soon":
        return <Badge className="bg-yellow-100 text-yellow-800">Expiring Soon</Badge>;
      case "Expired":
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      default:
        return <Badge variant="outline">Invalid</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Certificates</p>
                <p className="text-2xl font-bold">{completedTrainings.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Valid Certificates</p>
                <p className="text-2xl font-bold">
                  {completedTrainings.filter(t => getValidityStatus(t.completedDate) === "Valid").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Expiring Soon</p>
                <p className="text-2xl font-bold">
                  {completedTrainings.filter(t => getValidityStatus(t.completedDate) === "Expiring Soon").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Training Certificates
          </CardTitle>
        </CardHeader>
        <CardContent>
          {completedTrainings.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Training</TableHead>
                    <TableHead>Completion Date</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Validity</TableHead>
                    <TableHead>Certificate ID</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {completedTrainings.map((training) => {
                    const validityStatus = getValidityStatus(training.completedDate);
                    const certificateId = training.certificateId || `CERT-${training.id.toUpperCase()}`;
                    
                    return (
                      <TableRow key={training.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{training.training.title}</div>
                            <div className="text-sm text-gray-500">{training.training.category}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {training.completedDate ? 
                            new Date(training.completedDate).toLocaleDateString() : 
                            "N/A"
                          }
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Award className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">{training.quizScore || "N/A"}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getValidityBadge(validityStatus)}
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {certificateId}
                          </code>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleViewCertificate(certificateId)}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-4xl">
                                <DialogHeader>
                                  <DialogTitle>Certificate Preview</DialogTitle>
                                </DialogHeader>
                                <CertificatePreview
                                  employeeName={employeeName}
                                  trainingTitle={training.training.title}
                                  completionDate={training.completedDate || ""}
                                  certificateId={certificateId}
                                  score={training.quizScore || 0}
                                />
                              </DialogContent>
                            </Dialog>
                            
                            <Button 
                              variant="default" 
                              size="sm"
                              onClick={() => handleDownloadCertificate(training.training.id, training.training.title)}
                            >
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Award className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No certificates available yet.</p>
              <p className="text-sm">Complete training assignments to earn certificates.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

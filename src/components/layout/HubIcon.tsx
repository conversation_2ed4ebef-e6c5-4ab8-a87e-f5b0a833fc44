
import React from "react";
import { 
  Toolt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
  Toolt<PERSON><PERSON>rovider
} from "@/components/ui/tooltip";

interface HubIconProps {
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  onClick?: () => void;
}

export const HubIcon: React.FC<HubIconProps> = ({ 
  icon, 
  label, 
  isActive = false,
  onClick 
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className={`group flex items-center w-full justify-center gap-3 px-2 py-3 rounded-xl mb-1.5 transition-all duration-300 ease-smooth transform-gpu hover:scale-110 hover:bg-sidebar-hover focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring ${
              isActive ? "bg-sidebar-hover/80 shadow-soft" : "bg-transparent"
            }`}
            onClick={onClick}
          >
            <span className={`w-6 h-6 shrink-0 flex items-center justify-center ${isActive ? 'text-white' : 'text-sidebar-text group-hover:text-white'} transition-all duration-300`}>
              {React.cloneElement(icon as React.ReactElement, { 
                strokeWidth: isActive ? 2.5 : 2,
                size: 22,
                className: `${isActive ? "text-white" : "text-sidebar-text group-hover:text-white"} transition-all duration-300`
              })}
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent side="right" align="center" className="bg-slate-900 text-white border-none shadow-large rounded-lg px-3 py-2 font-medium animate-slide-in-from-left">
          {label}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};


import React from "react";
import { <PERSON>, ChevronDown, LogOut, Settings, User } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface UserProfileDropdownProps {
  name: string;
  role: string;
  initials: string;
  collapsed?: boolean;
}

export const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({
  name,
  role,
  initials,
  collapsed = false,
}) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-2">
      {/* Notification Button */}
      <button className="flex items-center gap-2 w-full rounded-md text-white hover:bg-sidebar-hover transition-colors p-2">
        <Bell className="w-5 h-5 text-white" />
        {!collapsed && <span className="text-sm font-medium">Notifications</span>}
      </button>
      
      {/* Separator */}
      <div className="border-t border-white/10 my-2" />
      
      {/* User Profile Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center gap-2 w-full rounded-md text-white hover:bg-sidebar-hover transition-colors p-1">
            <div className="w-8 h-8 rounded-full bg-teal-800 flex items-center justify-center text-white shrink-0">
              {initials}
            </div>
            
            {!collapsed && (
              <>
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium text-white">{name}</span>
                  <span className="text-xs text-white/80">{role}</span>
                </div>
                
                <ChevronDown className="ml-auto w-4 h-4 text-white/80" />
              </>
            )}
          </button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem onClick={() => navigate("/profile")}>
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Logout</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};



import React from "react";
import { 
  BarChart3, 
  FileText, 
  Users, 
  Package,
  Building2,
  Award,
  Settings,
  Shield,
  TrendingUp,
  Calendar,
  ClipboardList,
  BookOpen,
  Factory,
  Truck,
  Boxes
} from "lucide-react";
import { NavItem } from "../types/sidebar-types";

export const getNavItemsData = (): NavItem[] => [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <BarChart3 className="h-5 w-5" />,
    href: '/dashboard',
    isActive: false
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: <FileText className="h-5 w-5" />,
    href: '/document-hub',
    isActive: false
  },
  {
    id: 'people',
    label: 'People',
    icon: <Users className="h-5 w-5" />,
    href: '/people',
    isActive: false
  },
  {
    id: 'assets',
    label: 'Assets',
    icon: <Package className="h-5 w-5" />,
    href: '/assets',
    isActive: false
  },
  {
    id: 'vendors',
    label: 'Vendors',
    icon: <Building2 className="h-5 w-5" />,
    href: '/vendors',
    isActive: false
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: <Package className="h-5 w-5" />,
    href: '/inventory',
    isActive: false
  },
  {
    id: 'product-hub',
    label: 'Product Hub',
    icon: <Boxes className="h-5 w-5" />,
    href: '/product-hub',
    isActive: false
  },
  {
    id: 'production-hub',
    label: 'Production Hub',
    icon: <Factory className="h-5 w-5" />,
    href: '/production-hub',
    isActive: false
  },
  {
    id: 'supplier-quality',
    label: 'Supplier Quality',
    icon: <Award className="h-5 w-5" />,
    href: '/supplier-quality',
    isActive: false
  },
  {
    id: 'audits',
    label: 'Audits',
    icon: <Shield className="h-5 w-5" />,
    href: '/audits',
    isActive: false
  },
  {
    id: 'compliance',
    label: 'Customer Compliance',
    icon: <ClipboardList className="h-5 w-5" />,
    href: '/customer-compliance',
    isActive: false
  },
  {
    id: 'training',
    label: 'Training',
    icon: <BookOpen className="h-5 w-5" />,
    href: '/training-library',
    isActive: false
  },
  {
    id: 'customer-portal',
    label: 'Customer Portal',
    icon: <Users className="h-5 w-5" />,
    href: '/customer-portal',
    isActive: false
  },
  {
    id: 'ppap',
    label: 'PPAP',
    icon: <FileText className="h-5 w-5" />,
    href: '/ppap',
    isActive: false
  },
  {
    id: 'apqp',
    label: 'APQP',
    icon: <TrendingUp className="h-5 w-5" />,
    href: '/apqp',
    isActive: false
  },
  {
    id: 'improvement',
    label: 'Improvement',
    icon: <TrendingUp className="h-5 w-5" />,
    href: '/improvement',
    isActive: false
  }
];

export const getInitialNavItems = (pathname: string): NavItem[] => {
  const navItems = getNavItemsData();
  
  // Set active state based on current pathname
  return navItems.map(item => ({
    ...item,
    isActive: pathname === item.href || 
              (pathname === "/" && item.href === "/dashboard") ||
              (pathname.startsWith("/document") && item.href === "/document-hub") ||
              (pathname.startsWith("/supplier-quality") && item.href === "/supplier-quality") ||
              (pathname.startsWith("/customer-compliance") && item.href === "/customer-compliance") ||
              (pathname.startsWith("/production-hub") && item.href === "/production-hub") ||
              (pathname.startsWith("/product-hub") && item.href === "/product-hub") ||
              (pathname.startsWith("/people") && item.href === "/people") ||
              (pathname.startsWith("/assets") && item.href === "/assets") ||
              (pathname.startsWith("/vendors") && item.href === "/vendors") ||
              (pathname.startsWith("/audits") && item.href === "/audits") ||
              (pathname.startsWith("/ppap") && item.href === "/ppap") ||
              (pathname.startsWith("/apqp") && item.href === "/apqp") ||
              (pathname.startsWith("/training-library") && item.href === "/training-library")
  }));
};

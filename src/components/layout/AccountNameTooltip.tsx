
import React from "react";
import { Building2, ChevronDown } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAccount, AccountLocation } from "@/contexts/AccountContext";
import { toast } from "sonner";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const AccountNameTooltip = () => {
  const isMobile = useIsMobile();
  const { currentAccount, switchAccount, accounts } = useAccount();
  
  const handleAccountChange = (location: AccountLocation) => {
    if (location !== currentAccount.location) {
      switchAccount(location);
      toast.success(`Switched to ${accounts.find(acc => acc.location === location)?.name}`, {
        description: "Account changed successfully",
        duration: 3000,
      });
    }
  };
  
  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          {isMobile ? (
            <button className="flex items-center justify-center w-10 h-10 rounded-md border border-gray-200 bg-white hover:bg-gray-50 transition-colors">
              <Building2 className="h-5 w-5 text-gray-600" />
            </button>
          ) : (
            <button className="flex items-center gap-2 px-3 py-1.5 rounded-md border border-gray-200 bg-white hover:bg-gray-50 transition-colors min-w-[200px]">
              <Building2 className="h-5 w-5 text-gray-600 shrink-0" />
              <div className="text-left flex-1 min-w-0">
                <p className="text-xs text-gray-500 truncate">Account Name</p>
                <p className="text-sm font-medium truncate">{currentAccount.name}</p>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-600 shrink-0" />
            </button>
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64 bg-white border border-gray-200 shadow-lg z-50">
          {accounts.map((account) => (
            <DropdownMenuItem 
              key={account.location}
              onClick={() => handleAccountChange(account.location)}
              className={`${account.location === currentAccount.location ? 'bg-gray-100' : ''} flex items-center gap-2 py-2 hover:bg-gray-50`}
            >
              <Building2 className="h-4 w-4 text-gray-600 shrink-0" />
              <span className="truncate">{account.name}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};


import React from "react";
import { useSidebar } from "../ui/sidebar";
import { Logo } from "./Logo";
import { CollapseButton } from "./CollapseButton";

export const SidebarLogo: React.FC = () => {
  const {
    state,
    toggleSidebar
  } = useSidebar();
  const collapsed = state === "collapsed";

  // When collapsed and logo is clicked, expand the sidebar
  const handleLogoClick = () => {
    if (collapsed) {
      toggleSidebar();
    }
  };

  return (
    <div className="px-3 py-4 border-b border-white/10">
      <div className="flex w-full items-center justify-between gap-2">
        <Logo collapsed={collapsed} onClick={handleLogoClick} />
        {!collapsed && <CollapseButton onClick={toggleSidebar} />}
      </div>
    </div>
  );
};


export const checkPathMatchesActiveItem = (items: any[], path: string): boolean => {
  // Special case for root path and document hub
  if (path === "/" || path === "/document-hub" || path === "/documents") {
    const documentsItem = items.find(item => item.label === "Documents");
    return documentsItem?.isActive === true;
  }
  
  // Check dashboard
  if (path === "/dashboard") {
    const dashboardItem = items.find(item => item.label === "Dashboard");
    return dashboardItem?.isActive === true;
  }
  
  // Check for People and subpaths
  if (path === "/people" || path.startsWith("/people/")) {
    const peopleItem = items.find(item => item.label === "People");
    return peopleItem?.isActive === true;
  }
  
  // Check for Product Hub and subpaths
  if (path === "/product-hub" || path.startsWith("/product-hub/")) {
    const productHubItem = items.find(item => item.label === "Product Hub");
    return productHubItem?.isActive === true;
  }
  
  // Check for Production Hub and subpaths
  if (path === "/production-hub" || path.startsWith("/production-hub/")) {
    const productionHubItem = items.find(item => item.label === "Production Hub");
    return productionHubItem?.isActive === true;
  }
  
  // Check for Supplier Quality and subpaths
  if (
    path === "/supplier-quality" || 
    path.startsWith("/supplier-quality/") ||
    path === "/supplier-audits" ||
    path === "/supplier-audit-planning" ||
    path === "/supplier-matrix" ||
    path === "/supplier-approved-list" ||
    path === "/supplier-feedback" ||
    path === "/supplier-messages" ||
    path === "/supplier-documents" ||
    path === "/supplier-escalation" ||
    path === "/supplier-insights" ||
    path === "/supplier-nc" ||
    path === "/supplier-forms" ||
    path === "/supplier-onboarding" ||
    path === "/form-builder" ||
    path === "/form-preview" ||
    path === "/vendor-timeline" ||
    path === "/audit-templates" ||
    path === "/supplier-sustainability" ||
    path === "/supplier-relationship-management" ||
    path === "/esourcing"
  ) {
    const supplierQualityItem = items.find(item => item.label === "Supplier Quality");
    return supplierQualityItem?.isActive === true;
  }
  
  // Check for Customer Compliance and subpaths
  if (path === "/customer-compliance" || path.startsWith("/customer-compliance/") || path.startsWith("/customer-workspace/")) {
    const customerComplianceItem = items.find(item => item.label === "Customer Compliance");
    return customerComplianceItem?.isActive === true;
  }

  // Check for Customer Portal
  if (path === "/customer-portal") {
    const customerPortalItem = items.find(item => item.label === "Customer Portal");
    return customerPortalItem?.isActive === true;
  }

  // Check for Assets and subpaths
  if (path === "/assets" || path === "/asset-management" || path.startsWith("/assets/")) {
    const assetsItem = items.find(item => item.label === "Assets");
    return assetsItem?.isActive === true;
  }

  // Check for Vendors
  if (path === "/vendors" || path === "/vendor-management") {
    const vendorsItem = items.find(item => item.label === "Vendors");
    return vendorsItem?.isActive === true;
  }

  // Check for Audits and subpaths
  if (path === "/audits" || path.startsWith("/audits/")) {
    const auditsItem = items.find(item => item.label === "Audits");
    return auditsItem?.isActive === true;
  }

  // Check for PPAP and subpaths
  if (path === "/ppap" || path.startsWith("/ppap/")) {
    const ppapItem = items.find(item => item.label === "PPAP");
    return ppapItem?.isActive === true;
  }

  // Check for APQP and subpaths
  if (path === "/apqp" || path.startsWith("/apqp/")) {
    const apqpItem = items.find(item => item.label === "APQP");
    return apqpItem?.isActive === true;
  }
  
  // Check other paths directly
  for (const item of items) {
    const itemPath = `/${item.label.toLowerCase().replace(/\s+/g, '-')}`;
    if (path === itemPath && item.isActive) {
      return true;
    }
  }
  
  return false;
};

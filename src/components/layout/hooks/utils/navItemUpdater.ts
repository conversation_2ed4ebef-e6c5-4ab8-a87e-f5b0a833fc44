
import { NavItem } from "../../types/sidebar-types";

export const updateNavItemsForPath = (navItems: NavItem[], pathname: string): NavItem[] => {
  // Create a fresh copy to avoid mutating state
  const newNavItems = [...navItems];
  
  // Reset all active states first
  for (const item of newNavItems) {
    item.isActive = false;
    if (item.children) {
      for (const child of item.children) {
        child.isActive = false;
      }
    }
  }
  
  // Set the appropriate item as active based on current path
  if (pathname === "/dashboard" || pathname === "/") {
    const dashboardItem = newNavItems.find(item => item.label === "Dashboard");
    if (dashboardItem) dashboardItem.isActive = true;
  } 
  else if (pathname === "/document-hub" || pathname === "/documents") {
    const documentsItem = newNavItems.find(item => item.label === "Documents");
    if (documentsItem) documentsItem.isActive = true;
  }
  else if (pathname === "/standards") {
    const standardsItem = newNavItems.find(item => item.label === "Standards");
    if (standardsItem) standardsItem.isActive = true;
  }
  else if (pathname === "/people" || pathname.startsWith("/people/")) {
    const peopleItem = newNavItems.find(item => item.label === "People");
    if (peopleItem) peopleItem.isActive = true;
  }
  else if (pathname === "/product-hub" || pathname.startsWith("/product-hub/")) {
    const productHubItem = newNavItems.find(item => item.label === "Product Hub");
    if (productHubItem) productHubItem.isActive = true;
  }
  else if (pathname === "/production-hub" || pathname.startsWith("/production-hub/")) {
    const productionHubItem = newNavItems.find(item => item.label === "Production Hub");
    if (productionHubItem) productionHubItem.isActive = true;
  }
  else if (pathname === "/assets" || pathname === "/asset-management" || pathname.startsWith("/assets/")) {
    const assetsItem = newNavItems.find(item => item.label === "Assets");
    if (assetsItem) assetsItem.isActive = true;
  }
  else if (pathname === "/inventory") {
    const inventoryItem = newNavItems.find(item => item.label === "Inventory");
    if (inventoryItem) inventoryItem.isActive = true;
  }
  else if (pathname === "/vendors" || pathname === "/vendor-management") {
    const vendorsItem = newNavItems.find(item => item.label === "Vendors");
    if (vendorsItem) vendorsItem.isActive = true;
  }
  else if (
    pathname === "/supplier-quality" || 
    pathname.startsWith("/supplier-quality/") ||
    pathname === "/supplier-audits" ||
    pathname === "/supplier-audit-planning" ||
    pathname === "/supplier-matrix" ||
    pathname === "/supplier-approved-list" ||
    pathname === "/supplier-feedback" ||
    pathname === "/supplier-messages" ||
    pathname === "/supplier-documents" ||
    pathname === "/supplier-escalation" ||
    pathname === "/supplier-insights" ||
    pathname === "/supplier-nc" ||
    pathname === "/supplier-forms" ||
    pathname === "/supplier-onboarding" ||
    pathname === "/form-builder" ||
    pathname === "/form-preview" ||
    pathname === "/vendor-timeline" ||
    pathname === "/audit-templates" ||
    pathname === "/supplier-sustainability" ||
    pathname === "/supplier-relationship-management" ||
    pathname === "/esourcing"
  ) {
    const supplierQualityItem = newNavItems.find(item => item.label === "Supplier Quality");
    if (supplierQualityItem) supplierQualityItem.isActive = true;
  }
  else if (pathname === "/customer-compliance" || pathname.startsWith("/customer-compliance/") || pathname.startsWith("/customer-workspace/")) {
    const customerComplianceItem = newNavItems.find(item => item.label === "Customer Compliance");
    if (customerComplianceItem) customerComplianceItem.isActive = true;
  }
  else if (pathname === "/customer-portal") {
    const customerPortalItem = newNavItems.find(item => item.label === "Customer Portal");
    if (customerPortalItem) customerPortalItem.isActive = true;
  }
  else if (pathname === "/ppap" || pathname.startsWith("/ppap/")) {
    const ppapItem = newNavItems.find(item => item.label === "PPAP");
    if (ppapItem) ppapItem.isActive = true;
  }
  else if (pathname === "/apqp" || pathname.startsWith("/apqp/")) {
    const apqpItem = newNavItems.find(item => item.label === "APQP");
    if (apqpItem) apqpItem.isActive = true;
  }
  else if (pathname === "/improvement") {
    const improvementItem = newNavItems.find(item => item.label === "Improvement");
    if (improvementItem) improvementItem.isActive = true;
  }
  else if (pathname === "/audits" || pathname.startsWith("/audits/")) {
    const auditsItem = newNavItems.find(item => item.label === "Audits");
    if (auditsItem) auditsItem.isActive = true;
  }
  
  return newNavItems;
};

export const updateNavItemsForClick = (navItems: NavItem[], label: string, parentLabel?: string): NavItem[] => {
  const newNavItems = [...navItems];
  
  // Reset all active states first
  for (const item of newNavItems) {
    item.isActive = false;
    if (item.children) {
      for (const child of item.children) {
        child.isActive = false;
      }
    }
  }
  
  // Set the clicked item as active
  if (label === "Dashboard") {
    const dashboardItem = newNavItems.find(item => item.label === "Dashboard");
    if (dashboardItem) dashboardItem.isActive = true;
  } else {
    const hubItem = newNavItems.find(item => item.label === label);
    if (hubItem) hubItem.isActive = true;
    
    // If there's a parent label, find the parent and set its child as active
    if (parentLabel) {
      const parentItem = newNavItems.find(item => item.label === parentLabel);
      if (parentItem && parentItem.children) {
        const childItem = parentItem.children.find(child => child.label === label);
        if (childItem) childItem.isActive = true;
      }
    }
  }
  
  return newNavItems;
};

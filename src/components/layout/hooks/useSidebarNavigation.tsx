
import { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { NavItem } from "../types/sidebar-types";
import { checkPathMatchesActiveItem } from "./utils/pathMatchingUtils";
import { updateNavItemsForPath, updateNavItemsForClick } from "./utils/navItemUpdater";

export const useSidebarNavigation = (initialNavItems: NavItem[]) => {
  const [navItems, setNavItems] = useState<NavItem[]>(initialNavItems);
  const location = useLocation();
  
  // Update active state when route changes - use memoization to prevent unnecessary re-renders
  useEffect(() => {
    setNavItems(prev => {
      // Check if we need to update anything at all
      const pathMatch = checkPathMatchesActiveItem(prev, location.pathname);
      if (pathMatch) return prev; // No need to update if current path already matches active item
      
      return updateNavItemsForPath(prev, location.pathname);
    });
  }, [location.pathname]);
  
  // Memoize the handler to prevent unnecessary re-renders
  const handleHubItemClick = useCallback((label: string, parentLabel?: string) => {
    setNavItems(prev => updateNavItemsForClick(prev, label, parentLabel));
  }, []);
  
  return { navItems, setNavItems, handleHubItemClick };
};

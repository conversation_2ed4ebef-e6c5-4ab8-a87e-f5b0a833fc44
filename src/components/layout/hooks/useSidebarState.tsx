
import { useState } from "react";

export const useSidebarState = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [hubsExpanded, setHubsExpanded] = useState(true);
  
  const toggleSidebar = () => {
    setCollapsed(prev => !prev);
  };
  
  const toggleHubsSection = () => {
    setHubsExpanded(prev => !prev);
  };
  
  const handleLogoClick = () => {
    // When collapsed and logo is clicked, expand the sidebar
    if (collapsed) {
      setCollapsed(false);
    }
  };
  
  return { 
    collapsed, 
    setCollapsed, 
    toggleSidebar, 
    handleLogoClick,
    hubsExpanded,
    toggleHubsSection
  };
};

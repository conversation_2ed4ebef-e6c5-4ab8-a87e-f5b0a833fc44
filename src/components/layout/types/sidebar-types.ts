
export interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
  isActive: boolean;
  children?: NavItem[];
}

export interface SidebarContextProps {
  collapsed: boolean;
  setCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
  navItems: NavItem[];
  setNavItems: React.Dispatch<React.SetStateAction<NavItem[]>>;
  toggleSidebar: () => void;
  handleLogoClick: () => void;
  handleHubItemClick: (label: string, parentLabel?: string) => void;
  hubsExpanded: boolean;
  toggleHubsSection: () => void;
}

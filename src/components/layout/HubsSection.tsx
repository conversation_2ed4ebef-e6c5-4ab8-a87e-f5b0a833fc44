
import React, { useEffect, useState } from "react";
import { NavItem } from "./types/sidebar-types";
import { HubIcon } from "./HubIcon";
import { HubButton } from "./HubButton";
import { useNavigate, useLocation } from "react-router-dom";
import { ChevronDown, ChevronUp } from "lucide-react";
import { getInitialNavItems } from "./data/navItemsData";

interface HubsSectionProps {
  navItems: NavItem[];
  collapsed: boolean;
  hubsExpanded: boolean;
  onToggleHubs: () => void;
  onItemClick?: (label: string, parentLabel?: string) => void;
}

export const HubsSection: React.FC<HubsSectionProps> = ({ 
  collapsed,
  hubsExpanded,
  onToggleHubs,
  onItemClick 
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [navItems, setNavItems] = useState<NavItem[]>(getInitialNavItems(location.pathname));
  
  useEffect(() => {
    setNavItems(getInitialNavItems(location.pathname));
  }, [location.pathname]);
  
  const handleItemClick = (label: string, parentLabel?: string, path?: string) => {
    if (onItemClick) {
      onItemClick(label, parentLabel);
    }
    
    // Handle navigation based on the clicked item
    switch(label) {
      case "Dashboard":
        navigate("/dashboard");
        break;
      case "Documents":
        navigate("/document-hub");
        break;
      case "Standards":
        navigate("/standards");
        break;
      case "People":
        navigate("/people");
        break;
      case "Product Hub":
        navigate("/product-hub");
        break;
      case "Production Hub":
        navigate("/production-hub");
        break;
      case "Assets":
        navigate("/assets");
        break;
      case "Inventory":
        navigate("/inventory");
        break;
      case "Vendors":
        navigate("/vendors");
        break;
      case "Supplier Quality":
        navigate("/supplier-quality");
        break;
      case "Customer Compliance":
        navigate("/customer-compliance");
        break;
      case "Customer Portal":
        navigate("/customer-portal");
        break;
      case "Training":
        navigate("/training-library");
        break;
      case "PPAP":
        navigate("/ppap");
        break;
      case "APQP":
        navigate("/apqp");
        break;
      case "Improvement":
        navigate("/improvement");
        break;
      case "Audits":
        navigate("/audits");
        break;
      default:
        // For other items that don't have implementations yet
        navigate(`/${label.toLowerCase().replace(/\s+/g, '-')}`);
    }
  };
  
  // Filter out only the dashboard item
  const dashboardItem = navItems.find(item => item.label === "Dashboard");
  // Get all other hub items
  const hubItems = navItems.filter(item => item.label !== "Dashboard");
  
  return (
    <div className="mt-2">
      <nav className={`w-full text-sm font-normal overflow-y-auto ${collapsed ? 'px-1' : ''}`}>
        {/* Render Dashboard */}
        {dashboardItem && (
          <div className="relative mb-3">
            {collapsed ? (
              <HubIcon 
                icon={dashboardItem.icon} 
                label={dashboardItem.label} 
                isActive={dashboardItem.isActive} 
                onClick={() => handleItemClick(dashboardItem.label, undefined, "/dashboard")}
              />
            ) : (
              <HubButton 
                icon={dashboardItem.icon} 
                label={dashboardItem.label} 
                isActive={dashboardItem.isActive}
                onClick={() => handleItemClick(dashboardItem.label, undefined, "/dashboard")}
              />
            )}
          </div>
        )}
        
        {/* Hubs Header with Toggle */}
        {!collapsed && (
          <div className="mb-2 mt-4 flex items-center justify-between px-3">
            <h3 className="text-sidebar-text font-medium text-sm tracking-wide uppercase">Hubs</h3>
            <button 
              onClick={onToggleHubs} 
              className="text-sidebar-text hover:text-white hover:scale-125 transition-all duration-300"
            >
              {hubsExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
          </div>
        )}
        
        {/* Render Hub Items - only when expanded or collapsed sidebar */}
        {(hubsExpanded || collapsed) && (
          <div className="space-y-1">
            {hubItems.map((item) => {
              // Determine the navigation path based on the label
              let path: string;
              switch(item.label) {
                case "Documents": path = "/document-hub"; break;
                case "Standards": path = "/standards"; break;
                case "People": path = "/people"; break;
                case "Product Hub": path = "/product-hub"; break;
                case "Production Hub": path = "/production-hub"; break;
                case "Assets": path = "/assets"; break;
                case "Inventory": path = "/inventory"; break;
                case "Vendors": path = "/vendors"; break;
                case "Supplier Quality": path = "/supplier-quality"; break;
                case "Customer Compliance": path = "/customer-compliance"; break;
                case "Customer Portal": path = "/customer-portal"; break;
                case "Training": path = "/training-library"; break;
                case "PPAP": path = "/ppap"; break;
                case "APQP": path = "/apqp"; break;
                case "Improvement": path = "/improvement"; break;
                case "Audits": path = "/audits"; break;
                default: path = `/${item.label.toLowerCase().replace(/\s+/g, '-')}`; break;
              }
              
              return (
                <div key={item.label} className="relative mb-1">
                  {collapsed ? (
                    <HubIcon 
                      icon={item.icon} 
                      label={item.label} 
                      isActive={item.isActive} 
                      onClick={() => handleItemClick(item.label, undefined, path)}
                    />
                  ) : (
                    <HubButton 
                      icon={item.icon} 
                      label={item.label} 
                      isActive={item.isActive}
                      onClick={() => handleItemClick(item.label, undefined, path)}
                    />
                  )}
                </div>
              );
            })}
          </div>
        )}
      </nav>
    </div>
  );
}

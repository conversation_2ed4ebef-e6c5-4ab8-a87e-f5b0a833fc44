
import React from "react";
import { ChevronLeft } from "lucide-react";

interface CollapseButtonProps {
  onClick: () => void;
}

export const CollapseButton: React.FC<CollapseButtonProps> = ({ onClick }) => {
  return (
    <button 
      className="bg-teal-600/20 flex items-center justify-center w-6 h-6 rounded-full hover:bg-teal-600/30 transition-colors ml-auto"
      onClick={onClick}
    >
      <ChevronLeft size={14} className="text-white transition-transform duration-300" />
    </button>
  );
};


import React from "react";

interface LogoProps {
  collapsed: boolean;
  onClick?: () => void;
  className?: string;
}

export const Logo: React.FC<LogoProps> = ({
  collapsed,
  onClick,
  className = "",
}) => {
  return (
    <div 
      className={`flex items-center cursor-pointer ${collapsed ? 'w-full justify-center' : ''} ${className}`} 
      onClick={onClick}
    >
      <div className={`flex items-center ${collapsed ? 'justify-center' : ''}`}>
        {collapsed ? (
          <img 
            src="/lovable-uploads/5a7f5417-97dc-4566-8a3b-9cd384ff95f5.png" 
            alt="BPR Hub Icon" 
            className="w-8 h-8 object-contain invert" // Using invert to make the logo white
          />
        ) : (
          <img 
            src="/lovable-uploads/55d3f27c-4c35-47eb-9aea-174ad8fbad1f.png" 
            alt="BPR Hub Logo" 
            className="w-full h-9 object-contain" // Reduced from h-10 to h-9 to zoom out a bit
          />
        )}
      </div>
    </div>
  );
};

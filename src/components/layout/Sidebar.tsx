
import React, { memo } from "react";
import { HubsSection } from "./HubsSection";
import { UserProfileDropdown } from "./UserProfileDropdown";
import { SidebarLogo } from "./SidebarLogo";
import { useSidebar } from "../ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

const MemoizedHubsSection = memo(HubsSection);
const MemoizedUserProfileDropdown = memo(UserProfileDropdown);

export const Sidebar: React.FC = () => {
  const { state } = useSidebar();
  const collapsed = state === "collapsed";
  const isMobile = useIsMobile();
  
  return (
    <aside 
      className={`bg-sidebar-bg flex flex-col h-full ${
        collapsed ? 'w-[70px]' : 'w-[230px]'
      } ${isMobile ? 'shadow-lg' : ''} transition-all duration-300 ease-in-out`}
    >
      <SidebarLogo />
      
      <div className={`w-full flex-1 overflow-y-auto ${collapsed ? 'px-2' : 'px-3'}`}>
        <MemoizedHubsSection 
          navItems={[]}
          collapsed={collapsed}
          hubsExpanded={true}
          onToggleHubs={() => {}}
          onItemClick={() => {}}
        />
      </div>
      
      <div className="mt-auto border-t border-white/10 p-3">
        <MemoizedUserProfileDropdown 
          name="Vinodh Peddi" 
          role="Administrator" 
          initials="VP"
          collapsed={collapsed}
        />
      </div>
    </aside>
  );
}


import React from "react";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/components/ui/sidebar";

interface StandardPageLayoutProps {
  title?: string;
  children: React.ReactNode;
  headerContent?: React.ReactNode;
}

export const StandardPageLayout: React.FC<StandardPageLayoutProps> = ({ 
  title, 
  children, 
  headerContent 
}) => {
  const isMobile = useIsMobile();
  const { openMobile } = useSidebar();

  return (
    <div className="flex h-screen w-full bg-gray-50 font-inter overflow-hidden">
      <div className={`${isMobile && !openMobile ? 'hidden' : 'w-auto'} bg-white border-r border-gray-200/60 flex flex-col z-40 shadow-soft transition-all duration-300 ease-smooth`}>
        <Sidebar />
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden w-full animate-fade-in">
        <TopBar>
          {headerContent || (title && <h1 className="text-2xl font-bold tracking-tight">{title}</h1>)}
        </TopBar>
        
        <div className="flex-1 overflow-y-auto animate-slide-in-from-bottom">
          {children}
        </div>
      </div>
    </div>
  );
};


import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useNavigate } from "react-router-dom";
import { Logo } from "./Logo";
import { Button } from "@/components/ui/button";
import { Bell, HelpCircle, MessageSquare, Menu } from "lucide-react";
import { AccountNameTooltip } from "./AccountNameTooltip";
import { useComplianceChat } from "@/components/compliance-chat/hooks/useComplianceChat";
import { useSidebar } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

interface TopBarProps {
  title?: string;
  showLogo?: boolean;
  children?: React.ReactNode;
}

export const TopBar: React.FC<TopBarProps> = ({
  title = "Document Hub",
  showLogo = false,
  children
}) => {
  const navigate = useNavigate();
  const {
    messages,
    handleClearChat,
    handleSendMessage
  } = useComplianceChat();
  const hasUnreadNotifications = true;
  const hasUnreadMessages = messages.length > 0;
  const isMobile = useIsMobile();

  return (
    <header className="bg-white border-b border-slate-200/60 px-6 md:px-8 py-4 w-full shadow-subtle animate-slide-in-from-bottom">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center min-w-0 flex-1 gap-4">
          {isMobile && (
            <div className="transform-gpu hover:scale-105 transition-transform duration-200">
              <SidebarTrigger />
            </div>
          )}
          
          {showLogo && (
            <div className="flex items-center gap-3 mr-8 animate-fade-in">
              <Logo onClick={() => navigate("/")} collapsed={false} className="h-8 w-auto transform-gpu hover:scale-105 transition-transform duration-200" />
            </div>
          )}

          {children ? children : (
            <div className="flex flex-col min-w-0 animate-slide-in-from-left">
              <h1 className="text-2xl font-bold text-slate-900 tracking-tight">{title}</h1>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 ml-auto shrink-0 animate-slide-in-from-right">
          <AccountNameTooltip />
        </div>
      </div>
    </header>
  );
};


import React from "react";
import { ChevronDown } from "lucide-react";

interface HubButtonProps {
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  hasChildren?: boolean;
  isExpanded?: boolean;
  isChild?: boolean;
  onClick?: () => void;
  onExpandClick?: () => void;
}

export const HubButton: React.FC<HubButtonProps> = ({ 
  icon, 
  label, 
  isActive = false,
  hasChildren = false,
  isExpanded = false,
  isChild = false,
  onClick,
  onExpandClick
}) => {
  return (
    <div className={`relative ${isChild ? 'ml-1' : ''}`}>
      <button
        className={`flex items-center w-full gap-3 px-3 py-2.5 rounded-xl transition-all duration-250 ease-smooth transform-gpu hover:scale-[1.02] ${
          isActive 
            ? "bg-sidebar-hover/80 font-medium text-white shadow-soft backdrop-blur-sm" 
            : "bg-transparent text-sidebar-text hover:bg-sidebar-hover hover:text-white"
        } ${isChild ? "text-sm pl-4 py-2" : "text-[15px]"} focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-sidebar-ring`}
        onClick={onClick}
      >
        <span className={`w-5 h-5 shrink-0 flex items-center justify-center ${
          isActive 
            ? 'text-white' 
            : 'text-sidebar-text group-hover:text-white'
          } transition-all duration-250`}>
          {React.cloneElement(icon as React.ReactElement, { 
            strokeWidth: isActive ? 2.5 : 2,
            size: 20,
            className: `${isActive ? "text-white" : "text-sidebar-text"} transition-all duration-250`
          })}
        </span>
        <span className={`flex-1 text-left transition-all duration-250 ${isActive ? 'font-semibold' : 'font-medium'}`}>
          {label}
        </span>
        
        {hasChildren && (
          <span 
            className="ml-auto cursor-pointer text-sidebar-text hover:text-white transition-all duration-250 transform-gpu hover:scale-110"
            onClick={(e) => {
              e.stopPropagation();
              if (onExpandClick) onExpandClick();
            }}
          >
            <ChevronDown 
              size={16} 
              className={`transition-transform duration-300 ease-smooth ${isExpanded ? "rotate-180" : ""}`} 
            />
          </span>
        )}
      </button>
    </div>
  );
};

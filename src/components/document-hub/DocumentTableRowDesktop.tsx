
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Document } from "./DocumentTable";
import { ReviewDateCell } from "./ReviewDateCell";
import { DocumentTitle } from "./table-cells/DocumentTitle";
import { DocumentActions } from "./table-cells/DocumentActions";
import { MobileExpandedDetails } from "./table-cells/MobileExpandedDetails";

interface DocumentTableRowDesktopProps {
  document: Document;
  index: number;
  showExpandColumn: boolean;
}

export const DocumentTableRowDesktop: React.FC<DocumentTableRowDesktopProps> = ({ 
  document: doc, 
  index,
  showExpandColumn
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleExpandClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <>
      <TableRow 
        key={doc.id} 
        className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100 transition-colors`}
      >
        {showExpandColumn && (
          <TableCell className="w-12 p-3 text-center">
            <button onClick={handleExpandClick} className="focus:outline-none">
              {isExpanded ? (
                <ChevronDown size={16} className="text-gray-400" />
              ) : (
                <ChevronRight size={16} className="text-gray-400" />
              )}
            </button>
          </TableCell>
        )}
        
        <TableCell className="py-4 px-4 w-1/4 min-w-[200px]">
          <DocumentTitle document={doc} />
        </TableCell>
        
        <TableCell className="py-4 px-4 w-24">
          <span className="text-gray-700 capitalize">{doc.status}</span>
        </TableCell>
        
        {!showExpandColumn && (
          <>
            <TableCell className="py-4 px-4 w-28">
              <span className="text-gray-700">{doc.department}</span>
            </TableCell>
            
            <TableCell className="py-4 px-4 w-24">
              <span className="text-gray-700">{doc.category}</span>
            </TableCell>
            
            <TableCell className="py-4 px-4 w-28">
              <span className="text-gray-700">{doc.processes || '-'}</span>
            </TableCell>
            
            <TableCell className="py-4 px-4 w-24">
              <span className="text-gray-700">{doc.assignee}</span>
            </TableCell>
            
            <TableCell className="py-4 px-4 w-24">
              <span className="text-gray-700">{doc.publishedDate}</span>
            </TableCell>
            
            <TableCell className="py-4 px-4 w-24">
              <ReviewDateCell reviewDate={doc.reviewDate} />
            </TableCell>
            
            <TableCell className="py-4 px-4 w-20">
              <span className="text-gray-700">{doc.approver}</span>
            </TableCell>
          </>
        )}
        
        <TableCell className="py-4 px-4 w-20 text-right">
          <DocumentActions document={doc} />
        </TableCell>
      </TableRow>
      
      {showExpandColumn && isExpanded && (
        <MobileExpandedDetails document={doc} index={index} />
      )}
    </>
  );
};

import React from "react";

interface Tab {
  label: string;
  count: number;
  isActive?: boolean;
  color?: string;
}

const tabs: Tab[] = [
  { label: "All documents", count: 99, isActive: true },
  { label: "Published", count: 17 },
  { label: "Assigned to me", count: 6 },
  { label: "Needs approval", count: 4 },
  { label: "Review in 30 days", count: 1, color: "#F19413" },
  { label: "Past due", count: 7, color: "#E05252" },
];

export const TabSystem: React.FC = () => {
  return (
    <div className="flex items-center gap-1 text-base text-[#575757] font-medium leading-[1.6] flex-wrap mt-5">
      {tabs.map((tab, index) => (
        <div
          key={index}
          className={`self-stretch min-h-9 my-auto px-3 ${
            tab.isActive
              ? "text-[#016366] flex flex-col items-stretch justify-center w-[170px]"
              : ""
          }`}
          style={{ color: tab.color }}
        >
          <div className="gap-2.5">{`${tab.label} (${tab.count})`}</div>
          {tab.isActive && (
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/ea92c4ac4f439847ac227cea7f7ae4f6bec339e2?placeholderIfAbsent=true"
              alt="Active indicator"
              className="aspect-[71.43] object-contain w-[148px] stroke-[2px] stroke-[#016366] mt-2.5"
            />
          )}
        </div>
      ))}
    </div>
  );
};

import React, { useState } from "react";
import { DocumentTable } from "./DocumentTable";
import { SearchAndFilter } from "./SearchAndFilter";
import { useDocumentFilter } from "@/hooks/useDocumentFilter";
import { useDocumentPagination } from "@/hooks/useDocumentPagination";
import { StandaloneDocumentTableProps, Document } from "./table/DocumentTableTypes";
import { mockDocuments } from "@/data/mockDocuments";

export const StandaloneDocumentTable: React.FC<StandaloneDocumentTableProps> = ({
  title = "Document Table",
  subtitle = "Manage and view documents",
  showSearch = true,
  showFilters = true,
  initialDocuments = mockDocuments
}) => {
  const [appliedFilters, setAppliedFilters] = useState({
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: []
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterSidePanelOpen, setIsFilterSidePanelOpen] = useState(false);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleApplyFilters = (filters: any) => {
    setAppliedFilters(filters);
  };

  const toggleFilterSidePanel = () => {
    setIsFilterSidePanelOpen(!isFilterSidePanelOpen);
  };

  const { filteredDocuments } = useDocumentFilter({
    documents: initialDocuments,
    appliedFilters,
    initialSearchTerm: searchTerm
  });

  const {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData
  } = useDocumentPagination({
    totalCount: filteredDocuments.length,
    initialPageSize: 10,
    initialPage: 1
  });

  // Get the current slice of documents for the current page
  const paginatedDocuments = getPaginatedData(filteredDocuments);

  return (
    <div className="h-full w-full space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-gray-500 text-sm">{subtitle}</p>
        </div>
      </div>

      {/* Search and Filter */}
      {(showSearch || showFilters) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <SearchAndFilter 
            isFilterSidePanelOpen={isFilterSidePanelOpen}
            toggleFilterSidePanel={toggleFilterSidePanel}
            appliedFilters={appliedFilters}
            onApplyFilters={handleApplyFilters}
            onSearch={handleSearch}
            searchTerm={searchTerm}
            showSearch={showSearch}
            showFilters={showFilters}
          />
        </div>
      )}

      {/* Document Table */}
      <div className="flex-1 min-h-0">
        <div className="bg-white rounded-md shadow-sm border border-gray-200 h-full flex flex-col w-full overflow-hidden">
          <DocumentTable 
            documents={paginatedDocuments}
            currentPage={currentPage}
            totalCount={filteredDocuments.length}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
      </div>
    </div>
  );
};

// Export types and sample data for external use
export type { Document, StandaloneDocumentTableProps } from "./table/DocumentTableTypes";
export { mockDocuments } from "@/data/mockDocuments";

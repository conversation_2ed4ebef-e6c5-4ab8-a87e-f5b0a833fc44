
import React from "react";
import { TableHead } from "@/components/ui/table";
import { type SortField, type SortDirection } from "../DocumentTable";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";

interface SortableColumnHeaderProps {
  field: SortField;
  label: string;
  sortField: SortField;
  sortDirection: SortDirection;
  onSort: (field: SortField) => void;
}

export const SortableColumnHeader: React.FC<SortableColumnHeaderProps> = ({
  field,
  label,
  sortField,
  sortDirection,
  onSort,
}) => {
  const renderSortIcon = () => {
    if (sortField === field) {
      return sortDirection === 'asc' 
        ? <ArrowUp className="ml-1 h-4 w-4 text-teal-600" />
        : <ArrowDown className="ml-1 h-4 w-4 text-teal-600" />;
    }
    return <ArrowUpDown className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-100 text-gray-400" />;
  };

  return (
    <TableHead 
      className="text-xs font-semibold text-gray-600 uppercase tracking-wider hover:bg-gray-100 cursor-pointer py-3 px-4 w-full h-full flex items-center group bg-gray-50"
      onClick={() => onSort(field)}
    >
      <div className="flex items-center cursor-pointer">
        {label}
        {renderSortIcon()}
      </div>
    </TableHead>
  );
};


import React from "react";

export const capitalize = (str: string) => {
  return str.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
};

// Default column widths for the table
export const DEFAULT_COLUMN_WIDTHS: Record<string, number> = {
  title: 250,
  department: 150,
  category: 150,
  processes: 150,
  status: 120,
  assignee: 120,
  publishedDate: 120,
  reviewDate: 120,
  approver: 120,
};

// Common panel configuration for consistent column sizing
export const TABLE_LAYOUT = {
  titlePanel: { defaultSize: 30, minSize: 15 },
  departmentPanel: { defaultSize: 10, minSize: 7 },
  categoryPanel: { defaultSize: 10, minSize: 7 },
  processesPanel: { defaultSize: 10, minSize: 7 },
  statusPanel: { defaultSize: 8, minSize: 5 },
  assigneePanel: { defaultSize: 8, minSize: 5 },
  publishDatePanel: { defaultSize: 8, minSize: 5 },
  reviewDatePanel: { defaultSize: 8, minSize: 5 },
  approverPanel: { defaultSize: 8, minSize: 5 },
};

// Consistent ID for all table panel groups to sync resize state
export const TABLE_PANEL_GROUP_ID = "document-table-columns";

// Get saved column widths
export const getSavedColumnWidths = (): Record<string, number> => {
  try {
    const saved = localStorage.getItem('document-table-column-widths');
    return saved ? JSON.parse(saved) : DEFAULT_COLUMN_WIDTHS;
  } catch (e) {
    console.error("Error parsing saved column widths:", e);
    return DEFAULT_COLUMN_WIDTHS;
  }
};

// Save column widths to localStorage
export const saveColumnWidths = (widths: Record<string, number>): void => {
  try {
    localStorage.setItem('document-table-column-widths', JSON.stringify(widths));
  } catch (e) {
    console.error("Error saving column widths:", e);
  }
};

// Get saved layout sizes
export const getSavedLayout = () => {
  try {
    const savedSizes = localStorage.getItem(`${TABLE_PANEL_GROUP_ID}-sizes`);
    return savedSizes ? JSON.parse(savedSizes) : null;
  } catch (e) {
    console.error("Error parsing saved layout:", e);
    return null;
  }
};

// Apply saved layout to a panel group
export const applyPanelLayout = () => {
  const savedLayout = getSavedLayout();
  if (savedLayout) {
    // First apply to the header
    const headerEvent = new CustomEvent('resizable-panel-group:layout', {
      detail: {
        id: TABLE_PANEL_GROUP_ID,
        sizes: savedLayout
      }
    });
    window.dispatchEvent(headerEvent);
    
    // Then notify all data rows
    window.dispatchEvent(new CustomEvent('panel-resize', {
      detail: {
        id: TABLE_PANEL_GROUP_ID,
        sizes: savedLayout
      }
    }));
    
    return true;
  }
  return false;
};

// Force resync of all panel layouts - can be called when there are sync issues
export const resyncPanelLayouts = () => {
  const savedLayout = getSavedLayout();
  
  if (!savedLayout) {
    // If no saved layout, create an initial one based on defaults
    const defaultSizes = [
      TABLE_LAYOUT.titlePanel.defaultSize,
      TABLE_LAYOUT.departmentPanel.defaultSize,
      TABLE_LAYOUT.categoryPanel.defaultSize,
      TABLE_LAYOUT.processesPanel.defaultSize,
      TABLE_LAYOUT.statusPanel.defaultSize,
      TABLE_LAYOUT.assigneePanel.defaultSize,
      TABLE_LAYOUT.publishDatePanel.defaultSize,
      TABLE_LAYOUT.reviewDatePanel.defaultSize,
      TABLE_LAYOUT.approverPanel.defaultSize
    ];
    
    localStorage.setItem(`${TABLE_PANEL_GROUP_ID}-sizes`, JSON.stringify(defaultSizes));
    
    // Apply this default layout
    applyPanelLayout();
    return true;
  }
  
  // Otherwise just apply the existing layout
  return applyPanelLayout();
};

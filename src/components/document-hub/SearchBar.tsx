
import React from "react";
import { Search, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

interface SearchBarProps {
  placeholder?: string;
  onChange?: (value: string) => void;
  value?: string;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({ 
  placeholder = "Search documents...",
  onChange,
  value = "",
  className
}) => {
  const isMobile = useIsMobile();
  
  const handleClear = () => {
    onChange && onChange("");
  };
  
  return (
    <div className={cn(
      "flex items-center border border-[color:var(--Neutrals-Greys-Grey-100,#E1E1E1)] bg-white gap-2 overflow-hidden rounded-lg px-4 py-2.5",
      isMobile ? "py-3" : "",
      className
    )}>
      <Search className="w-5 h-5 text-gray-400 flex-shrink-0" />
      <input
        type="text"
        value={value}
        onChange={(e) => onChange && onChange(e.target.value)}
        placeholder={placeholder}
        className={cn(
          "flex-1 min-w-0 bg-transparent outline-none text-gray-700 placeholder-gray-400",
          isMobile ? "text-base py-1" : "text-sm"
        )}
      />
      {value.length > 0 && (
        <button 
          onClick={handleClear}
          className="text-gray-400 hover:text-gray-600 flex-shrink-0"
          aria-label="Clear search"
        >
          <X className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

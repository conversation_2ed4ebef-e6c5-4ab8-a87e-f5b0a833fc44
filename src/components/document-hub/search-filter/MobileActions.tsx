
import React from "react";
import { Filter, Cog, Plus, RotateCcw, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useNavigate } from "react-router-dom";
import { FilterState } from "@/types/filter";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface MobileActionsProps {
  toggleFilterSidePanel: () => void;
  handleCreateClick: () => void;
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: any[];
  countActiveFilters: () => number;
  resetAllFilters?: () => void;
}

export const MobileActions: React.FC<MobileActionsProps> = ({
  toggleFilterSidePanel,
  handleCreateClick,
  appliedFilters,
  handleStatusChange,
  statusOptions,
  countActiveFilters,
  resetAllFilters
}) => {
  const navigate = useNavigate();
  
  const navigateToAdminSettings = () => {
    navigate("/document-admin");
  };

  const handleResetFilters = () => {
    if (resetAllFilters) {
      resetAllFilters();
    }
  };
  
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              className="inline-flex items-center justify-center shrink-0 w-9 h-9 p-0 bg-teal-600"
              onClick={handleCreateClick}
            >
              <Plus size={18} />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Create Document</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="outline"
              size="icon" 
              className="h-9 w-9 shrink-0 border border-gray-200 bg-white"
              onClick={toggleFilterSidePanel}
            >
              <Filter size={18} className="text-gray-700" />
              {countActiveFilters() > 0 && (
                <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                  {countActiveFilters()}
                </span>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Filters</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-9 w-9 shrink-0 border border-gray-200 bg-white"
                >
                  <MoreVertical size={18} className="text-gray-600" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="z-50 bg-white">
                <DropdownMenuItem onClick={handleResetFilters} className="flex items-center gap-2">
                  <RotateCcw size={16} className="text-gray-600" />
                  Reset Filters
                </DropdownMenuItem>
                <DropdownMenuItem onClick={navigateToAdminSettings} className="flex items-center gap-2">
                  <Cog size={16} className="text-gray-600" />
                  Document Administration
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </TooltipTrigger>
          <TooltipContent>
            <p>More Options</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

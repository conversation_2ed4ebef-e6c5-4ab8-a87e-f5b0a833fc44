
import React from "react";
import { Filter, MoreVertical, Plus, RotateCcw, Cog } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useNavigate } from "react-router-dom";
import { FilterState } from "@/types/filter";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface DesktopActionsProps {
  toggleFilterSidePanel: () => void;
  handleCreateClick: () => void;
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: any[];
  countActiveFilters: () => number;
  resetAllFilters?: () => void;
}

export const DesktopActions: React.FC<DesktopActionsProps> = ({
  toggleFilterSidePanel,
  handleCreateClick,
  appliedFilters,
  handleStatusChange,
  statusOptions,
  countActiveFilters,
  resetAllFilters
}) => {
  const navigate = useNavigate();
  
  const navigateToAdminSettings = () => {
    navigate("/document-admin");
  };
  
  const handleResetFilters = () => {
    if (resetAllFilters) {
      resetAllFilters();
    }
  };
  
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" onClick={toggleFilterSidePanel} className="inline-flex items-center shrink-0 text-base h-10 border border-gray-300 bg-white px-4 py-2 gap-2">
              <Filter size={16} className="text-gray-700" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Filter Documents</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" onClick={handleResetFilters} className="h-10 w-10 border border-gray-300 bg-white">
              <RotateCcw size={16} className="text-gray-700" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Reset Filters</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button className="inline-flex items-center shrink-0 px-4 py-2 text-base h-10 bg-teal-600 hover:bg-teal-700 gap-2" onClick={handleCreateClick}>
              <Plus size={16} />
              Create
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Create Document</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="outline" onClick={navigateToAdminSettings} className="h-10 w-10 border border-gray-300 bg-white">
              <Cog size={16} className="text-gray-700" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Document Administration</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-10 w-10">
                  <MoreVertical size={16} className="text-gray-600" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {/* Future menu items can be added here */}
              </DropdownMenuContent>
            </DropdownMenu>
          </TooltipTrigger>
          <TooltipContent>
            <p>More Options</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

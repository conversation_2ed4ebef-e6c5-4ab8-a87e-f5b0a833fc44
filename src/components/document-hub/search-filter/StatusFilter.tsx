
import React from "react";
import { FilterDropdown } from "@/components/filters/FilterDropdown";
import { FilterState } from "@/types/filter";
import { FilterOption } from "@/types/filter";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StatusFilterProps {
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: FilterOption[];
}

export const StatusFilter: React.FC<StatusFilterProps> = ({
  appliedFilters,
  handleStatusChange,
  statusOptions
}) => {
  const isMobile = useIsMobile();
  
  const handleChange = (value: string) => {
    if (value === "all") {
      handleStatusChange([]);
    } else {
      handleStatusChange([value]);
    }
  };

  return (
    <div className="min-w-[150px]">
      <Select 
        value={appliedFilters.status.length > 0 ? appliedFilters.status[0] : "all"} 
        onValueChange={handleChange}
      >
        <SelectTrigger className={`border-gray-300 ${isMobile ? "h-8 text-xs" : "h-10"}`}>
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Statuses</SelectItem>
            {statusOptions.map((option) => (
              <SelectItem key={option.id} value={option.id}>
                {option.label} ({option.count})
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};


import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Upload, FileUp, X } from "lucide-react";

interface DocumentUploadProps {
  value: File | null;
  onChange: (file: File | null) => void;
  accept?: string;
  maxSize?: number; // in MB
}

export const DocumentUpload: React.FC<DocumentUploadProps> = ({ 
  value, 
  onChange, 
  accept = ".pdf,.doc,.docx,.xls,.xlsx,.csv,.svg",
  maxSize = 10 
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const validateFile = (file: File): string | null => {
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      return `File size must be less than ${maxSize}MB`;
    }
    
    // Check file type if accept is specified
    if (accept && accept !== "*") {
      const acceptedTypes = accept.split(',').map(type => type.trim().toLowerCase());
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (!acceptedTypes.includes(fileExtension)) {
        return `File type not supported. Accepted types: ${accept}`;
      }
    }
    
    return null;
  };
  
  const handleFileChange = (file: File) => {
    setError("");
    
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }
    
    onChange(file);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileChange(file);
    }
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileChange(files[0]);
    }
  };
  
  const clearFile = () => {
    setError("");
    onChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };
  
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  return (
    <div className="flex flex-col gap-2 w-full">
      {!value ? (
        <div
          className={`border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center gap-2 cursor-pointer transition-colors ${
            isDragging 
              ? 'border-primary bg-primary/5' 
              : error 
                ? 'border-red-300 bg-red-50' 
                : 'border-gray-300 hover:border-primary'
          }`}
          onClick={openFileDialog}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileUp className={`h-8 w-8 ${error ? 'text-red-400' : 'text-gray-400'}`} />
          <div className="text-center">
            <p className={`text-sm ${error ? 'text-red-600' : 'text-gray-600'}`}>
              {isDragging ? 'Drop file here' : 'Click to upload or drag and drop'}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {accept.replace(/\./g, '').toUpperCase()} (max {maxSize}MB)
            </p>
          </div>
          <input 
            ref={fileInputRef}
            type="file" 
            className="hidden"
            onChange={handleInputChange}
            accept={accept}
          />
        </div>
      ) : (
        <div className="flex items-center justify-between bg-gray-100 p-3 rounded border">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <FileUp className="h-4 w-4 text-green-600 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium truncate">{value.name}</p>
              <p className="text-xs text-gray-500">{formatFileSize(value.size)}</p>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={clearFile}
            className="flex-shrink-0 h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};


import React from "react";
import { MoreVertical, Edit, Download, Eye, MessageSquare } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Document } from "../DocumentTable";

interface DocumentActionsProps {
  document: Document;
}

export const DocumentActions: React.FC<DocumentActionsProps> = ({ document }) => {
  const navigate = useNavigate();

  const handleAction = (action: string) => {
    console.log(`Action clicked: ${action} for document:`, document.id);
    
    if (action === "View") {
      console.log("Navigating to document details:", document.id);
      navigate(`/document/${document.id}`);
    } else if (action === "Comments") {
      console.log("Navigating to document comments:", document.id);
      navigate(`/document/${document.id}?tab=comments`);
    } else {
      toast.success(`${action} document: ${document.title}`);
      console.log(`${action} document:`, document);
    }
  };

  return (
    <div className="w-16 flex items-center justify-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="focus:outline-none flex items-center justify-center p-1 hover:bg-gray-100 rounded">
            <MoreVertical size={18} className="text-gray-500 cursor-pointer" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="z-50 bg-white border shadow-lg">
          <DropdownMenuItem onClick={() => handleAction("View")} className="cursor-pointer">
            <Eye className="mr-2 h-4 w-4" /> View Details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Comments")} className="cursor-pointer">
            <MessageSquare className="mr-2 h-4 w-4" /> View Comments
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Edit")} className="cursor-pointer">
            <Edit className="mr-2 h-4 w-4" /> Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Download")} className="cursor-pointer">
            <Download className="mr-2 h-4 w-4" /> Download
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

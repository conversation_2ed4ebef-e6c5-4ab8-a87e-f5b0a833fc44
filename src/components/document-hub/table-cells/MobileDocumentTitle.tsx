
import React from "react";
import { useNavigate } from "react-router-dom";
import { Document } from "../DocumentTable";

interface MobileDocumentTitleProps {
  document: Document;
}

export const MobileDocumentTitle: React.FC<MobileDocumentTitleProps> = ({ document }) => {
  const navigate = useNavigate();

  const navigateToDocumentDetails = () => {
    console.log("Mobile - Navigating to document details:", document.id);
    
    // Add a small delay to allow console logs to show in order
    setTimeout(() => {
      navigate(`/document/${document.id}`);
    }, 50);
  };

  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-2">
        <span 
          className="font-bold text-blue-600 cursor-pointer hover:text-blue-700"
          onClick={navigateToDocumentDetails}
        >
          {document.title}
          {document.version && (
            <span className="ml-2 text-xs bg-gray-100 text-gray-700 rounded-full px-3 py-0.5">
              v{document.version}
            </span>
          )}
        </span>
      </div>
      <div className="flex items-center mt-1">
        <span className="text-xs text-gray-500 mr-2">ID: {document.id}</span>
      </div>
    </div>
  );
};

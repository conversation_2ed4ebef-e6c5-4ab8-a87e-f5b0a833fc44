
import React from "react";
import { TableCell } from "@/components/ui/table";
import { capitalize } from "../utils/DocumentUtils";

interface TextCellProps {
  value: string | undefined;
  className?: string;
}

export const TextCell: React.FC<TextCellProps> = ({ 
  value,
  className = "text-gray-700"
}) => {
  return (
    <TableCell className={`${className} px-4 py-4 w-full h-full`}>
      <div className="max-w-full overflow-hidden break-words">
        {value ? capitalize(value) : '-'}
      </div>
    </TableCell>
  );
};

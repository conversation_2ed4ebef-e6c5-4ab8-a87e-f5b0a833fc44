
import React from "react";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { Document } from "../DocumentTable";

interface DocumentTitleProps {
  document: Document;
}

export const DocumentTitle: React.FC<DocumentTitleProps> = ({ document }) => {
  const navigate = useNavigate();

  const navigateToDocumentDetails = () => {
    console.log("Desktop - Navigating to document details:", document.id);
    console.log("Document data:", document);
    
    // Add a small delay to allow console logs to show in order
    setTimeout(() => {
      navigate(`/document/${document.id}`);
    }, 50);
  };

  return (
    <div className="flex flex-col">
      <div className="flex items-center">
        <span 
          className="text-blue-600 cursor-pointer hover:text-blue-700 font-bold"
          onClick={navigateToDocumentDetails}
        >
          {document.title}
          {document.version && (
            <Badge 
              variant="outline" 
              className="ml-2 bg-gray-100 text-gray-700 rounded-full px-3 text-xs"
            >
              v{document.version}
            </Badge>
          )}
        </span>
      </div>
      <div className="flex items-center mt-1">
        <span className="text-xs text-gray-500 mr-2">ID: {document.id}</span>
      </div>
    </div>
  );
};

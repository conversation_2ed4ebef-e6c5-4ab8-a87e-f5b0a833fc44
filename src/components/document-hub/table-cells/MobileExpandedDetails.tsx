
import React from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Document } from "../DocumentTable";
import { capitalize } from "../utils/DocumentUtils";
import { differenceInDays, parse, isAfter } from "date-fns";

interface MobileExpandedDetailsProps {
  document: Document;
  index: number;
}

export const MobileExpandedDetails: React.FC<MobileExpandedDetailsProps> = ({ document, index }) => {
  // Helper function to determine review date background color
  const getReviewDateBackgroundColor = (reviewDate: string | undefined) => {
    if (!reviewDate) return "";
    
    const reviewDateObj = parse(reviewDate, "dd MMM yyyy", new Date());
    const today = new Date();
    
    if (isAfter(today, reviewDateObj)) {
      return "bg-red-50";
    }
    
    const daysUntilReview = differenceInDays(reviewDateObj, today);
    if (daysUntilReview <= 14) {
      return "bg-orange-50";
    }
    
    return "";
  };

  return (
    <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
      <TableCell colSpan={3} className="px-4 py-2">
        <div className="pl-6 space-y-2">
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Department:</span>
            <span className="text-gray-700">{capitalize(document.department)}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Category:</span>
            <span className="text-gray-700">{capitalize(document.category)}</span>
          </div>
          {document.processes && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Processes:</span>
              <span className="text-gray-700">{capitalize(document.processes)}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Assignee:</span>
            <span className="text-gray-700">{capitalize(document.assignee)}</span>
          </div>
          {document.publishedDate && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Dt Published:</span>
              <span className="text-gray-700">{document.publishedDate}</span>
            </div>
          )}
          {document.reviewDate && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Review Date:</span>
              <span className={`text-gray-700 ${getReviewDateBackgroundColor(document.reviewDate)}`}>
                {document.reviewDate}
              </span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="font-medium text-gray-600">Approver:</span>
            <span className="text-gray-700">{capitalize(document.approver || '-')}</span>
          </div>
          {document.version && (
            <div className="flex justify-between">
              <span className="font-medium text-gray-600">Version:</span>
              <span className="text-gray-700">v{document.version}</span>
            </div>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
};

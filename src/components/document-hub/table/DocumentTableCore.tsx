
import React, { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { TableBody } from "@/components/ui/table";
import { ResizableTable } from "@/components/ui/resizable-table";
import { VendorTablePagination } from "../../vendors/VendorTablePagination";
import { DocumentTableHeader } from "../DocumentTableHeader";
import { DocumentTableRow } from "../DocumentTableRow";
import { DocumentTableProps, SortField, SortDirection } from "./DocumentTableTypes";

export const DocumentTableCore: React.FC<DocumentTableProps> = ({ 
  documents, 
  currentPage, 
  totalCount, 
  pageSize, 
  onPageChange,
  onPageSizeChange
}) => {
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [showExpandColumn, setShowExpandColumn] = useState<boolean>(false);
  const isMobile = useIsMobile();

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / pageSize);

  // Check if we should show the expand column based on screen width
  useEffect(() => {
    const checkWidth = () => {
      // Show expand column for tablets and smaller screens (less than 1024px)
      // This will prevent horizontal scrolling and use expand rows instead
      setShowExpandColumn(window.innerWidth < 1024);
    };
    
    checkWidth(); // Initial check
    window.addEventListener('resize', checkWidth);
    
    return () => {
      window.removeEventListener('resize', checkWidth);
    };
  }, []);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedDocuments = [...documents].sort((a, b) => {
    if (!sortField || !sortDirection) return 0;
    
    const fieldA = a[sortField] ? a[sortField].toString().toLowerCase() : '';
    const fieldB = b[sortField] ? b[sortField].toString().toLowerCase() : '';
    
    if (sortDirection === 'asc') {
      return fieldA > fieldB ? 1 : -1;
    } else {
      return fieldA < fieldB ? 1 : -1;
    }
  });

  return (
    <div className="flex flex-col h-full w-full">
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="w-full">
          <div className="min-w-full overflow-hidden">
            <ResizableTable className="w-full table-fixed">
              <DocumentTableHeader 
                sortField={sortField}
                sortDirection={sortDirection}
                onSort={handleSort}
                isMobile={isMobile}
                showExpandColumn={showExpandColumn}
              />
              <TableBody>
                {sortedDocuments.length > 0 ? (
                  sortedDocuments.map((document, index) => (
                    <DocumentTableRow
                      key={document.id}
                      document={document}
                      index={index}
                      isMobile={isMobile}
                      showExpandColumn={showExpandColumn}
                    />
                  ))
                ) : (
                  <tr>
                    <td colSpan={isMobile ? 4 : 11} className="text-center py-8 text-gray-500">
                      No documents found
                    </td>
                  </tr>
                )}
              </TableBody>
            </ResizableTable>
          </div>
        </div>
      </div>
      
      <VendorTablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange || (() => {})}
        total={totalCount}
        className="mt-4 bg-white rounded-lg border border-gray-200"
      />
    </div>
  );
};

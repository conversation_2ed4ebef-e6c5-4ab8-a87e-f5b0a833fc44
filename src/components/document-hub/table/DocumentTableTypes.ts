export interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId?: string | null;
}

export interface DocumentTableProps {
  documents: Document[];
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  onSearch?: (term: string) => void;
}

export interface StandaloneDocumentTableProps {
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  initialDocuments?: Document[];
}

export type SortField = keyof Document | null;
export type SortDirection = "asc" | "desc" | null;

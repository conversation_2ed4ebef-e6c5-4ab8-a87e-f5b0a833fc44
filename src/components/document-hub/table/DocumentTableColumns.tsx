
import React from "react";
import { TableColumn } from "../../shared/table/DataTable";
import { DocumentTitle } from "../table-cells/DocumentTitle";
import { DocumentStatusBadge } from "../DocumentStatusBadge";
import { ReviewDateCell } from "../ReviewDateCell";
import { Document } from "./DocumentTableTypes";

export const createDocumentTableColumns = (): TableColumn[] => [
  {
    field: "title",
    header: "Title",
    sortable: true,
    width: "w-[250px]",
    cellRenderer: (doc: Document) => (
      <DocumentTitle document={doc} />
    )
  },
  {
    field: "department",
    header: "Department",
    sortable: true
  },
  {
    field: "category",
    header: "Category",
    sortable: true
  },
  {
    field: "processes",
    header: "Processes",
    sortable: true,
    cellRenderer: (doc: Document) => (
      <div className="max-w-[120px]">
        <span className="text-sm text-gray-700 break-words whitespace-normal leading-tight">
          {doc.processes || '-'}
        </span>
      </div>
    )
  },
  {
    field: "status",
    header: "Status",
    sortable: true,
    cellRenderer: (doc: Document) => (
      <DocumentStatusBadge status={doc.status} />
    )
  },
  {
    field: "assignee",
    header: "Assignee",
    sortable: true
  },
  {
    field: "publishedDate",
    header: "Publish Dt",
    sortable: true
  },
  {
    field: "reviewDate",
    header: "Review Dt",
    sortable: true,
    cellRenderer: (doc: Document) => (
      <ReviewDateCell reviewDate={doc.reviewDate} />
    )
  },
  {
    field: "approver",
    header: "Approver",
    sortable: true
  }
];

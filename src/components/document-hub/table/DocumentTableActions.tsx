
import React from "react";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreVertical, Edit, Download, Eye, MessageSquare } from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Document } from "./DocumentTableTypes";

interface DocumentTableActionsProps {
  document: Document;
}

export const DocumentTableActions: React.FC<DocumentTableActionsProps> = ({ document }) => {
  const navigate = useNavigate();

  const handleAction = (action: string) => {
    console.log(`Table action clicked: ${action} for document:`, document.id);
    
    if (action === "View") {
      console.log("Navigating to document details:", document.id);
      navigate(`/document/${document.id}`);
    } else if (action === "Comments") {
      console.log("Navigating to document comments:", document.id);
      navigate(`/document/${document.id}?tab=comments`);
    } else {
      toast.success(`${action} document: ${document.title}`);
    }
  };

  return (
    <div className="flex items-center justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-100">
            <MoreVertical className="h-4 w-4 text-gray-600" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="z-50 bg-white border shadow-lg">
          <DropdownMenuItem onClick={() => handleAction("View")} className="cursor-pointer">
            <Eye className="mr-2 h-4 w-4 text-blue-600" /> View Details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Comments")} className="cursor-pointer">
            <MessageSquare className="mr-2 h-4 w-4 text-blue-600" /> View Comments
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Edit")} className="cursor-pointer">
            <Edit className="mr-2 h-4 w-4 text-blue-600" /> Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleAction("Download")} className="cursor-pointer">
            <Download className="mr-2 h-4 w-4 text-blue-600" /> Download
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

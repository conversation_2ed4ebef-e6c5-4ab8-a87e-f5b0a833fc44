
import React, { useState } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import { Document } from "./DocumentTable";
import { capitalize } from "./utils/DocumentUtils";
import { MobileDocumentTitle } from "./table-cells/MobileDocumentTitle";
import { MobileExpandButton } from "./table-cells/MobileExpandButton";
import { MobileExpandedDetails } from "./table-cells/MobileExpandedDetails";
import { Edit, Download, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DocumentTableRowMobileProps {
  document: Document;
  index: number;
}

export const DocumentTableRowMobile: React.FC<DocumentTableRowMobileProps> = ({ document, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();
  
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleAction = (action: string) => {
    if (action === "View") {
      navigate(`/document/${document.id}`);
    } else {
      toast.success(`${action} document: ${document.title}`);
      console.log(`${action} document:`, document);
    }
  };

  return (
    <>
      <TableRow 
        key={document.id} 
        className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} hover:bg-gray-100 transition-colors`}
      >
        <TableCell className="pl-4 pr-2 py-4 w-10">
          <MobileExpandButton isExpanded={isExpanded} onClick={toggleExpand} />
        </TableCell>
        
        <TableCell className="py-4">
          <MobileDocumentTitle document={document} />
        </TableCell>
        
        <TableCell className="py-4 pr-4">
          <span className="text-gray-700">
            {capitalize(document.status)}
          </span>
        </TableCell>
      </TableRow>
      
      {isExpanded && (
        <>
          {/* Action buttons row - moved to the top when expanded */}
          <TableRow className={`${index % 2 === 0 ? "bg-white" : "bg-gray-50"} border-t-0`}>
            <TableCell colSpan={3} className="px-2 py-1">
              <div className="flex items-center justify-end gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("View")}
                      >
                        <Eye className="h-4 w-4 text-blue-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>View</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("Edit")}
                      >
                        <Edit className="h-4 w-4 text-amber-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        className="h-8 w-8 bg-white border-gray-200"
                        onClick={() => handleAction("Download")}
                      >
                        <Download className="h-4 w-4 text-green-600" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Download</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </TableCell>
          </TableRow>
          
          <MobileExpandedDetails document={document} index={index} />
        </>
      )}
    </>
  );
};

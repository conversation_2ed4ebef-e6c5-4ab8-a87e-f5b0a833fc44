
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, Download, Edit, Share, Eye } from "lucide-react";

interface DocumentViewerProps {
  documentId: string;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({ documentId }) => {
  const mockDocument = {
    id: documentId,
    title: "Quality Management System Procedure",
    version: "2.1",
    status: "Approved",
    category: "Quality",
    lastModified: "2024-01-15",
    author: "<PERSON>",
    approver: "<PERSON>",
    description: "Comprehensive procedure for quality management system implementation and maintenance."
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">{mockDocument.title}</CardTitle>
              <div className="flex items-center gap-2 mt-2">
                <Badge>Version {mockDocument.version}</Badge>
                <Badge variant={mockDocument.status === 'Approved' ? 'default' : 'secondary'}>
                  {mockDocument.status}
                </Badge>
                <Badge variant="outline">{mockDocument.category}</Badge>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p className="text-sm text-gray-600">Author: {mockDocument.author}</p>
              <p className="text-sm text-gray-600">Approver: {mockDocument.approver}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Last Modified: {mockDocument.lastModified}</p>
              <p className="text-sm text-gray-600">Document ID: {mockDocument.id}</p>
            </div>
          </div>
          <p className="text-gray-700">{mockDocument.description}</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Document Content</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-6 bg-gray-50 min-h-[400px] flex items-center justify-center">
            <div className="text-center">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Document content would be displayed here</p>
              <p className="text-sm text-gray-500">PDF viewer, text editor, or other content renderer</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

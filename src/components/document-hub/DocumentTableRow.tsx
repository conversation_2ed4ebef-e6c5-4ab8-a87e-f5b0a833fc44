
import React from "react";
import { DocumentTableRowMobile } from "./DocumentTableRowMobile";
import { DocumentTableRowDesktop } from "./DocumentTableRowDesktop";
import { Document } from "./DocumentTable";

export interface DocumentTableRowProps {
  document: Document;
  index: number;
  isMobile: boolean;
  showExpandColumn: boolean;
}

export const DocumentTableRow: React.FC<DocumentTableRowProps> = ({ 
  document, 
  index, 
  isMobile,
  showExpandColumn
}) => {
  if (isMobile) {
    return <DocumentTableRowMobile document={document} index={index} />;
  }

  return (
    <DocumentTableRowDesktop 
      document={document} 
      index={index} 
      showExpandColumn={showExpandColumn}
    />
  );
};

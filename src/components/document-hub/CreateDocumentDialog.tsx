
import React, { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useLoading } from "@/hooks/use-loading";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DocumentForm, FormValues } from "./document-form/DocumentForm";
import { UnsavedChangesAlert } from "./document-form/UnsavedChangesAlert";

interface CreateDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateDocumentDialog({
  open,
  onOpenChange,
}: CreateDocumentDialogProps) {
  const { toast } = useToast();
  const { showLoading, hideLoading } = useLoading();
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showExitPrompt, setShowExitPrompt] = useState(false);
  
  // Reset state when dialog opens or closes
  useEffect(() => {
    if (!open) {
      setHasUnsavedChanges(false);
      setShowExitPrompt(false);
    }
  }, [open]);

  const handleClose = () => {
    if (hasUnsavedChanges) {
      setShowExitPrompt(true);
    } else {
      onOpenChange(false);
    }
  };

  const confirmExit = () => {
    setHasUnsavedChanges(false);
    setShowExitPrompt(false);
    onOpenChange(false);
  };

  const cancelExit = () => {
    setShowExitPrompt(false);
  };

  function onSubmit(data: FormValues) {
    showLoading();
    
    // Simulate API call with timeout
    setTimeout(() => {
      console.log("Form submitted:", data);
      
      hideLoading();
      
      toast({
        title: "Document created",
        description: `Successfully created: ${data.title}`,
      });
      
      // Close the dialog and reset form
      setHasUnsavedChanges(false);
      onOpenChange(false);
    }, 1500);
  }

  const handleFormChanged = () => {
    setHasUnsavedChanges(true);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[750px] max-h-[85vh] p-0">
          <DialogHeader className="bg-gray-50 p-6 border-b border-gray-200">
            <DialogTitle className="text-xl font-semibold text-gray-800">Create Document</DialogTitle>
            <DialogDescription className="text-gray-600">
              Create a new document in the document hub. Fill in the required fields below.
            </DialogDescription>
          </DialogHeader>
          
          <div className="p-6">
            <DocumentForm 
              onSubmit={onSubmit}
              onCancel={handleClose}
              onChange={handleFormChanged}
            />
          </div>
        </DialogContent>
      </Dialog>
      
      <UnsavedChangesAlert
        open={showExitPrompt}
        onOpenChange={setShowExitPrompt}
        onConfirmExit={confirmExit}
        onCancelExit={cancelExit}
      />
    </>
  );
}

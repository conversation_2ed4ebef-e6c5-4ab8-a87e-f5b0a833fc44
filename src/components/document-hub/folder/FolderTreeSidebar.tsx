
import React, { useState } from 'react';
import { Folder, FolderOpen, Plus, MoreHorizontal, ChevronRight, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useFolder } from '@/contexts/FolderContext';
import { buildFolderTree } from '@/utils/folderUtils';
import { Folder as FolderType } from '@/types/folder';

interface FolderTreeNodeProps {
  folder: FolderType & { children?: FolderType[] };
  level: number;
  isSelected: boolean;
  onSelect: (folderId: string) => void;
  onCreateSubfolder: (parentId: string) => void;
}

const FolderTreeNode: React.FC<FolderTreeNodeProps> = ({
  folder,
  level,
  isSelected,
  onSelect,
  onCreateSubfolder
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = folder.children && folder.children.length > 0;

  return (
    <div className="select-none">
      <div
        className={`flex items-center py-1 px-2 rounded cursor-pointer hover:bg-gray-100 group ${
          isSelected ? 'bg-blue-50 text-blue-700' : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(folder.id)}
      >
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 mr-1"
            onClick={(e) => {
              e.stopPropagation();
              setIsExpanded(!isExpanded);
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        )}
        
        {!hasChildren && <div className="w-5" />}
        
        {isExpanded ? (
          <FolderOpen className="h-4 w-4 mr-2 text-blue-600" />
        ) : (
          <Folder className="h-4 w-4 mr-2 text-blue-600" />
        )}
        
        <span className="flex-1 text-sm truncate">{folder.name}</span>
        
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
          onClick={(e) => {
            e.stopPropagation();
            onCreateSubfolder(folder.id);
          }}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
      
      {isExpanded && hasChildren && (
        <div>
          {folder.children!.map((child) => (
            <FolderTreeNode
              key={child.id}
              folder={child}
              level={level + 1}
              isSelected={false}
              onSelect={onSelect}
              onCreateSubfolder={onCreateSubfolder}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const FolderTreeSidebar: React.FC = () => {
  const { folders, navigationState, navigateToFolder, createFolder } = useFolder();
  const [isCreatingFolder, setIsCreatingFolder] = useState<string | null>(null);
  const [newFolderName, setNewFolderName] = useState('');

  const folderTree = buildFolderTree(folders);

  const handleCreateFolder = (parentId: string | null) => {
    setIsCreatingFolder(parentId || 'root');
    setNewFolderName('');
  };

  const handleSaveFolder = () => {
    if (newFolderName.trim()) {
      const parentId = isCreatingFolder === 'root' ? null : isCreatingFolder;
      createFolder(newFolderName.trim(), parentId);
      setIsCreatingFolder(null);
      setNewFolderName('');
    }
  };

  const handleCancelCreate = () => {
    setIsCreatingFolder(null);
    setNewFolderName('');
  };

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-gray-900">Folders</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => handleCreateFolder(null)}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Root folder */}
        <div
          className={`flex items-center py-2 px-2 rounded cursor-pointer hover:bg-gray-100 ${
            navigationState.currentFolderId === null ? 'bg-blue-50 text-blue-700' : ''
          }`}
          onClick={() => navigateToFolder(null)}
        >
          <FolderOpen className="h-4 w-4 mr-2 text-blue-600" />
          <span className="flex-1 text-sm font-medium">All Documents</span>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-2">
        {isCreatingFolder === 'root' && (
          <div className="mb-2 px-2">
            <Input
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="h-8 text-sm"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSaveFolder();
                if (e.key === 'Escape') handleCancelCreate();
              }}
              onBlur={handleSaveFolder}
            />
          </div>
        )}
        
        {folderTree.map((folder) => (
          <FolderTreeNode
            key={folder.id}
            folder={folder}
            level={0}
            isSelected={navigationState.currentFolderId === folder.id}
            onSelect={navigateToFolder}
            onCreateSubfolder={handleCreateFolder}
          />
        ))}
        
        {isCreatingFolder && isCreatingFolder !== 'root' && (
          <div className="mt-2 px-2">
            <Input
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Folder name"
              className="h-8 text-sm"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleSaveFolder();
                if (e.key === 'Escape') handleCancelCreate();
              }}
              onBlur={handleSaveFolder}
            />
          </div>
        )}
      </div>
    </div>
  );
};

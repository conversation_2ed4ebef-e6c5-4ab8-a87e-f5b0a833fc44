
import React from 'react';
import { ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useFolder } from '@/contexts/FolderContext';

export const FolderBreadcrumb: React.FC = () => {
  const { navigationState, navigateToFolder } = useFolder();
  const { breadcrumbs } = navigationState;

  return (
    <div className="flex items-center space-x-1 text-sm text-gray-600 mb-4">
      <Button
        variant="ghost"
        size="sm"
        className="h-8 px-2 text-gray-600 hover:text-gray-900"
        onClick={() => navigateToFolder(null)}
      >
        <Home className="h-4 w-4 mr-1" />
        Documents
      </Button>
      
      {breadcrumbs.slice(1).map((crumb, index) => (
        <React.Fragment key={crumb.id}>
          <ChevronRight className="h-4 w-4 text-gray-400" />
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-gray-600 hover:text-gray-900"
            onClick={() => {
              // Navigate to the folder represented by this breadcrumb
              const targetIndex = index + 1;
              if (targetIndex < breadcrumbs.length - 1) {
                // This is an intermediate folder, find its ID
                const folderPath = breadcrumbs.slice(1, targetIndex + 1).map(b => b.name);
                // In a real app, you'd find the folder ID by path
                navigateToFolder(null); // Simplified for now
              }
            }}
          >
            {crumb.name}
          </Button>
        </React.Fragment>
      ))}
    </div>
  );
};

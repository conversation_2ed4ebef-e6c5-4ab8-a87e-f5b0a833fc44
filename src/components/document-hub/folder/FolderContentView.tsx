
import React, { useState } from 'react';
import { Folder, FolderOpen, FileText, Plus, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useFolder } from '@/contexts/FolderContext';
import { useAccount } from '@/contexts/AccountContext';
import { getFolderItems } from '@/utils/folderUtils';
import { DocumentWithFolder } from '@/types/folder';
import { DocumentStatusBadge } from '../DocumentStatusBadge';
import { formatDate } from '@/utils/dateUtils';

export const FolderContentView: React.FC = () => {
  const { folders, navigationState, navigateToFolder, createFolder } = useFolder();
  const { currentAccount } = useAccount();
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  // Convert account documents to DocumentWithFolder format
  const documentsWithFolder: DocumentWithFolder[] = (currentAccount?.documents || []).map(doc => ({
    ...doc,
    parentFolderId: doc.parentFolderId || null,
    isFolder: false
  }));

  const { documents, folders: currentFolders } = getFolderItems(
    navigationState.currentFolderId,
    documentsWithFolder,
    folders
  );

  const handleFolderDoubleClick = (folderId: string) => {
    navigateToFolder(folderId);
  };

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      createFolder(newFolderName.trim(), navigationState.currentFolderId);
      setNewFolderName('');
      setIsCreatingFolder(false);
    }
  };

  const allItems = [
    ...currentFolders.map(folder => ({ ...folder, type: 'folder' as const })),
    ...documents.map(doc => ({ ...doc, type: 'document' as const }))
  ].sort((a, b) => {
    // Folders first, then documents
    if (a.type !== b.type) {
      return a.type === 'folder' ? -1 : 1;
    }
    // Then alphabetically by name/title
    const aName = a.type === 'folder' ? a.name : a.title;
    const bName = b.type === 'folder' ? b.name : b.title;
    return aName.localeCompare(bName);
  });

  return (
    <div className="flex-1 bg-white">
      {/* Actions Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold">
            {navigationState.currentFolderId === null ? 'All Documents' : 
             folders.find(f => f.id === navigationState.currentFolderId)?.name || 'Folder'}
          </h2>
          <span className="text-sm text-gray-500">
            {allItems.length} items
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsCreatingFolder(true)}
          >
            <Plus className="h-4 w-4 mr-1" />
            New Folder
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Create Document
          </Button>
        </div>
      </div>

      {/* New Folder Input */}
      {isCreatingFolder && (
        <div className="p-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center space-x-2 max-w-sm">
            <input
              type="text"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              placeholder="Enter folder name"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateFolder();
                if (e.key === 'Escape') setIsCreatingFolder(false);
              }}
            />
            <Button size="sm" onClick={handleCreateFolder}>
              Create
            </Button>
            <Button size="sm" variant="outline" onClick={() => setIsCreatingFolder(false)}>
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Content Table */}
      <div className="overflow-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12"></TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Modified</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {allItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                  This folder is empty
                </TableCell>
              </TableRow>
            ) : (
              allItems.map((item) => (
                <TableRow 
                  key={item.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onDoubleClick={() => {
                    if (item.type === 'folder') {
                      handleFolderDoubleClick(item.id);
                    }
                  }}
                >
                  <TableCell>
                    {item.type === 'folder' ? (
                      <Folder className="h-5 w-5 text-blue-600" />
                    ) : (
                      <FileText className="h-5 w-5 text-gray-600" />
                    )}
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium text-sm">
                        {item.type === 'folder' ? item.name : item.title}
                      </span>
                      {item.type === 'document' && item.version && (
                        <span className="text-xs text-gray-500">v{item.version}</span>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-600">
                    {item.type === 'folder' ? 'Folder' : item.category}
                  </TableCell>
                  
                  <TableCell>
                    {item.type === 'document' ? (
                      <DocumentStatusBadge status={item.status} />
                    ) : (
                      <span className="text-sm text-gray-500">-</span>
                    )}
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-600">
                    {item.type === 'folder' 
                      ? formatDate(item.createdDate)
                      : formatDate(item.publishedDate || item.date || '')
                    }
                  </TableCell>
                  
                  <TableCell className="text-sm text-gray-600">
                    {item.type === 'folder' ? item.createdBy : item.assignee}
                  </TableCell>
                  
                  <TableCell>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

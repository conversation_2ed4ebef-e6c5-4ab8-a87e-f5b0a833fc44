
import React from "react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Folder, Table } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface ViewToggleProps {
  viewMode: "table" | "folders";
  setViewMode: (mode: "table" | "folders") => void;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({ viewMode, setViewMode }) => {
  const navigate = useNavigate();

  const handleViewModeChange = (mode: "table" | "folders") => {
    if (mode === "folders") {
      navigate("/documents-folders");
    } else {
      setViewMode("table");
    }
  };

  return (
    <TooltipProvider>
      <div className="flex gap-1 border rounded-lg p-1">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={viewMode === "table" ? "default" : "ghost"}
              size="sm"
              onClick={() => handleViewModeChange("table")}
            >
              <Table className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Table View</p>
          </TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={viewMode === "folders" ? "default" : "ghost"}
              size="sm"
              onClick={() => handleViewModeChange("folders")}
            >
              <Folder className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Folder View</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

# Document Hub Table Component

This directory contains a comprehensive document table component that can be used as a standalone component or integrated into larger applications.

## Components

### StandaloneDocumentTable

A self-contained document table component with built-in search, filtering, and pagination functionality.

#### Props

```typescript
interface StandaloneDocumentTableProps {
  title?: string;                    // Table title (default: "Document Table")
  subtitle?: string;                 // Table subtitle (default: "Manage and view documents")
  showSearch?: boolean;              // Show search functionality (default: true)
  showFilters?: boolean;             // Show filter functionality (default: true)
  initialDocuments?: Document[];     // Initial document data (default: mockDocuments)
}
```

#### Usage

```tsx
import { StandaloneDocumentTable } from '@/components/document-hub';

const MyPage = () => {
  return (
    <StandaloneDocumentTable
      title="My Document Library"
      subtitle="Manage and view all documents"
      showSearch={true}
      showFilters={true}
    />
  );
};
```

### DocumentTable

The core table component used internally by StandaloneDocumentTable.

#### Props

```typescript
interface DocumentTableProps {
  documents: Document[];
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  onSearch?: (term: string) => void;
}
```

## Types

### Document

```typescript
interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId?: string | null;
}
```

## Sample Data

The component comes with comprehensive sample data:

- `mockDocuments` - Default sample documents
- `mockDocumentsBengaluru` - Location-specific sample data
- `sampleDocumentTableData` - Extended sample data with examples

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Search**: Full-text search across document fields
- **Filtering**: Advanced filtering by status, department, category, etc.
- **Sorting**: Click column headers to sort data
- **Pagination**: Built-in pagination with configurable page sizes
- **Actions**: View, edit, and download document actions
- **Expandable Rows**: Mobile-friendly expandable rows for detailed view

## File Structure

```
src/components/document-hub/
├── StandaloneDocumentTable.tsx     # Main standalone component
├── DocumentTable.tsx               # Core table wrapper
├── DocumentTableContainer.tsx      # Container with data logic
├── SearchAndFilter.tsx             # Search and filter controls
├── DocumentTableHeader.tsx         # Table header component
├── DocumentTableRow.tsx            # Table row component
├── DocumentTableRowMobile.tsx      # Mobile-specific row
├── DocumentTableRowDesktop.tsx     # Desktop-specific row
├── DocumentStatusBadge.tsx         # Status badge component
├── ReviewDateCell.tsx              # Review date cell component
├── table/
│   ├── DocumentTableCore.tsx       # Core table logic
│   ├── DocumentTableTypes.ts       # TypeScript definitions
│   ├── DocumentTableColumns.tsx    # Column definitions
│   ├── DocumentTableActions.tsx    # Action components
│   └── index.ts                    # Table exports
├── table-cells/                    # Individual cell components
├── search-filter/                  # Search and filter components
├── utils/                          # Utility functions
└── index.ts                        # Main exports
```

## Route

The component is available at `/document-table` route, implemented in:
- `src/pages/DocumentTablePage.tsx` - Page component
- `src/components/app/AppRoutes.tsx` - Route configuration

## Dependencies

- React
- TypeScript
- Tailwind CSS
- Lucide React (icons)
- Custom UI components (@/components/ui/*)
- Custom hooks (@/hooks/*)

## Customization

The component is highly customizable:

1. **Styling**: Modify Tailwind classes in component files
2. **Data**: Provide custom document data via `initialDocuments` prop
3. **Features**: Toggle search and filters via props
4. **Columns**: Modify column definitions in `DocumentTableColumns.tsx`
5. **Actions**: Customize actions in `DocumentTableActions.tsx`

## Integration

To integrate into your application:

1. Import the component: `import { StandaloneDocumentTable } from '@/components/document-hub';`
2. Use with your data: `<StandaloneDocumentTable initialDocuments={yourData} />`
3. Customize as needed with props and styling

## Testing

Visit `/document-table` in your browser to see the component in action with sample data.

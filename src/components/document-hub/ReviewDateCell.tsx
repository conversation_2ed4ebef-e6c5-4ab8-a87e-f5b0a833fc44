
import React from "react";
import { differenceInDays, parse, isAfter } from "date-fns";
import { formatDate } from "@/utils/dateUtils";

interface ReviewDateCellProps {
  reviewDate: string | undefined;
}

export const ReviewDateCell: React.FC<ReviewDateCellProps> = ({ reviewDate }) => {
  const getBackgroundColor = () => {
    if (!reviewDate) return "";
    
    const reviewDateObj = parse(reviewDate, "dd MMM yyyy", new Date());
    const today = new Date();
    
    if (isAfter(today, reviewDateObj)) {
      return "bg-red-50";
    }
    
    const daysUntilReview = differenceInDays(reviewDateObj, today);
    if (daysUntilReview <= 14) {
      return "bg-orange-50";
    }
    
    return "";
  };

  return (
    <div className={`${getBackgroundColor()}`}>
      {reviewDate ? formatDate(reviewDate) : '-'}
    </div>
  );
};

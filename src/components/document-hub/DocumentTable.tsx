
import React from "react";
import { DocumentTableCore } from "./table/DocumentTableCore";
import { DocumentTableProps } from "./table/DocumentTableTypes";

// Re-export types for backward compatibility
export type { Document, DocumentTableProps, SortField, SortDirection } from "./table/DocumentTableTypes";

export const DocumentTable: React.FC<DocumentTableProps> = (props) => {
  return <DocumentTableCore {...props} />;
};

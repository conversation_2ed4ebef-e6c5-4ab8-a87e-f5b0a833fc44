
import React from "react";
import { Badge } from "@/components/ui/badge";

interface DocumentStatusBadgeProps {
  status: string;
}

export const DocumentStatusBadge: React.FC<DocumentStatusBadgeProps> = ({ status }) => {
  const lowercase = status.toLowerCase();
  
  if (lowercase === "draft") {
    return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Draft</Badge>;
  }
  
  if (lowercase === "review" || lowercase === "in review") {
    return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">In Review</Badge>;
  }
  
  if (lowercase === "approved") {
    return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Approved</Badge>;
  }
  
  if (lowercase === "published") {
    return <Badge variant="outline" className="bg-teal-50 text-teal-700 border-teal-200">Published</Badge>;
  }
  
  if (lowercase === "expired") {
    return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Expired</Badge>;
  }
  
  if (lowercase === "archived") {
    return <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">Archived</Badge>;
  }
  
  // Default
  return <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">{status}</Badge>;
};


import React from 'react';
import { DocumentTable } from './DocumentTable';
import { useDocumentFilter } from '@/hooks/useDocumentFilter';
import { useAccount } from '@/contexts/AccountContext';
import { useDocumentPagination } from '@/hooks/useDocumentPagination';

interface DocumentTableContainerProps {
  appliedFilters: any;
  searchTerm: string;
}

export const DocumentTableContainer: React.FC<DocumentTableContainerProps> = ({ 
  appliedFilters, 
  searchTerm 
}) => {
  const { currentAccount } = useAccount();
  const documents = currentAccount?.documents || [];
  
  const { filteredDocuments } = useDocumentFilter({
    documents: documents,
    appliedFilters,
    initialSearchTerm: searchTerm
  });
  
  const {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData
  } = useDocumentPagination({
    totalCount: filteredDocuments.length,
    initialPageSize: 10,
    initialPage: 1
  });
  
  // Get the current slice of documents for the current page
  const paginatedDocuments = getPaginatedData(filteredDocuments);
  
  return (
    <div className="bg-white rounded-md shadow-sm border border-gray-200 h-full flex flex-col w-full overflow-hidden">
      <DocumentTable 
        documents={paginatedDocuments}
        currentPage={currentPage}
        totalCount={filteredDocuments.length}
        pageSize={pageSize}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};


import React, { useState } from "react";
import { Plus, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CreateDocumentDialog } from "./CreateDocumentDialog";
import { toast } from "sonner";

export const ActionButtons: React.FC = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleCreateClick = () => {
    console.log("Create button clicked");
    setIsDialogOpen(true);
  };

  const handleDialogClose = (success?: boolean) => {
    setIsDialogOpen(false);
    if (success) {
      toast.success("Document created successfully!");
    }
  };

  return (
    <div className="flex items-center gap-[15px]">
      <Button
        variant="outline"
        size="icon"
        className="h-10 w-10 rounded-lg bg-[#E5F6F6]"
      >
        <Filter className="h-5 w-5 text-[#00797D]" />
      </Button>
      <Button
        className="bg-[#00797D] hover:bg-[#00696D] text-white font-medium"
        onClick={handleCreateClick}
      >
        <Plus className="mr-1" size={18} />
        Create Document
      </Button>
      
      <CreateDocumentDialog 
        open={isDialogOpen}
        onOpenChange={handleDialogClose}
      />
    </div>
  );
};

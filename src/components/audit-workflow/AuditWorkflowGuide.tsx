
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Play,
  BookOpen,
  CheckCircle,
  ArrowRight,
  FileText,
  Users,
  Calendar,
  AlertTriangle,
  Target,
  BarChart3
} from 'lucide-react';

interface GuideStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  details: string[];
  tips: string[];
}

export const AuditWorkflowGuide: React.FC = () => {
  const [selectedGuide, setSelectedGuide] = useState<string | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const workflowSteps: GuideStep[] = [
    {
      id: 'template-creation',
      title: 'Template Creation',
      description: 'Create and manage audit templates',
      icon: <FileText className="h-6 w-6" />,
      details: [
        'Access the Template Library from the main navigation',
        'Click "Create New Template" to start building',
        'Add sections and questions using the drag-and-drop editor',
        'Configure scoring systems (binary or weighted)',
        'Set evidence requirements for specific questions',
        'Save and activate your template'
      ],
      tips: [
        'Use clear, specific question wording',
        'Group related questions into logical sections',
        'Consider evidence requirements early in the process'
      ]
    },
    {
      id: 'supplier-management',
      title: 'Supplier Management',
      description: 'Manage multiple suppliers and their compliance',
      icon: <Users className="h-6 w-6" />,
      details: [
        'Navigate to Multi-Supplier Dashboard',
        'View compliance status across all suppliers',
        'Monitor risk levels and open non-conformances',
        'Access individual supplier audit history',
        'Track corrective action progress'
      ],
      tips: [
        'Regular monitoring prevents compliance issues',
        'Use risk-based approach for audit frequency',
        'Maintain clear communication with suppliers'
      ]
    },
    {
      id: 'audit-scheduling',
      title: 'Audit Scheduling',
      description: 'Schedule and manage audit appointments',
      icon: <Calendar className="h-6 w-6" />,
      details: [
        'Select supplier from the dashboard',
        'Choose appropriate audit template',
        'Assign qualified auditor',
        'Set audit date and duration',
        'Send notification to supplier',
        'Prepare audit checklist'
      ],
      tips: [
        'Schedule follow-ups based on previous findings',
        'Consider auditor availability and expertise',
        'Allow adequate preparation time'
      ]
    },
    {
      id: 'audit-execution',
      title: 'Audit Execution',
      description: 'Conduct audits using digital checklists',
      icon: <CheckCircle className="h-6 w-6" />,
      details: [
        'Access the Audit Execution Panel',
        'Navigate through checklist sections',
        'Score each question (Pass/Fail)',
        'Add detailed auditor comments',
        'Upload evidence photos/documents',
        'Record observations and non-conformances'
      ],
      tips: [
        'Take comprehensive notes during the audit',
        'Capture photographic evidence where relevant',
        'Discuss findings with supplier representatives'
      ]
    },
    {
      id: 'follow-up-management',
      title: 'Follow-up Management',
      description: 'Manage corrective actions and follow-up audits',
      icon: <Target className="h-6 w-6" />,
      details: [
        'Review non-conformances from previous audits',
        'Schedule appropriate follow-up audits',
        'Focus on specific areas of concern',
        'Track corrective action implementation',
        'Verify effectiveness of improvements'
      ],
      tips: [
        'Set realistic timelines for corrective actions',
        'Maintain regular communication with suppliers',
        'Document all follow-up activities'
      ]
    },
    {
      id: 'questionnaire-system',
      title: 'Questionnaire System',
      description: 'Send forms to suppliers and manage responses',
      icon: <BarChart3 className="h-6 w-6" />,
      details: [
        'Convert audit templates to supplier questionnaires',
        'Configure form settings and due dates',
        'Send forms to multiple suppliers',
        'Monitor response rates and completion',
        'Review and analyze supplier responses',
        'Export data for further analysis'
      ],
      tips: [
        'Provide clear instructions to suppliers',
        'Set reasonable completion deadlines',
        'Follow up on incomplete responses'
      ]
    }
  ];

  const handleViewDetails = (guideId: string) => {
    setSelectedGuide(guideId);
    setShowDetailModal(true);
  };

  const selectedStep = workflowSteps.find(step => step.id === selectedGuide);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Audit Workflow Guide</h2>
          <p className="text-gray-600">Learn how to effectively manage your audit processes</p>
        </div>
        <Button className="gap-2">
          <Play className="h-4 w-4" />
          Start Interactive Tour
        </Button>
      </div>

      <Tabs defaultValue="workflow" className="w-full">
        <TabsList>
          <TabsTrigger value="workflow">Workflow Steps</TabsTrigger>
          <TabsTrigger value="best-practices">Best Practices</TabsTrigger>
          <TabsTrigger value="troubleshooting">Troubleshooting</TabsTrigger>
        </TabsList>

        <TabsContent value="workflow">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workflowSteps.map((step, index) => (
              <Card key={step.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      {step.icon}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                      <Badge variant="outline">Step {index + 1}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{step.description}</p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full gap-2"
                    onClick={() => handleViewDetails(step.id)}
                  >
                    <BookOpen className="h-4 w-4" />
                    View Details
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="best-practices">
          <Card>
            <CardHeader>
              <CardTitle>Audit Best Practices</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-3">Pre-Audit Preparation</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Review supplier's previous audit history and corrective actions</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Ensure audit template is current and appropriate for supplier type</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Communicate audit objectives and scope clearly to supplier</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">During the Audit</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Maintain professional and collaborative approach</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Document findings with specific examples and evidence</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Discuss findings with supplier before finalizing</span>
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Post-Audit Follow-up</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Provide clear, actionable corrective action requirements</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Set realistic timelines for implementation</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Schedule appropriate follow-up activities</span>
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="troubleshooting">
          <Card>
            <CardHeader>
              <CardTitle>Common Issues & Solutions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-3 text-red-600">Issue: Supplier not responding to questionnaires</h4>
                <ul className="space-y-1 text-sm ml-4">
                  <li>• Check if questionnaire was sent to correct contact</li>
                  <li>• Verify email delivery and spam folder</li>
                  <li>• Follow up with phone call or alternative contact method</li>
                  <li>• Consider extending deadline if reasonable</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 text-red-600">Issue: Audit findings disputed by supplier</h4>
                <ul className="space-y-1 text-sm ml-4">
                  <li>• Review evidence and documentation together</li>
                  <li>• Clarify audit criteria and expectations</li>
                  <li>• Consider escalation to senior management if needed</li>
                  <li>• Document any agreed modifications to findings</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3 text-red-600">Issue: Recurring non-conformances</h4>
                <ul className="space-y-1 text-sm ml-4">
                  <li>• Evaluate effectiveness of corrective actions</li>
                  <li>• Consider root cause analysis requirement</li>
                  <li>• Increase audit frequency for problem areas</li>
                  <li>• Escalate to supplier quality management</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Detailed Step Modal */}
      {selectedStep && (
        <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                {selectedStep.icon}
                {selectedStep.title}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-6">
              <p className="text-gray-600">{selectedStep.description}</p>
              
              <div>
                <h4 className="font-semibold mb-3">Step-by-Step Instructions</h4>
                <ol className="space-y-2">
                  {selectedStep.details.map((detail, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Badge variant="outline" className="mt-0.5">{index + 1}</Badge>
                      <span className="text-sm">{detail}</span>
                    </li>
                  ))}
                </ol>
              </div>
              
              <div>
                <h4 className="font-semibold mb-3">Tips for Success</h4>
                <ul className="space-y-2">
                  {selectedStep.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={() => setShowDetailModal(false)}>
                Close
              </Button>
              <Button className="gap-2">
                <ArrowRight className="h-4 w-4" />
                Next Step
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

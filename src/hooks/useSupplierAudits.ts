
import { useState } from 'react';
import { SupplierAudit } from '@/types/supplierAudits';

const mockAudits: SupplierAudit[] = [
  {
    id: 'audit-001',
    supplierName: 'TechCorp Industries',
    auditType: 'Quality System Audit',
    scheduledDate: '2024-01-20',
    status: 'Planned',
    auditor: '<PERSON>'
  },
  {
    id: 'audit-002',
    supplierName: 'Global Manufacturing',
    auditType: 'ISO 9001 Surveillance',
    scheduledDate: '2024-01-25',
    status: 'In Progress',
    auditor: '<PERSON>',
    score: 85
  },
  {
    id: 'audit-003',
    supplierName: 'Precision Parts Ltd',
    auditType: 'Supplier Assessment',
    scheduledDate: '2024-01-15',
    status: 'Completed',
    auditor: '<PERSON>',
    score: 92,
    findings: ['Minor documentation gaps', 'Excellent quality control']
  }
];

export const useSupplierAudits = () => {
  const [audits] = useState<SupplierAudit[]>(mockAudits);

  return {
    audits
  };
};


import { useState, useMemo } from "react";
import { Person } from "@/types/people";
import { useAccount } from "@/contexts/AccountContext";

export const usePeopleData = () => {
  const { currentAccount } = useAccount();
  const [searchTerm, setSearchTerm] = useState("");

  // Get people from the current account location
  const people = currentAccount.people;

  // Filter people based on search term
  const filteredPeople = useMemo(() => {
    if (!searchTerm.trim()) return people;
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    return people.filter(person => {
      // Handle processes as string array
      const processesText = person.processes.join(" ").toLowerCase();
      
      return person.id.toLowerCase().includes(lowerSearchTerm) ||
        person.name.toLowerCase().includes(lowerSearchTerm) ||
        person.jobTitle.toLowerCase().includes(lowerSearchTerm) ||
        (person.email && person.email.toLowerCase().includes(lowerSearchTerm)) ||
        (person.department && person.department.toLowerCase().includes(lowerSearchTerm)) ||
        processesText.includes(lowerSearchTerm) ||
        person.status.toLowerCase().includes(lowerSearchTerm);
    });
  }, [people, searchTerm]);

  return {
    people,
    filteredPeople,
    searchTerm,
    setSearchTerm
  };
};

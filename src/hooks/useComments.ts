import { useState, useEffect, useCallback } from 'react';
import { Comment, CommentPermissions } from '@/types/comment';
import { commentService } from '@/services/commentService';
import { toast } from 'sonner';
import { mockEnterpriseComments } from '@/data/mockEnterpriseComments';

export const useComments = (documentId: string) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock current user - in real app this would come from auth context
  const currentUser = {
    id: 'current-user',
    name: 'Current User',
    role: 'Quality Manager'
  };

  const permissions: CommentPermissions = {
    canCreate: true,
    canEdit: true,
    canDelete: true,
    canReply: true,
    canModerate: true
  };

  // --- Custom: Use enterprise-style mock data for specific docId
  const loadComments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      // Use enterprise nested mock comments for this demo doc
      if (documentId === 'doc-bg-2') {
        setComments(mockEnterpriseComments);
      } else {
        const data = await commentService.getCommentsByDocumentId(documentId);
        setComments(data);
      }
    } catch (err) {
      setError('Failed to load comments');
      console.error('Error loading comments:', err);
    } finally {
      setLoading(false);
    }
  }, [documentId]);
  // --- End Customization

  const createComment = useCallback(async (content: string, parentId?: string, attachments?: File[]) => {
    try {
      const newComment = await commentService.createComment({
        documentId,
        content,
        author: currentUser,
        parentId,
        isDeleted: false,
        attachments: attachments ? await processAttachments(attachments) : undefined
      });

      if (parentId) {
        // Add reply to parent comment
        setComments(prev => prev.map(comment => 
          comment.id === parentId 
            ? { ...comment, replies: [...(comment.replies || []), newComment] }
            : comment
        ));
      } else {
        // Add new top-level comment
        setComments(prev => [newComment, ...prev]);
      }

      toast.success('Comment added successfully');
      return newComment;
    } catch (err) {
      toast.error('Failed to add comment');
      throw err;
    }
  }, [documentId, currentUser]);

  const updateComment = useCallback(async (id: string, content: string) => {
    try {
      const updatedComment = await commentService.updateComment(id, content);
      
      setComments(prev => prev.map(comment => {
        if (comment.id === id) {
          return updatedComment;
        }
        if (comment.replies) {
          return {
            ...comment,
            replies: comment.replies.map(reply => 
              reply.id === id ? updatedComment : reply
            )
          };
        }
        return comment;
      }));

      toast.success('Comment updated successfully');
    } catch (err) {
      toast.error('Failed to update comment');
      throw err;
    }
  }, []);

  const deleteComment = useCallback(async (id: string) => {
    try {
      await commentService.deleteComment(id);
      
      setComments(prev => prev.map(comment => {
        if (comment.id === id) {
          return { ...comment, isDeleted: true, content: '[This comment has been deleted]' };
        }
        if (comment.replies) {
          return {
            ...comment,
            replies: comment.replies.map(reply => 
              reply.id === id 
                ? { ...reply, isDeleted: true, content: '[This comment has been deleted]' }
                : reply
            )
          };
        }
        return comment;
      }));

      toast.success('Comment deleted successfully');
    } catch (err) {
      toast.error('Failed to delete comment');
      throw err;
    }
  }, []);

  const toggleReaction = useCallback(async (commentId: string, type: 'like' | 'dislike' | 'heart' | 'thumbs_up') => {
    try {
      await commentService.addReaction(commentId, currentUser.id, type);
      await loadComments(); // Reload to get updated reactions
    } catch (err) {
      toast.error('Failed to update reaction');
    }
  }, [currentUser.id, loadComments]);

  const toggleExpanded = useCallback((commentId: string) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, isExpanded: !comment.isExpanded }
        : comment
    ));
  }, []);

  useEffect(() => {
    loadComments();
  }, [loadComments]);

  return {
    comments,
    loading,
    error,
    permissions,
    currentUser,
    createComment,
    updateComment,
    deleteComment,
    toggleReaction,
    toggleExpanded,
    refetch: loadComments
  };
};

// Helper function to process file attachments
async function processAttachments(files: File[]) {
  // In a real app, this would upload files to a server
  // For now, we'll create mock attachment objects
  return files.map(file => ({
    id: `att-${Date.now()}-${Math.random()}`,
    fileName: file.name,
    fileUrl: URL.createObjectURL(file),
    fileType: file.type,
    fileSize: file.size,
    uploadedAt: new Date()
  }));
}


import React from "react";
import { useForm, UseFormReturn, FieldValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

export interface UseFormValidationOptions<T extends z.ZodType> {
  schema: T;
  defaultValues?: z.infer<T>;
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'onTouched' | 'all';
  reValidateMode?: 'onChange' | 'onBlur' | 'onSubmit';
  shouldFocusError?: boolean;
  delayError?: number;
}

export function useFormValidation<T extends z.ZodType>({
  schema,
  defaultValues,
  mode = 'onChange',
  reValidateMode = 'onChange',
  shouldFocusError = true,
  delayError = 0
}: UseFormValidationOptions<T>): UseFormReturn<z.infer<T>> {
  return useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode,
    reValidateMode,
    shouldFocusError,
    delayError,
  });
}

// Real-time validation hook with improved debouncing
export function useRealTimeValidation<T>(
  value: T,
  validationFn: (value: T) => string | null,
  delay: number = 300
) {
  const [error, setError] = React.useState<string | null>(null);
  const [isValidating, setIsValidating] = React.useState(false);

  React.useEffect(() => {
    if (!value) {
      setError(null);
      setIsValidating(false);
      return;
    }

    setIsValidating(true);
    const timeoutId = setTimeout(() => {
      try {
        const validationError = validationFn(value);
        setError(validationError);
      } catch (err) {
        setError("Validation error occurred");
      } finally {
        setIsValidating(false);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [value, validationFn, delay]);

  return { error, isValidating };
}

// Utility for async validation with better error handling
export function createAsyncValidation<T>(
  validationFn: (value: T) => Promise<boolean>,
  errorMessage: string
) {
  return async (value: T) => {
    try {
      const isValid = await validationFn(value);
      return isValid || errorMessage;
    } catch {
      return errorMessage;
    }
  };
}

// Enhanced validation patterns with better error messages
export const commonValidationPatterns = {
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  positiveNumber: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: "Must be a positive number"
  }),
  requiredString: (fieldName: string) => z.string().min(1, `${fieldName} is required`),
  optionalString: z.string().optional(),
  requiredDate: z.string().min(1, "Date is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  requiredSelect: (fieldName: string) => z.string().min(1, `${fieldName} is required`),
};

// Cross-field validation helper
export const createCrossFieldValidation = <T>(
  fieldName: string,
  validator: (data: T) => boolean,
  errorMessage: string
) => {
  return z.any().superRefine((data, ctx) => {
    if (!validator(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: errorMessage,
        path: [fieldName]
      });
    }
  });
};

// Conditional validation helper
export const createConditionalValidation = <T>(
  condition: (data: T) => boolean,
  schema: z.ZodType,
  fallbackSchema: z.ZodType = z.any()
) => {
  return z.any().superRefine((data, ctx) => {
    const validationSchema = condition(data) ? schema : fallbackSchema;
    const result = validationSchema.safeParse(data);
    
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        ctx.addIssue(issue);
      });
    }
  });
};

// Function to create password confirmation schema
export const createPasswordConfirmationSchema = () => {
  return z.object({
    password: commonValidationPatterns.password,
    confirmPassword: z.string().min(1, "Please confirm your password")
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"]
  });
};

// Pre-built schemas for common use cases
export const loginValidationSchema = z.object({
  email: commonValidationPatterns.email,
  password: z.string().min(1, "Password is required"),
});

export const employeeValidationSchema = z.object({
  name: commonValidationPatterns.requiredString("Name"),
  email: commonValidationPatterns.email,
  jobTitle: commonValidationPatterns.requiredString("Job Title"),
  department: commonValidationPatterns.optionalString,
  processes: z.array(z.string()).min(1, "At least one process must be selected"),
});

// Form state utilities
export const FormStateHelpers = {
  isDirty: (form: any) => Object.keys(form.formState.dirtyFields).length > 0,
  hasErrors: (form: any) => Object.keys(form.formState.errors).length > 0,
  getFirstError: (form: any) => {
    const errors = form.formState.errors;
    const firstErrorKey = Object.keys(errors)[0];
    return firstErrorKey ? errors[firstErrorKey]?.message : null;
  }
};

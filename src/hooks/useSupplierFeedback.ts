
import { useState, useMemo } from "react";
import { SupplierFeedback, FeedbackTag, FeedbackSentiment } from "@/types/supplierFeedback";
import { mockSupplierFeedback } from "@/data/mockSupplierFeedback";
import { useToast } from "@/hooks/use-toast";

export const useSupplierFeedback = () => {
  const [feedbackItems, setFeedbackItems] = useState<SupplierFeedback[]>(mockSupplierFeedback);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<FeedbackTag[]>([]);
  const [selectedSentiment, setSelectedSentiment] = useState<FeedbackSentiment | "All">("All");
  const [sortBy, setSortBy] = useState<keyof SupplierFeedback | null>("submittedDate");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const { toast } = useToast();

  const filteredFeedback = useMemo(() => {
    return feedbackItems
      .filter(item => {
        // Search filter
        const matchesSearch = searchQuery === "" || 
          item.supplierName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.feedback.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.submittedBy.toLowerCase().includes(searchQuery.toLowerCase());
        
        // Tags filter
        const matchesTags = selectedTags.length === 0 || 
          selectedTags.some(tag => item.tags.includes(tag));
        
        // Sentiment filter
        const matchesSentiment = selectedSentiment === "All" || 
          item.sentiment === selectedSentiment;
        
        return matchesSearch && matchesTags && matchesSentiment;
      })
      .sort((a, b) => {
        if (!sortBy) return 0;
        
        const aValue = a[sortBy];
        const bValue = b[sortBy];
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === "asc" 
            ? aValue.localeCompare(bValue) 
            : bValue.localeCompare(aValue);
        }
        
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
        }
        
        return 0;
      });
  }, [feedbackItems, searchQuery, selectedTags, selectedSentiment, sortBy, sortDirection]);

  const handleAddFeedback = (newFeedback: Omit<SupplierFeedback, 'id'>) => {
    const id = `FB-${String(feedbackItems.length + 1).padStart(3, '0')}`;
    setFeedbackItems(prev => [{ ...newFeedback, id }, ...prev]);
    toast({
      title: "Feedback submitted",
      description: `Your feedback for ${newFeedback.supplierName} has been recorded.`,
    });
  };

  const handleSort = (field: keyof SupplierFeedback) => {
    if (sortBy === field) {
      setSortDirection(prev => prev === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortDirection("asc");
    }
  };

  return {
    feedbackItems: filteredFeedback,
    searchQuery,
    setSearchQuery,
    selectedTags,
    setSelectedTags,
    selectedSentiment,
    setSelectedSentiment,
    sortBy,
    sortDirection,
    handleSort,
    handleAddFeedback
  };
};

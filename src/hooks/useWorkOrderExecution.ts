
import { useState, useEffect } from 'react';

interface ProcessStep {
  id: string;
  name: string;
  instruction: string;
  estimatedTime: number;
  dataCapture: Array<{
    field: string;
    type: 'numeric' | 'timer' | 'text' | 'checkbox';
    unit?: string;
    required: boolean;
  }>;
  status: 'pending' | 'in-progress' | 'completed';
}

const mockProcessSteps: ProcessStep[] = [
  {
    id: '1',
    name: 'Mixing',
    instruction: 'Mix materials at 60 RPM for 30 minutes. Ensure temperature stays between 20-25°C.',
    estimatedTime: 30,
    dataCapture: [
      { field: 'RPM', type: 'numeric', unit: 'rpm', required: true },
      { field: 'Duration', type: 'timer', unit: 'min', required: true },
      { field: 'Temperature', type: 'numeric', unit: '°C', required: true }
    ],
    status: 'pending'
  },
  {
    id: '2', 
    name: 'Granulation',
    instruction: 'Add binding solution slowly while mixing. Monitor granule formation.',
    estimatedTime: 20,
    dataCapture: [
      { field: 'Binding Solution Volume', type: 'numeric', unit: 'ml', required: true },
      { field: 'Granule Size Check', type: 'checkbox', required: true },
      { field: 'Notes', type: 'text', required: false }
    ],
    status: 'pending'
  },
  {
    id: '3',
    name: 'Drying',
    instruction: 'Dry granules at 40°C for 2 hours. Check moisture content every 30 minutes.',
    estimatedTime: 120,
    dataCapture: [
      { field: 'Temperature', type: 'numeric', unit: '°C', required: true },
      { field: 'Duration', type: 'timer', unit: 'min', required: true },
      { field: 'Final Moisture', type: 'numeric', unit: '%', required: true }
    ],
    status: 'pending'
  }
];

export const useWorkOrderExecution = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [steps, setSteps] = useState(mockProcessSteps);
  const [stepData, setStepData] = useState<Record<string, any>>({});
  const [timer, setTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);

  const currentStep = steps[currentStepIndex];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning]);

  const handleStartStep = () => {
    setSteps(prev => prev.map((step, index) => 
      index === currentStepIndex ? { ...step, status: 'in-progress' } : step
    ));
    setIsTimerRunning(true);
  };

  const handlePauseStep = () => {
    setIsTimerRunning(false);
  };

  const handleCompleteStep = () => {
    setSteps(prev => prev.map((step, index) => 
      index === currentStepIndex ? { ...step, status: 'completed' } : step
    ));
    setIsTimerRunning(false);
    setTimer(0);
    
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  };

  const handleDataChange = (field: string, value: any) => {
    setStepData(prev => ({
      ...prev,
      [currentStep.id]: {
        ...prev[currentStep.id],
        [field]: value
      }
    }));
  };

  const isStepComplete = () => {
    const stepDataForCurrent = stepData[currentStep.id] || {};
    const requiredFields = currentStep.dataCapture.filter(dc => dc.required);
    return requiredFields.every(field => 
      stepDataForCurrent[field.field] !== undefined && 
      stepDataForCurrent[field.field] !== ''
    );
  };

  return {
    currentStepIndex,
    steps,
    stepData,
    timer,
    isTimerRunning,
    currentStep,
    handleStartStep,
    handlePauseStep,
    handleCompleteStep,
    handleDataChange,
    isStepComplete
  };
};

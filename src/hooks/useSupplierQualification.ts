
import { useState, useMemo } from 'react';
import { SupplierQualification } from '@/types/supplierQualification';

export const useSupplierQualification = (suppliers: SupplierQualification[]) => {
  const [sortField, setSortField] = useState<keyof SupplierQualification>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filters, setFilters] = useState<Record<string, string>>({});

  const filteredSuppliers = useMemo(() => {
    let filtered = [...suppliers];

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all') {
        filtered = filtered.filter(supplier => {
          if (key === 'status') return supplier.status === value;
          if (key === 'riskScore') {
            const score = supplier.riskScore;
            if (value === 'Low') return score >= 80;
            if (value === 'Medium') return score >= 60 && score < 80;
            if (value === 'High') return score < 60;
          }
          return true;
        });
      }
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [suppliers, filters, sortField, sortDirection]);

  const handleSort = (field: keyof SupplierQualification) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  return {
    suppliers: filteredSuppliers,
    sortField,
    sortDirection,
    handleSort,
    handleFilterChange
  };
};

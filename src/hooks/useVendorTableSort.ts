
import { useState } from "react";
import { Vendor } from "@/types/vendors";

export type SortField = keyof Vendor | null;
export type SortDirection = 'asc' | 'desc' | null;

export const useVendorTableSort = (initialData: Vendor[]) => {
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedItems = [...initialData].sort((a, b) => {
    if (!sortField || !sortDirection) {
      return 0;
    }

    const valueA = a[sortField];
    const valueB = b[sortField];

    if (valueA === undefined && valueB === undefined) {
      return 0;
    }
    if (valueA === undefined) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    if (valueB === undefined) {
      return sortDirection === 'asc' ? -1 : 1;
    }

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return sortDirection === 'asc' 
        ? valueA.localeCompare(valueB) 
        : valueB.localeCompare(valueA);
    }

    if (valueA < valueB) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (valueA > valueB) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  return {
    sortField,
    sortDirection,
    handleSort,
    sortedItems
  };
};


import { useState, useMemo } from 'react';
import { Person } from '@/types/people';

export type SortField = keyof Person | null;
export type SortDirection = 'asc' | 'desc' | null;

export const usePeopleTableSort = (people: Person[]) => {
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedPeople = useMemo(() => {
    if (!sortField || !sortDirection || !people) return people || [];
    
    return [...people].sort((a, b) => {
      const fieldA = a[sortField] ? String(a[sortField]).toLowerCase() : '';
      const fieldB = b[sortField] ? String(b[sortField]).toLowerCase() : '';
      
      if (sortDirection === 'asc') {
        return fieldA > fieldB ? 1 : -1;
      } else {
        return fieldA < fieldB ? 1 : -1;
      }
    });
  }, [people, sortField, sortDirection]);

  return {
    sortField,
    sortDirection,
    handleSort,
    sortedPeople
  };
};

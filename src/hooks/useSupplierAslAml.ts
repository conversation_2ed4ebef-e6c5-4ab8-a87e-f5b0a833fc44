
import { useState, useMemo } from 'react';
import { SupplierAslAml, ApprovalType, RiskTier } from '@/types/supplierAslAml';

export const useSupplierAslAml = () => {
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [approvalTypeFilter, setApprovalTypeFilter] = useState<ApprovalType | 'All'>('All');
  const [riskTierFilter, setRiskTierFilter] = useState<RiskTier | 'All'>('All');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Mock data
  const suppliers = useMemo<SupplierAslAml[]>(() => {
    const mockData: SupplierAslAml[] = [
      {
        id: '1',
        name: 'Acme Manufacturing',
        approvalType: 'ASL',
        riskTier: 'Low',
        status: 'Approved',
        lastAudit: '2024-01-15',
        nextReview: '2024-07-15',
        qualificationBasis: 'Quality audit passed',
        categories: ['Raw Materials'],
        certifications: ['ISO 9001']
      },
      {
        id: '2',
        name: 'Beta Components',
        approvalType: 'AML',
        riskTier: 'Medium',
        status: 'Pending',
        lastAudit: '2024-02-10',
        nextReview: '2024-08-10',
        qualificationBasis: 'Component testing in progress',
        categories: ['Components'],
        certifications: ['ISO 14001']
      }
    ];

    // Apply filters
    return mockData.filter(supplier => {
      const matchesSearch = supplier.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesApprovalType = approvalTypeFilter === 'All' || supplier.approvalType === approvalTypeFilter;
      const matchesRiskTier = riskTierFilter === 'All' || supplier.riskTier === riskTierFilter;
      
      return matchesSearch && matchesApprovalType && matchesRiskTier;
    });
  }, [searchQuery, approvalTypeFilter, riskTierFilter]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const requestRequalification = (supplierId: string) => {
    console.log('Requesting requalification for supplier:', supplierId);
  };

  const revokeApproval = (supplierId: string) => {
    console.log('Revoking approval for supplier:', supplierId);
  };

  const exportSupplierList = () => {
    console.log('Exporting supplier list');
  };

  return {
    suppliers,
    sortField,
    sortDirection,
    approvalTypeFilter,
    riskTierFilter,
    searchQuery,
    handleSort,
    setApprovalTypeFilter: (value: ApprovalType | 'All') => setApprovalTypeFilter(value),
    setRiskTierFilter: (value: RiskTier | 'All') => setRiskTierFilter(value),
    setSearchQuery,
    requestRequalification,
    revokeApproval,
    exportSupplierList
  };
};

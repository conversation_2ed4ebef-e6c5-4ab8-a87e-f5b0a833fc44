
import { useState } from "react";
import { SupplierForm, FormQuestion, FormSection, FormResponse, FormRecipient } from "@/types/supplierForms";
import { mockSupplierForms, mockFormResponses, mockFormRecipients } from "@/data/mockSupplierForms";
import { useToast } from "@/hooks/use-toast";

export const useSupplierForms = () => {
  const [forms, setForms] = useState<SupplierForm[]>(mockSupplierForms);
  const [responses, setResponses] = useState<FormResponse[]>(mockFormResponses);
  const [recipients, setRecipients] = useState<FormRecipient[]>(mockFormRecipients);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const { toast } = useToast();

  const selectedForm = selectedFormId 
    ? forms.find(form => form.id === selectedFormId) 
    : null;

  const formResponses = selectedFormId 
    ? responses.filter(response => response.formId === selectedFormId)
    : [];

  const formRecipients = selectedFormId
    ? recipients.filter(r => formResponses.some(fr => fr.supplierId === r.id))
    : [];

  const createForm = (newForm: Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'>) => {
    const formId = `form-${Date.now()}`;
    const now = new Date().toISOString();
    
    const form: SupplierForm = {
      ...newForm,
      id: formId,
      createdAt: now,
      updatedAt: now,
    };
    
    setForms(prev => [...prev, form]);
    setSelectedFormId(formId);
    
    toast({
      title: "Form Created",
      description: `${newForm.name} has been created successfully.`
    });
    
    return formId;
  };

  const updateForm = (formId: string, updates: Partial<Omit<SupplierForm, 'id' | 'createdAt' | 'updatedAt'>>) => {
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          ...updates,
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
    
    toast({
      title: "Form Updated",
      description: "Form has been updated successfully."
    });
  };

  const deleteForm = (formId: string) => {
    setForms(prev => prev.filter(form => form.id !== formId));
    if (selectedFormId === formId) {
      setSelectedFormId(null);
    }
    
    toast({
      title: "Form Deleted",
      description: "Form has been deleted successfully."
    });
  };

  const addSection = (formId: string, section: Omit<FormSection, 'id'>) => {
    const sectionId = `section-${Date.now()}`;
    
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: [...form.sections, { ...section, id: sectionId }],
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const updateSection = (formId: string, sectionId: string, updates: Partial<Omit<FormSection, 'id'>>) => {
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: form.sections.map(section => 
            section.id === sectionId ? { ...section, ...updates } : section
          ),
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const deleteSection = (formId: string, sectionId: string) => {
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: form.sections.filter(section => section.id !== sectionId),
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const addQuestion = (formId: string, sectionId: string, question: Omit<FormQuestion, 'id'>) => {
    const questionId = `q-${Date.now()}`;
    
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: form.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                questions: [...section.questions, { ...question, id: questionId }]
              };
            }
            return section;
          }),
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const updateQuestion = (
    formId: string, 
    sectionId: string, 
    questionId: string, 
    updates: Partial<Omit<FormQuestion, 'id'>>
  ) => {
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: form.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                questions: section.questions.map(question => 
                  question.id === questionId ? { ...question, ...updates } : question
                )
              };
            }
            return section;
          }),
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const deleteQuestion = (formId: string, sectionId: string, questionId: string) => {
    setForms(prev => prev.map(form => {
      if (form.id === formId) {
        return {
          ...form,
          sections: form.sections.map(section => {
            if (section.id === sectionId) {
              return {
                ...section,
                questions: section.questions.filter(question => question.id !== questionId)
              };
            }
            return section;
          }),
          updatedAt: new Date().toISOString()
        };
      }
      return form;
    }));
  };

  const sendForm = (formId: string, recipientIds: string[]) => {
    // In a real app, this would trigger emails to recipients
    toast({
      title: "Form Sent",
      description: `Form sent to ${recipientIds.length} recipients.`
    });
  };

  return {
    forms,
    selectedForm,
    selectedFormId,
    formResponses,
    formRecipients,
    setSelectedFormId,
    createForm,
    updateForm,
    deleteForm,
    addSection,
    updateSection,
    deleteSection,
    addQuestion,
    updateQuestion,
    deleteQuestion,
    sendForm
  };
};

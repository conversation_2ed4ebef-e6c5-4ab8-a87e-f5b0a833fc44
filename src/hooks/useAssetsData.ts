
import { useState, useMemo } from "react";

interface Asset {
  id: string;
  name: string;
  type: string;
  status: string;
  location: string;
  lastMaintenance: string;
  nextMaintenance: string;
  condition: string;
  value: number;
}

const mockAssets: Asset[] = [
  {
    id: "AST-001",
    name: "Production Line A",
    type: "Manufacturing Equipment",
    status: "Active",
    location: "Factory Floor 1",
    lastMaintenance: "2024-01-15",
    nextMaintenance: "2024-04-15",
    condition: "Good",
    value: 150000
  },
  {
    id: "AST-002",
    name: "Quality Testing Station",
    type: "Testing Equipment",
    status: "Maintenance",
    location: "Quality Lab",
    lastMaintenance: "2024-01-10",
    nextMaintenance: "2024-03-10",
    condition: "Fair",
    value: 75000
  },
  {
    id: "AST-003",
    name: "Material Handling Robot",
    type: "Automation",
    status: "Active",
    location: "Warehouse",
    lastMaintenance: "2024-01-20",
    nextMaintenance: "2024-05-20",
    condition: "Excellent",
    value: 200000
  }
];

export const useAssetsData = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [assets] = useState<Asset[]>(mockAssets);

  const filteredAssets = useMemo(() => {
    return assets.filter(asset =>
      asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.location.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [assets, searchQuery]);

  return {
    searchQuery,
    setSearchQuery,
    assets,
    filteredAssets
  };
};

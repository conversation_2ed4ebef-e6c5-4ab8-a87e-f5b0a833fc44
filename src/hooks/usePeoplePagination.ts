
import { useState, useMemo } from "react";

export interface UsePeoplePaginationProps {
  totalCount: number;
  initialPageSize?: number;
  initialPage?: number;
}

export const usePeoplePagination = ({
  totalCount,
  initialPageSize = 10,
  initialPage = 1
}: UsePeoplePaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  // Calculate total pages
  const totalPages = useMemo(() => Math.ceil(totalCount / pageSize), [totalCount, pageSize]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle direct page input
  const handlePageInput = (input: string) => {
    const pageNumber = parseInt(input, 10);
    if (!isNaN(pageNumber) && pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  // Get the current slice of data based on pagination settings
  const getPaginatedData = <T>(data: T[]): T[] => {
    return data.slice(
      (currentPage - 1) * pageSize,
      currentPage * pageSize
    );
  };

  // Calculate displayed item range
  const displayRange = useMemo(() => {
    const start = totalCount === 0 ? 0 : (currentPage - 1) * pageSize + 1;
    const end = Math.min(currentPage * pageSize, totalCount);
    return { start, end };
  }, [currentPage, pageSize, totalCount]);

  return {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    handlePageInput,
    getPaginatedData,
    displayRange,
    totalCount
  };
};

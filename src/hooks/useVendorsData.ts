
import { useState, useEffect } from "react";
import { Vendor } from "@/types/vendors";
import { mockVendorsSanJose, mockVendorsBengaluru } from "@/data/mockVendors";
import { useAccount } from "@/contexts/AccountContext";

export const useVendorsData = () => {
  const { currentAccount } = useAccount();
  const [vendorsData, setVendorsData] = useState<Vendor[]>([]);

  useEffect(() => {
    if (currentAccount.name === "San Jose") {
      setVendorsData(mockVendorsSanJose);
    } else {
      setVendorsData(mockVendorsBengaluru);
    }
  }, [currentAccount]);

  return {
    vendors: vendorsData,
    setVendors: setVendorsData
  };
};

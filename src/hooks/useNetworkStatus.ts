
import { useState, useEffect } from 'react';

export interface NetworkStatus {
  isOnline: boolean;
  isSlowConnection: boolean;
  connectionType: string | null;
  lastDisconnected: Date | null;
  retryCount: number;
}

export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    isSlowConnection: false,
    connectionType: null,
    lastDisconnected: null,
    retryCount: 0
  });

  useEffect(() => {
    const updateOnlineStatus = () => {
      const isOnline = navigator.onLine;
      
      setNetworkStatus(prev => ({
        ...prev,
        isOnline,
        lastDisconnected: !isOnline ? new Date() : prev.lastDisconnected,
        retryCount: isOnline ? 0 : prev.retryCount
      }));
    };

    const checkConnectionSpeed = async () => {
      const startTime = Date.now();
      try {
        await fetch('/api/ping', { method: 'HEAD', cache: 'no-cache' });
        const endTime = Date.now();
        const isSlowConnection = (endTime - startTime) > 3000; // 3 seconds threshold
        
        setNetworkStatus(prev => ({
          ...prev,
          isSlowConnection
        }));
      } catch (error) {
        setNetworkStatus(prev => ({
          ...prev,
          isSlowConnection: true
        }));
      }
    };

    const getConnectionType = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      return connection ? connection.effectiveType : null;
    };

    // Initial connection type check
    setNetworkStatus(prev => ({
      ...prev,
      connectionType: getConnectionType()
    }));

    // Event listeners
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Check connection speed periodically when online
    const speedCheckInterval = setInterval(() => {
      if (navigator.onLine) {
        checkConnectionSpeed();
      }
    }, 30000); // Check every 30 seconds

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
      clearInterval(speedCheckInterval);
    };
  }, []);

  const retry = () => {
    setNetworkStatus(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1
    }));
  };

  return { networkStatus, retry };
}

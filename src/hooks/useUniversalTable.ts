
import { useState, useMemo, useCallback } from 'react';
import { UniversalTableState, UniversalTableFilter, UniversalTableSort } from '@/components/universal-table/types/UniversalTableTypes';

interface UseUniversalTableProps<T> {
  data: T[];
  initialState?: Partial<UniversalTableState>;
  rowIdField?: keyof T;
}

export function useUniversalTable<T>({
  data,
  initialState = {},
  rowIdField = 'id' as keyof T
}: UseUniversalTableProps<T>) {
  const [state, setState] = useState<UniversalTableState>({
    currentPage: 1,
    pageSize: 10,
    sortBy: null,
    filters: [],
    selectedRows: [],
    expandedRows: [],
    ...initialState
  });

  // Filter data
  const filteredData = useMemo(() => {
    if (state.filters.length === 0) return data;
    
    return data.filter(item => {
      return state.filters.every(filter => {
        const value = (item as any)[filter.field];
        const filterValue = filter.value;
        
        if (filterValue === null || filterValue === undefined || filterValue === '') {
          return true;
        }
        
        switch (filter.operator || 'contains') {
          case 'equals':
            return value === filterValue;
          case 'contains':
            return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
          case 'startsWith':
            return String(value).toLowerCase().startsWith(String(filterValue).toLowerCase());
          case 'endsWith':
            return String(value).toLowerCase().endsWith(String(filterValue).toLowerCase());
          case 'gt':
            return Number(value) > Number(filterValue);
          case 'lt':
            return Number(value) < Number(filterValue);
          case 'gte':
            return Number(value) >= Number(filterValue);
          case 'lte':
            return Number(value) <= Number(filterValue);
          default:
            return true;
        }
      });
    });
  }, [data, state.filters]);

  // Sort data
  const sortedData = useMemo(() => {
    if (!state.sortBy) return filteredData;
    
    return [...filteredData].sort((a, b) => {
      const aValue = (a as any)[state.sortBy!.field];
      const bValue = (b as any)[state.sortBy!.field];
      
      if (aValue === bValue) return 0;
      
      const comparison = aValue > bValue ? 1 : -1;
      return state.sortBy!.direction === 'desc' ? -comparison : comparison;
    });
  }, [filteredData, state.sortBy]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (state.currentPage - 1) * state.pageSize;
    const endIndex = startIndex + state.pageSize;
    return sortedData.slice(startIndex, endIndex);
  }, [sortedData, state.currentPage, state.pageSize]);

  // Handlers
  const handleSort = useCallback((sort: UniversalTableSort | null) => {
    setState(prev => ({ ...prev, sortBy: sort, currentPage: 1 }));
  }, []);

  const handleFilter = useCallback((filters: UniversalTableFilter[]) => {
    setState(prev => ({ ...prev, filters, currentPage: 1 }));
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
  }, []);

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setState(prev => ({ ...prev, pageSize, currentPage: 1 }));
  }, []);

  const handleSelectionChange = useCallback((selectedIds: string[]) => {
    setState(prev => ({ ...prev, selectedRows: selectedIds }));
  }, []);

  const handleRowExpand = useCallback((expandedIds: string[]) => {
    setState(prev => ({ ...prev, expandedRows: expandedIds }));
  }, []);

  const clearSelection = useCallback(() => {
    setState(prev => ({ ...prev, selectedRows: [] }));
  }, []);

  const selectAll = useCallback(() => {
    const allIds = sortedData.map(item => String((item as any)[rowIdField]));
    setState(prev => ({ ...prev, selectedRows: allIds }));
  }, [sortedData, rowIdField]);

  const clearFilters = useCallback(() => {
    setState(prev => ({ ...prev, filters: [], currentPage: 1 }));
  }, []);

  const getSelectedItems = useCallback(() => {
    return data.filter(item => 
      state.selectedRows.includes(String((item as any)[rowIdField]))
    );
  }, [data, state.selectedRows, rowIdField]);

  return {
    // State
    state,
    
    // Computed data
    filteredData,
    sortedData,
    paginatedData,
    totalCount: filteredData.length,
    totalPages: Math.ceil(filteredData.length / state.pageSize),
    
    // Handlers
    handleSort,
    handleFilter,
    handlePageChange,
    handlePageSizeChange,
    handleSelectionChange,
    handleRowExpand,
    
    // Utilities
    clearSelection,
    selectAll,
    clearFilters,
    getSelectedItems,
  };
}

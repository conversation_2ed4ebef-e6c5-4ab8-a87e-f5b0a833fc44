
import { useState, useMemo } from "react";
import { Material } from "@/types/inventory";

export const useInventoryPagination = (materials: Material[]) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return materials.slice(startIndex, endIndex);
  }, [materials, currentPage, pageSize]);
  
  const totalPages = useMemo(() => {
    return Math.ceil(materials.length / pageSize);
  }, [materials.length, pageSize]);

  // When changing page size, make sure we're still on a valid page
  const handlePageSizeChange = (newPageSize: number) => {
    const newTotalPages = Math.ceil(materials.length / newPageSize);
    if (currentPage > newTotalPages) {
      setCurrentPage(Math.max(1, newTotalPages));
    }
    setPageSize(newPageSize);
  };

  return {
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize: handlePageSizeChange,
    paginatedData,
    totalPages
  };
};

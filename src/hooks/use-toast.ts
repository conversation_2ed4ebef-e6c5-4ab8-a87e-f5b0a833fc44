
import { toast as sonnerToast } from "sonner"

type ToasterToast = {
  id: string;
  title?: string;
  description?: string;
  variant?: "default" | "destructive";
  action?: React.ReactNode;
  [key: string]: any;
}

export type ToastProps = {
  title?: string
  description?: string
  variant?: "default" | "destructive"
  action?: React.ReactNode
}

export const toasts: ToasterToast[] = []

export function toast({ title, description, variant = "default", ...props }: ToastProps) {
  const id = Math.random().toString(36).slice(2, 10)
  
  sonnerToast(title, {
    id,
    description,
    ...props,
  })
  
  return {
    id,
    title,
    description,
    variant,
    ...props,
  }
}

export function useToast() {
  return {
    toast,
    toasts,
  }
}

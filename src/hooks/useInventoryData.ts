
import { useState, useMemo } from "react";
import { Material } from "@/types/inventory";
import { useAccount } from "@/contexts/AccountContext";
import { mockMaterialsSanJose, mockMaterialsPuducherry } from "@/data/mockMaterials";

export const useInventoryData = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const { currentAccount } = useAccount();

  // Get materials based on the current account location
  const materials = useMemo(() => {
    if (currentAccount.location === "San Jose") {
      return mockMaterialsSanJose;
    } else {
      return mockMaterialsPuducherry;
    }
  }, [currentAccount.location]);

  // Filter materials based on search term
  const filteredMaterials = useMemo(() => {
    if (!searchTerm) return materials;
    
    const lowercasedFilter = searchTerm.toLowerCase();
    
    return materials.filter(material => {
      return (
        material.id.toLowerCase().includes(lowercasedFilter) ||
        material.name.toLowerCase().includes(lowercasedFilter) ||
        material.type.toLowerCase().includes(lowercasedFilter) ||
        material.category.toLowerCase().includes(lowercasedFilter) ||
        material.uom.toLowerCase().includes(lowercasedFilter)
      );
    });
  }, [materials, searchTerm]);

  return {
    searchTerm,
    setSearchTerm,
    materials,
    filteredMaterials
  };
};


import { useState, useMemo } from "react";
import { Vendor } from "@/types/vendors";

interface UseVendorPaginationProps {
  totalCount: number;
  initialPageSize?: number;
  initialPage?: number;
}

export const useVendorPagination = ({
  totalCount,
  initialPageSize = 10,
  initialPage = 1,
}: UseVendorPaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const totalPages = useMemo(() => {
    return Math.ceil(totalCount / pageSize);
  }, [totalCount, pageSize]);

  // Ensure current page is always valid
  useMemo(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  const getPaginatedData = (data: Vendor[]) => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return data.slice(start, end);
  };

  return {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange: setCurrentPage,
    handlePageSizeChange: setPageSize,
    getPaginatedData
  };
};

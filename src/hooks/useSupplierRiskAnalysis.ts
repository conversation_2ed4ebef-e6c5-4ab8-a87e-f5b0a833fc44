
import { SupplierRiskData } from "@/types/supplierRiskHeatmap";

export const useSupplierRiskAnalysis = () => {
  const getRiskLevel = (riskScore: number): 'Low' | 'Medium' | 'High' => {
    if (riskScore < 30) return 'Low';
    if (riskScore < 70) return 'Medium';
    return 'High';
  };
  
  const getAuditScoreLevel = (score: number): 'Low' | 'Medium' | 'High' => {
    if (score >= 85) return 'High';
    if (score >= 70) return 'Medium';
    return 'Low';
  };
  
  const getQuadrantLabel = (riskScore: number, auditScore: number): string => {
    const riskLevel = getRiskLevel(riskScore);
    const auditLevel = getAuditScoreLevel(auditScore);
    
    if (riskLevel === 'Low' && auditLevel === 'High') return 'Preferred';
    if (riskLevel === 'Low' && auditLevel === 'Medium') return 'Reliable';
    if (riskLevel === 'Medium' && auditLevel === 'High') return 'Monitor';
    if (riskLevel === 'Medium' && auditLevel === 'Medium') return 'Develop';
    if (riskLevel === 'High' && auditLevel === 'High') return 'Mitigate Risk';
    if (riskLevel === 'High' && auditLevel === 'Medium') return 'Action Required';
    return 'High Risk';
  };
  
  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    }
    return `$${value}`;
  };
  
  const getQuadrantColor = (riskScore: number, auditScore: number): string => {
    const riskLevel = getRiskLevel(riskScore);
    const auditLevel = getAuditScoreLevel(auditScore);
    
    if (riskLevel === 'Low' && auditLevel === 'High') return '#4ade80'; // green
    if (riskLevel === 'Low' && auditLevel === 'Medium') return '#60a5fa'; // blue
    if (riskLevel === 'Medium' && auditLevel === 'High') return '#a78bfa'; // purple
    if (riskLevel === 'Medium' && auditLevel === 'Medium') return '#fbbf24'; // yellow
    if (riskLevel === 'High' && auditLevel === 'High') return '#fb923c'; // orange
    if (riskLevel === 'High' && auditLevel === 'Medium') return '#f87171'; // light red
    return '#ef4444'; // red
  };

  return {
    getRiskLevel,
    getAuditScoreLevel,
    getQuadrantLabel,
    formatCurrency,
    getQuadrantColor
  };
};

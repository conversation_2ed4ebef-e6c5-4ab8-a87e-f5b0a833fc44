
import { useState, useMemo } from "react";
import { MessageThread, SupplierMessage, MessageTag, EmailTemplate } from "@/types/supplierMessages";
import { mockMessageThreads, mockEmailTemplates } from "@/data/mockSupplierMessages";
import { useToast } from "@/hooks/use-toast";

export const useSupplierMessages = () => {
  const [threads, setThreads] = useState<MessageThread[]>(mockMessageThreads);
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<MessageTag[]>([]);
  const { toast } = useToast();

  const selectedThread = useMemo(() => {
    if (!selectedThreadId) return null;
    return threads.find(thread => thread.id === selectedThreadId) || null;
  }, [threads, selectedThreadId]);

  const filteredThreads = useMemo(() => {
    return threads.filter(thread => {
      // Filter by search query
      const matchesSearch = searchQuery === "" || 
        thread.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
        thread.messages.some(msg => 
          msg.body.toLowerCase().includes(searchQuery.toLowerCase()) ||
          msg.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
          msg.to.toLowerCase().includes(searchQuery.toLowerCase())
        );
      
      // Filter by tags
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(tag => thread.tags.includes(tag));
      
      return matchesSearch && matchesTags;
    });
  }, [threads, searchQuery, selectedTags]);

  const markThreadAsRead = (threadId: string) => {
    setThreads(prevThreads => 
      prevThreads.map(thread => {
        if (thread.id === threadId) {
          return {
            ...thread,
            messages: thread.messages.map(msg => ({ ...msg, isRead: true }))
          };
        }
        return thread;
      })
    );
  };

  const sendReply = (threadId: string, message: Omit<SupplierMessage, 'id' | 'threadId'>) => {
    const newMessageId = `msg-${Date.now()}`;
    
    setThreads(prevThreads => 
      prevThreads.map(thread => {
        if (thread.id === threadId) {
          const newMessage: SupplierMessage = {
            ...message,
            id: newMessageId,
            threadId: threadId,
          };
          
          return {
            ...thread,
            messages: [...thread.messages, newMessage],
            lastMessageDate: message.timestamp
          };
        }
        return thread;
      })
    );
    
    toast({
      title: "Message sent",
      description: `Reply sent to ${message.to}`
    });
  };

  return {
    threads: filteredThreads,
    selectedThread,
    selectedThreadId,
    emailTemplates: mockEmailTemplates,
    setSelectedThreadId,
    searchQuery,
    setSearchQuery,
    selectedTags,
    setSelectedTags,
    markThreadAsRead,
    sendReply
  };
};

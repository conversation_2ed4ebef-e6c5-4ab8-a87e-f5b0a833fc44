
import { useState, useMemo } from "react";

export const useSupplierQualityFilters = () => {
  const [filters, setFilters] = useState({
    status: "all",
    risk: "all"
  });

  const filteredData = useMemo(() => ({
    auditScore: 89,
    openCapas: 8,
    expiringCertificates: 3,
    approvedSuppliers: 24,
    pendingApproval: 5,
    requiresFollowUp: 2,
    disapproved: 1,
    riskData: [
      { name: "Low Risk", value: 65, color: "#10b981" },
      { name: "Medium Risk", value: 25, color: "#f59e0b" },
      { name: "High Risk", value: 10, color: "#ef4444" }
    ]
  }), [filters]);

  return {
    filteredData,
    filters,
    setFilters
  };
};

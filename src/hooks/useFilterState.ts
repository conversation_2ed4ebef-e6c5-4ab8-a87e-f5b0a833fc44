
import { useState } from "react";
import { FilterState } from "@/types/filter";

interface UseFilterStateProps {
  initialFilters?: FilterState;
}

export const useFilterState = (props?: UseFilterStateProps) => {
  const defaultFilters: FilterState = {
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: []
  };

  const [appliedFilters, setAppliedFilters] = useState<FilterState>(props?.initialFilters || defaultFilters);
  const [isFilterSidePanelOpen, setIsFilterSidePanelOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>(props?.initialFilters || defaultFilters);

  const toggleFilterSidePanel = () => {
    setIsFilterSidePanelOpen(prev => !prev);
  };

  const handleFilterChange = (filterType: keyof FilterState, selectedIds: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: selectedIds
    }));
  };

  const handleResetFilters = () => {
    setFilters(defaultFilters);
  };

  const countActiveFilters = (): number => {
    return Object.values(filters).reduce((count, filterArray) => count + filterArray.length, 0);
  };

  const handleApplyFilters = () => {
    setAppliedFilters(filters);
  };

  return {
    appliedFilters,
    setAppliedFilters,
    isFilterSidePanelOpen,
    toggleFilterSidePanel,
    filters,
    setFilters,
    handleFilterChange,
    handleResetFilters,
    countActiveFilters,
    handleApplyFilters
  };
};


import { useState, useMemo } from "react";
import { mockSupplierInsights } from "@/data/mockSupplierInsights";
import { SupplierInsight, InsightType, InsightCategory, InsightStatus } from "@/types/supplierInsights";
import { useToast } from "@/hooks/use-toast";

export const useSupplierInsights = () => {
  const [insights, setInsights] = useState<SupplierInsight[]>(mockSupplierInsights);
  const [typeFilter, setTypeFilter] = useState<InsightType | 'All'>('All');
  const [categoryFilter, setCategoryFilter] = useState<InsightCategory | 'All'>('All');
  const [statusFilter, setStatusFilter] = useState<InsightStatus | 'All'>('All');
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedInsightId, setSelectedInsightId] = useState<string | null>(null);
  
  const { toast } = useToast();
  
  const filteredInsights = useMemo(() => {
    return insights.filter(insight => {
      const typeMatch = typeFilter === 'All' || insight.type === typeFilter;
      const categoryMatch = categoryFilter === 'All' || insight.category === categoryFilter;
      const statusMatch = statusFilter === 'All' || insight.status === statusFilter;
      const searchMatch = searchQuery === "" || 
        insight.supplierName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        insight.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        insight.title.toLowerCase().includes(searchQuery.toLowerCase());
        
      return typeMatch && categoryMatch && statusMatch && searchMatch;
    }).sort((a, b) => b.urgency - a.urgency); // Sort by urgency high to low
  }, [insights, typeFilter, categoryFilter, statusFilter, searchQuery]);
  
  const selectedInsight = useMemo(() => {
    if (!selectedInsightId) return null;
    return insights.find(insight => insight.id === selectedInsightId) || null;
  }, [insights, selectedInsightId]);
  
  const updateInsightStatus = (id: string, status: InsightStatus) => {
    setInsights(prevInsights => 
      prevInsights.map(insight => 
        insight.id === id ? { ...insight, status } : insight
      )
    );
    
    const statusMessages = {
      'In Progress': 'Insight marked as in progress',
      'Resolved': 'Insight resolved successfully',
      'Dismissed': 'Insight has been dismissed',
      'New': 'Insight reset to new status'
    };
    
    toast({
      title: "Status Updated",
      description: statusMessages[status],
    });
  };
  
  const addFollowUpAction = (id: string, action: string) => {
    setInsights(prevInsights => 
      prevInsights.map(insight => {
        if (insight.id === id) {
          const updatedActions = [...(insight.followUpActions || []), action];
          return {
            ...insight,
            followUpActions: updatedActions
          };
        }
        return insight;
      })
    );
    
    toast({
      title: "Action Added",
      description: "Follow-up action has been added",
    });
  };
  
  return {
    insights: filteredInsights,
    selectedInsight,
    typeFilter,
    categoryFilter,
    statusFilter,
    searchQuery,
    setTypeFilter,
    setCategoryFilter,
    setStatusFilter,
    setSearchQuery,
    setSelectedInsightId,
    updateInsightStatus,
    addFollowUpAction
  };
};

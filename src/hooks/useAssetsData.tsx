
import { useState, useMemo } from "react";
import { Monitor, Server, Printer, Laptop } from "lucide-react";
import { Asset, AssetType } from "@/types/assets";
import { useAccount } from "@/contexts/AccountContext";

export const useAssetsData = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const { currentAccount } = useAccount();
  
  const assetTypes: AssetType[] = [
    { name: "Monitors", count: currentAccount.assets.filter(asset => asset.type === "Monitor").length, icon: <Monitor className="h-5 w-5 text-teal-600" /> },
    { name: "Servers", count: currentAccount.assets.filter(asset => asset.type === "Server").length, icon: <Server className="h-5 w-5 text-teal-600" /> },
    { name: "Printers", count: currentAccount.assets.filter(asset => asset.type === "Printer").length, icon: <Printer className="h-5 w-5 text-teal-600" /> },
    { name: "Laptops", count: currentAccount.assets.filter(asset => asset.type === "Laptop").length, icon: <Laptop className="h-5 w-5 text-teal-600" /> },
  ];

  const assets = currentAccount.assets;

  const filteredAssets = useMemo(() => {
    return assets.filter(asset => 
      asset.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.status.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [assets, searchQuery]);

  const filterAssetsByStatus = (status: Asset['status']) => {
    return filteredAssets.filter(asset => asset.status === status);
  };

  return {
    searchQuery,
    setSearchQuery,
    assetTypes,
    assets,
    filteredAssets,
    filterAssetsByStatus
  };
};

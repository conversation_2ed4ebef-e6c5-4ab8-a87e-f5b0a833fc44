
export interface Asset {
  id: string;
  name: string;
  type: string;
  description: string;
  location: string;
  owner: string;
  assignedTo: string;
  status: "In Use" | "Available" | "Under Maintenance" | "Retired" | "Breakdown";
  lastUpdated: string;
  purchaseDate: string;
  calibrationDate: string;
  calibrationCertNo: string;
  calibrationPeriod: string;
}

export interface AssetType {
  name: string;
  count: number;
  icon: React.ReactNode;
}

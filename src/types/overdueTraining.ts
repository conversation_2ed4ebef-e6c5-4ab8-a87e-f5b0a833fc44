
export interface OverdueTraining {
  id: string;
  employeeName: string;
  trainingTitle: string;
  dueDate: string;
  daysOverdue: number;
  managerName: string;
  lastReminderSent: string;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  department: string;
  assignedRoles: string[];
  employeeId: string;
  trainingId: string;
}

export interface OverdueFilters {
  department: string;
  role: string;
  overdueDays: string;
}

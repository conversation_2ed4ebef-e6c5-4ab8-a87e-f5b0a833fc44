
export type NCStatus = 'Open' | 'In Review' | 'Closed';
export type NCSeverity = 'Critical' | 'Major' | 'Minor';

export interface SupplierNonConformance {
  id: string;
  ncNumber: string;
  supplierName: string;
  issueDescription: string;
  status: NCStatus;
  severity: NCSeverity;
  capaLinked: boolean;
  dateReported: string;
  dateResolved?: string;
  responsiblePerson?: string;
  rootCause?: string;
  containmentActions?: string;
}

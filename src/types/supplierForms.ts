
export type QuestionType = 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'file' | 'yesno';

export interface FormQuestion {
  id: string;
  type: QuestionType;
  text: string;
  required: boolean;
  options?: string[];
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  questions: FormQuestion[];
}

export interface SupplierForm {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  sections: FormSection[];
  recipientCount: number;
  status: {
    completed: number;
    pending: number;
    overdue: number;
  };
}

export interface FormResponse {
  id: string;
  formId: string;
  supplierId: string;
  supplierName: string;
  status: 'completed' | 'pending' | 'overdue';
  submittedAt?: string;
  answers: Record<string, any>;
}

export interface FormRecipient {
  id: string;
  name: string;
  email: string;
  company: string;
  status: 'completed' | 'pending' | 'overdue';
}


export type DocumentStatus = 'Valid' | 'Expiring Soon' | 'Expired';

export interface SupplierDocument {
  id: string;
  name: string;
  fileName: string;
  uploadDate: string;
  expiryDate: string;
  status: DocumentStatus;
  version: number;
  reviewer?: string;
  supplierName: string;
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: number;
  uploadDate: string;
  uploadedBy: string;
  fileName: string;
  notes?: string;
}


export interface SustainabilitySupplier {
  id: string;
  name: string;
  status: 'Compliant' | 'Watchlist' | 'High Risk';
  lastAudit: string;
  carbonFootprint: number;
  overallScore: number;
  declarationUploaded: boolean;
  declarationDate?: string;
  contact: {
    name: string;
    email: string;
    phone: string;
  };
  assessments: {
    environmental: SustainabilityAssessment;
    labor: SustainabilityAssessment;
    ethical: SustainabilityAssessment;
  };
}

export interface SustainabilityAssessment {
  selfScore: number;
  auditScore?: number;
  lastUpdated: string;
  items: AssessmentItem[];
}

export interface AssessmentItem {
  id: string;
  question: string;
  selfRating: number;
  auditRating?: number;
  notes?: string;
  evidence?: string[];
}

export interface SustainabilityDeclaration {
  id: string;
  supplierId: string;
  fileName: string;
  uploadDate: string;
  status: 'Pending Review' | 'Approved' | 'Rejected';
  signedBy: string;
  validUntil: string;
}

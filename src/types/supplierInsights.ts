
export type InsightType = 'Alert' | 'Insight' | 'Recommendation';
export type InsightStatus = 'New' | 'In Progress' | 'Resolved' | 'Dismissed';
export type InsightCategory = 'Quality' | 'Delivery' | 'Compliance' | 'Risk' | 'Audit' | 'Financial';

export interface SupplierInsight {
  id: string;
  supplierId: string;
  supplierName: string;
  type: InsightType;
  category: InsightCategory;
  title: string;
  description: string;
  detectedDate: string;
  status: InsightStatus;
  urgency: number; // 1-10 scale with 10 being most urgent
  relatedData?: {
    type: string;
    value: string | number;
    trend?: 'up' | 'down' | 'stable';
  }[];
  followUpActions?: string[];
}

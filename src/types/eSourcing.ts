
export interface RFQBidder {
  supplierId: string;
  supplierName: string;
  bidAmount: number;
  currency: string;
  submittedAt: string;
  documents: string[];
  notes?: string;
  status: 'Pending' | 'Submitted' | 'Withdrawn';
}

export interface RFQDocument {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  size: string;
}

export interface RFQ {
  id: string;
  title: string;
  description: string;
  category: string;
  publishDate: string;
  closingDate: string;
  status: 'Draft' | 'Published' | 'Closed' | 'Awarded';
  quantity: number;
  unit: string;
  estimatedBudget?: number;
  bidders: RFQBidder[];
  documents: RFQDocument[];
  requirements: string[];
  evaluationCriteria: {
    price: number;
    quality: number;
    delivery: number;
    experience: number;
  };
  awardedTo?: string;
}

export interface LiveAuction {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  currentLeader: string;
  currentBid: number;
  participants: number;
  status: 'Upcoming' | 'Live' | 'Ended';
}

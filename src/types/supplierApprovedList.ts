
export type ApprovalType = 'ASL' | 'AML' | 'Both' | 'None';

export interface ApprovalCategory {
  name: string;
  status: 'Approved' | 'Restricted' | 'Suspended';
  validFrom: string;
  validTo: string;
}

export interface ApprovedSupplier {
  id: string;
  name: string;
  approvalType: ApprovalType;
  riskTier: 'Low' | 'Medium' | 'High';
  qualificationBasis: string[];
  categories: ApprovalCategory[];
  lastAuditDate?: string;
  lastAuditScore?: number;
  certifications: string[];
  notes?: string;
}

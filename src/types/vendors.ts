
export interface Vendor {
  id: string;
  name: string;
  productService: string;
  contactName?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  description?: string;
  assignee?: string;
  status: 'Under evaluation' | 'Approved' | 'Rejected' | 'Pending';
  lastEvaluationDate?: string;
  nextEvaluationDate?: string;
  reviewPeriod?: 'Quarterly' | 'Bi-annually' | 'Annually';
  dateOfContact?: string;
}

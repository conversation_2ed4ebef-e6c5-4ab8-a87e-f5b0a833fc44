
export interface CommentAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadedAt: Date;
}

export interface CommentReaction {
  id: string;
  userId: string;
  type: 'like' | 'dislike' | 'heart' | 'thumbs_up';
  createdAt: Date;
}

export interface CommentEdit {
  id: string;
  content: string;
  editedAt: Date;
  editedBy: string;
}

export interface CommentAuthor {
  id: string;
  name: string;
  avatar?: string;
  role: string;
}

export interface Comment {
  id: string;
  documentId: string;
  content: string;
  author: CommentAuthor;
  createdAt: Date;
  updatedAt?: Date;
  editHistory?: CommentEdit[];
  attachments?: CommentAttachment[];
  replies?: Comment[];
  parentId?: string;
  isDeleted: boolean;
  reactions?: CommentReaction[];
  mentions?: string[];
  isExpanded?: boolean;
}

export interface CommentPermissions {
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canReply: boolean;
  canModerate: boolean;
}

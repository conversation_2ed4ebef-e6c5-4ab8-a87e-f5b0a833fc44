
export interface Training {
  id: string;
  title: string;
  type: string;
  description: string;
  estimatedDuration: string;
  category: string;
  language: string;
  roleApplicability: string[];
  lastUpdated: string;
  createdBy: string;
  tags: string[];
  difficulty: string;
  requirements?: string[];
  learningObjectives?: string[];
}

export interface EmployeeTraining {
  id: string;
  trainingId: string;
  employeeId: string;
  training: Training;
  status: "Not Started" | "In Progress" | "Completed" | "Overdue";
  progress: number;
  assignedDate: string;
  dueDate: string;
  timeSpent: string;
  completedDate: string | null;
  quizScore: number | null;
  assignedBy: string;
  notes?: string;
  attempts?: number;
  lastAccessed?: string | null;
  certificateId?: string;
}

export interface TrainingAssignment {
  id: string;
  trainingName: string;
  trainingType: string;
  assignedTo: Array<{
    id: string;
    name: string;
    status: string;
    completedDate?: string;
    timeSpent?: string;
  }>;
  assignedRoles: string[];
  department: string;
  status: string;
  dueDate: string;
  assignedDate: string;
  averageTimeSpent: string;
  completionPercentage: number;
  totalAssigned: number;
  completed: number;
  priority: string;
}


export type ApprovalType = 'ASL' | 'AML';
export type RiskTier = 'Low' | 'Medium' | 'High';

export interface SupplierAslAml {
  id: string;
  name: string;
  approvalType: ApprovalType;
  riskTier: RiskTier;
  status: string;
  lastAudit: string;
  nextReview: string;
  qualificationBasis?: string;
  categories?: string[];
  certifications?: string[];
}

export interface ApprovedSupplier {
  id: string;
  name: string;
  approvalType: ApprovalType;
  riskTier: RiskTier;
  status: string;
  lastAudit: string;
  nextReview: string;
  qualificationBasis: string;
  categories: string[];
  certifications: string[];
}

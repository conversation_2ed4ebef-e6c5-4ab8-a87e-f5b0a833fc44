
export interface SupplierAudit {
  id: string;
  title: string;
  type: string;
  status: 'Scheduled' | 'In Progress' | 'Completed' | 'Overdue';
  dueDate: string;
  auditor?: string;
  priority: 'Low' | 'Medium' | 'High';
}

export interface SupplierDocument {
  id: string;
  title: string;
  type: string;
  status: 'Valid' | 'Expiring Soon' | 'Expired' | 'Pending Upload';
  dueDate?: string;
  expiryDate?: string;
  uploadDate?: string;
}

export interface CAPAAssignment {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'Open' | 'In Progress' | 'Under Review' | 'Completed';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  assignedBy: string;
}

export interface ContractRenewal {
  id: string;
  contractTitle: string;
  currentEndDate: string;
  renewalDueDate: string;
  status: 'Action Required' | 'Under Review' | 'Renewed';
  value: number;
  reminderDays: number;
}

export interface ComplianceDeclaration {
  id: string;
  title: string;
  type: string;
  dueDate: string;
  status: 'Not Started' | 'In Progress' | 'Submitted' | 'Overdue';
  description: string;
}

export interface DocumentLanguage {
  code: string;
  name: string;
  flag: string;
}

export interface MultilingualDocument {
  id: string;
  title: string;
  language: string;
  languageCode: string;
  type: string;
  effectiveDate: string;
  lastUpdated: string;
  status: 'Active' | 'Draft' | 'Archived';
  version: string;
  fileSize: string;
  description?: string;
  tags: string[];
}

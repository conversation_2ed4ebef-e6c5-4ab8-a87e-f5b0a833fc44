
export interface Contact {
  name: string;
  email: string;
  phone: string;
  role: string;
}

export interface Contract {
  id: string;
  name: string;
  title: string;
  type: string;
  status: string;
  startDate: string;
  endDate: string;
  value: number;
  renewalDate: string;
}

export interface Communication {
  id: string;
  type: string;
  subject: string;
  date: string;
  status: string;
  priority: string;
}

export interface PerformanceHistory {
  period: string;
  score: number;
  date: string;
  qualityScore: number;
  deliveryScore: number;
  costScore: number;
  otifScore: number;
}

export interface PerformanceRecord {
  period: string;
  score: number;
  qualityScore: number;
  deliveryScore: number;
  costScore: number;
  otifScore?: number;
}

export interface RiskEvent {
  id: string;
  type: string;
  description: string;
  severity: string;
  status: string;
  date: string;
  mitigationPlan: string;
}

export interface MonthlyOTIFData {
  month: string;
  otif: number;
  onTime: number;
  inFull: number;
  orders: number;
}

export interface DeliveryPerformance {
  onTime: number;
  late: number;
  early: number;
}

export interface QualityMetrics {
  firstPassYield: number;
  defectRate: number;
  customerComplaints: number;
}

export interface OTIFMetrics {
  currentOTIF: number;
  targetOTIF: number;
  onTimeDelivery: number;
  inFullDelivery: number;
  trend: string;
  monthlyData: MonthlyOTIFData[];
  deliveryPerformance: DeliveryPerformance;
  qualityMetrics: QualityMetrics;
}

export interface SupplierRelationship {
  id: string;
  name: string;
  category: string;
  status: string;
  overallScore: number;
  contact: Contact;
  contracts: Contract[];
  communications: Communication[];
  performanceHistory: PerformanceHistory[];
  riskEvents: RiskEvent[];
  otifMetrics: OTIFMetrics;
}

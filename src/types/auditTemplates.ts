
export interface AuditTemplate {
  id: string;
  name: string;
  description?: string;
  version: string;
  sections: TemplateSection[];
  totalQuestions: number;
  defaultScoring: 'binary' | 'weighted';
  supplierCategory: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  tags: string[];
}

export interface TemplateSection {
  id: string;
  name: string;
  description?: string;
  questions: TemplateQuestion[];
  order: number;
  weight?: number;
}

export interface TemplateQuestion {
  id: string;
  question: string;
  description?: string;
  scoring: 'binary' | 'weighted';
  weight?: number;
  category: string;
  required: boolean;
  evidenceRequired: boolean;
  order: number;
}

export interface TemplateVersion {
  id: string;
  templateId: string;
  version: string;
  changes: string[];
  createdBy: string;
  createdAt: string;
}

export type ScoringType = 'binary' | 'weighted';
export type SupplierCategory = 'Electronics' | 'Mechanical' | 'Chemical' | 'Software' | 'General';

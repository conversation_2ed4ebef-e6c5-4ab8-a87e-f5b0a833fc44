
export interface TrainingAssignment {
  id: string;
  trainingName: string;
  trainingType: 'Video' | 'PDF' | 'Quiz' | 'Interactive';
  assignedTo: AssignedEmployee[];
  assignedRoles: string[];
  department: string;
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Overdue';
  dueDate: string;
  assignedDate: string;
  averageTimeSpent: string;
  completionPercentage: number;
  totalAssigned: number;
  completed: number;
  priority: 'High' | 'Medium' | 'Low';
}

export interface AssignedEmployee {
  id: string;
  name: string;
  avatar?: string;
  status: 'Not Started' | 'In Progress' | 'Completed' | 'Overdue';
  completedDate?: string;
  timeSpent?: string;
}

export interface TrainingFilters {
  role: string;
  department: string;
  status: string;
  dueDateRange: string;
}

export interface TrainingMetrics {
  totalAssigned: number;
  completed: number;
  overdue: number;
  inProgress: number;
  notStarted: number;
  averageTimeSpent: string;
  completionRate: number;
}


export interface RecurringTraining {
  id: string;
  trainingId: string;
  trainingTitle: string;
  recurrenceRule: RecurrenceRule;
  autoReassign: boolean;
  reminderSettings: ReminderSettings;
  isActive: boolean;
  createdDate: string;
  lastAssigned: string;
  nextDue: string;
  assignedRoles: string[];
  assignedEmployees: string[];
}

export interface RecurrenceRule {
  type: 'months' | 'years';
  interval: number; // e.g., 6 for every 6 months, 1 for every year
  description: string; // human readable description
}

export interface ReminderSettings {
  enabled: boolean;
  reminderDays: number[]; // days before due date to send reminders
  escalationDays: number; // days after due date to escalate
}

export interface RecertificationSchedule {
  id: string;
  recurringTrainingId: string;
  employeeId: string;
  employeeName: string;
  currentCycleStart: string;
  currentCycleDue: string;
  status: 'Upcoming' | 'Assigned' | 'Completed' | 'Overdue';
  completedDate?: string;
  certificateId?: string;
  nextCycleStart?: string;
  nextCycleDue?: string;
}

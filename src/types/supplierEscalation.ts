
export type EscalationStatus = "New" | "In Review" | "Escalated" | "Resolved";

export interface EscalationHistoryEvent {
  date: string;
  time: string;
  action: string;
  by: string;
}

export interface EscalationComment {
  author: string;
  date: string;
  time: string;
  text: string;
}

export interface SupplierEscalation {
  id: string;
  supplier: string;
  raisedBy: string;
  raisedDate: string;
  issue: string;
  impact: string;
  status: EscalationStatus;
  dueDate: string;
  assignedTo: string;
  history: EscalationHistoryEvent[];
  comments: EscalationComment[];
}

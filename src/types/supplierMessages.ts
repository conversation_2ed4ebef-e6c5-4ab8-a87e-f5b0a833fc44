
export type MessageTag = "Audit" | "NC" | "PO" | "CAPA" | "General" | "Urgent";

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: string;
  url: string;
}

export interface SupplierMessage {
  id: string;
  threadId: string;
  from: string;
  fromName: string;
  to: string;
  toName: string;
  subject?: string;
  body: string;
  timestamp: string;
  attachments: MessageAttachment[];
  isRead: boolean;
}

export interface MessageThread {
  id: string;
  subject: string;
  tags: MessageTag[];
  messages: SupplierMessage[];
  lastMessageDate: string;
  participants: string[];
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  tags: MessageTag[];
}

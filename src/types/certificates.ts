
export interface TrainingCertificate {
  id: string;
  trainingId: string;
  trainingName: string;
  employeeId: string;
  employeeName: string;
  completionDate: string;
  issueDate: string;
  certificateId: string;
  validUntil?: string;
  certificateType: 'PDF' | 'Digital Badge';
  status: 'Active' | 'Expired' | 'Expiring Soon';
  downloadUrl?: string;
  badgeUrl?: string;
  issuer: string;
  credentialHash?: string;
}

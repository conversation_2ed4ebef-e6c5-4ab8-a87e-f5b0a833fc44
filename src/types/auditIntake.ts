
export interface AuditIntake {
  id: string;
  vendorName: string;
  productLevel: string;
  auditDates: {
    startDate: string;
    endDate: string;
  };
  address: string;
  auditors: string[];
  status: 'draft' | 'in-progress' | 'completed';
  createdAt: string;
  updatedAt: string;
}

export interface AuditQuestion {
  id: string;
  question: string;
  score: 0 | 1;
  evidenceFiles: File[];
  auditorComments: string;
  required: boolean;
}

export interface AuditSection {
  id: string;
  name: string;
  questions: AuditQuestion[];
  completionStatus: 'not-started' | 'in-progress' | 'completed';
}

export interface AuditForm {
  id: string;
  intakeId: string;
  sections: AuditSection[];
  overallScore: number;
  createdAt: string;
  updatedAt: string;
}

export type AuditSectionType = 'receiving-inspection' | 'warehouse' | 'assembly' | 'clean-room';

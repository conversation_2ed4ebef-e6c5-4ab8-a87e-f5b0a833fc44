
export interface Folder {
  id: string;
  name: string;
  parentId: string | null;
  createdBy: string;
  createdDate: string;
  description?: string;
  path: string[];
  isShared?: boolean;
  permissions?: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
  };
}

export interface DocumentWithFolder {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId: string | null;
  folderPath?: string[];
  isFolder?: boolean;
}

export interface FolderNavigationState {
  currentFolderId: string | null;
  currentPath: string[];
  breadcrumbs: { id: string; name: string }[];
}

export type FolderAction = 
  | { type: 'NAVIGATE_TO_FOLDER'; folderId: string | null; path: string[] }
  | { type: 'CREATE_FOLDER'; folder: Folder }
  | { type: 'DELETE_FOLDER'; folderId: string }
  | { type: 'MOVE_ITEM'; itemId: string; targetFolderId: string | null }
  | { type: 'UPDATE_FOLDER'; folder: Folder };

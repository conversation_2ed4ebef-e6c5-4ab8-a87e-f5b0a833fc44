export type FieldType = 
  | 'text' 
  | 'textarea' 
  | 'select' 
  | 'checkbox' 
  | 'radio' 
  | 'file' 
  | 'date' 
  | 'number' 
  | 'rating' 
  | 'yesno'
  | 'sectionHeader'
  | 'table'
  | 'signature'
  | 'currency'
  | 'link'
  | 'image'
  | 'email'
  | 'phone'
  | 'address'
  | 'time'
  | 'datetime'
  | 'slider'
  | 'multiselect'
  | 'matrix'
  | 'location'
  | 'barcode'
  | 'calculation'
  | 'conditional'
  | 'repeater';

export type SupportedLanguage = 'en' | 'de' | 'es-mx' | 'fr';

export interface TranslatedText {
  en: string;
  de?: string;
  'es-mx'?: string;
  fr?: string;
}

export interface ConditionalOperator {
  type: 'AND' | 'OR';
}

export interface ConditionRule {
  fieldId: string;
  operator: '==' | '!=' | '>' | '<' | '>=' | '<=';
  value: string;
}

export interface ConditionalLogic {
  dependsOn: string;
  showWhen: string;
  conditions?: (ConditionRule | ConditionalOperator)[];
}

// Modified to allow mixed arrays
export type TranslatedOption = string | TranslatedText;
export type TranslatedArray = TranslatedOption[];

export interface FormField {
  id: string;
  type: FieldType;
  label: string | TranslatedText;
  required?: boolean;
  placeholder?: string | TranslatedText;
  description?: string | TranslatedText;
  options?: TranslatedArray;
  min?: number;
  max?: number;
  step?: number;
  conditionalLogic?: ConditionalLogic;
  helpText?: string | TranslatedText;
  allowAttachments?: boolean;
  scoreWeight?: number;
  reviewerNotes?: boolean;
  requireSignature?: boolean;
  columns?: TranslatedArray;
  rows?: TranslatedArray;
  currency?: string;
  linkUrl?: string;
}

export interface FormSection {
  id: string;
  title: string | TranslatedText;
  description?: string | TranslatedText;
  fields: FormField[];
}

export interface FormTemplate {
  id: string;
  name: string | TranslatedText;
  description?: string | TranslatedText;
  sections: FormSection[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  status: 'draft' | 'published';
  isTemplate: boolean;
  approvers?: string[];
  languages?: SupportedLanguage[];
  defaultLanguage?: SupportedLanguage;
}

export interface Language {
  code: SupportedLanguage;
  name: string;
  flag: string;
}

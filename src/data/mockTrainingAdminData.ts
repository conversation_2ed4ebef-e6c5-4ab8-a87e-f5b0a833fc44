
import { TrainingAssignment } from "@/types/trainingAdmin";

export const mockTrainingAssignments: TrainingAssignment[] = [
  {
    id: "TA-001",
    trainingName: "ISO 9001:2015 Quality Management Systems",
    trainingType: "Video",
    assignedTo: [
      { id: "EMP001", name: "<PERSON>", status: "Completed", completedDate: "2024-11-15", timeSpent: "2.5h" },
      { id: "EMP002", name: "<PERSON>", status: "In Progress", timeSpent: "1.2h" },
      { id: "EMP003", name: "<PERSON>", status: "Not Started" },
      { id: "EMP004", name: "<PERSON>", status: "Overdue" }
    ],
    assignedRoles: ["Quality Manager", "Process Engineer", "Auditor"],
    department: "Quality Assurance",
    status: "In Progress",
    dueDate: "2024-12-15",
    assignedDate: "2024-11-01",
    averageTimeSpent: "1.9h",
    completionPercentage: 25,
    totalAssigned: 4,
    completed: 1,
    priority: "High"
  },
  {
    id: "TA-002",
    trainingName: "Fire Safety and Emergency Procedures",
    trainingType: "<PERSON>",
    assignedTo: [
      { id: "EMP005", name: "<PERSON>", status: "Completed", completedDate: "2024-11-05", timeSpent: "45min" },
      { id: "EMP006", name: "<PERSON> <PERSON>", status: "Completed", completedDate: "2024-11-03", timeSpent: "42min" },
      { id: "EMP007", name: "Maria Garcia", status: "Completed", completedDate: "2024-11-08", timeSpent: "48min" }
    ],
    assignedRoles: ["All Employees"],
    department: "All Departments",
    status: "Completed",
    dueDate: "2024-11-30",
    assignedDate: "2024-10-15",
    averageTimeSpent: "45min",
    completionPercentage: 100,
    totalAssigned: 3,
    completed: 3,
    priority: "High"
  },
  {
    id: "TA-003",
    trainingName: "Good Manufacturing Practices (GMP)",
    trainingType: "PDF",
    assignedTo: [
      { id: "EMP008", name: "Robert Kim", status: "In Progress", timeSpent: "1.5h" },
      { id: "EMP009", name: "Jennifer Lee", status: "Not Started" },
      { id: "EMP010", name: "Thomas Brown", status: "Overdue" },
      { id: "EMP011", name: "Amanda White", status: "Completed", completedDate: "2024-11-20", timeSpent: "2.1h" }
    ],
    assignedRoles: ["Production Staff", "Quality Control", "Warehouse"],
    department: "Production",
    status: "In Progress",
    dueDate: "2024-12-01",
    assignedDate: "2024-10-01",
    averageTimeSpent: "1.8h",
    completionPercentage: 25,
    totalAssigned: 4,
    completed: 1,
    priority: "Medium"
  },
  {
    id: "TA-004",
    trainingName: "Preventive Maintenance Procedures",
    trainingType: "Video",
    assignedTo: [
      { id: "EMP012", name: "Carlos Martinez", status: "Completed", completedDate: "2024-11-18", timeSpent: "3.2h" },
      { id: "EMP013", name: "Susan Davis", status: "In Progress", timeSpent: "1.8h" }
    ],
    assignedRoles: ["Maintenance Technician", "Equipment Operator"],
    department: "Maintenance",
    status: "In Progress",
    dueDate: "2024-12-10",
    assignedDate: "2024-11-01",
    averageTimeSpent: "2.5h",
    completionPercentage: 50,
    totalAssigned: 2,
    completed: 1,
    priority: "Medium"
  },
  {
    id: "TA-005",
    trainingName: "Chemical Handling and Safety",
    trainingType: "Interactive",
    assignedTo: [
      { id: "EMP014", name: "Patricia Taylor", status: "Not Started" },
      { id: "EMP015", name: "Daniel Moore", status: "Not Started" },
      { id: "EMP016", name: "Linda Anderson", status: "Not Started" }
    ],
    assignedRoles: ["Lab Technician", "Production Staff"],
    department: "Quality Assurance",
    status: "Not Started",
    dueDate: "2024-12-20",
    assignedDate: "2024-11-15",
    averageTimeSpent: "0min",
    completionPercentage: 0,
    totalAssigned: 3,
    completed: 0,
    priority: "High"
  },
  {
    id: "TA-006",
    trainingName: "Lean Manufacturing Principles",
    trainingType: "Quiz",
    assignedTo: [
      { id: "EMP017", name: "Mark Johnson", status: "Completed", completedDate: "2024-11-12", timeSpent: "2.8h" },
      { id: "EMP018", name: "Rachel Green", status: "Completed", completedDate: "2024-11-14", timeSpent: "3.1h" },
      { id: "EMP019", name: "Kevin Brown", status: "In Progress", timeSpent: "1.4h" }
    ],
    assignedRoles: ["Production Manager", "Process Engineer", "Team Lead"],
    department: "Production",
    status: "In Progress",
    dueDate: "2024-11-25",
    assignedDate: "2024-10-25",
    averageTimeSpent: "2.4h",
    completionPercentage: 67,
    totalAssigned: 3,
    completed: 2,
    priority: "Medium"
  },
  {
    id: "TA-007",
    trainingName: "Data Privacy & GDPR Compliance",
    trainingType: "Video",
    assignedTo: [
      { id: "EMP020", name: "Nicole Wilson", status: "Overdue" },
      { id: "EMP021", name: "Christopher Lee", status: "Overdue" },
      { id: "EMP022", name: "Michelle Davis", status: "Completed", completedDate: "2024-10-15", timeSpent: "1.5h" }
    ],
    assignedRoles: ["All Employees"],
    department: "All Departments",
    status: "Overdue",
    dueDate: "2024-11-15",
    assignedDate: "2024-09-15",
    averageTimeSpent: "1.5h",
    completionPercentage: 33,
    totalAssigned: 3,
    completed: 1,
    priority: "High"
  },
  {
    id: "TA-008",
    trainingName: "Equipment Calibration Standards",
    trainingType: "PDF",
    assignedTo: [
      { id: "EMP023", name: "Steven Miller", status: "In Progress", timeSpent: "2.2h" },
      { id: "EMP024", name: "Angela Clark", status: "Not Started" }
    ],
    assignedRoles: ["Maintenance Technician", "Quality Control"],
    department: "Maintenance",
    status: "In Progress",
    dueDate: "2024-12-05",
    assignedDate: "2024-11-05",
    averageTimeSpent: "2.2h",
    completionPercentage: 50,
    totalAssigned: 2,
    completed: 0,
    priority: "Low"
  },
  {
    id: "TA-009",
    trainingName: "Root Cause Analysis for Manufacturing Deviations",
    trainingType: "Interactive",
    assignedTo: [
      { id: "EMP025", name: "Brian Rodriguez", status: "Completed", completedDate: "2024-11-22", timeSpent: "4.1h" },
      { id: "EMP026", name: "Stephanie Martinez", status: "In Progress", timeSpent: "2.3h" },
      { id: "EMP027", name: "Andrew Thompson", status: "Not Started" }
    ],
    assignedRoles: ["Quality Manager", "Process Engineer", "Production Manager"],
    department: "Quality Assurance",
    status: "In Progress",
    dueDate: "2024-12-31",
    assignedDate: "2024-11-20",
    averageTimeSpent: "3.2h",
    completionPercentage: 33,
    totalAssigned: 3,
    completed: 1,
    priority: "Medium"
  },
  {
    id: "TA-010",
    trainingName: "5S Workplace Organization",
    trainingType: "Video",
    assignedTo: [
      { id: "EMP028", name: "Jessica Garcia", status: "Not Started" },
      { id: "EMP029", name: "William Jones", status: "Not Started" },
      { id: "EMP030", name: "Sarah Wilson", status: "Not Started" },
      { id: "EMP031", name: "Michael Brown", status: "Not Started" }
    ],
    assignedRoles: ["Production Staff", "Team Lead"],
    department: "Production",
    status: "Not Started",
    dueDate: "2025-01-15",
    assignedDate: "2024-11-25",
    averageTimeSpent: "0min",
    completionPercentage: 0,
    totalAssigned: 4,
    completed: 0,
    priority: "Low"
  }
];

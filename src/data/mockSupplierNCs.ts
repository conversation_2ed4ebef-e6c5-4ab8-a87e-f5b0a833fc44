
import { SupplierNonConformance } from "@/types/supplierNonConformance";

export const mockSupplierNCs: SupplierNonConformance[] = [
  {
    id: "NC-1053",
    ncNumber: "1053",
    supplierName: "Delta Fasteners",
    issueDescription: "Dimensional Deviation",
    status: "Open",
    severity: "Major",
    capaLinked: true,
    dateReported: "2025-08-10",
    responsiblePerson: "<PERSON>"
  },
  {
    id: "NC-1062",
    ncNumber: "1062",
    supplierName: "NovaChem",
    issueDescription: "Incorrect labeling",
    status: "Closed",
    severity: "Minor",
    capaLinked: false,
    dateReported: "2025-07-25",
    dateResolved: "2025-08-05",
    rootCause: "Supplier training deficiency",
    containmentActions: "Labels inspected and corrected before use"
  },
  {
    id: "NC-1048",
    ncNumber: "1048",
    supplierName: "Zenith Steel",
    issueDescription: "Surface finish defect",
    status: "In Review",
    severity: "Major",
    capaLinked: true,
    dateReported: "2025-08-02",
    responsiblePerson: "<PERSON>"
  },
  {
    id: "NC-1039",
    ncNumber: "1039",
    supplierName: "Omega Polymers",
    issueDescription: "Material hardness out of spec",
    status: "Closed",
    severity: "Critical",
    capaLinked: true,
    dateReported: "2025-07-15",
    dateResolved: "2025-08-01",
    rootCause: "Process control issue at supplier",
    containmentActions: "100% inspection implemented"
  },
  {
    id: "NC-1071",
    ncNumber: "1071",
    supplierName: "Precision Circuit Boards",
    issueDescription: "Solder joint failures",
    status: "Open",
    severity: "Critical",
    capaLinked: true,
    dateReported: "2025-08-15",
    responsiblePerson: "Michael Brown"
  },
  {
    id: "NC-1057",
    ncNumber: "1057",
    supplierName: "MetalTech Inc.",
    issueDescription: "Missing certifications",
    status: "Closed",
    severity: "Minor",
    capaLinked: false,
    dateReported: "2025-07-20",
    dateResolved: "2025-07-28",
    rootCause: "Documentation error",
    containmentActions: "Certifications provided and verified"
  },
  {
    id: "NC-1065",
    ncNumber: "1065",
    supplierName: "Global Packaging Solutions",
    issueDescription: "Packaging damage",
    status: "In Review",
    severity: "Minor",
    capaLinked: true,
    dateReported: "2025-08-05",
    responsiblePerson: "Lisa Garcia"
  },
  {
    id: "NC-1074",
    ncNumber: "1074",
    supplierName: "FastChip Electronics",
    issueDescription: "Component failure",
    status: "Open",
    severity: "Major",
    capaLinked: true,
    dateReported: "2025-08-18",
    responsiblePerson: "Robert Chen"
  }
];

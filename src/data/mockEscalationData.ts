
import { SupplierEscalation } from "@/types/supplierEscalation";

export const mockEscalationData: SupplierEscalation = {
  id: "ESC-0091",
  supplier: "Delta Fasteners",
  raisedBy: "<PERSON> (QA)",
  raisedDate: "Aug 12, 2025",
  issue: "Repeated late delivery despite warnings",
  impact: "Production delays affecting multiple product lines",
  status: "Escalated",
  dueDate: "Aug 25, 2025",
  assignedTo: "<PERSON>",
  history: [
    {
      date: "Aug 12, 2025",
      time: "09:45 AM",
      action: "Escalation created and assigned to <PERSON>",
      by: "<PERSON>"
    },
    {
      date: "Aug 13, 2025",
      time: "11:20 AM",
      action: "Supplier contacted via email regarding delivery issues",
      by: "<PERSON>"
    },
    {
      date: "Aug 15, 2025",
      time: "02:30 PM",
      action: "Status changed from New to In Review",
      by: "<PERSON>"
    },
    {
      date: "Aug 17, 2025",
      time: "10:15 AM",
      action: "Escalated to Procurement Lead due to lack of response",
      by: "<PERSON>"
    },
    {
      date: "Aug 18, 2025",
      time: "03:45 PM",
      action: "Status changed from In Review to Escalated",
      by: "System"
    }
  ],
  comments: [
    {
      author: "<PERSON>",
      date: "Aug 12, 2025",
      time: "09:45 AM",
      text: "This is the third time in two months <PERSON> has missed delivery commitments. Previous warnings have not improved the situation."
    },
    {
      author: "<PERSON>",
      date: "Aug 13, 2025",
      time: "11:25 AM",
      text: "I've sent an email to their account manager requesting an immediate explanation and recovery plan."
    },
    {
      author: "<PERSON> <PERSON>",
      date: "Aug 15, 2025",
      time: "02:35 PM",
      text: "<PERSON> responded with apologies but no concrete plan. They cited logistics issues but didn't provide specifics."
    },
    {
      author: "James Wilson",
      date: "Aug 16, 2025",
      time: "09:10 AM",
      text: "We need to consider alternatives. I've asked procurement to check our second source options for these components."
    },
    {
      author: "Sarah Johnson",
      date: "Aug 17, 2025",
      time: "10:20 AM",
      text: "I'm escalating this to Marcus in Procurement as we need executive intervention. Production schedule for next week is at risk."
    }
  ]
};

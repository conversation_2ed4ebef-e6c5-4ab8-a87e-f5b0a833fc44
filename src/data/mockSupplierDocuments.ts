
import { SupplierDocument, DocumentVersion } from "@/types/supplierDocuments";

export const mockSupplierDocuments: SupplierDocument[] = [
  {
    id: "DOC-001",
    name: "ISO 14001 Certificate",
    fileName: "ISO_14001.pdf",
    uploadDate: "2024-05-15",
    expiryDate: "2025-09-30",
    status: "Valid",
    version: 2,
    reviewer: "<PERSON>",
    supplierName: "Zenith Steel"
  },
  {
    id: "DOC-002",
    name: "RoHS Compliance Certificate",
    fileName: "RoHS_Compliance.pdf",
    uploadDate: "2023-12-10",
    expiryDate: "2024-12-15",
    status: "Valid",
    version: 1,
    reviewer: "<PERSON>",
    supplierName: "Zenith Steel"
  },
  {
    id: "DOC-003",
    name: "ISO 9001 Certificate",
    fileName: "ISO_9001.pdf",
    uploadDate: "2023-08-05",
    expiryDate: "2023-08-05",
    status: "Expired",
    version: 3,
    reviewer: "<PERSON>",
    supplierName: "Zenith Steel"
  },
  {
    id: "DOC-004",
    name: "REACH Compliance",
    fileName: "REACH_Compliance.pdf",
    uploadDate: "2024-01-20",
    expiryDate: "2024-07-30",
    status: "Expiring Soon",
    version: 1,
    reviewer: "David Chen",
    supplierName: "Zenith Steel"
  },
  {
    id: "DOC-005",
    name: "Quality Manual",
    fileName: "Quality_Manual_v2.pdf",
    uploadDate: "2024-03-12",
    expiryDate: "2026-03-12",
    status: "Valid",
    version: 2,
    reviewer: "Sarah Johnson",
    supplierName: "Zenith Steel"
  },
  {
    id: "DOC-006",
    name: "Environmental Policy",
    fileName: "Environmental_Policy.pdf",
    uploadDate: "2023-11-08",
    expiryDate: "2024-06-30",
    status: "Expiring Soon",
    version: 1,
    reviewer: "Maria Garcia",
    supplierName: "Zenith Steel"
  }
];

export const mockDocumentVersions: DocumentVersion[] = [
  {
    id: "VER-001",
    documentId: "DOC-001",
    version: 2,
    uploadDate: "2024-05-15",
    uploadedBy: "John Smith",
    fileName: "ISO_14001_v2.pdf",
    notes: "Updated to include new environmental management processes"
  },
  {
    id: "VER-002",
    documentId: "DOC-001",
    version: 1,
    uploadDate: "2023-05-20",
    uploadedBy: "John Smith",
    fileName: "ISO_14001_v1.pdf",
    notes: "Initial upload"
  },
  {
    id: "VER-003",
    documentId: "DOC-002",
    version: 1,
    uploadDate: "2023-12-10",
    uploadedBy: "Sarah Johnson",
    fileName: "RoHS_Compliance_v1.pdf",
    notes: "Initial upload"
  },
  {
    id: "VER-004",
    documentId: "DOC-003",
    version: 3,
    uploadDate: "2023-08-05",
    uploadedBy: "David Chen",
    fileName: "ISO_9001_v3.pdf",
    notes: "Updated after audit findings"
  },
  {
    id: "VER-005",
    documentId: "DOC-003",
    version: 2,
    uploadDate: "2022-08-10",
    uploadedBy: "Rachel Kim",
    fileName: "ISO_9001_v2.pdf",
    notes: "Annual renewal"
  }
];

export const mockReviewers = [
  { id: "REV-001", name: "Rachel Kim", role: "QA Manager" },
  { id: "REV-002", name: "Alex Wong", role: "Compliance Officer" },
  { id: "REV-003", name: "David Chen", role: "Supplier Quality Engineer" },
  { id: "REV-004", name: "Sarah Johnson", role: "Quality Specialist" },
  { id: "REV-005", name: "Maria Garcia", role: "Environmental Compliance" }
];

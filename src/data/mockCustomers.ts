
export type CustomerStatus = 'Active' | 'Read-only' | 'Access Expired';

export interface Customer {
  id: string;
  name: string;
  logoUrl: string;
  status: CustomerStatus;
  lastAccessed: string;
  sharedDocuments: number;
  complianceScore: number;
  qaContact: string;
  industry: 'Aerospace' | 'Automotive' | 'Electronics' | 'Medical' | 'Unknown';
  auditStatus: 'Passed' | 'Pending' | 'Failed' | 'Scheduled';
}

export const mockCustomers: Customer[] = [
  {
    id: "cust-1",
    name: "Boeing Commercial Airplanes",
    logoUrl: "/placeholder.svg",
    status: "Active",
    lastAccessed: "2024-01-20",
    sharedDocuments: 45,
    complianceScore: 98,
    qaContact: "<PERSON>",
    industry: "Aerospace",
    auditStatus: "Passed"
  },
  {
    id: "cust-2", 
    name: "Tesla Motors",
    logoUrl: "/placeholder.svg",
    status: "Active",
    lastAccessed: "2024-01-18",
    sharedDocuments: 32,
    complianceScore: 95,
    qaContact: "<PERSON>",
    industry: "Automotive",
    auditStatus: "Scheduled"
  },
  {
    id: "cust-3",
    name: "Apple Inc.",
    logoUrl: "/placeholder.svg", 
    status: "Read-only",
    lastAccessed: "2024-01-15",
    sharedDocuments: 28,
    complianceScore: 92,
    qaContact: "Mike Davis",
    industry: "Electronics",
    auditStatus: "Pending"
  }
];

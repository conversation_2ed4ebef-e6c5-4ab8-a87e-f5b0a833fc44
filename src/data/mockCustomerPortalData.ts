
export interface CustomerManagementData {
  customerId: string;
  customerName: string;
  activeUsers: number;
  documentsShared: number;
  recentActivity: string[];
  permissions: {
    viewDocuments: boolean;
    downloadDocuments: boolean;
    commentOnDocuments: boolean;
    auditAccess: boolean;
  };
  accessLogs: {
    userId: string;
    userName: string;
    action: string;
    timestamp: string;
  }[];
}

export interface CustomerPortalData {
  customerId: string;
  customerName: string;
  availableDocuments: {
    id: number;
    name: string;
    type: string;
    lastUpdated: string;
    downloadCount: number;
  }[];
  recentAudits: {
    date: string;
    type: string;
    status: string;
    findings?: number;
  }[];
  messages: {
    id: number;
    from: string;
    subject: string;
    date: string;
    isRead: boolean;
  }[];
  complianceMetrics: {
    score: number;
    trend: 'up' | 'down' | 'stable';
    lastAssessment: string;
  };
}

export const mockCustomerManagementData: Record<string, CustomerManagementData> = {
  "cust-001": {
    customerId: "cust-001",
    customerName: "Boeing Commercial",
    activeUsers: 12,
    documentsShared: 45,
    recentActivity: [
      "User access granted to <PERSON>",
      "Document QM-2024-001 downloaded by <PERSON>",
      "Audit report QAR-2024-003 reviewed",
      "Compliance score updated to 98%",
      "New user invitation sent to Engineering Team"
    ],
    permissions: {
      viewDocuments: true,
      downloadDocuments: true,
      commentOnDocuments: true,
      auditAccess: true
    },
    accessLogs: [
      { userId: "u-001", userName: "Sarah Johnson", action: "Document Downloaded", timestamp: "2024-01-15 14:30" },
      { userId: "u-002", userName: "Mike Chen", action: "Portal Login", timestamp: "2024-01-15 09:15" },
      { userId: "u-003", userName: "Lisa Wong", action: "Audit Report Viewed", timestamp: "2024-01-14 16:45" }
    ]
  },
  "cust-002": {
    customerId: "cust-002",
    customerName: "Airbus Industries",
    activeUsers: 8,
    documentsShared: 32,
    recentActivity: [
      "Quality manual v2.1 shared",
      "Process control plan updated",
      "User permissions modified for Quality Team",
      "Compliance assessment completed"
    ],
    permissions: {
      viewDocuments: true,
      downloadDocuments: true,
      commentOnDocuments: false,
      auditAccess: true
    },
    accessLogs: [
      { userId: "u-004", userName: "Jean Dubois", action: "Document Viewed", timestamp: "2024-01-15 11:20" },
      { userId: "u-005", userName: "Marie Laurent", action: "Portal Login", timestamp: "2024-01-15 08:30" }
    ]
  }
};

export const mockCustomerPortalData: Record<string, CustomerPortalData> = {
  "cust-001": {
    customerId: "cust-001",
    customerName: "Boeing Commercial",
    availableDocuments: [
      { id: 1, name: "Quality Manual v2.1", type: "PDF", lastUpdated: "2024-01-15", downloadCount: 23 },
      { id: 2, name: "Process Control Plan", type: "Excel", lastUpdated: "2024-01-10", downloadCount: 18 },
      { id: 3, name: "AS9100 Certificate", type: "PDF", lastUpdated: "2024-01-05", downloadCount: 45 },
      { id: 4, name: "Supplier Requirements Doc", type: "Word", lastUpdated: "2023-12-20", downloadCount: 12 }
    ],
    recentAudits: [
      { date: "2024-01-15", type: "Internal Audit", status: "Passed", findings: 0 },
      { date: "2023-12-10", type: "Customer Audit", status: "Minor Finding", findings: 2 },
      { date: "2023-11-05", type: "Compliance Review", status: "Passed", findings: 1 }
    ],
    messages: [
      { id: 1, from: "QA Team", subject: "New quality manual version available", date: "2024-01-16", isRead: false },
      { id: 2, from: "Compliance", subject: "Audit findings response required", date: "2024-01-12", isRead: true },
      { id: 3, from: "Document Control", subject: "Annual document review reminder", date: "2024-01-08", isRead: true }
    ],
    complianceMetrics: {
      score: 98,
      trend: 'up',
      lastAssessment: "2024-01-15"
    }
  },
  "cust-002": {
    customerId: "cust-002",
    customerName: "Airbus Industries",
    availableDocuments: [
      { id: 5, name: "Manufacturing Process Guide", type: "PDF", lastUpdated: "2024-01-12", downloadCount: 15 },
      { id: 6, name: "Quality Inspection Checklist", type: "Excel", lastUpdated: "2024-01-08", downloadCount: 22 },
      { id: 7, name: "EASA Compliance Report", type: "PDF", lastUpdated: "2023-12-15", downloadCount: 8 }
    ],
    recentAudits: [
      { date: "2024-01-08", type: "EASA Audit", status: "Passed", findings: 0 },
      { date: "2023-11-20", type: "Internal Review", status: "Minor Finding", findings: 1 }
    ],
    messages: [
      { id: 4, from: "EASA Compliance", subject: "Certification renewal reminder", date: "2024-01-14", isRead: false },
      { id: 5, from: "Quality Assurance", subject: "Process improvement suggestions", date: "2024-01-10", isRead: true }
    ],
    complianceMetrics: {
      score: 95,
      trend: 'stable',
      lastAssessment: "2024-01-08"
    }
  }
};

export const getCustomerManagementData = (customerId: string): CustomerManagementData | null => {
  return mockCustomerManagementData[customerId] || null;
};

export const getCustomerPortalData = (customerId: string): CustomerPortalData | null => {
  return mockCustomerPortalData[customerId] || null;
};

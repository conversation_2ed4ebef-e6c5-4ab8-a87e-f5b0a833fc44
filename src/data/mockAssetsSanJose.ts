
import { Asset } from "@/types/assets";
import { createMockAsset, commonStaff } from "@/utils/mockAssetGenerator";

// Use the utility function to create mock assets for San Jose
export const mockAssetsSanJose: Asset[] = [
  createMockAsset({
    id: "SJ-LAP-045",
    name: "MacBook Pro M2",
    type: "Laptop",
    description: "Engineering laptop for software development",
    location: "Tech Park, San Jose",
    owner: commonStaff.michaelChen,
    assignedTo: commonStaff.michael<PERSON>hen,
    status: "In Use",
    lastUpdated: "2023-12-10",
    purchaseDate: "05 Jan 2024"
  }),
  createMockAsset({
    id: "SJ-MON-032",
    name: "Dell P2419H",
    type: "Monitor",
    description: "Standard office monitor",
    location: "Operations, San Jose",
    owner: commonStaff.emilyWang,
    assignedTo: commonStaff.carlosRodriguez,
    status: "In Use",
    lastUpdated: "2023-11-15",
    purchaseDate: "22 Feb 2024"
  }),
  createMockAsset({
    id: "SJ-SRV-009",
    name: "Cisco UCS C240 M5",
    type: "Server",
    description: "High-performance computing server",
    location: "Data Center, San Jose",
    owner: commonStaff.davePatel,
    assignedTo: "IT Department",
    status: "Under Maintenance",
    lastUpdated: "2023-10-20",
    purchaseDate: "15 Aug 2023",
    calibrationDate: "15 Feb 2025",
    calibrationCertNo: "SJ-SRV-CAL-009",
    calibrationPeriod: "Half-yearly"
  }),
  createMockAsset({
    id: "SJ-PRT-012",
    name: "Brother MFC-L9570CDW",
    type: "Printer",
    description: "Multi-function color laser printer",
    location: "3rd Floor, San Jose",
    owner: "Facilities",
    assignedTo: "Shared",
    status: "Available",
    lastUpdated: "2023-09-30",
    purchaseDate: "10 Oct 2023"
  }),
  createMockAsset({
    id: "SJ-LAP-052",
    name: "HP ZBook Studio G9",
    type: "Laptop",
    description: "Design workstation for 3D modeling",
    location: "Product Design, San Jose",
    owner: commonStaff.sarahJohnson,
    assignedTo: commonStaff.sarahJohnson,
    status: "In Use",
    lastUpdated: "2023-12-05",
    purchaseDate: "12 Mar 2024",
    calibrationDate: "12 Jun 2024",
    calibrationCertNo: "CAL-LT-HP-078",
    calibrationPeriod: "Quarterly"
  }),
  createMockAsset({
    id: "SJ-MON-047",
    name: "ASUS ProArt PA329C",
    type: "Monitor",
    description: "Professional 4K HDR display",
    location: "Media Lab, San Jose",
    owner: commonStaff.carlosRodriguez,
    assignedTo: commonStaff.carlosRodriguez,
    status: "In Use",
    lastUpdated: "2023-11-28",
    purchaseDate: "03 Feb 2024"
  }),
  createMockAsset({
    id: "SJ-SRV-014",
    name: "IBM Power System S922",
    type: "Server",
    description: "Enterprise database server",
    location: "Data Center, San Jose",
    owner: commonStaff.davePatel,
    assignedTo: "Database Team",
    status: "In Use",
    lastUpdated: "2023-10-15",
    purchaseDate: "20 Jul 2023",
    calibrationDate: "20 Jan 2025",
    calibrationCertNo: "SJ-SRV-CAL-014",
    calibrationPeriod: "Half-yearly"
  }),
  createMockAsset({
    id: "SJ-PRT-019",
    name: "Xerox VersaLink C7000",
    type: "Printer",
    description: "High-volume color printer",
    location: "Marketing Dept, San Jose",
    owner: "Facilities",
    assignedTo: "Marketing Team",
    status: "Breakdown",
    lastUpdated: "2023-12-01",
    purchaseDate: "15 Sep 2023"
  }),
  createMockAsset({
    id: "SJ-LAP-063",
    name: "Surface Laptop Studio",
    type: "Laptop",
    description: "Convertible touch laptop for UX team",
    location: "UX Lab, San Jose",
    owner: commonStaff.emilyWang,
    assignedTo: commonStaff.emilyWang,
    status: "In Use",
    lastUpdated: "2023-12-08",
    purchaseDate: "25 Mar 2024"
  }),
  createMockAsset({
    id: "SJ-MON-056",
    name: "BenQ PD3220U",
    type: "Monitor",
    description: "Designer monitor with Thunderbolt 3",
    location: "Creative Studio, San Jose",
    owner: commonStaff.sarahJohnson,
    assignedTo: "Design Team",
    status: "Available",
    lastUpdated: "2023-11-20",
    purchaseDate: "05 Jan 2024",
    calibrationDate: "05 Apr 2024",
    calibrationCertNo: "CAL-MON-BQ-124",
    calibrationPeriod: "Quarterly"
  }),
  createMockAsset({
    id: "SJ-SRV-021",
    name: "Oracle SPARC T8-1",
    type: "Server",
    description: "Enterprise application server",
    location: "Data Center, San Jose",
    owner: commonStaff.davePatel,
    assignedTo: "IT Department",
    status: "Retired",
    lastUpdated: "2023-08-15",
    purchaseDate: "10 May 2023",
    calibrationDate: "10 Aug 2023",
    calibrationCertNo: "SJ-SRV-CAL-021",
    calibrationPeriod: "Quarterly"
  }),
  createMockAsset({
    id: "SJ-LAP-071",
    name: "Dell Precision 5570",
    type: "Laptop",
    description: "CAD workstation laptop",
    location: "Engineering, San Jose",
    owner: commonStaff.carlosRodriguez,
    assignedTo: commonStaff.michaelChen,
    status: "Under Maintenance",
    lastUpdated: "2023-12-12",
    purchaseDate: "15 Apr 2024"
  })
];


import { getDocumentCounts } from "@/utils/documentUtils";

const { statusCounts, categoryCounts, departmentCounts, assigneeCounts, processesCounts } = getDocumentCounts();

// Filter options with counts
export const statusOptions = [
  { id: "published", label: "Published", count: statusCounts["published"] || 0 },
  { id: "draft", label: "Draft", count: statusCounts["draft"] || 0 },
  { id: "needs-approval", label: "Needs Approval", count: statusCounts["needs-approval"] || 0 },
  { id: "rejected", label: "Rejected", count: statusCounts["rejected"] || 0 },
  { id: "expired", label: "Expired", count: statusCounts["expired"] || 0 },
  { id: "not-uploaded", label: "Not Uploaded", count: statusCounts["not-uploaded"] || 0 }
];

export const categoriesOptions = [
  { id: "report", label: "Report", count: categoryCounts["report"] || 0 },
  { id: "process", label: "Process", count: categoryCounts["process"] || 0 },
  { id: "manual", label: "Manual", count: categoryCounts["manual"] || 0 },
  { id: "chart", label: "Chart", count: categoryCounts["chart"] || 0 },
  { id: "standard", label: "Standard", count: categoryCounts["standard"] || 0 }
];

export const departmentsOptions = [
  { id: "maintenance", label: "Maintenance", count: departmentCounts["maintenance"] || 0 },
  { id: "project-management", label: "Project Management", count: departmentCounts["project-management"] || 0 },
  { id: "production", label: "Production", count: departmentCounts["production"] || 0 },
  { id: "quality-assurance", label: "Quality Assurance", count: departmentCounts["quality-assurance"] || 0 },
  { id: "human-resources", label: "Human Resources", count: departmentCounts["human-resources"] || 0 }
];

export const processesOptions = [
  { id: "qa-process", label: "QA Process", count: processesCounts["qa-process"] || 0 },
  { id: "inspection", label: "Inspection", count: processesCounts["inspection"] || 0 },
  { id: "procurement", label: "Procurement", count: processesCounts["procurement"] || 0 },
  { id: "manufacturing", label: "Manufacturing", count: processesCounts["manufacturing"] || 0 },
  { id: "certification", label: "Certification", count: processesCounts["certification"] || 0 },
  { id: "project-planning", label: "Project Planning", count: processesCounts["project-planning"] || 0 },
  { id: "onboarding", label: "Onboarding", count: processesCounts["onboarding"] || 0 },
  { id: "maintenance", label: "Maintenance", count: processesCounts["maintenance"] || 0 },
  { id: "testing", label: "Testing", count: processesCounts["testing"] || 0 },
  { id: "execution", label: "Execution", count: processesCounts["execution"] || 0 }
];

export const assigneeOptions = [
  { id: "harsh-jha", label: "Harsh Jha", count: assigneeCounts["harsh-jha"] || 0 },
  { id: "milanjeet-singh", label: "Milanjeet Singh", count: assigneeCounts["milanjeet-singh"] || 0 },
  { id: "vishnu-tripathi", label: "Vishnu Tripathi", count: assigneeCounts["vishnu-tripathi"] || 0 },
  { id: "maitreyi-sharma", label: "Maitreyi Sharma", count: assigneeCounts["maitreyi-sharma"] || 0 },
  { id: "rehan-qureshi", label: "Rehan Qureshi", count: assigneeCounts["rehan-qureshi"] || 0 }
];

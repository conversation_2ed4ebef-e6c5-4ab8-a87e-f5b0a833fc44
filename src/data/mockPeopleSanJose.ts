
import { Person } from "@/types/people";

export const mockPeople<PERSON>anJose: Person[] = [
  { 
    id: "SJ001", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "Project Manager", 
    email: "<EMAIL>", 
    department: "Project Management", 
    processes: ["Delivery"], 
    joiningDate: "2022-08-12", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "SJ002", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "Lead Developer", 
    email: "<EMAIL>", 
    department: "Engineering", 
    processes: ["Software Architecture"], 
    joiningDate: "2021-11-05", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "SJ003", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "UI/UX Designer", 
    email: "<EMAIL>", 
    department: "Design", 
    processes: ["User Interface"], 
    joiningDate: "2022-03-15", 
    leavingDate: null, 
    status: "On vacation" 
  },
  { 
    id: "SJ004", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "Product Specialist", 
    email: "<EMAIL>", 
    department: "Product", 
    processes: ["Strategy"], 
    joiningDate: "2022-05-20", 
    leavingDate: "2023-12-15", 
    status: "Suspended" 
  },
  { 
    id: "SJ005", 
    name: "Jennifer Parker", 
    firstName: "Jennifer",
    lastName: "Parker",
    jobTitle: "Marketing Lead", 
    email: "<EMAIL>", 
    department: "Marketing", 
    processes: ["Digital Marketing"], 
    joiningDate: "2022-02-28", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "SJ006", 
    name: "Daniel Wright", 
    firstName: "Daniel",
    lastName: "Wright",
    jobTitle: "Systems Analyst", 
    email: "<EMAIL>", 
    department: "IT", 
    processes: ["System Architecture"], 
    joiningDate: "2021-10-10", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "SJ007", 
    name: "Sophia Garcia", 
    firstName: "Sophia",
    lastName: "Garcia",
    jobTitle: "Customer Success Manager", 
    email: "<EMAIL>", 
    department: "Customer Support", 
    processes: ["Client Relations"], 
    joiningDate: "2022-07-19", 
    leavingDate: null, 
    status: "On duty" 
  }
];


import { Document } from "@/components/document-hub/DocumentTable";
import { createMockDocument, commonApprovers, commonAssignees } from "@/utils/mockDataGenerator";

// Use the utility function to create mock documents
export const mockDocumentsBengaluru: Document[] = [
  createMockDocument({
    id: "doc-bg-1",
    title: "Safety Procedures",
    version: "2.3",
    status: "Published",
    category: "Procedure",
    department: "Health-Safety",
    processes: "Risk Assessment, Emergency Protocols",
    assignee: commonAssignees.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    approver: commonApprovers.<PERSON><PERSON><PERSON><PERSON><PERSON>,
    publishedDate: "12 Apr 2025",
    reviewDate: "12 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-2",
    title: "Product Specification",
    version: "1.5",
    status: "Published",
    category: "Specification",
    department: "Engineering",
    processes: "Design, Development",
    assignee: commonAssignees.<PERSON>rah<PERSON><PERSON><PERSON>,
    approver: commonApprovers.john<PERSON><PERSON>,
    publishedDate: "14 Apr 2025",
    reviewDate: "14 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-3",
    title: "System Design",
    version: "4.2",
    status: "Published",
    category: "Design",
    department: "Engineering",
    processes: "Architecture, Planning",
    assignee: commonAssignees.davidMartinez,
    approver: commonApprovers.mariaGarcia,
    publishedDate: "16 Apr 2025",
    reviewDate: "16 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-4",
    title: "Marketing Plan",
    version: "0.8",
    status: "Draft",
    category: "Plan",
    department: "Marketing",
    processes: "Campaign, Strategy",
    assignee: commonAssignees.emilyWilson,
    approver: commonApprovers.ahmedHassan,
    publishedDate: "17 Apr 2025",
    reviewDate: "17 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-5",
    title: "Client Agreement",
    status: "Needs Approval",
    category: "Legal",
    department: "Legal",
    processes: "Contract, Review",
    assignee: commonAssignees.robertChen,
    approver: commonApprovers.sophieAnderson,
    publishedDate: "19 Apr 2025",
    reviewDate: "19 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-6",
    title: "Safety Audit",
    version: "3.0",
    status: "Rejected",
    category: "Audit",
    department: "Health-Safety",
    processes: "Inspection, Compliance",
    assignee: commonAssignees.jenniferParker,
    approver: commonApprovers.rajPatel,
    publishedDate: "21 Apr 2025",
    reviewDate: "21 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-7",
    title: "Product Roadmap",
    version: "2.3",
    status: "Expired",
    category: "Roadmap",
    department: "Product",
    processes: "Planning, Forecasting",
    assignee: commonAssignees.alexRodriguez,
    approver: commonApprovers.liuWei,
    publishedDate: "23 Apr 2025",
    reviewDate: "23 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-8",
    title: "System Architecture",
    status: "Not Uploaded",
    category: "Architecture",
    department: "Engineering",
    processes: "Planning, Implementation",
    assignee: commonAssignees.rachelKim,
    approver: commonApprovers.sarahJohnson,
    publishedDate: "25 Apr 2025",
    reviewDate: "25 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-9",
    title: "Campaign Strategy",
    version: "1.7",
    status: "Published",
    category: "Strategy",
    department: "Marketing",
    processes: "Execution, Analytics",
    assignee: commonAssignees.chrisTaylor,
    approver: commonApprovers.mariaGarcia,
    publishedDate: "27 Apr 2025",
    reviewDate: "27 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-10",
    title: "Contract Template",
    status: "Draft",
    category: "Legal",
    department: "Legal",
    processes: "Review, Standardization",
    assignee: commonAssignees.sofiaGarcia,
    approver: commonApprovers.johnSmith,
    publishedDate: "29 Apr 2025",
    reviewDate: "29 Oct 2025"
  }),
  createMockDocument({
    id: "doc-bg-11",
    title: "Testing Protocol",
    status: "Not Uploaded",
    category: "Protocol",
    department: "Engineering",
    processes: "Testing, Quality",
    assignee: commonAssignees.jamesWilson,
    approver: commonApprovers.ahmedHassan,
    publishedDate: "01 May 2025",
    reviewDate: "01 Nov 2025"
  }),
  createMockDocument({
    id: "doc-bg-12",
    title: "Safety Guidelines",
    version: "2.5",
    status: "Published",
    category: "Guidelines",
    department: "Health-Safety",
    processes: "Training, Implementation",
    assignee: commonAssignees.lisaJohnson,
    approver: commonApprovers.sophieAnderson,
    publishedDate: "03 May 2025",
    reviewDate: "03 Nov 2025"
  }),
  createMockDocument({
    id: "doc-bg-13",
    title: "Project Proposal",
    version: "1.1",
    status: "Needs Approval",
    category: "Proposal",
    department: "Product",
    processes: "Ideation, Validation",
    assignee: commonAssignees.markStevens,
    approver: commonApprovers.rajPatel,
    publishedDate: "05 May 2025",
    reviewDate: "05 Nov 2025"
  }),
  createMockDocument({
    id: "doc-bg-14",
    title: "Marketing Analysis",
    version: "0.7",
    status: "Rejected",
    category: "Analysis",
    department: "Marketing",
    processes: "Research, Reporting",
    assignee: commonAssignees.amandaLee,
    approver: commonApprovers.liuWei,
    publishedDate: "07 May 2025",
    reviewDate: "07 Nov 2025"
  }),
  createMockDocument({
    id: "doc-bg-15",
    title: "Legal Compliance",
    version: "3.2",
    status: "Expired",
    category: "Compliance",
    department: "Legal",
    processes: "Audit, Remediation",
    assignee: commonAssignees.danielWright,
    approver: commonApprovers.johnSmith,
    publishedDate: "09 May 2025",
    reviewDate: "09 Nov 2025"
  })
];

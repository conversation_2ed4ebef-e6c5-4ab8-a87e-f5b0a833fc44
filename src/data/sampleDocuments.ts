
export interface DocumentItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  size?: string;
  modifiedDate: string;
  modifiedBy: string;
  path: string;
  parentId?: string;
  fileType?: string;
  tags?: string[];
  status?: 'draft' | 'review' | 'approved' | 'archived';
}

export const sampleDocuments: DocumentItem[] = [
  // Root level folders
  {
    id: 'folder-1',
    name: 'Quality Procedures',
    type: 'folder',
    modifiedDate: '2025-01-15',
    modifiedBy: '<PERSON>',
    path: '/Quality Procedures'
  },
  {
    id: 'folder-2',
    name: 'Product Specifications',
    type: 'folder',
    modifiedDate: '2025-01-14',
    modifiedBy: '<PERSON>',
    path: '/Product Specifications'
  },
  {
    id: 'folder-3',
    name: 'Training Materials',
    type: 'folder',
    modifiedDate: '2025-01-13',
    modifiedBy: '<PERSON>',
    path: '/Training Materials'
  },
  {
    id: 'folder-4',
    name: 'Supplier Documents',
    type: 'folder',
    modifiedDate: '2025-01-12',
    modifiedBy: '<PERSON>',
    path: '/Supplier Documents'
  },
  
  // Quality Procedures subfolder and files
  {
    id: 'folder-1-1',
    name: 'ISO 9001',
    type: 'folder',
    modifiedDate: '2025-01-15',
    modifiedBy: '<PERSON>',
    path: '/Quality Procedures/ISO 9001',
    parentId: 'folder-1'
  },
  {
    id: 'file-1-1',
    name: 'Quality Manual v2.1.pdf',
    type: 'file',
    size: '2.4 MB',
    modifiedDate: '2025-01-15',
    modifiedBy: 'Sarah Johnson',
    path: '/Quality Procedures/Quality Manual v2.1.pdf',
    parentId: 'folder-1',
    fileType: 'pdf',
    status: 'approved',
    tags: ['quality', 'manual', 'iso9001']
  },
  {
    id: 'file-1-2',
    name: 'Document Control Procedure.docx',
    type: 'file',
    size: '584 KB',
    modifiedDate: '2025-01-14',
    modifiedBy: 'Sarah Johnson',
    path: '/Quality Procedures/Document Control Procedure.docx',
    parentId: 'folder-1',
    fileType: 'docx',
    status: 'review',
    tags: ['procedure', 'control']
  },
  
  // ISO 9001 subfolder files
  {
    id: 'file-1-1-1',
    name: 'ISO 9001 Certification.pdf',
    type: 'file',
    size: '1.2 MB',
    modifiedDate: '2025-01-10',
    modifiedBy: 'Sarah Johnson',
    path: '/Quality Procedures/ISO 9001/ISO 9001 Certification.pdf',
    parentId: 'folder-1-1',
    fileType: 'pdf',
    status: 'approved',
    tags: ['iso9001', 'certification']
  },
  {
    id: 'file-1-1-2',
    name: 'Internal Audit Checklist.xlsx',
    type: 'file',
    size: '245 KB',
    modifiedDate: '2025-01-08',
    modifiedBy: 'Sarah Johnson',
    path: '/Quality Procedures/ISO 9001/Internal Audit Checklist.xlsx',
    parentId: 'folder-1-1',
    fileType: 'xlsx',
    status: 'approved',
    tags: ['audit', 'checklist']
  },
  
  // Product Specifications files
  {
    id: 'file-2-1',
    name: 'PCB Assembly Spec v1.3.pdf',
    type: 'file',
    size: '3.1 MB',
    modifiedDate: '2025-01-14',
    modifiedBy: 'Mike Chen',
    path: '/Product Specifications/PCB Assembly Spec v1.3.pdf',
    parentId: 'folder-2',
    fileType: 'pdf',
    status: 'approved',
    tags: ['pcb', 'assembly', 'specification']
  },
  {
    id: 'file-2-2',
    name: 'Component Requirements.docx',
    type: 'file',
    size: '728 KB',
    modifiedDate: '2025-01-13',
    modifiedBy: 'Mike Chen',
    path: '/Product Specifications/Component Requirements.docx',
    parentId: 'folder-2',
    fileType: 'docx',
    status: 'draft',
    tags: ['components', 'requirements']
  },
  {
    id: 'file-2-3',
    name: 'Test Procedures.pdf',
    type: 'file',
    size: '1.8 MB',
    modifiedDate: '2025-01-12',
    modifiedBy: 'Mike Chen',
    path: '/Product Specifications/Test Procedures.pdf',
    parentId: 'folder-2',
    fileType: 'pdf',
    status: 'review',
    tags: ['testing', 'procedures']
  },
  
  // Training Materials files
  {
    id: 'file-3-1',
    name: 'Safety Training Module 1.pptx',
    type: 'file',
    size: '15.2 MB',
    modifiedDate: '2025-01-13',
    modifiedBy: 'Lisa Rodriguez',
    path: '/Training Materials/Safety Training Module 1.pptx',
    parentId: 'folder-3',
    fileType: 'pptx',
    status: 'approved',
    tags: ['safety', 'training', 'module1']
  },
  {
    id: 'file-3-2',
    name: 'Quality Standards Overview.mp4',
    type: 'file',
    size: '45.7 MB',
    modifiedDate: '2025-01-11',
    modifiedBy: 'Lisa Rodriguez',
    path: '/Training Materials/Quality Standards Overview.mp4',
    parentId: 'folder-3',
    fileType: 'mp4',
    status: 'approved',
    tags: ['quality', 'standards', 'video']
  },
  {
    id: 'file-3-3',
    name: 'New Employee Handbook.pdf',
    type: 'file',
    size: '2.9 MB',
    modifiedDate: '2025-01-09',
    modifiedBy: 'Lisa Rodriguez',
    path: '/Training Materials/New Employee Handbook.pdf',
    parentId: 'folder-3',
    fileType: 'pdf',
    status: 'approved',
    tags: ['handbook', 'employee', 'onboarding']
  },
  
  // Supplier Documents files
  {
    id: 'file-4-1',
    name: 'Supplier Audit Report - Acme Corp.pdf',
    type: 'file',
    size: '4.2 MB',
    modifiedDate: '2025-01-12',
    modifiedBy: 'David Kim',
    path: '/Supplier Documents/Supplier Audit Report - Acme Corp.pdf',
    parentId: 'folder-4',
    fileType: 'pdf',
    status: 'approved',
    tags: ['supplier', 'audit', 'acme']
  },
  {
    id: 'file-4-2',
    name: 'Certificate of Compliance - TechFlow.pdf',
    type: 'file',
    size: '856 KB',
    modifiedDate: '2025-01-11',
    modifiedBy: 'David Kim',
    path: '/Supplier Documents/Certificate of Compliance - TechFlow.pdf',
    parentId: 'folder-4',
    fileType: 'pdf',
    status: 'approved',
    tags: ['certificate', 'compliance', 'techflow']
  },
  {
    id: 'file-4-3',
    name: 'Supplier Scorecard Q4 2024.xlsx',
    type: 'file',
    size: '1.1 MB',
    modifiedDate: '2025-01-10',
    modifiedBy: 'David Kim',
    path: '/Supplier Documents/Supplier Scorecard Q4 2024.xlsx',
    parentId: 'folder-4',
    fileType: 'xlsx',
    status: 'approved',
    tags: ['scorecard', 'supplier', 'q4']
  }
];

export const getDocumentsByParent = (parentId?: string): DocumentItem[] => {
  return sampleDocuments.filter(doc => doc.parentId === parentId);
};

export const getDocumentById = (id: string): DocumentItem | undefined => {
  return sampleDocuments.find(doc => doc.id === id);
};

export const searchDocuments = (query: string): DocumentItem[] => {
  const lowercaseQuery = query.toLowerCase();
  return sampleDocuments.filter(doc => 
    doc.name.toLowerCase().includes(lowercaseQuery) ||
    doc.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    doc.path.toLowerCase().includes(lowercaseQuery)
  );
};

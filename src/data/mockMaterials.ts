
import { Material } from "@/types/inventory";

// Materials for San Jose location
export const mockMaterialsSanJose: Material[] = [
  { id: "SJ001", name: "Silicon Wafer", type: "Raw Material", category: "Electronics", uom: "Pieces" },
  { id: "SJ002", name: "Thermal Paste", type: "Consumable", category: "Cooling", uom: "Tubes" },
  { id: "SJ003", name: "Copper Wire", type: "Raw Material", category: "Electronics", uom: "Meters" },
  { id: "SJ004", name: "Aluminum Housing", type: "Component", category: "Enclosures", uom: "Units" },
  { id: "SJ005", name: "Plastic Resin", type: "Raw Material", category: "Molding", uom: "Kilograms" },
  { id: "SJ006", name: "Resistors 10kΩ", type: "Component", category: "Electronics", uom: "Pieces" },
  { id: "SJ007", name: "Capacitors 0.1μF", type: "Component", category: "Electronics", uom: "Pieces" },
  { id: "SJ008", name: "LED Lights", type: "Component", category: "Lighting", uom: "Units" },
  { id: "SJ009", name: "Thermal Adhesive", type: "Consumable", category: "Assembly", uom: "Grams" },
  { id: "SJ010", name: "PCB Boards", type: "Component", category: "Electronics", uom: "Pieces" },
  { id: "SJ011", name: "Soldering Wire", type: "Consumable", category: "Assembly", uom: "Spools" },
  { id: "SJ012", name: "Heat Shrink Tubing", type: "Consumable", category: "Wiring", uom: "Meters" },
  { id: "SJ013", name: "Transistors", type: "Component", category: "Electronics", uom: "Pieces" },
  { id: "SJ014", name: "Power Connectors", type: "Component", category: "Electronics", uom: "Pieces" },
  { id: "SJ015", name: "Thermal Compound", type: "Consumable", category: "Cooling", uom: "Tubes" }
];

// Materials for Puducherry location
export const mockMaterialsPuducherry: Material[] = [
  { id: "PY001", name: "Cotton Fabric", type: "Raw Material", category: "Textiles", uom: "Yards" },
  { id: "PY002", name: "Dye Solution", type: "Chemical", category: "Coloring", uom: "Liters" },
  { id: "PY003", name: "Thread Spools", type: "Consumable", category: "Stitching", uom: "Units" },
  { id: "PY004", name: "Buttons", type: "Component", category: "Fasteners", uom: "Gross" },
  { id: "PY005", name: "Zippers", type: "Component", category: "Fasteners", uom: "Dozen" },
  { id: "PY006", name: "Needles", type: "Consumable", category: "Stitching", uom: "Pack" },
  { id: "PY007", name: "Elastic Band", type: "Raw Material", category: "Textiles", uom: "Meters" },
  { id: "PY008", name: "Polyester Fabric", type: "Raw Material", category: "Textiles", uom: "Yards" },
  { id: "PY009", name: "Metal Buckles", type: "Component", category: "Fasteners", uom: "Dozen" },
  { id: "PY010", name: "Nylon Thread", type: "Consumable", category: "Stitching", uom: "Spools" },
  { id: "PY011", name: "Sewing Machine Oil", type: "Consumable", category: "Maintenance", uom: "Bottles" },
  { id: "PY012", name: "Velcro Strips", type: "Component", category: "Fasteners", uom: "Meters" },
  { id: "PY013", name: "Fabric Dye", type: "Chemical", category: "Coloring", uom: "Kilograms" },
  { id: "PY014", name: "Pattern Paper", type: "Consumable", category: "Design", uom: "Sheets" },
  { id: "PY015", name: "Embroidery Thread", type: "Consumable", category: "Decoration", uom: "Spools" }
];


import { SupplierFeedback } from "@/types/supplierFeedback";

export const mockSupplierFeedback: SupplierFeedback[] = [
  {
    id: "FB-001",
    supplierName: "Falcon Alloys",
    rating: 3,
    feedback: "Needs to improve packaging consistency. Last three shipments had different packaging standards.",
    tags: ["Packaging Issue"],
    submittedBy: "<PERSON>",
    submitterRole: "QA Analyst",
    submittedDate: "2025-08-10",
    sentiment: "Neutral"
  },
  {
    id: "FB-002",
    supplierName: "NovaChem",
    rating: 2,
    feedback: "Delays in responding to quality concerns. Takes an average of 72 hours to acknowledge issues.",
    tags: ["Communication Issue", "Quality Issue"],
    submittedBy: "<PERSON>",
    submitterRole: "Procurement Manager",
    submittedDate: "2025-08-05",
    sentiment: "Negative"
  },
  {
    id: "FB-003",
    supplierName: "Omega Polymers",
    rating: 5,
    feedback: "Excellent communication and responsiveness. Technical support team is highly knowledgeable.",
    tags: ["Communication Issue"],
    submittedBy: "<PERSON>",
    submitterRole: "Production Lead",
    submittedDate: "2025-08-12",
    sentiment: "Positive"
  },
  {
    id: "FB-004",
    supplierName: "MetalTech Inc.",
    rating: 4,
    feedback: "Good quality overall, but occasional delays in delivery schedules.",
    tags: ["Delivery Issue", "Quality Issue"],
    submittedBy: "Michael T.",
    submitterRole: "Operations Director",
    submittedDate: "2025-07-28",
    sentiment: "Positive"
  },
  {
    id: "FB-005",
    supplierName: "Global Packaging Solutions",
    rating: 2,
    feedback: "Documentation frequently missing key details required for compliance.",
    tags: ["Documentation Issue"],
    submittedBy: "Lisa R.",
    submitterRole: "Compliance Officer",
    submittedDate: "2025-08-08",
    sentiment: "Negative"
  },
  {
    id: "FB-006",
    supplierName: "FastChip Electronics",
    rating: 4,
    feedback: "Components consistently meet specifications. Would appreciate more detailed technical documentation.",
    tags: ["Documentation Issue"],
    submittedBy: "David L.",
    submitterRole: "Product Engineer",
    submittedDate: "2025-07-30",
    sentiment: "Positive"
  }
];

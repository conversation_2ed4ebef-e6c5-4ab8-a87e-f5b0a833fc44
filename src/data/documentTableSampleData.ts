import { Document } from "@/components/document-hub/table/DocumentTableTypes";

/**
 * Sample data for the Document Table Component
 * This demonstrates the structure and types of data expected by the DocumentTable
 */
export const sampleDocumentTableData: Document[] = [
  {
    id: "doc-001",
    title: "Quality Management System Manual",
    version: "v2.1",
    department: "Quality Assurance",
    category: "Quality Manual",
    processes: "Document Control, Management Review",
    status: "Published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-03-15",
    publishedDate: "2024-03-15",
    reviewDate: "2025-03-15",
    description: "Comprehensive quality management system documentation following ISO 9001 standards.",
    parentFolderId: "folder-quality"
  },
  {
    id: "doc-002",
    title: "Standard Operating Procedure - Equipment Calibration",
    version: "v1.3",
    department: "Quality Assurance",
    category: "SOP",
    processes: "Calibration Management",
    status: "Published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-20",
    publishedDate: "2024-02-20",
    reviewDate: "2025-02-20",
    description: "Standard procedure for equipment calibration and maintenance schedules.",
    parentFolderId: "folder-procedures"
  },
  {
    id: "doc-003",
    title: "Employee Training Record Template",
    version: "v1.0",
    department: "Human Resources",
    category: "Template",
    processes: "Training Management",
    status: "Draft",
    assignee: "Emma Davis",
    approver: "Robert Taylor",
    date: "2024-01-10",
    description: "Template for recording employee training activities and certifications.",
    parentFolderId: null
  },
  {
    id: "doc-004",
    title: "Supplier Evaluation Checklist",
    version: "v2.0",
    department: "Procurement",
    category: "Checklist",
    processes: "Supplier Management",
    status: "Under Review",
    assignee: "James Anderson",
    approver: "Lisa Williams",
    date: "2024-03-01",
    description: "Comprehensive checklist for evaluating new suppliers and vendors.",
    parentFolderId: "folder-procurement"
  },
  {
    id: "doc-005",
    title: "Risk Assessment Matrix",
    version: "v1.2",
    department: "Quality Assurance",
    category: "Risk Management",
    processes: "Risk Assessment",
    status: "Published",
    assignee: "Michael Brown",
    approver: "Sarah Johnson",
    date: "2024-02-15",
    publishedDate: "2024-02-15",
    reviewDate: "2025-02-15",
    description: "Matrix for identifying and assessing operational risks across departments.",
    parentFolderId: "folder-risk"
  },
  {
    id: "doc-006",
    title: "Customer Complaint Handling Procedure",
    version: "v1.1",
    department: "Customer Service",
    category: "Procedure",
    processes: "Customer Relations",
    status: "Published",
    assignee: "Jennifer Lee",
    approver: "Mark Thompson",
    date: "2024-01-25",
    publishedDate: "2024-01-25",
    reviewDate: "2025-01-25",
    description: "Step-by-step procedure for handling customer complaints and feedback.",
    parentFolderId: "folder-customer"
  },
  {
    id: "doc-007",
    title: "Environmental Impact Assessment",
    version: "v1.0",
    department: "Environmental",
    category: "Assessment",
    processes: "Environmental Management",
    status: "Under Review",
    assignee: "Alex Chen",
    approver: "Dr. Patricia Green",
    date: "2024-03-10",
    description: "Assessment of environmental impact for new manufacturing processes.",
    parentFolderId: "folder-environmental"
  },
  {
    id: "doc-008",
    title: "IT Security Policy",
    version: "v3.0",
    department: "Information Technology",
    category: "Policy",
    processes: "Information Security",
    status: "Published",
    assignee: "Kevin Park",
    approver: "Chief Technology Officer",
    date: "2024-02-01",
    publishedDate: "2024-02-01",
    reviewDate: "2025-02-01",
    description: "Comprehensive IT security policy covering data protection and access controls.",
    parentFolderId: "folder-it"
  },
  {
    id: "doc-009",
    title: "Financial Audit Checklist",
    version: "v2.2",
    department: "Finance",
    category: "Checklist",
    processes: "Financial Management",
    status: "Published",
    assignee: "Rachel Martinez",
    approver: "Chief Financial Officer",
    date: "2024-01-15",
    publishedDate: "2024-01-15",
    reviewDate: "2025-01-15",
    description: "Checklist for conducting internal financial audits and compliance reviews.",
    parentFolderId: "folder-finance"
  },
  {
    id: "doc-010",
    title: "Product Development Workflow",
    version: "v1.4",
    department: "Research & Development",
    category: "Workflow",
    processes: "Product Development",
    status: "Draft",
    assignee: "Dr. Thomas Kim",
    approver: "VP of R&D",
    date: "2024-03-05",
    description: "Workflow documentation for new product development lifecycle.",
    parentFolderId: "folder-rd"
  }
];

/**
 * Example usage of the StandaloneDocumentTable component
 */
export const documentTableUsageExample = `
import React from 'react';
import { StandaloneDocumentTable } from '@/components/document-hub';
import { sampleDocumentTableData } from '@/data/documentTableSampleData';

const MyDocumentPage = () => {
  return (
    <div className="p-6">
      <StandaloneDocumentTable
        title="My Document Library"
        subtitle="Manage and view all documents"
        showSearch={true}
        showFilters={true}
        initialDocuments={sampleDocumentTableData}
      />
    </div>
  );
};

export default MyDocumentPage;
`;

/**
 * Available document statuses
 */
export const documentStatuses = [
  "Draft",
  "Under Review", 
  "Published",
  "Archived",
  "Expired"
] as const;

/**
 * Available document categories
 */
export const documentCategories = [
  "Quality Manual",
  "SOP",
  "Template", 
  "Checklist",
  "Risk Management",
  "Procedure",
  "Assessment",
  "Policy",
  "Workflow"
] as const;

/**
 * Available departments
 */
export const departments = [
  "Quality Assurance",
  "Human Resources",
  "Procurement",
  "Customer Service", 
  "Environmental",
  "Information Technology",
  "Finance",
  "Research & Development"
] as const;

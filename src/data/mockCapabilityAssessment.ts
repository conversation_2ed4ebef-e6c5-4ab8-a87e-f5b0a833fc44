
import { FormTemplate } from "@/types/formBuilder";

export const mockCapabilityAssessment: FormTemplate = {
  id: "capability-assessment-001",
  name: "Supplier Capability Assessment",
  description: "Comprehensive assessment of supplier capabilities and quality systems",
  sections: [
    {
      id: "company-info",
      title: "Company Information",
      description: "Basic company details and contact information",
      fields: [
        {
          id: "company-name",
          type: "text",
          label: "Company Name",
          required: true,
          placeholder: "Enter your company name"
        },
        {
          id: "company-address",
          type: "textarea",
          label: "Company Address",
          required: true,
          placeholder: "Enter your complete address"
        },
        {
          id: "primary-contact",
          type: "text",
          label: "Primary Contact Person",
          required: true
        },
        {
          id: "contact-email",
          type: "text",
          label: "Contact Email",
          required: true,
          placeholder: "<EMAIL>"
        }
      ]
    },
    {
      id: "quality-systems",
      title: "Quality Management Systems",
      description: "Information about your quality management certifications and processes",
      fields: [
        {
          id: "iso-certification",
          type: "yesno",
          label: "Do you have ISO 9001 certification?",
          required: true
        },
        {
          id: "other-certifications",
          type: "checkbox",
          label: "Other Quality Certifications",
          options: ["ISO 14001", "ISO 45001", "TS 16949", "AS9100", "Other"],
          required: false
        },
        {
          id: "quality-manual",
          type: "file",
          label: "Upload Quality Manual",
          required: false,
          allowAttachments: true
        }
      ]
    },
    {
      id: "capabilities",
      title: "Manufacturing Capabilities",
      description: "Details about your manufacturing processes and capabilities",
      fields: [
        {
          id: "manufacturing-processes",
          type: "checkbox",
          label: "Manufacturing Processes",
          options: ["Machining", "Welding", "Assembly", "Testing", "Coating", "Heat Treatment"],
          required: true
        },
        {
          id: "capacity-rating",
          type: "rating",
          label: "Overall Production Capacity",
          min: 1,
          max: 5,
          required: true
        },
        {
          id: "annual-revenue",
          type: "currency",
          label: "Annual Revenue",
          currency: "USD",
          required: false
        }
      ]
    }
  ],
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  createdBy: "system",
  status: "published",
  isTemplate: true,
  languages: ["en"],
  defaultLanguage: "en"
};

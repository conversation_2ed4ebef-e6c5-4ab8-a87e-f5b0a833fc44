
export interface BatchInfo {
  id: string;
  name: string;
  lotNumber: string;
  productionDate: string;
  quantity: number;
  status: 'Valid' | 'At Risk' | 'Expired' | 'Recalled';
  location: string;
  supplier: string;
  riskScore?: number;
  qualityScore?: number;
  documents?: string[];
}

export interface SupplyChainNode {
  id: string;
  tier: string;
  component: string;
  supplier: string;
  batchId: string;
  status: 'Valid' | 'At Risk' | 'Expired';
  risk: 'Low' | 'Medium' | 'High';
  certifications: string[];
  lastAudit: string;
  location: string;
  coordinates?: { x: number; y: number };
  riskScore?: number;
  qualityScore?: number;
  documents?: string[];
}

export interface MaterialOrigin {
  id: string;
  materialName: string;
  origin: string;
  supplier: string;
  batchNumber: string;
  certifications: string[];
  extractionDate: string;
  processingDate: string;
  status: 'Verified' | 'Pending' | 'Expired';
  riskScore?: number;
  qualityScore?: number;
  documents?: string[];
}

export interface QualityAlert {
  id: string;
  alertType: 'Critical' | 'Warning' | 'Info';
  title: string;
  description: string;
  batchId: string;
  supplier: string;
  dateCreated: string;
  status: 'Open' | 'Investigating' | 'Resolved';
  priority: 'High' | 'Medium' | 'Low';
  affectedQuantity?: string;
  estimatedImpact?: string;
  assignedTo?: string;
}

export const mockBatches: BatchInfo[] = [
  {
    id: 'BATT-2319-AL',
    name: 'Lithium Battery Pack v3.1',
    lotNumber: 'BATT-2319-AL',
    productionDate: '2024-01-15',
    quantity: 2500,
    status: 'Valid',
    location: 'San Jose Factory',
    supplier: 'Advanced Battery Systems',
    riskScore: 15,
    qualityScore: 96,
    documents: ['Quality Certificate', 'Performance Test Report', 'Safety Compliance']
  },
  {
    id: 'CASE-2319-01',
    name: 'Battery Casing Assembly',
    lotNumber: 'CASE-2319-01',
    productionDate: '2024-01-10',
    quantity: 3000,
    status: 'Valid',
    location: 'Bengaluru Plant',
    supplier: 'Advanced Casing Solutions',
    riskScore: 8,
    qualityScore: 98,
    documents: ['Material Certificate', 'Dimensional Report', 'Leak Test Results']
  },
  {
    id: 'CHIP-4521-BC',
    name: 'Control Circuit Board',
    lotNumber: 'CHIP-4521-BC',
    productionDate: '2024-01-05',
    quantity: 2800,
    status: 'At Risk',
    location: 'Puducherry Facility',
    supplier: 'TechChip Industries',
    riskScore: 25,
    qualityScore: 89,
    documents: ['Electrical Test Report', 'Component Traceability', 'Quality Audit']
  },
  {
    id: 'SENS-7841-MX',
    name: 'Temperature Sensor Module',
    lotNumber: 'SENS-7841-MX',
    productionDate: '2024-01-08',
    quantity: 5000,
    status: 'Valid',
    location: 'Tijuana, Mexico',
    supplier: 'SensorTech Mexico',
    riskScore: 12,
    qualityScore: 94,
    documents: ['Calibration Certificate', 'Environmental Test', 'Reliability Report']
  },
  {
    id: 'WIRE-9921-CN',
    name: 'High Voltage Wiring Harness',
    lotNumber: 'WIRE-9921-CN',
    productionDate: '2024-01-12',
    quantity: 1200,
    status: 'Expired',
    location: 'Guangzhou, China',
    supplier: 'CableMax Industries',
    riskScore: 42,
    qualityScore: 78,
    documents: ['Material Certificate', 'Insulation Test', 'Continuity Check']
  }
];

export const mockSupplyChainNodes: SupplyChainNode[] = [
  {
    id: '1',
    tier: 'Tier 1',
    component: 'Battery Casing',
    supplier: 'Advanced Casing Solutions',
    batchId: 'CASE-2319-01',
    status: 'Valid',
    risk: 'Low',
    certifications: ['ISO 9001', 'IATF 16949', 'AS9100'],
    lastAudit: '2024-01-10',
    location: 'Bengaluru, India',
    coordinates: { x: 20, y: 30 },
    riskScore: 8,
    qualityScore: 98,
    documents: ['Supplier Audit Report', 'Material Certificate', 'Process Control Plan']
  },
  {
    id: '2',
    tier: 'Tier 2',
    component: 'Aluminum Sheet',
    supplier: 'MetalForm Industries',
    batchId: 'ALU-4521-BC',
    status: 'At Risk',
    risk: 'Medium',
    certifications: ['ISO 9001', 'ASI Performance Standard'],
    lastAudit: '2023-11-15',
    location: 'Mumbai, India',
    coordinates: { x: 15, y: 50 },
    riskScore: 22,
    qualityScore: 91,
    documents: ['Mill Certificate', 'Chemical Analysis', 'Supplier Assessment']
  },
  {
    id: '3',
    tier: 'Tier 3',
    component: 'Raw Aluminum',
    supplier: 'Global Mining Corp',
    batchId: 'RAW-7834-MX',
    status: 'Expired',
    risk: 'High',
    certifications: ['Mining License', 'Environmental Permit'],
    lastAudit: '2023-08-22',
    location: 'Mexico City, Mexico',
    coordinates: { x: 60, y: 70 },
    riskScore: 38,
    qualityScore: 82,
    documents: ['Environmental Impact Assessment', 'Grade Analysis', 'Mining Permit']
  },
  {
    id: '4',
    tier: 'Tier 1',
    component: 'Control Circuit',
    supplier: 'TechChip Industries',
    batchId: 'CHIP-4521-BC',
    status: 'Valid',
    risk: 'Low',
    certifications: ['ISO 9001', 'IPC-A-610', 'ISO 14001'],
    lastAudit: '2024-01-12',
    location: 'Puducherry, India',
    coordinates: { x: 25, y: 35 },
    riskScore: 12,
    qualityScore: 95,
    documents: ['Functional Test Report', 'Component Traceability', 'Quality Manual']
  },
  {
    id: '5',
    tier: 'Tier 2',
    component: 'Microprocessor',
    supplier: 'SemiConductor Global',
    batchId: 'MCU-8844-TW',
    status: 'Valid',
    risk: 'Low',
    certifications: ['ISO 9001', 'AEC-Q100', 'ISO/TS 16949'],
    lastAudit: '2024-01-05',
    location: 'Hsinchu, Taiwan',
    coordinates: { x: 45, y: 25 },
    riskScore: 6,
    qualityScore: 99,
    documents: ['Reliability Test Report', 'Electrical Specifications', 'Qualification Report']
  },
  {
    id: '6',
    tier: 'Tier 1',
    component: 'Battery Cells',
    supplier: 'PowerCell Technologies',
    batchId: 'CELL-9912-KR',
    status: 'Valid',
    risk: 'Low',
    certifications: ['UN38.3', 'UL 1973', 'IEC 62133'],
    lastAudit: '2024-01-14',
    location: 'Seoul, South Korea',
    coordinates: { x: 75, y: 45 },
    riskScore: 10,
    qualityScore: 97,
    documents: ['Battery Test Report', 'Safety Certificate', 'Performance Analysis']
  }
];

export const mockMaterialOrigins: MaterialOrigin[] = [
  {
    id: '1',
    materialName: 'Lithium Carbonate',
    origin: 'Salar de Atacama, Chile',
    supplier: 'SQM Lithium',
    batchNumber: 'LIC-8834-CL',
    certifications: ['RMI Compliant', 'IRMA Standard', 'ISO 14001'],
    extractionDate: '2023-12-10',
    processingDate: '2023-12-15',
    status: 'Verified',
    riskScore: 5,
    qualityScore: 99,
    documents: ['Origin Certificate', 'Environmental Impact Report', 'Purity Analysis']
  },
  {
    id: '2',
    materialName: 'Cobalt Sulfate',
    origin: 'Katanga Province, DRC',
    supplier: 'Ethical Minerals Co.',
    batchNumber: 'COB-5521-DR',
    certifications: ['RCS Compliant', 'OECD Guidelines', 'CRAFT Certified'],
    extractionDate: '2023-11-20',
    processingDate: '2023-11-25',
    status: 'Verified',
    riskScore: 18,
    qualityScore: 94,
    documents: ['Conflict Minerals Report', 'Chain of Custody', 'Artisanal Mining Certificate']
  },
  {
    id: '3',
    materialName: 'Aluminum Alloy 6061',
    origin: 'Bauxite Mines, Australia',
    supplier: 'AluSource International',
    batchNumber: 'ALU-9921-AU',
    certifications: ['ASI Certified', 'ISO 14001'],
    extractionDate: '2023-10-15',
    processingDate: '2023-10-20',
    status: 'Pending',
    riskScore: 12,
    qualityScore: 96,
    documents: ['Mining Permit', 'Environmental Assessment', 'Material Properties']
  },
  {
    id: '4',
    materialName: 'Nickel Hydroxide',
    origin: 'Sudbury Basin, Canada',
    supplier: 'Canadian Nickel Corp',
    batchNumber: 'NIC-7744-CA',
    certifications: ['TSM Certified', 'ISO 14001', 'ICMM Member'],
    extractionDate: '2023-11-05',
    processingDate: '2023-11-12',
    status: 'Verified',
    riskScore: 8,
    qualityScore: 98,
    documents: ['Mining License', 'Environmental Monitoring', 'Quality Specification']
  },
  {
    id: '5',
    materialName: 'Rare Earth Elements',
    origin: 'Ganzhou, China',
    supplier: 'REE Processing Ltd',
    batchNumber: 'REE-3366-CN',
    certifications: ['China RoHS', 'ISO 9001'],
    extractionDate: '2023-12-01',
    processingDate: '2023-12-08',
    status: 'Expired',
    riskScore: 35,
    qualityScore: 85,
    documents: ['Export License', 'Processing Certificate', 'Contamination Report']
  }
];

export const mockQualityAlerts: QualityAlert[] = [
  {
    id: 'QA-001',
    alertType: 'Critical',
    title: 'Contamination Detected in Batch CASE-2319-01',
    description: 'Trace amounts of foreign metallic particles detected during final quality inspection. Immediate investigation required to prevent potential safety hazards.',
    batchId: 'CASE-2319-01',
    supplier: 'Advanced Casing Solutions',
    dateCreated: '2024-01-20',
    status: 'Investigating',
    priority: 'High',
    affectedQuantity: '500 units',
    estimatedImpact: 'Production halt for 2-3 days',
    assignedTo: 'Quality Team Lead - Sarah Chen'
  },
  {
    id: 'QA-002',
    alertType: 'Warning',
    title: 'Certificate Expiry Notice - ISO 9001',
    description: 'ISO 9001 certificate for supplier MetalForm Industries expires in 30 days. Renewal documentation required to maintain supplier qualification.',
    batchId: 'ALU-4521-BC',
    supplier: 'MetalForm Industries',
    dateCreated: '2024-01-18',
    status: 'Open',
    priority: 'Medium',
    affectedQuantity: 'All future batches',
    estimatedImpact: 'Supplier disqualification risk',
    assignedTo: 'Supplier Quality Manager - Mike Rodriguez'
  },
  {
    id: 'QA-003',
    alertType: 'Info',
    title: 'Routine Audit Scheduled',
    description: 'Routine quality audit scheduled for TechChip Industries next week. All documentation and process controls to be reviewed.',
    batchId: 'CHIP-4521-BC',
    supplier: 'TechChip Industries',
    dateCreated: '2024-01-16',
    status: 'Open',
    priority: 'Low',
    affectedQuantity: 'N/A',
    estimatedImpact: 'Operational verification',
    assignedTo: 'Senior Auditor - David Kim'
  },
  {
    id: 'QA-004',
    alertType: 'Critical',
    title: 'Raw Material Quality Issue',
    description: 'Quality parameters out of specification for raw aluminum batch. Tensile strength and corrosion resistance below acceptable limits. Production hold recommended.',
    batchId: 'RAW-7834-MX',
    supplier: 'Global Mining Corp',
    dateCreated: '2024-01-22',
    status: 'Open',
    priority: 'High',
    affectedQuantity: '2,500 kg',
    estimatedImpact: 'Material rejection and replacement',
    assignedTo: 'Materials Engineer - Lisa Wang'
  },
  {
    id: 'QA-005',
    alertType: 'Warning',
    title: 'Temperature Deviation in Transport',
    description: 'Battery cells exposed to temperatures outside specified range during transportation from Seoul facility. Performance testing required.',
    batchId: 'CELL-9912-KR',
    supplier: 'PowerCell Technologies',
    dateCreated: '2024-01-19',
    status: 'Investigating',
    priority: 'Medium',
    affectedQuantity: '1,200 cells',
    estimatedImpact: 'Additional testing and validation',
    assignedTo: 'Logistics Quality Specialist - James Park'
  },
  {
    id: 'QA-006',
    alertType: 'Info',
    title: 'Supplier Performance Review Due',
    description: 'Quarterly performance review due for SemiConductor Global. All KPIs and metrics to be evaluated.',
    batchId: 'MCU-8844-TW',
    supplier: 'SemiConductor Global',
    dateCreated: '2024-01-15',
    status: 'Resolved',
    priority: 'Low',
    affectedQuantity: 'N/A',
    estimatedImpact: 'Performance baseline update',
    assignedTo: 'Supplier Development Manager - Anna Zhang'
  },
  {
    id: 'QA-007',
    alertType: 'Critical',
    title: 'Conflict Minerals Documentation Gap',
    description: 'Missing conflict minerals documentation for cobalt sulfate batch. Compliance risk identified for OECD Due Diligence requirements.',
    batchId: 'COB-5521-DR',
    supplier: 'Ethical Minerals Co.',
    dateCreated: '2024-01-21',
    status: 'Open',
    priority: 'High',
    affectedQuantity: '95 kg',
    estimatedImpact: 'Regulatory compliance risk',
    assignedTo: 'Compliance Officer - Robert Martinez'
  },
  {
    id: 'QA-008',
    alertType: 'Warning',
    title: 'Calibration Due for Test Equipment',
    description: 'Calibration due for critical measurement equipment used in final inspection process. Potential impact on measurement accuracy.',
    batchId: 'SENS-7841-MX',
    supplier: 'SensorTech Mexico',
    dateCreated: '2024-01-17',
    status: 'Investigating',
    priority: 'Medium',
    affectedQuantity: 'All measurements after due date',
    estimatedImpact: 'Measurement reliability concern',
    assignedTo: 'Metrology Technician - Carlos Ruiz'
  }
];

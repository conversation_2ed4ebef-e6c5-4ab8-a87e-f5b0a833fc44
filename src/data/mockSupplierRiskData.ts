
import { SupplierRiskData } from "@/types/supplierRiskHeatmap";

export const mockSupplierRiskData: SupplierRiskData[] = [
  {
    id: "SUP-001",
    name: "Omega Polymers",
    auditScore: 92,
    riskScore: 15, // Low risk (scale 0-100, lower is better)
    spend: 1200000, // $1.2M
    category: "Raw Materials"
  },
  {
    id: "SUP-002",
    name: "NovaChem",
    auditScore: 64,
    riskScore: 85, // High risk
    spend: 500000, // $500K
    category: "Chemicals"
  },
  {
    id: "SUP-003",
    name: "MetalTech Inc.",
    auditScore: 78,
    riskScore: 45, // Medium risk
    spend: 820000, // $820K
    category: "Components"
  },
  {
    id: "SUP-004",
    name: "Precision Circuit Boards",
    auditScore: 88,
    riskScore: 25, // Low risk
    spend: 950000, // $950K
    category: "Electronics"
  },
  {
    id: "SUP-005",
    name: "Johnson Controls",
    auditScore: 69,
    riskScore: 65, // Medium-high risk
    spend: 350000, // $350K
    category: "Assemblies"
  },
  {
    id: "SUP-006",
    name: "FastChip Electronics",
    auditScore: 81,
    riskScore: 30, // Low-medium risk
    spend: 780000, // $780K
    category: "Electronics"
  },
  {
    id: "SUP-007",
    name: "Global Packaging Solutions",
    auditScore: 73,
    riskScore: 55, // Medium risk
    spend: 420000, // $420K
    category: "Packaging"
  },
  {
    id: "SUP-008",
    name: "Ace Chemical Supplies",
    auditScore: 62,
    riskScore: 80, // High risk
    spend: 290000, // $290K
    category: "Chemicals"
  },
  {
    id: "SUP-009",
    name: "FirstTier Components",
    auditScore: 85,
    riskScore: 28, // Low-medium risk
    spend: 680000, // $680K
    category: "Components"
  },
  {
    id: "SUP-010",
    name: "PowerTech Manufacturing",
    auditScore: 76,
    riskScore: 48, // Medium risk
    spend: 540000, // $540K
    category: "Assemblies"
  }
];

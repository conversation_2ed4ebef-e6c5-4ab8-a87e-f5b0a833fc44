
import { TrainingCertificate } from "@/types/certificates";

export const mockCertificates: TrainingCertificate[] = [
  // <PERSON> certificates
  {
    id: "CERT-001",
    trainingId: "TRN-001",
    trainingName: "ISO 9001:2015 Quality Management Systems",
    employeeId: "EMP001",
    employeeName: "<PERSON>",
    completionDate: "2024-11-15",
    issueDate: "2024-11-16",
    certificateId: "ISO9001-2024-SJ-001",
    validUntil: "2026-11-15",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/iso9001-sarah-johnson.pdf",
    issuer: "Quality Training Institute",
    credentialHash: "abc123def456"
  },
  {
    id: "CERT-002",
    trainingId: "TRN-002",
    trainingName: "Fire Safety and Emergency Procedures",
    employeeId: "EMP001",
    employeeName: "<PERSON>",
    completionDate: "2024-10-28",
    issueDate: "2024-10-29",
    certificateId: "FIRE-2024-SJ-002",
    validUntil: "2025-10-28",
    certificateType: "Digital Badge",
    status: "Active",
    badgeUrl: "/badges/fire-safety-badge.svg",
    issuer: "Safety First Training",
    credentialHash: "def456ghi789"
  },
  {
    id: "CERT-003",
    trainingId: "TRN-003",
    trainingName: "Good Manufacturing Practices (GMP)",
    employeeId: "EMP001",
    employeeName: "Sarah Johnson",
    completionDate: "2024-09-15",
    issueDate: "2024-09-16",
    certificateId: "GMP-2024-SJ-003",
    validUntil: "2025-01-15",
    certificateType: "PDF",
    status: "Expiring Soon",
    downloadUrl: "/certificates/gmp-sarah-johnson.pdf",
    issuer: "Pharmaceutical Training Board",
    credentialHash: "ghi789jkl012"
  },
  {
    id: "CERT-004",
    trainingId: "TRN-004",
    trainingName: "Data Privacy & GDPR Compliance",
    employeeId: "EMP001",
    employeeName: "Sarah Johnson",
    completionDate: "2023-12-10",
    issueDate: "2023-12-11",
    certificateId: "GDPR-2023-SJ-004",
    validUntil: "2024-12-10",
    certificateType: "Digital Badge",
    status: "Expired",
    badgeUrl: "/badges/gdpr-compliance-badge.svg",
    issuer: "Privacy Institute",
    credentialHash: "jkl012mno345"
  },
  {
    id: "CERT-005",
    trainingId: "TRN-005",
    trainingName: "Chemical Handling and Safety",
    employeeId: "EMP001",
    employeeName: "Sarah Johnson",
    completionDate: "2024-08-20",
    issueDate: "2024-08-21",
    certificateId: "CHEM-2024-SJ-005",
    validUntil: "2027-08-20",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/chemical-safety-sarah-johnson.pdf",
    issuer: "Industrial Safety Council",
    credentialHash: "mno345pqr678"
  },
  
  // Michael Chen certificates
  {
    id: "CERT-006",
    trainingId: "TRN-002",
    trainingName: "Fire Safety and Emergency Procedures",
    employeeId: "EMP002",
    employeeName: "Michael Chen",
    completionDate: "2024-10-28",
    issueDate: "2024-10-29",
    certificateId: "FIRE-2024-MC-002",
    validUntil: "2025-10-28",
    certificateType: "Digital Badge",
    status: "Active",
    badgeUrl: "/badges/fire-safety-badge.svg",
    issuer: "Safety First Training",
    credentialHash: "pqr678stu901"
  },
  {
    id: "CERT-007",
    trainingId: "TRN-001",
    trainingName: "ISO 9001:2015 Quality Management Systems",
    employeeId: "EMP002",
    employeeName: "Michael Chen",
    completionDate: "2024-09-20",
    issueDate: "2024-09-21",
    certificateId: "ISO9001-2024-MC-001",
    validUntil: "2026-09-20",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/iso9001-michael-chen.pdf",
    issuer: "Quality Training Institute",
    credentialHash: "stu901vwx234"
  },
  {
    id: "CERT-008",
    trainingId: "TRN-006",
    trainingName: "Lean Manufacturing Principles",
    employeeId: "EMP002",
    employeeName: "Michael Chen",
    completionDate: "2024-08-15",
    issueDate: "2024-08-16",
    certificateId: "LEAN-2024-MC-006",
    validUntil: "2026-08-15",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/lean-michael-chen.pdf",
    issuer: "Operations Excellence Institute",
    credentialHash: "vwx234yzab567"
  },

  // Emily Davis certificates
  {
    id: "CERT-009",
    trainingId: "TRN-003",
    trainingName: "Good Manufacturing Practices (GMP)",
    employeeId: "EMP003",
    employeeName: "Emily Davis",
    completionDate: "2024-01-10",
    issueDate: "2024-01-11",
    certificateId: "GMP-2024-ED-003",
    validUntil: "2025-01-10",
    certificateType: "PDF",
    status: "Expiring Soon",
    downloadUrl: "/certificates/gmp-emily-davis.pdf",
    issuer: "Pharmaceutical Training Board",
    credentialHash: "yzab567cdef890"
  },
  {
    id: "CERT-010",
    trainingId: "TRN-002",
    trainingName: "Fire Safety and Emergency Procedures",
    employeeId: "EMP003",
    employeeName: "Emily Davis",
    completionDate: "2024-11-01",
    issueDate: "2024-11-02",
    certificateId: "FIRE-2024-ED-002",
    validUntil: "2025-11-01",
    certificateType: "Digital Badge",
    status: "Active",
    badgeUrl: "/badges/fire-safety-badge.svg",
    issuer: "Safety First Training",
    credentialHash: "cdef890ghij123"
  },

  // David Rodriguez certificates
  {
    id: "CERT-011",
    trainingId: "TRN-005",
    trainingName: "Chemical Handling and Safety",
    employeeId: "EMP004",
    employeeName: "David Rodriguez",
    completionDate: "2024-07-15",
    issueDate: "2024-07-16",
    certificateId: "CHEM-2024-DR-005",
    validUntil: "2027-07-15",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/chemical-safety-david-rodriguez.pdf",
    issuer: "Industrial Safety Council",
    credentialHash: "ghij123klmn456"
  },
  {
    id: "CERT-012",
    trainingId: "TRN-004",
    trainingName: "Data Privacy & GDPR Compliance",
    employeeId: "EMP004",
    employeeName: "David Rodriguez",
    completionDate: "2024-06-05",
    issueDate: "2024-06-06",
    certificateId: "GDPR-2024-DR-004",
    validUntil: "2025-06-05",
    certificateType: "Digital Badge",
    status: "Active",
    badgeUrl: "/badges/gdpr-compliance-badge.svg",
    issuer: "Privacy Institute",
    credentialHash: "klmn456opqr789"
  },

  // Lisa Thompson certificates
  {
    id: "CERT-013",
    trainingId: "TRN-001",
    trainingName: "ISO 9001:2015 Quality Management Systems",
    employeeId: "EMP005",
    employeeName: "Lisa Thompson",
    completionDate: "2024-05-10",
    issueDate: "2024-05-11",
    certificateId: "ISO9001-2024-LT-001",
    validUntil: "2026-05-10",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/iso9001-lisa-thompson.pdf",
    issuer: "Quality Training Institute",
    credentialHash: "opqr789stuv012"
  },
  {
    id: "CERT-014",
    trainingId: "TRN-002",
    trainingName: "Fire Safety and Emergency Procedures",
    employeeId: "EMP005",
    employeeName: "Lisa Thompson",
    completionDate: "2024-11-05",
    issueDate: "2024-11-06",
    certificateId: "FIRE-2024-LT-002",
    validUntil: "2025-11-05",
    certificateType: "Digital Badge",
    status: "Active",
    badgeUrl: "/badges/fire-safety-badge.svg",
    issuer: "Safety First Training",
    credentialHash: "stuv012wxyz345"
  },

  // James Wilson certificates
  {
    id: "CERT-015",
    trainingId: "TRN-006",
    trainingName: "Lean Manufacturing Principles",
    employeeId: "EMP006",
    employeeName: "James Wilson",
    completionDate: "2024-03-20",
    issueDate: "2024-03-21",
    certificateId: "LEAN-2024-JW-006",
    validUntil: "2026-03-20",
    certificateType: "PDF",
    status: "Active",
    downloadUrl: "/certificates/lean-james-wilson.pdf",
    issuer: "Operations Excellence Institute",
    credentialHash: "wxyz345abcd678"
  },
  {
    id: "CERT-016",
    trainingId: "TRN-003",
    trainingName: "Good Manufacturing Practices (GMP)",
    employeeId: "EMP006",
    employeeName: "James Wilson",
    completionDate: "2023-12-15",
    issueDate: "2023-12-16",
    certificateId: "GMP-2023-JW-003",
    validUntil: "2024-12-15",
    certificateType: "PDF",
    status: "Expired",
    downloadUrl: "/certificates/gmp-james-wilson.pdf",
    issuer: "Pharmaceutical Training Board",
    credentialHash: "abcd678efgh901"
  }
];

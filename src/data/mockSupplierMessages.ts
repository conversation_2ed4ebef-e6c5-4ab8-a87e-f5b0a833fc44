
import { MessageThread, EmailTemplate } from "@/types/supplierMessages";

export const mockMessageThreads: MessageThread[] = [
  {
    id: "thread-001",
    subject: "Q2 2024 Audit Schedule Confirmation",
    tags: ["Audit"],
    lastMessageDate: "2024-03-20T14:30:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-001",
        threadId: "thread-001",
        from: "<EMAIL>",
        fromName: "<PERSON> - Quality Assurance",
        to: "<EMAIL>",
        toName: "TechChip Quality Team",
        subject: "Q2 2024 Audit Schedule Confirmation",
        body: "Dear TechChip Quality Team,\n\nWe are confirming the audit schedule for Q2 2024. The on-site audit has been scheduled for April 15-17, 2024. Please ensure all documentation is prepared according to our audit checklist.\n\nBest regards,\nJane <PERSON>",
        timestamp: "2024-03-20T14:30:00Z",
        attachments: [
          {
            id: "att-001",
            name: "Audit_Checklist_Q2_2024.pdf",
            type: "application/pdf",
            size: "2.3 MB",
            url: "/documents/audit-checklist.pdf"
          }
        ],
        isRead: false
      }
    ]
  },
  {
    id: "thread-002",
    subject: "CAPA-2024-087 - Calibration Records Issue",
    tags: ["CAPA", "Urgent"],
    lastMessageDate: "2024-03-19T16:45:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-002",
        threadId: "thread-002",
        from: "<EMAIL>",
        fromName: "Michael Rodriguez - CAPA Manager",
        to: "<EMAIL>",
        toName: "MetalForm Operations",
        subject: "CAPA-2024-087 - Calibration Records Issue",
        body: "Dear MetalForm Team,\n\nWe have identified a non-conformance regarding calibration records for measurement equipment (CAPA-2024-087). Please provide corrective action plan within 5 business days.\n\nThe issue involves missing calibration certificates for gauges used in dimensional inspection. This affects lots shipped between Feb 1-15, 2024.\n\nRegards,\nMichael Rodriguez",
        timestamp: "2024-03-19T16:45:00Z",
        attachments: [
          {
            id: "att-002",
            name: "CAPA-2024-087_Details.pdf",
            type: "application/pdf",
            size: "1.8 MB",
            url: "/documents/capa-details.pdf"
          }
        ],
        isRead: false
      }
    ]
  },
  {
    id: "thread-003",
    subject: "New Purchase Order PO-2024-156 - Battery Components",
    tags: ["PO"],
    lastMessageDate: "2024-03-18T11:20:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-003",
        threadId: "thread-003",
        from: "<EMAIL>",
        fromName: "Sarah Johnson - Procurement",
        to: "<EMAIL>",
        toName: "PowerCell Technologies Sales",
        subject: "New Purchase Order PO-2024-156 - Battery Components",
        body: "Dear PowerCell Sales Team,\n\nPlease find attached Purchase Order PO-2024-156 for lithium-ion battery components. The order includes:\n\n- 2,000 units of Model BT-3500\n- 1,500 units of Model BT-2200\n\nRequired delivery date: May 15, 2024\nShipping address: [Facility details attached]\n\nPlease confirm receipt and provide delivery confirmation.\n\nBest regards,\nSarah Johnson",
        timestamp: "2024-03-18T11:20:00Z",
        attachments: [
          {
            id: "att-003",
            name: "PO-2024-156.pdf",
            type: "application/pdf",
            size: "945 KB",
            url: "/documents/purchase-order.pdf"
          },
          {
            id: "att-004",
            name: "Shipping_Details.xlsx",
            type: "application/excel",
            size: "156 KB",
            url: "/documents/shipping-details.xlsx"
          }
        ],
        isRead: true
      }
    ]
  },
  {
    id: "thread-004",
    subject: "Certificate Expiration Notice - ISO 14001",
    tags: ["General", "Urgent"],
    lastMessageDate: "2024-03-17T09:15:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-004",
        threadId: "thread-004",
        from: "<EMAIL>",
        fromName: "Lisa Chen - Compliance Manager",
        to: "<EMAIL>",
        toName: "Global Mining Administration",
        subject: "Certificate Expiration Notice - ISO 14001",
        body: "Dear Global Mining Team,\n\nThis is a notification that your ISO 14001:2015 Environmental Management System certificate is set to expire on April 30, 2024.\n\nTo maintain your approved supplier status, please provide:\n1. Updated certificate or renewal documentation\n2. Surveillance audit reports\n3. Any changes to environmental management procedures\n\nPlease submit these documents by April 15, 2024.\n\nThank you,\nLisa Chen",
        timestamp: "2024-03-17T09:15:00Z",
        attachments: [],
        isRead: true
      }
    ]
  },
  {
    id: "thread-005",
    subject: "Monthly Performance Review - February 2024",
    tags: ["General"],
    lastMessageDate: "2024-03-16T14:00:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-005",
        threadId: "thread-005",
        from: "<EMAIL>",
        fromName: "David Park - Performance Analyst",
        to: "<EMAIL>",
        toName: "SemiCondu Technologies Management",
        subject: "Monthly Performance Review - February 2024",
        body: "Dear SemiCondu Management,\n\nAttached is your February 2024 performance review. Key highlights:\n\n✅ On-time delivery: 97.2% (Target: 95%)\n✅ Quality score: 94.8% (Target: 90%)\n⚠️ Cost performance: 88.1% (Target: 85%)\n\nOverall score: 93.4% - Excellent performance!\n\nAreas for improvement:\n- Lead time optimization\n- Packaging efficiency\n\nNext review scheduled for April 15, 2024.\n\nBest regards,\nDavid Park",
        timestamp: "2024-03-16T14:00:00Z",
        attachments: [
          {
            id: "att-005",
            name: "Performance_Review_Feb2024.pdf",
            type: "application/pdf",
            size: "3.2 MB",
            url: "/documents/performance-review.pdf"
          }
        ],
        isRead: true
      }
    ]
  },
  {
    id: "thread-006",
    subject: "Security Vulnerability Alert - Action Required",
    tags: ["Urgent"],
    lastMessageDate: "2024-03-21T08:30:00Z",
    participants: ["<EMAIL>", "<EMAIL>"],
    messages: [
      {
        id: "msg-006",
        threadId: "thread-006", 
        from: "<EMAIL>",
        fromName: "Alex Thompson - Information Security",
        to: "<EMAIL>",
        toName: "TechChip IT Department",
        subject: "Security Vulnerability Alert - Action Required",
        body: "URGENT: Security Vulnerability Alert\n\nDear TechChip IT Team,\n\nWe have identified a critical security vulnerability in your supplier portal access. Immediate action required:\n\n1. Update all user passwords\n2. Enable multi-factor authentication\n3. Review access logs for suspicious activity\n\nDetailed remediation steps are attached. Please confirm completion by March 23, 2024.\n\nContact our security team immediately if you need assistance.\n\nUrgent regards,\nAlex Thompson",
        timestamp: "2024-03-21T08:30:00Z",
        attachments: [
          {
            id: "att-006",
            name: "Security_Remediation_Steps.pdf",
            type: "application/pdf",
            size: "1.1 MB",
            url: "/documents/security-remediation.pdf"
          }
        ],
        isRead: false
      }
    ]
  }
];

export const mockEmailTemplates: EmailTemplate[] = [
  {
    id: "template-001",
    name: "Audit Notification",
    subject: "Upcoming Supplier Audit - {{supplier_name}}",
    body: "Dear {{supplier_name}} Team,\n\nThis is to notify you of an upcoming supplier audit scheduled for {{audit_date}}.\n\nPlease prepare the following documentation:\n- Quality management system records\n- Calibration certificates\n- Training records\n- Process control documents\n\nOur audit team will arrive at {{audit_time}} at your facility located at {{facility_address}}.\n\nIf you have any questions, please contact our audit <NAME_EMAIL>.\n\nBest regards,\n{{auditor_name}}\nQuality Assurance Team",
    tags: ["Audit"]
  },
  {
    id: "template-002",
    name: "CAPA Request",
    subject: "Corrective Action Required - {{capa_number}}",
    body: "Dear {{supplier_name}},\n\nWe have identified a non-conformance that requires corrective action (CAPA Number: {{capa_number}}).\n\nIssue Description:\n{{issue_description}}\n\nAffected Products/Lots:\n{{affected_products}}\n\nRequired Actions:\n1. Immediate containment\n2. Root cause analysis\n3. Corrective action plan\n4. Preventive measures\n\nPlease respond within {{response_days}} business days with your action plan.\n\nRegards,\n{{capa_manager}}\nCAPA Management Team",
    tags: ["CAPA", "NC"]
  },
  {
    id: "template-003", 
    name: "Purchase Order",
    subject: "New Purchase Order - {{po_number}}",
    body: "Dear {{supplier_name}},\n\nPlease find attached Purchase Order {{po_number}} dated {{po_date}}.\n\nOrder Details:\n{{order_details}}\n\nDelivery Requirements:\n- Required delivery date: {{delivery_date}}\n- Shipping address: {{shipping_address}}\n- Special instructions: {{special_instructions}}\n\nPlease confirm receipt and provide expected delivery confirmation.\n\nThank you,\n{{buyer_name}}\nProcurement Team",
    tags: ["PO"]
  }
];

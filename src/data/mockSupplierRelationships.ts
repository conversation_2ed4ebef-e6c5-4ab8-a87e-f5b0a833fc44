
import { SupplierRelationship } from "@/types/supplierRelationship";

export const mockSupplierRelationships: SupplierRelationship[] = [
  {
    id: "SRM-001",
    name: "Delta Fasteners",
    category: "Hardware",
    status: "Active",
    overallScore: 88,
    contracts: [
      {
        id: "CON-001",
        name: "Supply Agreement 2024-2026",
        title: "Supply Agreement 2024-2026",
        type: "Supply Agreement",
        status: "Active",
        startDate: "2024-01-01",
        endDate: "2026-12-31",
        value: 2500000,
        renewalDate: "2026-10-01"
      }
    ],
    communications: [
      {
        id: "COMM-001",
        date: "2024-11-20",
        type: "email",
        subject: "NCR Root Cause Analysis Required",
        status: "sent",
        priority: "High"
      },
      {
        id: "COMM-002",
        date: "2024-10-15",
        type: "meeting",
        subject: "Q3 Performance Review",
        status: "completed",
        priority: "Medium"
      }
    ],
    performanceHistory: [
      {
        period: "Q2 2024",
        score: 88,
        date: "2024-06-30",
        qualityScore: 85,
        deliveryScore: 90,
        costScore: 89,
        otifScore: 87
      },
      {
        period: "Q3 2024",
        score: 91,
        date: "2024-09-30",
        qualityScore: 89,
        deliveryScore: 95,
        costScore: 89,
        otifScore: 91
      }
    ],
    riskEvents: [
      {
        id: "RISK-001",
        date: "2024-11-18",
        type: "Quality",
        severity: "Medium",
        description: "Non-conforming fasteners found in batch DF-2024-Q3-001",
        status: "In Progress",
        mitigationPlan: "Additional quality checks implemented"
      }
    ],
    contact: {
      name: "Mike Chen",
      email: "<EMAIL>",
      phone: "******-0123",
      role: "Quality Manager"
    },
    otifMetrics: {
      currentOTIF: 87.3,
      targetOTIF: 92.0,
      onTimeDelivery: 91.2,
      inFullDelivery: 95.8,
      trend: "up",
      monthlyData: [
        { month: "Jan", otif: 85.2, onTime: 89.1, inFull: 95.6, orders: 78 },
        { month: "Feb", otif: 86.8, onTime: 90.4, inFull: 95.9, orders: 82 },
        { month: "Mar", otif: 87.3, onTime: 91.2, inFull: 95.8, orders: 85 },
        { month: "Apr", otif: 88.1, onTime: 92.1, inFull: 95.7, orders: 79 },
        { month: "May", otif: 86.9, onTime: 90.8, inFull: 95.6, orders: 83 },
        { month: "Jun", otif: 87.3, onTime: 91.2, inFull: 95.8, orders: 81 }
      ],
      deliveryPerformance: {
        onTime: 91.2,
        late: 7.3,
        early: 1.5
      },
      qualityMetrics: {
        firstPassYield: 97.1,
        defectRate: 0.03,
        customerComplaints: 5
      }
    }
  },
  {
    id: "SRM-002",
    name: "Omega Polymers",
    category: "Materials",
    status: "Active",
    overallScore: 94,
    contracts: [
      {
        id: "CON-002",
        name: "Polymer Supply Contract",
        title: "Polymer Supply Contract",
        type: "Supply Agreement",
        status: "Active",
        startDate: "2023-06-01",
        endDate: "2025-05-31",
        value: 1800000,
        renewalDate: "2025-02-01"
      }
    ],
    communications: [
      {
        id: "COMM-003",
        date: "2024-11-22",
        type: "email",
        subject: "New Product Certification Complete",
        status: "sent",
        priority: "Medium"
      }
    ],
    performanceHistory: [
      {
        period: "Q2 2024",
        score: 92,
        date: "2024-06-30",
        qualityScore: 95,
        deliveryScore: 88,
        costScore: 93,
        otifScore: 91
      },
      {
        period: "Q3 2024",
        score: 94,
        date: "2024-09-30",
        qualityScore: 96,
        deliveryScore: 92,
        costScore: 94,
        otifScore: 93
      }
    ],
    riskEvents: [],
    contact: {
      name: "Dr. Lisa Rodriguez",
      email: "<EMAIL>",
      phone: "******-0456",
      role: "Technical Director"
    },
    otifMetrics: {
      currentOTIF: 93.1,
      targetOTIF: 95.0,
      onTimeDelivery: 96.8,
      inFullDelivery: 96.2,
      trend: "up",
      monthlyData: [
        { month: "Jan", otif: 91.4, onTime: 95.2, inFull: 96.1, orders: 124 },
        { month: "Feb", otif: 92.3, onTime: 96.1, inFull: 96.0, orders: 118 },
        { month: "Mar", otif: 93.1, onTime: 96.8, inFull: 96.2, orders: 131 },
        { month: "Apr", otif: 93.8, onTime: 97.2, inFull: 96.5, orders: 127 },
        { month: "May", otif: 92.9, onTime: 96.5, inFull: 96.3, orders: 122 },
        { month: "Jun", otif: 93.1, onTime: 96.8, inFull: 96.2, orders: 129 }
      ],
      deliveryPerformance: {
        onTime: 96.8,
        late: 2.4,
        early: 0.8
      },
      qualityMetrics: {
        firstPassYield: 98.7,
        defectRate: 0.01,
        customerComplaints: 1
      }
    }
  }
];

import { 
  SupplierAudit, 
  SupplierDocument, 
  CAPAAssignment, 
  ContractRenewal, 
  ComplianceDeclaration,
  DocumentLanguage,
  MultilingualDocument
} from "@/types/supplierPortal";

export const mockSupplierAudits: SupplierAudit[] = [
  {
    id: "AUD-001",
    title: "Q4 Quality Audit",
    type: "Quality Management",
    status: "Scheduled",
    dueDate: "2024-12-15",
    auditor: "<PERSON>",
    priority: "High"
  },
  {
    id: "AUD-002",
    title: "Environmental Compliance Check",
    type: "Environmental",
    status: "In Progress",
    dueDate: "2024-11-30",
    auditor: "<PERSON>",
    priority: "Medium"
  },
  {
    id: "AUD-003",
    title: "Security Assessment",
    type: "Information Security",
    status: "Overdue",
    dueDate: "2024-11-20",
    priority: "High"
  }
];

export const mockSupplierDocuments: SupplierDocument[] = [
  {
    id: "DOC-001",
    title: "ISO 9001 Certificate",
    type: "Quality Certificate",
    status: "Expiring Soon",
    expiryDate: "2025-08-10",
    uploadDate: "2022-08-10"
  },
  {
    id: "DOC-002",
    title: "Non-Disclosure Agreement",
    type: "Legal Document",
    status: "Valid",
    expiryDate: "2026-03-15",
    uploadDate: "2024-03-15"
  },
  {
    id: "DOC-003",
    title: "Sustainability Declaration",
    type: "Compliance Form",
    status: "Pending Upload",
    dueDate: "2024-12-01"
  }
];

export const mockCAPAAssignments: CAPAAssignment[] = [
  {
    id: "CAPA-001",
    title: "Product Quality Improvement",
    description: "Address non-conforming products found in batch PQ-2024-Q3",
    dueDate: "2024-12-20",
    status: "In Progress",
    priority: "High",
    assignedBy: "Quality Team"
  },
  {
    id: "CAPA-002",
    title: "Process Documentation Update",
    description: "Update manufacturing process documentation per audit findings",
    dueDate: "2024-12-05",
    status: "Open",
    priority: "Medium",
    assignedBy: "Audit Team"
  }
];

export const mockContractRenewals: ContractRenewal[] = [
  {
    id: "CON-001",
    contractTitle: "Supply Agreement 2024-2026",
    currentEndDate: "2026-12-31",
    renewalDueDate: "2026-10-01",
    status: "Action Required",
    value: 2500000,
    reminderDays: 90
  }
];

export const mockComplianceDeclarations: ComplianceDeclaration[] = [
  {
    id: "COMP-001",
    title: "Annual Conflict Minerals Declaration",
    type: "Regulatory Compliance",
    dueDate: "2024-12-31",
    status: "Not Started",
    description: "Submit annual declaration on conflict minerals usage"
  },
  {
    id: "COMP-002",
    title: "GDPR Data Processing Agreement",
    type: "Data Privacy",
    dueDate: "2024-11-30",
    status: "In Progress",
    description: "Update data processing agreement for GDPR compliance"
  }
];

export const documentLanguages: DocumentLanguage[] = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "es", name: "Spanish", flag: "🇪🇸" },
  { code: "de", name: "German", flag: "🇩🇪" },
  { code: "zh", name: "Mandarin", flag: "🇨🇳" }
];

export const mockMultilingualDocuments: MultilingualDocument[] = [
  {
    id: "ML-001",
    title: "Code of Conduct",
    language: "English",
    languageCode: "en",
    type: "Policy Document",
    effectiveDate: "2024-01-01",
    lastUpdated: "2024-11-15",
    status: "Active",
    version: "2.1",
    fileSize: "2.3 MB",
    description: "Company code of conduct and ethical guidelines",
    tags: ["ethics", "compliance", "policy"]
  },
  {
    id: "ML-002",
    title: "Manual de Calidad Proveedor",
    language: "Spanish",
    languageCode: "es",
    type: "Quality Manual",
    effectiveDate: "2024-01-01",
    lastUpdated: "2024-10-20",
    status: "Active",
    version: "1.5",
    fileSize: "3.1 MB",
    description: "Manual de procedimientos de calidad para proveedores",
    tags: ["calidad", "procedimientos", "manual"]
  },
  {
    id: "ML-003",
    title: "Lieferantenselbstauskunft",
    language: "German",
    languageCode: "de",
    type: "Self-Assessment",
    effectiveDate: "2024-06-01",
    lastUpdated: "2024-11-01",
    status: "Active",
    version: "1.2",
    fileSize: "1.8 MB",
    description: "Lieferanten Selbstauskunft Formular",
    tags: ["selbstauskunft", "lieferant", "bewertung"]
  },
  {
    id: "ML-004",
    title: "供应商质量手册",
    language: "Mandarin",
    languageCode: "zh",
    type: "Quality Manual",
    effectiveDate: "2024-03-01",
    lastUpdated: "2024-09-15",
    status: "Active",
    version: "1.3",
    fileSize: "2.7 MB",
    description: "供应商质量管理手册和程序",
    tags: ["质量", "手册", "程序"]
  },
  {
    id: "ML-005",
    title: "Environmental Guidelines",
    language: "English",
    languageCode: "en",
    type: "Environmental Policy",
    effectiveDate: "2024-01-15",
    lastUpdated: "2024-08-30",
    status: "Active",
    version: "3.0",
    fileSize: "1.5 MB",
    description: "Environmental sustainability guidelines for suppliers",
    tags: ["environment", "sustainability", "guidelines"]
  }
];

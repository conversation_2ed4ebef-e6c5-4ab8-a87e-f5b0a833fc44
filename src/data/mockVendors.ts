
import { Vendor } from "@/types/vendors";

export const mockVendorsSanJose: Vendor[] = [
  {
    id: "VEN-001",
    name: "<PERSON><PERSON>",
    productService: "test 2",
    assignee: "<PERSON><PERSON>",
    status: "Under evaluation",
    lastEvaluationDate: "2025-05-14",
    nextEvaluationDate: "2025-08-14",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-002",
    name: "test bug",
    productService: "test bug",
    status: "Under evaluation",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-003",
    name: "Test new Vendor",
    productService: "RRRR",
    status: "Under evaluation",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-004",
    name: "dsffsdfv",
    productService: "dsfvdsf",
    status: "Under evaluation",
    reviewPeriod: "Bi-annually"
  },
  {
    id: "VEN-005",
    name: "Test Vendor 2 - Vinodh",
    productService: "Software",
    status: "Under evaluation",
    reviewPeriod: "Annually"
  },
  {
    id: "VEN-006",
    name: "Test Vendor - Vinodh 1",
    productService: "Software",
    assignee: "Vinodh Peddi",
    status: "Under evaluation",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-010",
    name: "Pacific Cleaning Services",
    productService: "Facility Maintenance",
    assignee: "David Wong",
    status: "Approved",
    lastEvaluationDate: "2025-04-22",
    nextEvaluationDate: "2025-07-22",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-011",
    name: "West Coast IT Solutions",
    productService: "Network Security",
    assignee: "Maria Rodriguez",
    status: "Approved",
    lastEvaluationDate: "2025-05-01",
    nextEvaluationDate: "2025-11-01",
    reviewPeriod: "Bi-annually"
  },
  {
    id: "VEN-012",
    name: "Mountainview Catering",
    productService: "Food Services",
    assignee: "John Smith",
    status: "Rejected",
    lastEvaluationDate: "2025-05-10",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-013",
    name: "Silicon Valley Tech",
    productService: "Cloud Services",
    status: "Pending",
    reviewPeriod: "Annually"
  }
];

export const mockVendorsBengaluru: Vendor[] = [
  {
    id: "VEN-007",
    name: "Bengaluru Tech Solutions",
    productService: "IT Support",
    assignee: "Rahul Sharma",
    status: "Approved",
    lastEvaluationDate: "2025-04-10",
    nextEvaluationDate: "2025-07-10",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-008",
    name: "InfoSys Services",
    productService: "Software Development",
    assignee: "Ananya Patel",
    status: "Approved",
    lastEvaluationDate: "2025-03-22",
    nextEvaluationDate: "2025-09-22",
    reviewPeriod: "Bi-annually"
  },
  {
    id: "VEN-009",
    name: "Global Solutions Ltd",
    productService: "Consulting",
    status: "Under evaluation",
    reviewPeriod: "Annually"
  },
  {
    id: "VEN-014",
    name: "Mysore Hardware Suppliers",
    productService: "Computer Hardware",
    assignee: "Priya Nair",
    status: "Approved",
    lastEvaluationDate: "2025-05-05",
    nextEvaluationDate: "2025-08-05",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-015",
    name: "Chennai Software Systems",
    productService: "Software Testing",
    assignee: "Vijay Kumar",
    status: "Under evaluation",
    lastEvaluationDate: "2025-05-12",
    nextEvaluationDate: "2025-11-12",
    reviewPeriod: "Bi-annually"
  },
  {
    id: "VEN-016",
    name: "Delhi Network Solutions",
    productService: "Network Infrastructure",
    status: "Pending",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-017",
    name: "Mumbai Data Centers",
    productService: "Cloud Hosting",
    assignee: "Ravi Desai",
    status: "Approved",
    lastEvaluationDate: "2025-04-15",
    nextEvaluationDate: "2025-07-15",
    reviewPeriod: "Quarterly"
  },
  {
    id: "VEN-018",
    name: "Hyderabad Security Systems",
    productService: "Security Services",
    assignee: "Anand Reddy",
    status: "Rejected",
    lastEvaluationDate: "2025-05-08",
    reviewPeriod: "Annually"
  }
];

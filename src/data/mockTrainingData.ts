import { Training, EmployeeTraining } from "@/types/training";

export const mockTrainings: Training[] = [
  {
    id: "T001",
    title: "ISO 9001:2015 Quality Management Systems",
    type: "Video",
    category: "Quality",
    language: "English",
    roleApplicability: ["Quality Manager", "Process Engineer", "Auditor"],
    estimatedDuration: "120 minutes",
    description: "Comprehensive training on ISO 9001:2015 standards and implementation",
    lastUpdated: "2024-01-15",
    createdBy: "Training Admin",
    tags: ["ISO", "Quality", "Management"],
    difficulty: "Intermediate"
  },
  {
    id: "T002",
    title: "Fire Safety and Emergency Procedures",
    type: "Interactive",
    category: "Safety",
    language: "English",
    roleApplicability: ["All Employees"],
    estimatedDuration: "45 minutes",
    description: "Essential fire safety training and emergency evacuation procedures",
    lastUpdated: "2024-01-10",
    createdBy: "Safety Officer",
    tags: ["Fire Safety", "Emergency", "Compliance"],
    difficulty: "Beginner"
  },
  {
    id: "T003",
    title: "Good Manufacturing Practices (GMP)",
    type: "PDF",
    category: "Compliance",
    language: "English",
    roleApplicability: ["Production Staff", "Quality Control"],
    estimatedDuration: "90 minutes",
    description: "Understanding and implementing GMP in manufacturing environments",
    lastUpdated: "2024-01-08",
    createdBy: "Compliance Manager",
    tags: ["GMP", "Manufacturing", "Compliance"],
    difficulty: "Intermediate"
  },
  {
    id: "T004",
    title: "Lean Manufacturing Principles",
    type: "Interactive",
    category: "Technical",
    language: "English",
    roleApplicability: ["Production Manager", "Team Lead"],
    estimatedDuration: "60 minutes",
    description: "Learn the core principles of lean manufacturing and waste reduction",
    lastUpdated: "2024-01-05",
    createdBy: "Operations Manager",
    tags: ["Lean", "Manufacturing", "Efficiency"],
    difficulty: "Advanced"
  },
  {
    id: "T005",
    title: "Leadership Development Program",
    type: "Video",
    category: "Leadership",
    language: "English",
    roleApplicability: ["Team Lead", "Manager", "Supervisor"],
    estimatedDuration: "180 minutes",
    description: "Develop essential leadership skills and team management techniques",
    lastUpdated: "2024-01-12",
    createdBy: "HR Manager",
    tags: ["Leadership", "Management", "Development"],
    difficulty: "Advanced"
  },
  {
    id: "T006",
    title: "HACCP Food Safety Management",
    type: "Interactive",
    category: "Food Safety",
    language: "English",
    roleApplicability: ["Food Handler", "Quality Control"],
    estimatedDuration: "75 minutes",
    description: "Hazard Analysis and Critical Control Points system implementation",
    lastUpdated: "2024-01-18",
    createdBy: "Food Safety Manager",
    tags: ["HACCP", "Food Safety", "Compliance"],
    difficulty: "Intermediate"
  },
  {
    id: "T007",
    title: "Root Cause Analysis Techniques",
    type: "Video",
    category: "Quality",
    language: "English",
    roleApplicability: ["Quality Engineer", "Manager"],
    estimatedDuration: "100 minutes",
    description: "Master systematic problem-solving and root cause analysis methods",
    lastUpdated: "2024-01-22",
    createdBy: "Quality Director",
    tags: ["RCA", "Problem Solving", "Quality"],
    difficulty: "Advanced"
  },
  {
    id: "T008",
    title: "Environmental Management Systems",
    type: "PDF",
    category: "Environmental",
    language: "English",
    roleApplicability: ["Environmental Officer", "Manager"],
    estimatedDuration: "85 minutes",
    description: "ISO 14001 environmental management system principles and implementation",
    lastUpdated: "2024-01-25",
    createdBy: "Environmental Manager",
    tags: ["ISO 14001", "Environment", "Sustainability"],
    difficulty: "Intermediate"
  }
];

export const mockEmployeeTrainings: EmployeeTraining[] = [
  {
    id: "ET001",
    trainingId: "T001",
    employeeId: "EMPID12345",
    training: mockTrainings[0],
    assignedDate: "2024-01-20",
    dueDate: "2024-02-20",
    status: "In Progress",
    timeSpent: "45 minutes",
    progress: 65,
    assignedBy: "Training Admin",
    notes: "Priority training for quality certification",
    lastAccessed: "2024-01-25",
    completedDate: null,
    quizScore: null,
    attempts: 1
  },
  {
    id: "ET002",
    trainingId: "T002",
    employeeId: "EMPID12345",
    training: mockTrainings[1],
    assignedDate: "2024-01-15",
    dueDate: "2024-02-15",
    status: "Completed",
    completedDate: "2024-01-22",
    timeSpent: "45 minutes",
    progress: 100,
    assignedBy: "Safety Officer",
    quizScore: 95,
    attempts: 1,
    certificateId: "CERT-ET002"
  },
  {
    id: "ET003",
    trainingId: "T003",
    employeeId: "EMPID12345",
    training: mockTrainings[2],
    assignedDate: "2024-01-18",
    dueDate: "2024-02-18",
    status: "Not Started",
    timeSpent: "0 minutes",
    progress: 0,
    assignedBy: "Compliance Manager",
    completedDate: null,
    quizScore: null,
    attempts: 0
  },
  {
    id: "ET004",
    trainingId: "T004",
    employeeId: "EMPID12345",
    training: mockTrainings[3],
    assignedDate: "2024-01-10",
    dueDate: "2024-02-10",
    status: "In Progress",
    timeSpent: "30 minutes",
    progress: 50,
    assignedBy: "Operations Manager",
    completedDate: null,
    quizScore: null,
    attempts: 1,
    lastAccessed: "2024-01-23"
  },
  {
    id: "ET005",
    trainingId: "T005",
    employeeId: "EMPID12345",
    training: mockTrainings[4],
    assignedDate: "2024-01-12",
    dueDate: "2024-03-12",
    status: "Not Started",
    timeSpent: "0 minutes",
    progress: 0,
    assignedBy: "HR Manager",
    notes: "Leadership development track",
    completedDate: null,
    quizScore: null,
    attempts: 0
  },
  {
    id: "ET006",
    trainingId: "T006",
    employeeId: "EMPID12345",
    training: mockTrainings[5],
    assignedDate: "2024-01-28",
    dueDate: "2024-02-28",
    status: "In Progress",
    timeSpent: "20 minutes",
    progress: 25,
    assignedBy: "Food Safety Manager",
    notes: "Required for food handling certification",
    completedDate: null,
    quizScore: null,
    attempts: 1,
    lastAccessed: "2024-02-01"
  },
  {
    id: "ET007",
    trainingId: "T007",
    employeeId: "EMPID12345",
    training: mockTrainings[6],
    assignedDate: "2024-02-01",
    dueDate: "2024-03-01",
    status: "Not Started",
    timeSpent: "0 minutes",
    progress: 0,
    assignedBy: "Quality Director",
    notes: "Advanced skill development",
    completedDate: null,
    quizScore: null,
    attempts: 0
  },
  {
    id: "ET008",
    trainingId: "T008",
    employeeId: "EMPID12345",
    training: mockTrainings[7],
    assignedDate: "2024-02-05",
    dueDate: "2024-03-05",
    status: "Not Started",
    timeSpent: "0 minutes",
    progress: 0,
    assignedBy: "Environmental Manager",
    completedDate: null,
    quizScore: null,
    attempts: 0
  }
];

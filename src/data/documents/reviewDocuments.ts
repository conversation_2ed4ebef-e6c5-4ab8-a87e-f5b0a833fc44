
import { Document } from "@/components/document-hub/DocumentTable";

// Collection of documents in review process (draft, needs approval, rejected)
export const draftDocuments: Document[] = [
  {
    id: "TEST_CFR_22",
    title: "Test CFR 22",
    version: "v0.2",
    department: "Production",
    category: "Chart",
    status: "Draft",
    assignee: "<PERSON><PERSON><PERSON>",
    approver: "<PERSON>",
    date: "--",
  },
  {
    id: "DOC-0013",
    title: "Employee Handbook 2025",
    version: "v4.1",
    department: "Human Resources",
    category: "Manual",
    status: "Draft",
    assignee: "<PERSON><PERSON>",
    approver: "<PERSON>",
    date: "16 Apr 2025",
  },
  {
    id: "DOC-0017",
    title: "Product Testing Procedures",
    version: "v2.5",
    department: "Quality Assurance",
    category: "Procedure",
    status: "Draft",
    assignee: "<PERSON><PERSON><PERSON>",
    approver: "<PERSON>",
    date: "24 Apr 2025",
  },
  {
    id: "DOC-0022",
    title: "Environmental Compliance Report",
    version: "v1.0",
    department: "Legal",
    category: "Report",
    status: "Draft",
    assignee: "<PERSON>hru<PERSON> <PERSON>",
    approver: "<PERSON>",
    date: "4 May 2025",
  }
];

export const needsApprovalDocuments: Document[] = [
  {
    id: "DOC-APPROVAL-123",
    title: "Safety Protocol Review",
    version: "v2.1",
    department: "Quality Assurance",
    category: "Process",
    status: "Needs Approval",
    assignee: "Maitreyi Sharma",
    approver: "Sophie Anderson",
    date: "15 Apr 2025",
  },
  {
    id: "DOC-0014",
    title: "Quarterly Financial Report",
    version: "v1.0",
    department: "Finance",
    category: "Report",
    status: "Needs Approval",
    assignee: "Sameer Joshi",
    approver: "Liu Wei",
    date: "18 Apr 2025",
  },
  {
    id: "DOC-0018",
    title: "Marketing Campaign Strategy",
    version: "v1.2",
    department: "Marketing",
    category: "Strategy",
    status: "Needs Approval",
    assignee: "Rohit Verma",
    approver: "Sarah Johnson",
    date: "26 Apr 2025",
  },
  {
    id: "DOC-0024",
    title: "Risk Assessment Matrix",
    version: "v1.5",
    department: "Risk Management",
    category: "Assessment",
    status: "Needs Approval",
    assignee: "Aditya Kapoor",
    approver: "John Smith",
    date: "8 May 2025",
  }
];

export const rejectedDocuments: Document[] = [
  {
    id: "DOC-REJECT-456",
    title: "Equipment Maintenance Guide",
    version: "v1.4",
    department: "Maintenance",
    category: "Manual",
    status: "Rejected",
    assignee: "Vishnu Tripathi",
    approver: "Ahmed Hassan",
    date: "22 Mar 2025",
  },
  {
    id: "DOC-0020",
    title: "Supply Chain Optimization Plan",
    version: "v1.4",
    department: "Logistics",
    category: "Plan",
    status: "Rejected",
    assignee: "Vikram Malhotra",
    approver: "Raj Patel",
    date: "30 Apr 2025",
  }
];

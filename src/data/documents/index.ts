
import { publishedDocuments } from './publishedDocuments';
import { draftDocuments, needsApprovalDocuments, rejectedDocuments } from './reviewDocuments';
import { expiredDocuments, notUploadedDocuments } from './otherDocuments';
import { mockDocuments } from '../mockDocuments';
import { mockDocumentsBengaluru } from '../mockDocumentsBengaluru';
import { Document } from "@/components/document-hub/DocumentTable";

// Combine all documents into a single array
export const documentData: Document[] = [
  ...notUploadedDocuments,
  ...publishedDocuments,
  ...draftDocuments,
  ...needsApprovalDocuments,
  ...rejectedDocuments,
  ...expiredDocuments,
  ...mockDocuments,
  ...mockDocumentsBengaluru
];

// Export individual collections for more granular access
export {
  publishedDocuments,
  draftDocuments,
  needsApprovalDocuments,
  rejectedDocuments,
  expiredDocuments,
  notUploadedDocuments
};

// Log all document IDs for debugging
console.log("All document IDs:", documentData.map(doc => doc.id));

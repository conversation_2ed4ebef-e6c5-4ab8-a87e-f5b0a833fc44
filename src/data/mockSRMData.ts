
export const mockPerformanceAnalytics = {
  kpiMetrics: [
    { name: "Quality Score", current: 92, target: 95, trend: "up" },
    { name: "Delivery Performance", current: 88, target: 90, trend: "stable" },
    { name: "Cost Performance", current: 91, target: 85, trend: "up" },
    { name: "Innovation Score", current: 85, target: 80, trend: "up" }
  ],
  trendData: [
    { month: "Jan", performance: 88, quality: 90, delivery: 85, cost: 89 },
    { month: "Feb", performance: 90, quality: 92, delivery: 87, cost: 91 },
    { month: "Mar", performance: 89, quality: 91, delivery: 86, cost: 90 },
    { month: "Apr", performance: 91, quality: 93, delivery: 88, cost: 92 },
    { month: "May", performance: 92, quality: 94, delivery: 89, cost: 93 },
    { month: "Jun", performance: 94, quality: 95, delivery: 91, cost: 95 }
  ],
  benchmarkData: [
    { category: "Quality", avgScore: 92, topPerformer: 98, industryAvg: 85 },
    { category: "Delivery", avgScore: 88, topPerformer: 95, industryAvg: 82 },
    { category: "Cost", avgScore: 91, topPerformer: 96, industryAvg: 87 },
    { category: "Innovation", avgScore: 85, topPerformer: 92, industryAvg: 78 }
  ]
};

export const mockContractData = {
  activeContracts: [
    {
      id: "CON-001",
      supplier: "Delta Fasteners",
      title: "Supply Agreement 2024-2026",
      value: 2500000,
      endDate: "2026-12-31",
      performance: 88,
      status: "Active"
    },
    {
      id: "CON-002", 
      supplier: "Omega Polymers",
      title: "Polymer Supply Contract",
      value: 1800000,
      endDate: "2025-05-31",
      performance: 94,
      status: "Active"
    }
  ],
  contractValue: {
    total: 4300000
  },
  upcomingRenewals: [
    {
      supplier: "Omega Polymers",
      renewalDate: "2025-02-01",
      value: 1800000
    }
  ]
};

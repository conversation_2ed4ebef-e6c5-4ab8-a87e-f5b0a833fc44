
import { AuditTemplate } from "@/types/auditTemplates";

export const mockAuditTemplates: AuditTemplate[] = [
  {
    id: "template-1",
    name: "System Quality Process Audit",
    description: "Comprehensive audit template for system quality processes covering all major operational areas including receiving, assembly, and final inspection.",
    version: "1.2",
    sections: [
      {
        id: "receiving",
        name: "Receiving Inspection",
        description: "Evaluation of incoming material handling and inspection processes",
        questions: [
          {
            id: "q1",
            question: "Are materials stored per specification?",
            description: "Verify that all incoming materials are stored according to manufacturer specifications and company protocols",
            scoring: "binary",
            category: "Storage",
            required: true,
            evidenceRequired: true,
            order: 1
          },
          {
            id: "q2",
            question: "Is incoming inspection documentation complete?",
            description: "Check that all required documentation accompanies incoming materials",
            scoring: "binary",
            category: "Documentation",
            required: true,
            evidenceRequired: false,
            order: 2
          },
          {
            id: "q3",
            question: "Are rejected materials properly segregated?",
            description: "Verify proper segregation and identification of rejected materials",
            scoring: "binary",
            category: "Quality Control",
            required: true,
            evidenceRequired: true,
            order: 3
          }
        ],
        order: 1
      },
      {
        id: "assembly",
        name: "Assembly",
        description: "Assessment of assembly line processes and quality controls",
        questions: [
          {
            id: "q4",
            question: "Are assembly procedures followed correctly?",
            description: "Observe assembly operations to ensure adherence to documented procedures",
            scoring: "binary",
            category: "Process",
            required: true,
            evidenceRequired: false,
            order: 1
          },
          {
            id: "q5",
            question: "Is work instruction documentation current and accessible?",
            description: "Verify that current work instructions are available at all workstations",
            scoring: "binary",
            category: "Documentation",
            required: true,
            evidenceRequired: false,
            order: 2
          },
          {
            id: "q6",
            question: "Are quality checkpoints implemented during assembly?",
            description: "Confirm that quality control checkpoints are established and being followed",
            scoring: "binary",
            category: "Quality Control",
            required: true,
            evidenceRequired: true,
            order: 3
          }
        ],
        order: 2
      },
      {
        id: "final-inspection",
        name: "Final Inspection",
        description: "Evaluation of final product inspection and testing procedures",
        questions: [
          {
            id: "q7",
            question: "Are final products inspected according to specifications?",
            description: "Verify that final inspection follows documented specifications and test procedures",
            scoring: "binary",
            category: "Quality",
            required: true,
            evidenceRequired: true,
            order: 1
          },
          {
            id: "q8",
            question: "Is test equipment calibrated and current?",
            description: "Check calibration status of all test and measurement equipment",
            scoring: "binary",
            category: "Equipment",
            required: true,
            evidenceRequired: false,
            order: 2
          }
        ],
        order: 3
      }
    ],
    totalQuestions: 8,
    defaultScoring: "binary",
    supplierCategory: ["Electronics", "Mechanical"],
    createdBy: "Alex Rodriguez",
    createdAt: "2024-01-05T09:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
    isActive: true,
    tags: ["ISO9001", "Electronics", "Process"]
  },
  {
    id: "template-2",
    name: "Electronics Manufacturing Audit",
    description: "Specialized audit template for electronics manufacturers focusing on ESD controls, component handling, and assembly quality.",
    version: "2.1",
    sections: [
      {
        id: "esd-controls",
        name: "ESD Controls",
        description: "Electrostatic discharge prevention and control measures",
        questions: [
          {
            id: "q9",
            question: "Are ESD control measures properly implemented?",
            description: "Verify implementation of ESD control program including wrist straps, mats, and ionizers",
            scoring: "weighted",
            weight: 15,
            category: "ESD",
            required: true,
            evidenceRequired: true,
            order: 1
          },
          {
            id: "q10",
            question: "Is ESD training current for all personnel?",
            description: "Check training records for ESD awareness and handling procedures",
            scoring: "weighted",
            weight: 10,
            category: "Training",
            required: true,
            evidenceRequired: false,
            order: 2
          }
        ],
        order: 1,
        weight: 30
      },
      {
        id: "component-handling",
        name: "Component Handling",
        description: "Proper handling and storage of electronic components",
        questions: [
          {
            id: "q11",
            question: "Are components stored in appropriate environmental conditions?",
            description: "Verify temperature and humidity controls for component storage areas",
            scoring: "weighted",
            weight: 12,
            category: "Environment",
            required: true,
            evidenceRequired: true,
            order: 1
          }
        ],
        order: 2,
        weight: 25
      }
    ],
    totalQuestions: 3,
    defaultScoring: "weighted",
    supplierCategory: ["Electronics"],
    createdBy: "Sarah Chen",
    createdAt: "2024-01-08T11:15:00Z",
    updatedAt: "2024-01-20T16:45:00Z",
    isActive: true,
    tags: ["Electronics", "ESD", "Manufacturing"]
  },
  {
    id: "template-3",
    name: "Software Quality Assurance Audit",
    description: "Audit template for software development processes including code review, testing, and deployment procedures.",
    version: "1.0",
    sections: [
      {
        id: "development",
        name: "Development Process",
        description: "Software development lifecycle and coding standards",
        questions: [
          {
            id: "q12",
            question: "Are coding standards documented and followed?",
            description: "Review coding standards documentation and sample code for compliance",
            scoring: "binary",
            category: "Standards",
            required: true,
            evidenceRequired: false,
            order: 1
          },
          {
            id: "q13",
            question: "Is code review process implemented?",
            description: "Verify that peer code reviews are conducted before deployment",
            scoring: "binary",
            category: "Process",
            required: true,
            evidenceRequired: true,
            order: 2
          }
        ],
        order: 1
      },
      {
        id: "testing",
        name: "Testing Process",
        description: "Software testing procedures and quality assurance",
        questions: [
          {
            id: "q14",
            question: "Are automated tests implemented and maintained?",
            description: "Check for presence and maintenance of automated test suites",
            scoring: "binary",
            category: "Testing",
            required: true,
            evidenceRequired: false,
            order: 1
          }
        ],
        order: 2
      }
    ],
    totalQuestions: 3,
    defaultScoring: "binary",
    supplierCategory: ["Software"],
    createdBy: "Mike Johnson",
    createdAt: "2024-01-12T14:30:00Z",
    updatedAt: "2024-01-12T14:30:00Z",
    isActive: true,
    tags: ["Software", "QA", "Testing"]
  }
];


import { RecurringTraining, RecertificationSchedule } from "@/types/recertification";

export const mockRecurringTrainings: RecurringTraining[] = [
  {
    id: "RT-001",
    trainingId: "TRN-003",
    trainingTitle: "Good Manufacturing Practices (GMP)",
    recurrenceRule: {
      type: "years",
      interval: 1,
      description: "Every 1 year"
    },
    autoReassign: true,
    reminderSettings: {
      enabled: true,
      reminderDays: [30, 14, 7, 1],
      escalationDays: 3
    },
    isActive: true,
    createdDate: "2024-01-15",
    lastAssigned: "2024-01-15",
    nextDue: "2025-01-15",
    assignedRoles: ["Production Staff", "Quality Control", "Warehouse"],
    assignedEmployees: []
  },
  {
    id: "RT-002",
    trainingId: "TRN-002",
    trainingTitle: "Fire Safety and Emergency Procedures",
    recurrenceRule: {
      type: "months",
      interval: 6,
      description: "Every 6 months"
    },
    autoReassign: true,
    reminderSettings: {
      enabled: true,
      reminderDays: [14, 7, 3],
      escalationDays: 1
    },
    isActive: true,
    createdDate: "2024-06-01",
    lastAssigned: "2024-11-01",
    nextDue: "2025-05-01",
    assignedRoles: ["All Employees"],
    assignedEmployees: []
  },
  {
    id: "RT-003",
    trainingId: "TRN-005",
    trainingTitle: "Chemical Handling and Safety",
    recurrenceRule: {
      type: "years",
      interval: 2,
      description: "Every 2 years"
    },
    autoReassign: false,
    reminderSettings: {
      enabled: true,
      reminderDays: [60, 30, 14],
      escalationDays: 7
    },
    isActive: true,
    createdDate: "2023-08-20",
    lastAssigned: "2023-08-20",
    nextDue: "2025-08-20",
    assignedRoles: ["Lab Technician", "Production Staff"],
    assignedEmployees: []
  }
];

export const mockRecertificationSchedules: RecertificationSchedule[] = [
  {
    id: "RS-001",
    recurringTrainingId: "RT-001",
    employeeId: "EMP001",
    employeeName: "Sarah Johnson",
    currentCycleStart: "2024-01-15",
    currentCycleDue: "2025-01-15",
    status: "Upcoming",
    nextCycleStart: "2025-01-15",
    nextCycleDue: "2026-01-15"
  },
  {
    id: "RS-002",
    recurringTrainingId: "RT-002",
    employeeId: "EMP001",
    employeeName: "Sarah Johnson",
    currentCycleStart: "2024-11-01",
    currentCycleDue: "2025-05-01",
    status: "Assigned"
  },
  {
    id: "RS-003",
    recurringTrainingId: "RT-002",
    employeeId: "EMP002",
    employeeName: "Michael Chen",
    currentCycleStart: "2024-05-01",
    currentCycleDue: "2024-11-01",
    status: "Completed",
    completedDate: "2024-10-28",
    certificateId: "FIRE-2024-MC-002",
    nextCycleStart: "2024-11-01",
    nextCycleDue: "2025-05-01"
  },
  {
    id: "RS-004",
    recurringTrainingId: "RT-001",
    employeeId: "EMP003",
    employeeName: "Emily Davis",
    currentCycleStart: "2023-01-15",
    currentCycleDue: "2024-01-15",
    status: "Overdue"
  }
];

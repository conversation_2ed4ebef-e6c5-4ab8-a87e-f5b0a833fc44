
import { SupplierForm, FormResponse, FormRecipient } from "@/types/supplierForms";

export const mockSupplierForms: SupplierForm[] = [
  {
    id: "form-001",
    name: "Supplier Capability Checklist",
    description: "Assessment of supplier manufacturing capabilities and quality systems",
    createdAt: "2025-05-01T09:00:00Z",
    updatedAt: "2025-05-05T14:30:00Z",
    dueDate: "2025-06-15T23:59:59Z",
    sections: [
      {
        id: "section-001",
        title: "Quality Management System",
        description: "Information about your quality management system certifications and processes",
        questions: [
          {
            id: "q-001",
            type: "yesno",
            text: "Do you have ISO 9001 certification?",
            required: true
          },
          {
            id: "q-002",
            type: "yesno",
            text: "Do you have ISO 13485 certification?",
            required: true
          },
          {
            id: "q-003",
            type: "file",
            text: "Please upload your quality manual or relevant certifications",
            required: false
          }
        ]
      },
      {
        id: "section-002",
        title: "Manufacturing Capabilities",
        description: "Information about your manufacturing processes and capabilities",
        questions: [
          {
            id: "q-004",
            type: "text",
            text: "What is your total annual production capacity?",
            required: true
          },
          {
            id: "q-005",
            type: "select",
            text: "What is your typical lead time for orders?",
            required: true,
            options: ["1-2 weeks", "3-4 weeks", "5-8 weeks", "More than 8 weeks"]
          },
          {
            id: "q-006",
            type: "checkbox",
            text: "Which of the following processes do you perform in-house?",
            required: true,
            options: ["Injection molding", "CNC machining", "Assembly", "Sterilization", "Packaging"]
          }
        ]
      },
      {
        id: "section-003",
        title: "Compliance & Risk Management",
        description: "Information about your regulatory compliance and risk management",
        questions: [
          {
            id: "q-007",
            type: "yesno",
            text: "Do you have a formal risk management process?",
            required: true
          },
          {
            id: "q-008",
            type: "textarea",
            text: "Please describe your process for handling non-conformances",
            required: true
          },
          {
            id: "q-009",
            type: "radio",
            text: "How frequently do you perform internal quality audits?",
            required: true,
            options: ["Monthly", "Quarterly", "Bi-annually", "Annually"]
          }
        ]
      }
    ],
    recipientCount: 12,
    status: {
      completed: 7,
      pending: 3,
      overdue: 2
    }
  },
  {
    id: "form-002",
    name: "Environmental Compliance Survey",
    description: "Assessment of supplier environmental practices and certifications",
    createdAt: "2025-04-15T10:30:00Z",
    updatedAt: "2025-04-18T16:45:00Z",
    dueDate: "2025-05-30T23:59:59Z",
    sections: [
      {
        id: "section-001",
        title: "Environmental Management",
        questions: [
          {
            id: "q-001",
            type: "yesno",
            text: "Do you have ISO 14001 certification?",
            required: true
          },
          {
            id: "q-002",
            type: "textarea",
            text: "Describe your waste management practices",
            required: true
          }
        ]
      }
    ],
    recipientCount: 8,
    status: {
      completed: 5,
      pending: 2,
      overdue: 1
    }
  },
  {
    id: "form-003",
    name: "Supplier Self-Audit",
    description: "Annual self-assessment of quality systems and processes",
    createdAt: "2025-03-10T08:15:00Z",
    updatedAt: "2025-03-12T11:20:00Z",
    dueDate: "2025-04-20T23:59:59Z",
    sections: [
      {
        id: "section-001",
        title: "General Information",
        questions: [
          {
            id: "q-001",
            type: "text",
            text: "Company Name",
            required: true
          },
          {
            id: "q-002",
            type: "text",
            text: "Primary Contact",
            required: true
          }
        ]
      }
    ],
    recipientCount: 15,
    status: {
      completed: 12,
      pending: 1,
      overdue: 2
    }
  }
];

export const mockFormResponses: FormResponse[] = [
  {
    id: "response-001",
    formId: "form-001",
    supplierId: "supplier-001",
    supplierName: "Zenith Materials",
    status: "completed",
    submittedAt: "2025-05-20T14:35:00Z",
    answers: {
      "q-001": true,
      "q-002": true,
      "q-003": { filename: "ISO-13485-Certificate.pdf", url: "#" },
      "q-004": "500,000 units",
      "q-005": "3-4 weeks",
      "q-006": ["Injection molding", "Assembly", "Packaging"],
      "q-007": true,
      "q-008": "We have a dedicated QA team that reviews all non-conformances within 24 hours...",
      "q-009": "Quarterly"
    }
  },
  {
    id: "response-002",
    formId: "form-001",
    supplierId: "supplier-002",
    supplierName: "Precision Components",
    status: "pending",
    answers: {}
  },
  {
    id: "response-003",
    formId: "form-001",
    supplierId: "supplier-003",
    supplierName: "Global Manufacturing Solutions",
    status: "overdue",
    answers: {}
  }
];

export const mockFormRecipients: FormRecipient[] = [
  {
    id: "recipient-001",
    name: "John Smith",
    email: "<EMAIL>",
    company: "Zenith Materials",
    status: "completed"
  },
  {
    id: "recipient-002",
    name: "Mary Johnson",
    email: "<EMAIL>",
    company: "Precision Components",
    status: "pending"
  },
  {
    id: "recipient-003",
    name: "Robert Chen",
    email: "<EMAIL>",
    company: "Global Manufacturing Solutions",
    status: "overdue"
  },
  {
    id: "recipient-004",
    name: "Sarah Williams",
    email: "<EMAIL>",
    company: "MediTech Supply",
    status: "completed"
  }
];

export const mockQuestionTemplates = [
  {
    id: "template-001",
    type: "yesno",
    text: "Do you have [certification] certification?",
    required: true
  },
  {
    id: "template-002",
    type: "textarea",
    text: "Please describe your process for [process name]",
    required: true
  },
  {
    id: "template-003",
    type: "file",
    text: "Please upload your [document type]",
    required: false
  },
  {
    id: "template-004",
    type: "select",
    text: "What is your [metric]?",
    required: true,
    options: ["Option 1", "Option 2", "Option 3", "Option 4"]
  }
];

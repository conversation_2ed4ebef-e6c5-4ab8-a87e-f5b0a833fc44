
import { SustainabilitySupplier, SustainabilityDeclaration } from "@/types/supplierSustainability";

export const mockSustainabilitySuppliers: SustainabilitySupplier[] = [
  {
    id: "1",
    name: "EcoParts Inc.",
    status: "Compliant",
    lastAudit: "July 2024",
    carbonFootprint: 1.8,
    overallScore: 92,
    declarationUploaded: true,
    declarationDate: "2024-01-15",
    contact: {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "******-0123"
    },
    assessments: {
      environmental: {
        selfScore: 95,
        auditScore: 92,
        lastUpdated: "2024-07-15",
        items: [
          {
            id: "env-1",
            question: "Has implemented carbon reduction initiatives",
            selfRating: 5,
            auditRating: 4,
            notes: "Strong renewable energy adoption"
          },
          {
            id: "env-2",
            question: "Waste management compliance",
            selfRating: 4,
            auditRating: 5,
            notes: "Excellent recycling programs"
          }
        ]
      },
      labor: {
        selfScore: 88,
        auditScore: 90,
        lastUpdated: "2024-07-15",
        items: [
          {
            id: "lab-1",
            question: "Fair wage practices",
            selfRating: 4,
            auditRating: 5,
            notes: "Above industry standards"
          }
        ]
      },
      ethical: {
        selfScore: 94,
        auditScore: 94,
        lastUpdated: "2024-07-15",
        items: [
          {
            id: "eth-1",
            question: "Anti-corruption policies in place",
            selfRating: 5,
            auditRating: 5,
            notes: "Comprehensive ethics program"
          }
        ]
      }
    }
  },
  {
    id: "2",
    name: "Global Manufacturing Ltd.",
    status: "Watchlist",
    lastAudit: "May 2024",
    carbonFootprint: 3.2,
    overallScore: 74,
    declarationUploaded: true,
    declarationDate: "2024-02-01",
    contact: {
      name: "Mike Johnson",
      email: "<EMAIL>",
      phone: "******-0456"
    },
    assessments: {
      environmental: {
        selfScore: 72,
        auditScore: 68,
        lastUpdated: "2024-05-20",
        items: [
          {
            id: "env-1",
            question: "Has implemented carbon reduction initiatives",
            selfRating: 3,
            auditRating: 3,
            notes: "Limited progress on emissions"
          }
        ]
      },
      labor: {
        selfScore: 78,
        auditScore: 75,
        lastUpdated: "2024-05-20",
        items: [
          {
            id: "lab-1",
            question: "Fair wage practices",
            selfRating: 4,
            auditRating: 3,
            notes: "Some improvement needed"
          }
        ]
      },
      ethical: {
        selfScore: 82,
        auditScore: 79,
        lastUpdated: "2024-05-20",
        items: [
          {
            id: "eth-1",
            question: "Anti-corruption policies in place",
            selfRating: 4,
            auditRating: 4,
            notes: "Good policies, implementation varies"
          }
        ]
      }
    }
  },
  {
    id: "3",
    name: "QuickBuild Corp.",
    status: "High Risk",
    lastAudit: "March 2024",
    carbonFootprint: 5.7,
    overallScore: 45,
    declarationUploaded: false,
    contact: {
      name: "Robert Chen",
      email: "<EMAIL>",
      phone: "******-0789"
    },
    assessments: {
      environmental: {
        selfScore: 65,
        auditScore: 42,
        lastUpdated: "2024-03-10",
        items: [
          {
            id: "env-1",
            question: "Has implemented carbon reduction initiatives",
            selfRating: 3,
            auditRating: 2,
            notes: "Significant gaps in environmental controls"
          }
        ]
      },
      labor: {
        selfScore: 58,
        auditScore: 45,
        lastUpdated: "2024-03-10",
        items: [
          {
            id: "lab-1",
            question: "Fair wage practices",
            selfRating: 3,
            auditRating: 2,
            notes: "Below standard wages reported"
          }
        ]
      },
      ethical: {
        selfScore: 70,
        auditScore: 48,
        lastUpdated: "2024-03-10",
        items: [
          {
            id: "eth-1",
            question: "Anti-corruption policies in place",
            selfRating: 4,
            auditRating: 2,
            notes: "Policies exist but not enforced"
          }
        ]
      }
    }
  }
];

export const mockSustainabilityDeclarations: SustainabilityDeclaration[] = [
  {
    id: "1",
    supplierId: "1",
    fileName: "EcoParts_Sustainability_Declaration_2024.pdf",
    uploadDate: "2024-01-15",
    status: "Approved",
    signedBy: "Sarah Green, CEO",
    validUntil: "2025-01-15"
  },
  {
    id: "2",
    supplierId: "2",
    fileName: "GlobalManuf_Sustainability_Declaration_2024.pdf",
    uploadDate: "2024-02-01",
    status: "Approved",
    signedBy: "Mike Johnson, Operations Director",
    validUntil: "2025-02-01"
  }
];


interface CertificateFile {
  name: string;
  size: string;
  type: string;
}

export interface SupplierOnboardingData {
  companyName: string;
  country: string;
  commodity: string;
  certificates: CertificateFile[];
  riskScore: number;
  assignedReviewer: string;
  contactName: string;
  email: string;
  phone: string;
}

export const mockSupplierData: SupplierOnboardingData = {
  companyName: "Precision Molds Ltd",
  country: "Germany",
  commodity: "Injection Molded Parts",
  certificates: [
    { name: "ISO 9001.pdf", size: "1.2 MB", type: "application/pdf" },
    { name: "REACH.pdf", size: "0.8 MB", type: "application/pdf" }
  ],
  riskScore: 72,
  assignedReviewer: "<PERSON> (QA Manager)",
  contactName: "<PERSON>",
  email: "<EMAIL>",
  phone: "+49 ************"
};

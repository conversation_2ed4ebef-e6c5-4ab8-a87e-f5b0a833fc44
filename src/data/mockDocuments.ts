
import { Document } from "@/components/document-hub/DocumentTable";

export const mockDocuments: Document[] = [
  {
    id: "1",
    title: "Quality Management System Manual",
    version: "v2.1",
    department: "Quality",
    category: "Quality Manual",
    processes: "Document Control, Management Review",
    status: "Published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-03-15",
    publishedDate: "2024-03-15",
    reviewDate: "2025-03-15",
    description: "Comprehensive quality management system documentation following ISO 9001 standards.",
    parentFolderId: "folder-1"
  },
  {
    id: "2", 
    title: "Standard Operating Procedure - Calibration",
    version: "v1.3",
    department: "Quality",
    category: "SOP",
    processes: "Calibration Management",
    status: "Published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-20",
    publishedDate: "2024-02-20", 
    reviewDate: "2025-02-20",
    description: "Standard procedure for equipment calibration and maintenance.",
    parentFolderId: "folder-2"
  },
  {
    id: "3",
    title: "Training Record Template",
    version: "v1.0",
    department: "HR",
    category: "Template",
    processes: "Training Management",
    status: "Draft",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-01-10",
    description: "Template for recording employee training activities.",
    parentFolderId: null
  },
  {
    id: "4",
    title: "Supplier Evaluation Checklist",
    version: "v2.0",
    department: "Procurement",
    category: "Checklist",
    processes: "Supplier Management",
    status: "Under Review",
    assignee: "James Anderson",
    approver: "Lisa Williams",
    date: "2024-03-01",
    description: "Comprehensive checklist for evaluating new suppliers.",
    parentFolderId: "folder-3"
  },
  {
    id: "5",
    title: "Risk Assessment Matrix",
    version: "v1.2", 
    department: "Quality",
    category: "Risk Management",
    processes: "Risk Assessment",
    status: "Published",
    assignee: "Michael Brown",
    approver: "Sarah Johnson",
    date: "2024-02-15",
    publishedDate: "2024-02-15",
    reviewDate: "2025-02-15",
    description: "Matrix for identifying and assessing operational risks.",
    parentFolderId: "folder-1"
  }
];

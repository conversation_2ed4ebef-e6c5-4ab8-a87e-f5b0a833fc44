
import { SupplierScorecard } from "@/types/supplierScorecard";

export const mockSupplierScorecards: SupplierScorecard[] = [
  {
    id: "SC-001",
    supplierName: "Omega Polymers",
    period: "Q4",
    year: 2024,
    finalScore: 91.2,
    scores: [
      { category: "Quality", score: 92, weight: 30, maxScore: 100 },
      { category: "Delivery", score: 88, weight: 25, maxScore: 100 },
      { category: "Cost", score: 94, weight: 20, maxScore: 100 },
      { category: "Service", score: 90, weight: 15, maxScore: 100 },
      { category: "Innovation", score: 89, weight: 10, maxScore: 100 }
    ],
    comments: "Excellent performance across all categories. Minor improvement needed in delivery consistency.",
    sentToSupplier: true,
    sentDate: "2024-12-15",
    createdDate: "2024-12-01",
    lastUpdated: "2024-12-15"
  },
  {
    id: "SC-002",
    supplierName: "MetalTech Inc.",
    period: "Q4",
    year: 2024,
    finalScore: 86.3,
    scores: [
      { category: "Quality", score: 85, weight: 30, maxScore: 100 },
      { category: "Delivery", score: 90, weight: 25, maxScore: 100 },
      { category: "Cost", score: 82, weight: 20, maxScore: 100 },
      { category: "Service", score: 88, weight: 15, maxScore: 100 },
      { category: "Innovation", score: 84, weight: 10, maxScore: 100 }
    ],
    comments: "Good overall performance. Cost optimization opportunities identified.",
    sentToSupplier: false,
    createdDate: "2024-12-05",
    lastUpdated: "2024-12-20"
  },
  {
    id: "SC-003",
    supplierName: "FastChip Electronics",
    period: "Q4",
    year: 2024,
    finalScore: 89.0,
    scores: [
      { category: "Quality", score: 91, weight: 30, maxScore: 100 },
      { category: "Delivery", score: 87, weight: 25, maxScore: 100 },
      { category: "Cost", score: 88, weight: 20, maxScore: 100 },
      { category: "Service", score: 90, weight: 15, maxScore: 100 },
      { category: "Innovation", score: 92, weight: 10, maxScore: 100 }
    ],
    comments: "Strong performance in quality and innovation. Delivery timing could be improved.",
    sentToSupplier: true,
    sentDate: "2024-12-18",
    createdDate: "2024-12-03",
    lastUpdated: "2024-12-18"
  },
  {
    id: "SC-004",
    supplierName: "Global Packaging",
    period: "Q3",
    year: 2024,
    finalScore: 78.5,
    scores: [
      { category: "Quality", score: 75, weight: 30, maxScore: 100 },
      { category: "Delivery", score: 82, weight: 25, maxScore: 100 },
      { category: "Cost", score: 80, weight: 20, maxScore: 100 },
      { category: "Service", score: 76, weight: 15, maxScore: 100 },
      { category: "Innovation", score: 74, weight: 10, maxScore: 100 }
    ],
    comments: "Performance below expectations. Improvement plan required for quality and service.",
    sentToSupplier: true,
    sentDate: "2024-10-15",
    createdDate: "2024-10-01",
    lastUpdated: "2024-10-15"
  }
];

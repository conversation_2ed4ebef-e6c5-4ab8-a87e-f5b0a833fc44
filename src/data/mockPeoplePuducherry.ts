
import { Person } from "@/types/people";

export const mockPeoplePuducherry: Person[] = [
  { 
    id: "EMPID12345", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON><PERSON>",
    jobTitle: "Product Manager", 
    email: "<EMAIL>", 
    department: "Product Management", 
    processes: ["Business Development"], 
    joiningDate: "2023-05-15", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "EMPID12346", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "Senior Software Engineer", 
    email: "<EMAIL>", 
    department: "Engineering", 
    processes: ["Software Development"], 
    joiningDate: "2022-11-03", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "EMPID12347", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "UX Designer", 
    email: "<EMAIL>", 
    department: "Design", 
    processes: ["User Experience"], 
    joiningDate: "2023-02-20", 
    leavingDate: null, 
    status: "On vacation" 
  },
  { 
    id: "EMPID12348", 
    name: "<PERSON>", 
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    jobTitle: "QA Specialist", 
    email: "<EMAIL>", 
    department: "Quality Assurance", 
    processes: ["Testing"], 
    joiningDate: "2023-01-10", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "EMPID12349", 
    name: "Alex Chen", 
    firstName: "Alex",
    lastName: "Chen",
    jobTitle: "DevOps Engineer", 
    email: "<EMAIL>", 
    department: "Operations", 
    processes: ["Infrastructure"], 
    joiningDate: "2022-09-15", 
    leavingDate: null, 
    status: "On duty" 
  },
  { 
    id: "EMPID12350", 
    name: "Priya Sharma", 
    firstName: "Priya",
    lastName: "Sharma",
    jobTitle: "HR Manager", 
    email: "<EMAIL>", 
    department: "Human Resources", 
    processes: ["Recruitment"], 
    joiningDate: "2022-07-22", 
    leavingDate: "2024-06-30", 
    status: "Suspended" 
  },
  { 
    id: "EMPID12351", 
    name: "David Wilson", 
    firstName: "David",
    lastName: "Wilson",
    jobTitle: "Finance Analyst", 
    email: "<EMAIL>", 
    department: "Finance", 
    processes: ["Accounting"], 
    joiningDate: "2023-03-05", 
    leavingDate: null, 
    status: "On duty" 
  }
];

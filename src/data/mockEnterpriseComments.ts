
import { Comment } from "@/types/comment";

export const mockEnterpriseComments: Comment[] = [
  {
    id: "c1",
    documentId: "doc-bg-2",
    content: "Reviewed section 3.4, recommend adding ISO clause clarification.",
    author: { id: "u1", name: "<PERSON>", role: "Quality Auditor" },
    createdAt: new Date("2024-06-10T09:21:00"),
    updatedAt: new Date("2024-06-10T09:21:00"),
    editHistory: [],
    attachments: [],
    replies: [
      {
        id: "c1-1",
        documentId: "doc-bg-2",
        content: "Added ISO 9001 clarification in section 3.4 per your suggestion.",
        author: { id: "u2", name: "<PERSON><PERSON><PERSON>", role: "Document Owner" },
        createdAt: new Date("2024-06-10T10:05:00"),
        updatedAt: new Date("2024-06-10T10:05:00"),
        editHistory: [],
        attachments: [],
        replies: [
          {
            id: "c1-1-1",
            documentId: "doc-bg-2",
            content: "Great, marking this as resolved.",
            author: { id: "u1", name: "<PERSON>", role: "Quality Auditor" },
            createdAt: new Date("2024-06-10T10:10:00"),
            updatedAt: new Date("2024-06-10T10:10:00"),
            editHistory: [],
            attachments: [],
            replies: [],
            parentId: "c1-1",
            isDeleted: false,
            reactions: [],
            mentions: [],
            isExpanded: true,
          },
        ],
        parentId: "c1",
        isDeleted: false,
        reactions: [],
        mentions: [],
        isExpanded: true,
      },
    ],
    parentId: undefined,
    isDeleted: false,
    reactions: [{ id: "r1", userId: "u3", type: "like", createdAt: new Date("2024-06-10T11:00:00") }],
    mentions: [],
    isExpanded: true,
  },
  {
    id: "c2",
    documentId: "doc-bg-2",
    content: "Action required: Please update supplier name in table A.",
    author: { id: "u3", name: "Marina Kuznetsova", role: "Procurement Lead" },
    createdAt: new Date("2024-06-11T09:30:00"),
    updatedAt: new Date("2024-06-11T09:30:00"),
    editHistory: [],
    attachments: [],
    replies: [
      {
        id: "c2-1",
        documentId: "doc-bg-2",
        content: "@Raj Patel I've updated the supplier name as suggested. Kindly review.",
        author: { id: "u2", name: "Vikram Singh", role: "Document Owner" },
        createdAt: new Date("2024-06-11T09:50:00"),
        updatedAt: new Date("2024-06-11T09:50:00"),
        editHistory: [],
        attachments: [],
        replies: [],
        parentId: "c2",
        isDeleted: false,
        reactions: [{ id: "r2", userId: "u4", type: "thumbs_up", createdAt: new Date("2024-06-11T09:51:00") }],
        mentions: ["Raj Patel"],
        isExpanded: true,
      },
      {
        id: "c2-2",
        documentId: "doc-bg-2",
        content: "Confirmed. Table A now reflects correct supplier details.",
        author: { id: "u4", name: "Raj Patel", role: "Supplier Manager" },
        createdAt: new Date("2024-06-11T10:10:00"),
        updatedAt: new Date("2024-06-11T10:10:00"),
        editHistory: [],
        attachments: [],
        replies: [
          {
            id: "c2-2-1",
            documentId: "doc-bg-2",
            content: "Thank you for the quick update!",
            author: { id: "u3", name: "Marina Kuznetsova", role: "Procurement Lead" },
            createdAt: new Date("2024-06-11T10:11:00"),
            updatedAt: new Date("2024-06-11T10:11:00"),
            editHistory: [],
            attachments: [],
            replies: [],
            parentId: "c2-2",
            isDeleted: false,
            reactions: [],
            mentions: [],
            isExpanded: true,
          }
        ],
        parentId: "c2",
        isDeleted: false,
        reactions: [],
        mentions: [],
        isExpanded: true,
      }
    ],
    parentId: undefined,
    isDeleted: false,
    reactions: [],
    mentions: [],
    isExpanded: true,
  },
  {
    id: "c3",
    documentId: "doc-bg-2",
    content: "Requested compliance review for section 7, see attached.",
    author: { id: "u5", name: "Helen Zhou", role: "Compliance Officer" },
    createdAt: new Date("2024-06-12T08:45:00"),
    updatedAt: new Date("2024-06-12T08:45:00"),
    editHistory: [],
    attachments: [
      {
        id: "a1",
        fileName: "ComplianceCheck.pdf",
        fileUrl: "/mockfiles/ComplianceCheck.pdf",
        fileType: "application/pdf",
        fileSize: 256000,
        uploadedAt: new Date("2024-06-12T08:47:00")
      }
    ],
    replies: [
      {
        id: "c3-1",
        documentId: "doc-bg-2",
        content: "Reviewed attachment. All points comply with FDA guidelines.",
        author: { id: "u1", name: "Jessica Lam", role: "Quality Auditor" },
        createdAt: new Date("2024-06-12T09:01:00"),
        updatedAt: new Date("2024-06-12T09:01:00"),
        editHistory: [],
        attachments: [],
        replies: [],
        parentId: "c3",
        isDeleted: false,
        reactions: [],
        mentions: [],
        isExpanded: true,
      }
    ],
    parentId: undefined,
    isDeleted: false,
    reactions: [],
    mentions: [],
    isExpanded: true,
  },
  {
    id: "c4",
    documentId: "doc-bg-2",
    content: "Change request: Please adjust schedule to align with Q4 rollout.",
    author: { id: "u6", name: "Enrique Morales", role: "Project Manager" },
    createdAt: new Date("2024-06-12T11:30:00"),
    updatedAt: new Date("2024-06-12T11:30:00"),
    editHistory: [],
    attachments: [],
    replies: [
      {
        id: "c4-1",
        documentId: "doc-bg-2",
        content: "Schedule revised for Q4 rollout as requested.",
        author: { id: "u2", name: "Vikram Singh", role: "Document Owner" },
        createdAt: new Date("2024-06-12T11:55:00"),
        updatedAt: new Date("2024-06-12T11:55:00"),
        editHistory: [],
        attachments: [],
        replies: [],
        parentId: "c4",
        isDeleted: false,
        reactions: [],
        mentions: [],
        isExpanded: true,
      }
    ],
    parentId: undefined,
    isDeleted: false,
    reactions: [],
    mentions: [],
    isExpanded: true,
  }
];

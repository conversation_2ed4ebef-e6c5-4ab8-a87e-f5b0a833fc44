
import { OverdueTraining } from "@/types/overdueTraining";

export const mockOverdueTrainings: OverdueTraining[] = [
  {
    id: "OT-001",
    employeeName: "<PERSON>",
    trainingTitle: "ISO 9001:2015 Quality Management Systems",
    dueDate: "2024-10-15",
    daysOverdue: 45,
    managerName: "<PERSON>",
    lastReminderSent: "2024-11-10",
    urgencyLevel: "critical",
    department: "Quality Assurance",
    assignedRoles: ["Quality Manager"],
    employeeId: "EMP001",
    trainingId: "TRN-001"
  },
  {
    id: "OT-002",
    employeeName: "<PERSON>",
    trainingTitle: "Fire Safety and Emergency Procedures",
    dueDate: "2024-11-01",
    daysOverdue: 28,
    managerName: "<PERSON>",
    lastReminderSent: "2024-11-15",
    urgencyLevel: "high",
    department: "Production",
    assignedRoles: ["Production Staff"],
    employeeId: "EMP004",
    trainingId: "TRN-002"
  },
  {
    id: "OT-003",
    employeeName: "<PERSON>",
    trainingTitle: "Good Manufacturing Practices (GMP)",
    dueDate: "2024-11-15",
    daysOverdue: 14,
    managerName: "<PERSON>",
    lastReminderSent: "2024-11-20",
    urgencyLevel: "medium",
    department: "Production",
    assignedRoles: ["Production Staff"],
    employeeId: "EMP010",
    trainingId: "TRN-003"
  },
  {
    id: "OT-004",
    employeeName: "Nicole Wilson",
    trainingTitle: "Data Privacy & GDPR Compliance",
    dueDate: "2024-11-10",
    daysOverdue: 19,
    managerName: "Christopher Lee",
    lastReminderSent: "2024-11-12",
    urgencyLevel: "medium",
    department: "All Departments",
    assignedRoles: ["All Employees"],
    employeeId: "EMP020",
    trainingId: "TRN-007"
  },
  {
    id: "OT-005",
    employeeName: "Christopher Lee",
    trainingTitle: "Data Privacy & GDPR Compliance",
    dueDate: "2024-11-10",
    daysOverdue: 19,
    managerName: "Michelle Davis",
    lastReminderSent: "2024-11-12",
    urgencyLevel: "medium",
    department: "All Departments",
    assignedRoles: ["All Employees"],
    employeeId: "EMP021",
    trainingId: "TRN-007"
  },
  {
    id: "OT-006",
    employeeName: "Amanda Rodriguez",
    trainingTitle: "Chemical Handling and Safety",
    dueDate: "2024-11-25",
    daysOverdue: 4,
    managerName: "Patricia Taylor",
    lastReminderSent: "2024-11-28",
    urgencyLevel: "low",
    department: "Quality Assurance",
    assignedRoles: ["Lab Technician"],
    employeeId: "EMP025",
    trainingId: "TRN-005"
  },
  {
    id: "OT-007",
    employeeName: "Kevin Brown",
    trainingTitle: "Lean Manufacturing Principles",
    dueDate: "2024-11-20",
    daysOverdue: 9,
    managerName: "Mark Johnson",
    lastReminderSent: "2024-11-22",
    urgencyLevel: "medium",
    department: "Production",
    assignedRoles: ["Team Lead"],
    employeeId: "EMP019",
    trainingId: "TRN-006"
  },
  {
    id: "OT-008",
    employeeName: "Steven Miller",
    trainingTitle: "Equipment Calibration Standards",
    dueDate: "2024-11-30",
    daysOverdue: -1,
    managerName: "Angela Clark",
    lastReminderSent: "2024-11-25",
    urgencyLevel: "low",
    department: "Maintenance",
    assignedRoles: ["Maintenance Technician"],
    employeeId: "EMP023",
    trainingId: "TRN-008"
  },
  {
    id: "OT-009",
    employeeName: "Jessica Garcia",
    trainingTitle: "5S Workplace Organization",
    dueDate: "2024-12-01",
    daysOverdue: -2,
    managerName: "William Jones",
    lastReminderSent: "Never",
    urgencyLevel: "low",
    department: "Production",
    assignedRoles: ["Production Staff"],
    employeeId: "EMP028",
    trainingId: "TRN-010"
  },
  {
    id: "OT-010",
    employeeName: "Maria Santos",
    trainingTitle: "Workplace Safety Fundamentals",
    dueDate: "2024-10-30",
    daysOverdue: 30,
    managerName: "Carlos Martinez",
    lastReminderSent: "2024-11-05",
    urgencyLevel: "high",
    department: "Maintenance",
    assignedRoles: ["Maintenance Technician"],
    employeeId: "EMP030",
    trainingId: "TRN-011"
  },
  {
    id: "OT-011",
    employeeName: "Robert Kim",
    trainingTitle: "Quality Control Procedures",
    dueDate: "2024-11-05",
    daysOverdue: 24,
    managerName: "Susan Davis",
    lastReminderSent: "2024-11-08",
    urgencyLevel: "high",
    department: "Quality Assurance",
    assignedRoles: ["Quality Control"],
    employeeId: "EMP031",
    trainingId: "TRN-012"
  },
  {
    id: "OT-012",
    employeeName: "Linda Anderson",
    trainingTitle: "Environmental Compliance",
    dueDate: "2024-11-12",
    daysOverdue: 17,
    managerName: "Daniel Moore",
    lastReminderSent: "2024-11-15",
    urgencyLevel: "medium",
    department: "Quality Assurance",
    assignedRoles: ["Lab Technician"],
    employeeId: "EMP032",
    trainingId: "TRN-013"
  },
  {
    id: "OT-013",
    employeeName: "James Wilson",
    trainingTitle: "Preventive Maintenance Best Practices",
    dueDate: "2024-11-08",
    daysOverdue: 21,
    managerName: "Patricia Taylor",
    lastReminderSent: "2024-11-11",
    urgencyLevel: "medium",
    department: "Maintenance",
    assignedRoles: ["Equipment Operator"],
    employeeId: "EMP033",
    trainingId: "TRN-014"
  },
  {
    id: "OT-014",
    employeeName: "Rachel Green",
    trainingTitle: "Continuous Improvement Methods",
    dueDate: "2024-11-18",
    daysOverdue: 11,
    managerName: "Kevin Brown",
    lastReminderSent: "2024-11-20",
    urgencyLevel: "medium",
    department: "Production",
    assignedRoles: ["Process Engineer"],
    employeeId: "EMP034",
    trainingId: "TRN-015"
  },
  {
    id: "OT-015",
    employeeName: "Andrew Thompson",
    trainingTitle: "Root Cause Analysis for Manufacturing Deviations",
    dueDate: "2024-10-25",
    daysOverdue: 35,
    managerName: "Brian Rodriguez",
    lastReminderSent: "2024-11-01",
    urgencyLevel: "critical",
    department: "Quality Assurance",
    assignedRoles: ["Quality Manager"],
    employeeId: "EMP035",
    trainingId: "TRN-009"
  }
];

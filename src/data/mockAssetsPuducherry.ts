
import { Asset } from "@/types/assets";
import { createMockAsset, commonStaff } from "@/utils/mockAssetGenerator";

// Use the utility function to create mock assets for Puducherry
export const mockAssetsPuducherry: Asset[] = [
  createMockAsset({
    id: "PDY-MON-001",
    name: "Dell UltraSharp 27\"",
    type: "Monitor",
    description: "27-inch 4K UHD Monitor with USB-C connectivity",
    location: "Engineering Dept, Puducherry",
    owner: commonStaff.harshJha,
    assignedTo: commonStaff.harshJha,
    status: "In Use",
    lastUpdated: "2023-10-15",
    purchaseDate: "15 Jan 2024",
    calibrationDate: "15 Apr 2025",
    calibrationCertNo: "CAL-24-0042",
    calibrationPeriod: "Yearly"
  }),
  createMockAsset({
    id: "PDY-LAP-017",
    name: "Dell XPS 15",
    type: "Laptop",
    description: "Developer Workstation with 32GB RAM",
    location: "R&D Building, Puducherry",
    owner: commonStaff.aishaPatel,
    assignedTo: commonStaff.aishaPatel,
    status: "In Use",
    lastUpdated: "2023-11-02",
    purchaseDate: "22 Mar 2024"
  }),
  createMockAsset({
    id: "PDY-SRV-003",
    name: "HPE ProLiant DL380",
    type: "Server",
    description: "Production database server",
    location: "Server Room, Puducherry",
    owner: commonStaff.rajeshKumar,
    assignedTo: commonStaff.rajeshKumar,
    status: "In Use",
    lastUpdated: "2023-09-18",
    purchaseDate: "05 Dec 2023",
    calibrationDate: "05 Mar 2025",
    calibrationCertNo: "SRV-CAL-056",
    calibrationPeriod: "Quarterly"
  }),
  createMockAsset({
    id: "PDY-PRT-009",
    name: "HP LaserJet Enterprise",
    type: "Printer",
    description: "Color LaserJet for marketing department",
    location: "Marketing Floor, Puducherry",
    owner: commonStaff.sophiaLee,
    assignedTo: "Shared",
    status: "Under Maintenance",
    lastUpdated: "2023-10-28",
    purchaseDate: "17 Feb 2024",
    calibrationDate: "17 May 2025",
    calibrationCertNo: "PR-CAL-2024-028",
    calibrationPeriod: "Half-yearly"
  }),
  createMockAsset({
    id: "PDY-MON-012",
    name: "LG UltraFine 24\"",
    type: "Monitor",
    description: "Design team monitor",
    location: "Design Studio, Puducherry",
    owner: commonStaff.harshJha,
    assignedTo: commonStaff.jamesBrown,
    status: "In Use",
    lastUpdated: "2023-11-15",
    purchaseDate: "28 Jan 2024"
  }),
  createMockAsset({
    id: "PDY-LAP-028",
    name: "MacBook Pro 16\"",
    type: "Laptop",
    description: "Design team laptop with M2 Pro",
    location: "Design Studio, Puducherry",
    owner: commonStaff.jamesBrown,
    assignedTo: commonStaff.jamesBrown,
    status: "Breakdown",
    lastUpdated: "2023-11-10",
    purchaseDate: "14 Mar 2024"
  }),
  createMockAsset({
    id: "PDY-SRV-006",
    name: "Dell PowerEdge R740",
    type: "Server",
    description: "Development environment server",
    location: "Server Room, Puducherry",
    owner: commonStaff.rajeshKumar,
    assignedTo: "IT Department",
    status: "Available",
    lastUpdated: "2023-09-25",
    purchaseDate: "10 Nov 2023",
    calibrationDate: "10 Feb 2025",
    calibrationCertNo: "SRV-CAL-032",
    calibrationPeriod: "Quarterly"
  }),
  createMockAsset({
    id: "PDY-PRT-004",
    name: "Epson WorkForce Pro",
    type: "Printer",
    description: "Admin department printer",
    location: "Administration, Puducherry",
    owner: commonStaff.sophiaLee,
    assignedTo: "Shared",
    status: "In Use",
    lastUpdated: "2023-10-05",
    purchaseDate: "15 Dec 2023"
  }),
  createMockAsset({
    id: "PDY-MON-023",
    name: "Samsung Odyssey G9",
    type: "Monitor",
    description: "Ultra-wide curved monitor for CAD team",
    location: "Engineering Dept, Puducherry",
    owner: commonStaff.michaelChen,
    assignedTo: commonStaff.michaelChen,
    status: "In Use",
    lastUpdated: "2023-12-01",
    purchaseDate: "07 Feb 2024",
    calibrationDate: "07 Aug 2024",
    calibrationCertNo: "CAL-MON-G9-101",
    calibrationPeriod: "Half-yearly"
  }),
  createMockAsset({
    id: "PDY-LAP-035",
    name: "Lenovo ThinkPad X1",
    type: "Laptop",
    description: "Sales team laptop",
    location: "Sales Office, Puducherry",
    owner: commonStaff.sophiaLee,
    assignedTo: commonStaff.aishaPatel,
    status: "In Use",
    lastUpdated: "2023-11-25",
    purchaseDate: "14 Apr 2024"
  })
];

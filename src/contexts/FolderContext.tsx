
import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Folder, FolderNavigationState, FolderAction } from '@/types/folder';

interface FolderContextType {
  folders: Folder[];
  navigationState: FolderNavigationState;
  dispatch: React.Dispatch<FolderAction>;
  createFolder: (name: string, parentId: string | null) => void;
  deleteFolder: (folderId: string) => void;
  navigateToFolder: (folderId: string | null) => void;
  moveItem: (itemId: string, targetFolderId: string | null) => void;
}

const FolderContext = createContext<FolderContextType | undefined>(undefined);

const initialNavigationState: FolderNavigationState = {
  currentFolderId: null,
  currentPath: [],
  breadcrumbs: [{ id: 'root', name: 'Documents' }]
};

// Mock folders data - in a real app this would come from a database
const mockFolders: Folder[] = [
  {
    id: 'folder-1',
    name: 'Quality Documents',
    parentId: null,
    createdBy: 'admin',
    createdDate: '2025-01-01',
    path: ['Quality Documents'],
    description: 'All quality-related documentation'
  },
  {
    id: 'folder-2',
    name: 'SOPs',
    parentId: 'folder-1',
    createdBy: 'admin',
    createdDate: '2025-01-02',
    path: ['Quality Documents', 'SOPs'],
    description: 'Standard Operating Procedures'
  },
  {
    id: 'folder-3',
    name: 'Production',
    parentId: null,
    createdBy: 'admin',
    createdDate: '2025-01-03',
    path: ['Production'],
    description: 'Production documentation and procedures'
  }
];

function folderReducer(state: { folders: Folder[]; navigation: FolderNavigationState }, action: FolderAction) {
  switch (action.type) {
    case 'NAVIGATE_TO_FOLDER':
      const folder = state.folders.find(f => f.id === action.folderId);
      const breadcrumbs = action.folderId === null 
        ? [{ id: 'root', name: 'Documents' }]
        : [
            { id: 'root', name: 'Documents' },
            ...action.path.map((name, index) => ({
              id: `path-${index}`,
              name
            }))
          ];
      
      return {
        ...state,
        navigation: {
          currentFolderId: action.folderId,
          currentPath: action.path,
          breadcrumbs
        }
      };
    
    case 'CREATE_FOLDER':
      return {
        ...state,
        folders: [...state.folders, action.folder]
      };
    
    case 'DELETE_FOLDER':
      return {
        ...state,
        folders: state.folders.filter(f => f.id !== action.folderId)
      };
    
    default:
      return state;
  }
}

interface FolderProviderProps {
  children: ReactNode;
}

export const FolderProvider: React.FC<FolderProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(folderReducer, {
    folders: mockFolders,
    navigation: initialNavigationState
  });

  const createFolder = (name: string, parentId: string | null) => {
    const newFolder: Folder = {
      id: `folder-${Date.now()}`,
      name,
      parentId,
      createdBy: 'current-user',
      createdDate: new Date().toISOString(),
      path: parentId 
        ? [...(state.folders.find(f => f.id === parentId)?.path || []), name]
        : [name]
    };
    
    dispatch({ type: 'CREATE_FOLDER', folder: newFolder });
  };

  const deleteFolder = (folderId: string) => {
    dispatch({ type: 'DELETE_FOLDER', folderId });
  };

  const navigateToFolder = (folderId: string | null) => {
    const folder = state.folders.find(f => f.id === folderId);
    const path = folder ? folder.path : [];
    dispatch({ type: 'NAVIGATE_TO_FOLDER', folderId, path });
  };

  const moveItem = (itemId: string, targetFolderId: string | null) => {
    dispatch({ type: 'MOVE_ITEM', itemId, targetFolderId });
  };

  const value: FolderContextType = {
    folders: state.folders,
    navigationState: state.navigation,
    dispatch,
    createFolder,
    deleteFolder,
    navigateToFolder,
    moveItem
  };

  return (
    <FolderContext.Provider value={value}>
      {children}
    </FolderContext.Provider>
  );
};

export const useFolder = (): FolderContextType => {
  const context = useContext(FolderContext);
  if (!context) {
    throw new Error('useFolder must be used within a FolderProvider');
  }
  return context;
};

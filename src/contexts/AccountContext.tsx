
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { toast } from "sonner";
import { mockDocuments } from "@/data/mockDocuments";
import { mockDocumentsBengaluru } from "@/data/mockDocumentsBengaluru";
import { Document } from "@/components/document-hub/DocumentTable";
import { mockAssetsPuducherry } from '@/data/mockAssetsPuducherry';
import { mockAssetsSanJose } from '@/data/mockAssetsSanJose';
import { mockPeoplePuducherry } from '@/data/mockPeoplePuducherry';
import { mockPeopleSanJose } from '@/data/mockPeopleSanJose';
import { Asset } from '@/types/assets';
import { Person } from '@/types/people';

export type AccountLocation = "Puducherry" | "San Jose" | "Bengaluru";

interface Account {
  id: string;
  name: string;
  location: AccountLocation;
  timezone: string;
  currency: string;
  documents: Document[];
  assets: Asset[];
  people: Person[];
}

interface AccountContextType {
  currentAccount: Account;
  setCurrentAccount: (account: Account) => void;
  switchAccount: (location: AccountLocation) => void;
  accounts: Account[];
}

const mockAccounts: Account[] = [
  {
    id: 'acc-001',
    name: 'Hub Electronics - San Jose',
    location: 'San Jose' as AccountLocation,
    timezone: 'PST',
    currency: 'USD',
    documents: mockDocumentsBengaluru,
    assets: mockAssetsSanJose,
    people: mockPeopleSanJose,
  },
  {
    id: 'acc-002',
    name: 'Hub Electronics - Bengaluru',
    location: 'Bengaluru' as AccountLocation,
    timezone: 'IST',
    currency: 'INR',
    documents: mockDocuments,
    assets: mockAssetsPuducherry,
    people: mockPeoplePuducherry,
  },
  {
    id: 'acc-003',
    name: 'Hub Electronics - Puducherry',
    location: 'Puducherry' as AccountLocation,
    timezone: 'IST',
    currency: 'INR',
    documents: mockDocuments,
    assets: mockAssetsPuducherry,
    people: mockPeoplePuducherry,
  }
];

const AccountContext = createContext<AccountContextType | undefined>(undefined);

interface AccountProviderProps {
  children: ReactNode;
}

export const AccountProvider: React.FC<AccountProviderProps> = ({ children }) => {
  const [currentAccount, setCurrentAccount] = useState<Account>(mockAccounts[0]);

  const switchAccount = (location: AccountLocation) => {
    const newAccount = mockAccounts.find(account => account.location === location);
    if (newAccount && newAccount.location !== currentAccount.location) {
      setCurrentAccount(newAccount);
      toast(`Now using ${newAccount.name}`, {
        description: `You have switched to ${newAccount.location} account.`,
        icon: "🏢",
      });
    }
  };

  const value: AccountContextType = {
    currentAccount,
    setCurrentAccount,
    switchAccount,
    accounts: mockAccounts
  };

  return (
    <AccountContext.Provider value={value}>
      {children}
    </AccountContext.Provider>
  );
};

export const useAccount = (): AccountContextType => {
  const context = useContext(AccountContext);
  if (!context) {
    throw new Error('useAccount must be used within an AccountProvider');
  }
  return context;
};

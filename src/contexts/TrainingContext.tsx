
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { EmployeeTraining, Training } from '@/types/training';
import { mockTrainingData } from '@/components/people/mockData/trainingData';

// Available trainings that can be assigned
const availableTrainings: Training[] = [
  {
    id: "t-001",
    title: "ISO 9001:2015 Quality Management Fundamentals",
    type: "Video",
    description: "Comprehensive overview of ISO 9001:2015 requirements and implementation",
    estimatedDuration: "45",
    category: "Quality Management",
    language: "English",
    roleApplicability: ["Quality Engineer", "Manager"],
    lastUpdated: "2024-01-01",
    createdBy: "Training Admin",
    tags: ["ISO", "Quality", "Management"],
    difficulty: "Intermediate",
    requirements: ["Basic understanding of quality concepts"],
    learningObjectives: [
      "Understand ISO 9001:2015 structure",
      "Learn process approach",
      "Apply risk-based thinking"
    ]
  },
  {
    id: "t-002",
    title: "Safety Protocols and Procedures",
    type: "Interactive",
    description: "Essential safety training for all employees",
    estimatedDuration: "30",
    category: "Safety",
    language: "English",
    roleApplicability: ["All Employees"],
    lastUpdated: "2024-01-01",
    createdBy: "Safety Officer",
    tags: ["Safety", "Protocols", "Procedures"],
    difficulty: "Beginner",
    requirements: ["None"],
    learningObjectives: [
      "Identify workplace hazards",
      "Follow safety procedures",
      "Use protective equipment"
    ]
  },
  {
    id: "t-003",
    title: "Document Control and Management",
    type: "Video",
    description: "Learn how to manage and control documents effectively",
    estimatedDuration: "35",
    category: "Documentation",
    language: "English",
    roleApplicability: ["Document Controller", "Quality Engineer"],
    lastUpdated: "2024-01-01",
    createdBy: "Compliance Manager",
    tags: ["Documentation", "Control", "Management"],
    difficulty: "Intermediate",
    requirements: ["Basic computer skills"],
    learningObjectives: [
      "Understand document lifecycle",
      "Implement version control",
      "Ensure document integrity"
    ]
  },
  {
    id: "t-004",
    title: "Supplier Quality Management",
    type: "Interactive",
    description: "Advanced training on managing supplier quality relationships",
    estimatedDuration: "60",
    category: "Supplier Management",
    language: "English",
    roleApplicability: ["Supplier Quality Engineer", "Manager"],
    lastUpdated: "2024-01-01",
    createdBy: "Quality Director",
    tags: ["Supplier", "Quality", "Management"],
    difficulty: "Advanced",
    requirements: ["ISO 9001 fundamentals"],
    learningObjectives: [
      "Assess supplier capabilities",
      "Implement supplier audits",
      "Manage supplier performance"
    ]
  }
];

interface TrainingContextType {
  trainings: EmployeeTraining[];
  employeeTrainings: EmployeeTraining[];
  availableTrainings: Training[];
  updateTrainingProgress: (trainingId: string, progress: number) => void;
  completeTraining: (trainingId: string, score?: number) => void;
  updateProgress: (trainingId: string, progress: number, timeSpent: string) => void;
  assignTraining: (employeeId: string, training: Training, dueDate: string, notes?: string) => void;
  startTraining: (trainingId: string) => void;
  incrementAttempts: (trainingId: string) => void;
}

const TrainingContext = createContext<TrainingContextType | undefined>(undefined);

export function TrainingProvider({ children }: { children: ReactNode }) {
  const [trainings, setTrainings] = useState<EmployeeTraining[]>(mockTrainingData);

  const updateTrainingProgress = (trainingId: string, progress: number) => {
    setTrainings(prev => prev.map(training => 
      training.id === trainingId 
        ? { 
            ...training, 
            progress: Math.min(100, progress),
            status: progress >= 100 ? 'Completed' : progress > 0 ? 'In Progress' : 'Not Started',
            lastAccessed: new Date().toISOString()
          }
        : training
    ));
  };

  const completeTraining = (trainingId: string, score?: number) => {
    setTrainings(prev => prev.map(training => 
      training.id === trainingId 
        ? { 
            ...training, 
            progress: 100,
            status: 'Completed',
            completedDate: new Date().toISOString(),
            quizScore: score || null,
            certificateId: `CERT-${trainingId.toUpperCase()}`,
            lastAccessed: new Date().toISOString()
          }
        : training
    ));
  };

  const updateProgress = (trainingId: string, progress: number, timeSpent: string) => {
    setTrainings(prev => prev.map(training => 
      training.id === trainingId 
        ? { 
            ...training, 
            progress: Math.min(100, progress),
            timeSpent: timeSpent,
            status: progress >= 100 ? 'Completed' : progress > 0 ? 'In Progress' : 'Not Started',
            lastAccessed: new Date().toISOString()
          }
        : training
    ));
  };

  const assignTraining = (employeeId: string, training: Training, dueDate: string, notes?: string) => {
    const newAssignment: EmployeeTraining = {
      id: `training-${Date.now()}`,
      trainingId: training.id,
      employeeId: employeeId,
      training: training,
      status: 'Not Started',
      progress: 0,
      assignedDate: new Date().toISOString().split('T')[0],
      dueDate: dueDate,
      timeSpent: "0 minutes",
      completedDate: null,
      quizScore: null,
      assignedBy: "Training Admin",
      notes: notes,
      attempts: 0,
      lastAccessed: null
    };
    
    setTrainings(prev => [...prev, newAssignment]);
  };

  const startTraining = (trainingId: string) => {
    setTrainings(prev => prev.map(training => 
      training.id === trainingId 
        ? { 
            ...training, 
            status: 'In Progress',
            lastAccessed: new Date().toISOString(),
            attempts: (training.attempts || 0) + 1
          }
        : training
    ));
  };

  const incrementAttempts = (trainingId: string) => {
    setTrainings(prev => prev.map(training => 
      training.id === trainingId 
        ? { 
            ...training, 
            attempts: (training.attempts || 0) + 1,
            lastAccessed: new Date().toISOString()
          }
        : training
    ));
  };

  return (
    <TrainingContext.Provider value={{ 
      trainings, 
      employeeTrainings: trainings,
      availableTrainings,
      updateTrainingProgress, 
      completeTraining, 
      updateProgress,
      assignTraining,
      startTraining,
      incrementAttempts
    }}>
      {children}
    </TrainingContext.Provider>
  );
}

export function useTraining() {
  const context = useContext(TrainingContext);
  if (context === undefined) {
    throw new Error('useTraining must be used within a TrainingProvider');
  }
  return context;
}

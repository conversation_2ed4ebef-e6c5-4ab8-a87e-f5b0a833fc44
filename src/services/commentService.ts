
import { Comment, CommentAttachment, CommentReaction } from '@/types/comment';

// Mock data for demonstration
const mockComments: Comment[] = [
  {
    id: 'comment-1',
    documentId: '1',
    content: 'This document looks comprehensive. I have a few suggestions for section 3.2 regarding the quality metrics.',
    author: {
      id: 'user-1',
      name: '<PERSON>',
      avatar: '/avatars/sarah.jpg',
      role: 'Quality Manager'
    },
    createdAt: new Date('2024-01-15T10:30:00'),
    isDeleted: false,
    reactions: [
      { id: 'r1', userId: 'user-2', type: 'like', createdAt: new Date('2024-01-15T10:35:00') }
    ],
    replies: [
      {
        id: 'comment-1-1',
        documentId: '1',
        parentId: 'comment-1',
        content: 'I agree with <PERSON>. We should also consider adding more specific KPIs for measurement.',
        author: {
          id: 'user-2',
          name: '<PERSON>',
          role: 'Operations Lead'
        },
        createdAt: new Date('2024-01-15T14:20:00'),
        isDeleted: false,
        mentions: ['<PERSON>']
      }
    ]
  },
  {
    id: 'comment-2',
    documentId: '1',
    content: 'The approval workflow section needs clarification. Can we add a flowchart to illustrate the process?',
    author: {
      id: 'user-3',
      name: 'Lisa Rodriguez',
      role: 'Process Analyst'
    },
    createdAt: new Date('2024-01-16T09:15:00'),
    isDeleted: false,
    attachments: [
      {
        id: 'att-1',
        fileName: 'workflow-suggestion.png',
        fileUrl: '/uploads/workflow-suggestion.png',
        fileType: 'image/png',
        fileSize: 245000,
        uploadedAt: new Date('2024-01-16T09:15:00')
      }
    ]
  }
];

export class CommentService {
  private comments: Comment[] = [...mockComments];

  async getCommentsByDocumentId(documentId: string): Promise<Comment[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return this.comments.filter(comment => comment.documentId === documentId && !comment.parentId);
  }

  async createComment(comment: Omit<Comment, 'id' | 'createdAt'>): Promise<Comment> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const newComment: Comment = {
      ...comment,
      id: `comment-${Date.now()}`,
      createdAt: new Date(),
      isDeleted: false
    };
    this.comments.push(newComment);
    return newComment;
  }

  async updateComment(id: string, content: string): Promise<Comment> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const commentIndex = this.comments.findIndex(c => c.id === id);
    if (commentIndex === -1) throw new Error('Comment not found');
    
    const comment = this.comments[commentIndex];
    const edit = {
      id: `edit-${Date.now()}`,
      content: comment.content,
      editedAt: comment.updatedAt || comment.createdAt,
      editedBy: comment.author.id
    };
    
    this.comments[commentIndex] = {
      ...comment,
      content,
      updatedAt: new Date(),
      editHistory: [...(comment.editHistory || []), edit]
    };
    
    return this.comments[commentIndex];
  }

  async deleteComment(id: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const commentIndex = this.comments.findIndex(c => c.id === id);
    if (commentIndex === -1) throw new Error('Comment not found');
    
    this.comments[commentIndex] = {
      ...this.comments[commentIndex],
      isDeleted: true,
      content: '[This comment has been deleted]'
    };
  }

  async addReaction(commentId: string, userId: string, type: CommentReaction['type']): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 200));
    const commentIndex = this.comments.findIndex(c => c.id === commentId);
    if (commentIndex === -1) throw new Error('Comment not found');
    
    const comment = this.comments[commentIndex];
    const existingReaction = comment.reactions?.find(r => r.userId === userId && r.type === type);
    
    if (existingReaction) {
      // Remove reaction if it exists
      comment.reactions = comment.reactions?.filter(r => !(r.userId === userId && r.type === type));
    } else {
      // Add new reaction
      const newReaction: CommentReaction = {
        id: `reaction-${Date.now()}`,
        userId,
        type,
        createdAt: new Date()
      };
      comment.reactions = [...(comment.reactions || []), newReaction];
    }
  }
}

export const commentService = new CommentService();

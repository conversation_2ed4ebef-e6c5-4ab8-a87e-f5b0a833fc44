
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { AppProviders } from "@/components/app/AppProviders";
import { AppRoutes } from "@/components/app/AppRoutes";
import { SidebarProvider } from "@/components/ui/sidebar";
import { MainLayout } from "@/components/layout/MainLayout";
import { LoadingProvider } from "@/hooks/use-loading";
import { FolderProvider } from "@/contexts/FolderContext";
import { ErrorBoundary, NetworkStatusBanner } from "@/components/error-handling";

function App() {
  return (
    <ErrorBoundary>
      <AppProviders>
        <LoadingProvider>
          <FolderProvider>
            <SidebarProvider>
              <div className="min-h-screen bg-gray-50 w-full">
                <NetworkStatusBanner />
                <MainLayout>
                  <AppRoutes />
                </MainLayout>
              </div>
              <Toaster />
            </SidebarProvider>
          </FolderProvider>
        </LoadingProvider>
      </AppProviders>
    </ErrorBoundary>
  );
}

export default App;

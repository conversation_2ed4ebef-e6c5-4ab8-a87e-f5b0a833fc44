
import { EmployeeTraining } from "@/types/training";

export interface ExportData {
  trainingId: string;
  trainingTitle: string;
  employeeId: string;
  status: string;
  progress: number;
  assignedDate: string;
  dueDate: string;
  completedDate: string;
  timeSpent: string;
  assignedBy: string;
  department?: string;
  role?: string;
}

export class ExportService {
  static exportToCSV(data: ExportData[], filename: string = 'training_data') {
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map(row => Object.values(row).map(value => 
      typeof value === 'string' && value.includes(',') ? `"${value}"` : value
    ).join(','));
    
    const csvContent = [headers, ...rows].join('\n');
    this.downloadFile(csvContent, `${filename}.csv`, 'text/csv');
  }

  static exportToJSON(data: ExportData[], filename: string = 'training_data') {
    const jsonContent = JSON.stringify(data, null, 2);
    this.downloadFile(jsonContent, `${filename}.json`, 'application/json');
  }

  static exportToExcel(data: ExportData[], filename: string = 'training_data') {
    // Simple tab-separated format that Excel can read
    const headers = Object.keys(data[0]).join('\t');
    const rows = data.map(row => Object.values(row).join('\t'));
    
    const tsvContent = [headers, ...rows].join('\n');
    this.downloadFile(tsvContent, `${filename}.xls`, 'application/vnd.ms-excel');
  }

  static generateReportData(trainings: EmployeeTraining[]): ExportData[] {
    return trainings.map(training => ({
      trainingId: training.id,
      trainingTitle: training.training.title,
      employeeId: training.employeeId,
      status: training.status,
      progress: training.progress,
      assignedDate: training.assignedDate,
      dueDate: training.dueDate,
      completedDate: training.completedDate || '',
      timeSpent: training.timeSpent,
      assignedBy: training.assignedBy,
      department: 'Unknown', // Could be enhanced with actual employee data
      role: 'Unknown' // Could be enhanced with actual employee data
    }));
  }

  static generateDepartmentReport(trainings: EmployeeTraining[]) {
    const departmentStats = new Map();
    
    trainings.forEach(training => {
      const dept = 'Unknown'; // Would need employee department data
      if (!departmentStats.has(dept)) {
        departmentStats.set(dept, {
          department: dept,
          totalAssignments: 0,
          completed: 0,
          inProgress: 0,
          overdue: 0,
          notStarted: 0
        });
      }
      
      const stats = departmentStats.get(dept);
      stats.totalAssignments++;
      
      switch (training.status) {
        case 'Completed':
          stats.completed++;
          break;
        case 'In Progress':
          stats.inProgress++;
          break;
        case 'Not Started':
          stats.notStarted++;
          break;
        default:
          if (new Date() > new Date(training.dueDate)) {
            stats.overdue++;
          }
      }
    });
    
    return Array.from(departmentStats.values());
  }

  private static downloadFile(content: string, filename: string, mimeType: string) {
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
}

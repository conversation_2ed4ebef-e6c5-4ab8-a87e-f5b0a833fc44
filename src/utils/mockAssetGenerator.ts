
import { Asset } from "@/types/assets";

type AssetStatusType = 'In Use' | 'Available' | 'Under Maintenance' | 'Breakdown' | 'Retired';

interface MockAssetOptions {
  id: string;
  name: string;
  type: string;
  description: string;
  location: string;
  owner: string;
  assignedTo: string;
  status: AssetStatusType;
  lastUpdated: string;
  purchaseDate: string;
  calibrationDate?: string;
  calibrationCertNo?: string;
  calibrationPeriod?: string;
}

/**
 * Creates a mock asset with provided options
 */
export const createMockAsset = (options: MockAssetOptions): Asset => {
  return {
    id: options.id,
    name: options.name,
    type: options.type,
    description: options.description,
    location: options.location,
    owner: options.owner,
    assignedTo: options.assignedTo,
    status: options.status,
    lastUpdated: options.lastUpdated,
    purchaseDate: options.purchaseDate,
    calibrationDate: options.calibrationDate || "--",
    calibrationCertNo: options.calibrationCertNo || "--",
    calibrationPeriod: options.calibrationPeriod || "--",
  };
};

/**
 * Common owners and staff that can be used across different locations
 */
export const commonStaff = {
  harshJha: "Harsh Jha",
  aishaPatel: "<PERSON><PERSON> <PERSON>",
  micha<PERSON><PERSON><PERSON>: "<PERSON> <PERSON>",
  jam<PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
  so<PERSON>a<PERSON>ee: "<PERSON> <PERSON>",
  r<PERSON><PERSON><PERSON><PERSON>r: "<PERSON><PERSON> <PERSON>",
  em<PERSON><PERSON><PERSON>: "<PERSON> <PERSON>",
  dave<PERSON>atel: "<PERSON> <PERSON>",
  sa<PERSON><PERSON><PERSON><PERSON>: "<PERSON> <PERSON>",
  car<PERSON><PERSON>od<PERSON><PERSON><PERSON>: "<PERSON> <PERSON>",
};

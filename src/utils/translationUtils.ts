
import { SupportedLanguage, TranslatedText, TranslatedArray } from "@/types/formBuilder";

export const SUPPORTED_LANGUAGES: Record<SupportedLanguage, { name: string; flag: string }> = {
  'en': { name: 'English', flag: '🇺🇸' },
  'de': { name: 'German', flag: '🇩🇪' },
  'es-mx': { name: 'Spanish', flag: '🇲🇽' },
  'fr': { name: 'French', flag: '🇫🇷' }
};

export const getTranslatedText = (
  text: string | TranslatedText | undefined,
  language: SupportedLanguage
): string => {
  if (!text) return '';
  
  if (typeof text === 'string') {
    return text;
  }
  
  return text[language] || text.en || '';
};

export const updateTranslation = (
  currentText: string | TranslatedText | undefined,
  language: SupportedLanguage,
  newValue: string
): string | TranslatedText => {
  if (typeof currentText === 'string') {
    if (language === 'en') {
      return newValue;
    }
    return {
      en: currentText,
      [language]: newValue
    };
  }
  
  if (!currentText) {
    if (language === 'en') {
      return newValue;
    }
    return {
      en: '',
      [language]: newValue
    };
  }
  
  return {
    ...currentText,
    [language]: newValue
  };
};


import { Folder, DocumentWithFolder } from '@/types/folder';

export const buildFolderTree = (folders: Folder[]): Folder[] => {
  const folderMap = new Map<string, Folder & { children: Folder[] }>();
  
  // Initialize all folders with children array
  folders.forEach(folder => {
    folderMap.set(folder.id, { ...folder, children: [] });
  });
  
  // Build the tree structure
  const rootFolders: Folder[] = [];
  
  folders.forEach(folder => {
    const folderWithChildren = folderMap.get(folder.id)!;
    
    if (folder.parentId === null) {
      rootFolders.push(folderWithChildren);
    } else {
      const parent = folderMap.get(folder.parentId);
      if (parent) {
        parent.children.push(folderWithChildren);
      }
    }
  });
  
  return rootFolders;
};

export const getFolderPath = (folderId: string, folders: Folder[]): string[] => {
  const folder = folders.find(f => f.id === folderId);
  return folder ? folder.path : [];
};

export const getSubfolders = (parentId: string | null, folders: Folder[]): Folder[] => {
  return folders.filter(folder => folder.parentId === parentId);
};

export const getFolderItems = (
  folderId: string | null, 
  documents: DocumentWithFolder[], 
  folders: Folder[]
): { documents: DocumentWithFolder[]; folders: Folder[] } => {
  const folderDocuments = documents.filter(doc => doc.parentFolderId === folderId);
  const subfolders = getSubfolders(folderId, folders);
  
  return {
    documents: folderDocuments,
    folders: subfolders
  };
};

export const searchInFolder = (
  query: string,
  folderId: string | null,
  documents: DocumentWithFolder[],
  folders: Folder[]
): { documents: DocumentWithFolder[]; folders: Folder[] } => {
  const lowerQuery = query.toLowerCase();
  
  // Get all items in current folder
  const { documents: folderDocs, folders: subfolders } = getFolderItems(folderId, documents, folders);
  
  // Filter by search query
  const matchingDocuments = folderDocs.filter(doc =>
    doc.title.toLowerCase().includes(lowerQuery) ||
    doc.description?.toLowerCase().includes(lowerQuery) ||
    doc.category.toLowerCase().includes(lowerQuery)
  );
  
  const matchingFolders = subfolders.filter(folder =>
    folder.name.toLowerCase().includes(lowerQuery) ||
    folder.description?.toLowerCase().includes(lowerQuery)
  );
  
  return {
    documents: matchingDocuments,
    folders: matchingFolders
  };
};

export const canMoveItem = (
  sourceId: string,
  targetFolderId: string | null,
  folders: Folder[]
): boolean => {
  // Prevent moving a folder into itself or its descendants
  if (targetFolderId === sourceId) return false;
  
  const isDescendant = (parentId: string, childId: string): boolean => {
    const child = folders.find(f => f.id === childId);
    if (!child || child.parentId === null) return false;
    if (child.parentId === parentId) return true;
    return isDescendant(parentId, child.parentId);
  };
  
  return targetFolderId === null || !isDescendant(sourceId, targetFolderId);
};

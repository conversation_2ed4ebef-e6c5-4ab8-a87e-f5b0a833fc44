
import { z } from "zod";

// Common validation utilities and error messages
export const ValidationMessages = {
  REQUIRED: (field: string) => `${field} is required`,
  MIN_LENGTH: (field: string, min: number) => `${field} must be at least ${min} characters`,
  MAX_LENGTH: (field: string, max: number) => `${field} must be less than ${max} characters`,
  INVALID_EMAIL: "Please enter a valid email address",
  INVALID_PHONE: "Please enter a valid phone number",
  INVALID_URL: "Please enter a valid URL",
  POSITIVE_NUMBER: "Must be a positive number",
  INVALID_DATE: "Please enter a valid date",
  PASSWORD_WEAK: "Password must contain at least 8 characters, including uppercase, lowercase, and numbers"
};

// Reusable validation schemas
export const BaseValidations = {
  email: z.string().email(ValidationMessages.INVALID_EMAIL),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, ValidationMessages.INVALID_PHONE),
  url: z.string().url(ValidationMessages.INVALID_URL),
  positiveNumber: z.number().positive(ValidationMessages.POSITIVE_NUMBER),
  strongPassword: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, ValidationMessages.PASSWORD_WEAK),
  
  // Dynamic validations
  requiredString: (fieldName: string, minLength = 1) => 
    z.string().min(minLength, ValidationMessages.MIN_LENGTH(fieldName, minLength)),
  
  optionalString: (maxLength?: number) => 
    maxLength 
      ? z.string().max(maxLength, ValidationMessages.MAX_LENGTH("Field", maxLength)).optional()
      : z.string().optional(),
  
  requiredSelect: (fieldName: string) => 
    z.string().min(1, ValidationMessages.REQUIRED(fieldName)),
  
  dateString: z.string().min(1, ValidationMessages.INVALID_DATE)
};

// Form state utilities
export const FormStateHelpers = {
  isDirty: (form: any) => Object.keys(form.formState.dirtyFields).length > 0,
  hasErrors: (form: any) => Object.keys(form.formState.errors).length > 0,
  getFirstError: (form: any) => {
    const errors = form.formState.errors;
    const firstErrorKey = Object.keys(errors)[0];
    return firstErrorKey ? errors[firstErrorKey]?.message : null;
  }
};

// Conditional validation helper
export const createConditionalValidation = <T>(
  condition: (data: T) => boolean,
  schema: z.ZodType,
  fallbackSchema: z.ZodType = z.any()
) => {
  return z.any().superRefine((data, ctx) => {
    const validationSchema = condition(data) ? schema : fallbackSchema;
    const result = validationSchema.safeParse(data);
    
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        ctx.addIssue(issue);
      });
    }
  });
};

// Cross-field validation helper
export const createCrossFieldValidation = <T>(
  fieldName: string,
  validator: (data: T) => boolean,
  errorMessage: string
) => {
  return z.any().superRefine((data, ctx) => {
    if (!validator(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: errorMessage,
        path: [fieldName]
      });
    }
  });
};

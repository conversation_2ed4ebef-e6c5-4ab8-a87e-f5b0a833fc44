
import { mockDocuments } from "@/data/mockDocuments";
import { mockDocumentsBengaluru } from "@/data/mockDocumentsBengaluru";

export const getDocumentCounts = () => {
  const allDocuments = [...mockDocuments, ...mockDocumentsBengaluru];
  
  const statusCounts = {};
  const categoryCounts = {};
  const departmentCounts = {};
  const assigneeCounts = {};
  const processesCounts = {};
  
  allDocuments.forEach(doc => {
    // Count statuses
    if (doc.status) {
      const statusKey = doc.status.toLowerCase();
      statusCounts[statusKey] = (statusCounts[statusKey] || 0) + 1;
    }
    
    // Count categories
    if (doc.category) {
      const categoryKey = doc.category.toLowerCase();
      categoryCounts[categoryKey] = (categoryCounts[categoryKey] || 0) + 1;
    }
    
    // Count departments
    if (doc.department) {
      const departmentKey = doc.department.toLowerCase();
      departmentCounts[departmentKey] = (departmentCounts[departmentKey] || 0) + 1;
    }
    
    // Count assignees
    if (doc.assignee) {
      const assigneeKey = doc.assignee.toLowerCase().replace(/\s+/g, '-');
      assigneeCounts[assigneeKey] = (assigneeCounts[assigneeKey] || 0) + 1;
    }
    
    // Count processes - handle comma separated values
    if (doc.processes) {
      const processList = doc.processes.split(',').map(p => p.trim());
      processList.forEach(process => {
        const processKey = process.toLowerCase().replace(/\s+/g, '-');
        processesCounts[processKey] = (processesCounts[processKey] || 0) + 1;
      });
    }
  });
  
  return {
    statusCounts,
    categoryCounts,
    departmentCounts,
    assigneeCounts,
    processesCounts
  };
};


import { Document } from "@/components/document-hub/DocumentTable";

type DocStatusType = 'Published' | 'Draft' | 'Needs Approval' | 'Rejected' | 'Expired' | 'Not Uploaded';

interface MockDocumentOptions {
  id: string;
  title: string;
  version?: string;
  status: DocStatusType;
  category: string;
  department: string;
  processes: string;
  assignee: string;
  approver: string;
  publishedDate: string;
  reviewDate: string;
}

/**
 * Creates a mock document with provided options
 */
export const createMockDocument = (options: MockDocumentOptions): Document => {
  return {
    id: options.id,
    title: options.title,
    version: options.version,
    status: options.status,
    category: options.category,
    department: options.department,
    processes: options.processes,
    assignee: options.assignee,
    approver: options.approver,
    publishedDate: options.publishedDate,
    reviewDate: options.reviewDate,
  };
};

/**
 * Common approvers that can be used across different mock data files
 */
export const commonApprovers = {
  john<PERSON>mith: "<PERSON>",
  sa<PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
  ma<PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
  raj<PERSON><PERSON><PERSON>: "<PERSON>",
  <PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
  so<PERSON><PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
  <PERSON><PERSON><PERSON><PERSON><PERSON>: "<PERSON>",
};

/**
 * Common assignees that can be used across different mock data files
 */
export const commonAssignees = {
  micha<PERSON><PERSON><PERSON><PERSON>on: "<PERSON>",
  sa<PERSON><PERSON>on: "<PERSON>",
  david<PERSON><PERSON><PERSON><PERSON>: "<PERSON> <PERSON>",
  emily<PERSON>ilson: "<PERSON> <PERSON>",
  robert<PERSON><PERSON>: "<PERSON> <PERSON>",
  j<PERSON>r<PERSON><PERSON>er: "<PERSON> <PERSON>",
  alexRod<PERSON>uez: "<PERSON> <PERSON>",
  r<PERSON>l<PERSON><PERSON>: "<PERSON> <PERSON>",
  ch<PERSON><PERSON>ay<PERSON>: "<PERSON> <PERSON>",
  so<PERSON><PERSON>arcia: "<PERSON> <PERSON>",
  jamesWilson: "James Wilson",
  lisaJohnson: "Lisa Johnson",
  markStevens: "Mark Stevens",
  amandaLee: "Amanda Lee",
  danielWright: "Daniel Wright",
};


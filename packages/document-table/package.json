{"name": "@bprhub/document-table", "version": "1.0.0", "type": "module", "description": "A comprehensive, standalone document table component with search, filtering, and pagination", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "prepare": "npm run build", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["react", "table", "document", "component", "typescript", "pagination", "search", "filter"], "author": "BPR Hub", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"lucide-react": "^0.263.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.2.1", "@rollup/plugin-typescript": "^11.1.3", "@rollup/plugin-terser": "^0.4.4", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "rollup": "^3.29.2", "rollup-plugin-peer-deps-external": "^2.2.4", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "https://github.com/bprhub/document-table.git"}, "bugs": {"url": "https://github.com/bprhub/document-table/issues"}, "homepage": "https://github.com/bprhub/document-table#readme"}
// Default column widths for the table
export const DEFAULT_COLUMN_WIDTHS: Record<string, number> = {
  title: 250,
  department: 150,
  category: 150,
  processes: 150,
  status: 120,
  assignee: 120,
  publishedDate: 120,
  reviewDate: 120,
  approver: 120,
  actions: 120,
};

// Common panel configuration for consistent column sizing
export const TABLE_LAYOUT = {
  titlePanel: { defaultSize: 30, minSize: 15 },
  departmentPanel: { defaultSize: 10, minSize: 7 },
  categoryPanel: { defaultSize: 10, minSize: 7 },
  processesPanel: { defaultSize: 10, minSize: 7 },
  statusPanel: { defaultSize: 8, minSize: 5 },
  assigneePanel: { defaultSize: 8, minSize: 5 },
  publishDatePanel: { defaultSize: 8, minSize: 5 },
  reviewDatePanel: { defaultSize: 8, minSize: 5 },
  approverPanel: { defaultSize: 8, minSize: 5 },
  actionsPanel: { defaultSize: 8, minSize: 5 },
};

// Consistent ID for all table panel groups to sync resize state
export const TABLE_PANEL_GROUP_ID = "document-table-columns";

// Get saved column widths from localStorage
export const getSavedColumnWidths = (): Record<string, number> => {
  try {
    const saved = localStorage.getItem('document-table-column-widths');
    return saved ? JSON.parse(saved) : DEFAULT_COLUMN_WIDTHS;
  } catch (e) {
    console.error("Error parsing saved column widths:", e);
    return DEFAULT_COLUMN_WIDTHS;
  }
};

// Save column widths to localStorage
export const saveColumnWidths = (widths: Record<string, number>): void => {
  try {
    localStorage.setItem('document-table-column-widths', JSON.stringify(widths));
  } catch (e) {
    console.error("Error saving column widths:", e);
  }
};

// Reset column widths to defaults
export const resetColumnWidths = (): void => {
  try {
    localStorage.removeItem('document-table-column-widths');
  } catch (e) {
    console.error("Error resetting column widths:", e);
  }
};

// Get column width for a specific column
export const getColumnWidth = (columnKey: string): number => {
  const savedWidths = getSavedColumnWidths();
  return savedWidths[columnKey] || DEFAULT_COLUMN_WIDTHS[columnKey] || 120;
};

// Update a specific column width
export const updateColumnWidth = (columnKey: string, width: number): void => {
  const currentWidths = getSavedColumnWidths();
  const updatedWidths = {
    ...currentWidths,
    [columnKey]: Math.max(80, width) // Minimum width of 80px
  };
  saveColumnWidths(updatedWidths);
};

// Calculate total table width
export const calculateTotalTableWidth = (): number => {
  const savedWidths = getSavedColumnWidths();
  return Object.values(savedWidths).reduce((total, width) => total + width, 0);
};

// Get responsive column configuration
export const getResponsiveColumnConfig = (isMobile: boolean) => {
  if (isMobile) {
    return {
      visibleColumns: ['title', 'status'],
      hiddenColumns: ['department', 'category', 'processes', 'assignee', 'publishedDate', 'reviewDate', 'approver'],
      expandableDetails: true
    };
  }
  
  return {
    visibleColumns: Object.keys(DEFAULT_COLUMN_WIDTHS),
    hiddenColumns: [],
    expandableDetails: false
  };
};

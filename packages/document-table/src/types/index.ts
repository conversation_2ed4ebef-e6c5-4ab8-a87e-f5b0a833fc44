export interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string[];
  status: DocumentStatus;
  assignee?: string;
  approver?: string;
  date?: string;
  publishedDate?: string | null;
  reviewDate?: string | null;
  description?: string;
  parentFolderId?: string | null;
  createdAt?: string;
  updatedAt?: string;
  fileUrl?: string;
  fileSize?: number;
  fileType?: string;
}

// Document status types
export type DocumentStatus =
  | "published"
  | "draft"
  | "needs-approval"
  | "rejected"
  | "expired"
  | "not-uploaded"
  | "review"
  | "under review"
  | "approved"
  | "archived";

export interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
  processes: string[];
}

export interface DocumentTableProps {
  documents?: Document[];
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  actions?: DocumentActions;
  initialFilters?: FilterState;
  initialSortField?: SortField;
  initialSortDirection?: SortDirection;
  initialPageSize?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  showPagination?: boolean;
  showViewToggle?: boolean;
  showCreateButton?: boolean;
  onCreateClick?: () => void;
  filterOptions?: FilterOptions;
  className?: string;
}

export interface StandaloneDocumentTableProps {
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  initialDocuments?: Document[];
  className?: string;
}

// Sorting types
export type SortField =
  | "title"
  | "status"
  | "department"
  | "category"
  | "assignee"
  | "publishedDate"
  | "reviewDate";

export type SortDirection = "asc" | "desc";

export type ViewMode = "table" | "folder";

// Filter option types
export interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

export interface FilterOptions {
  status?: FilterOption[];
  categories?: FilterOption[];
  departments?: FilterOption[];
  assignee?: FilterOption[];
  processes?: FilterOption[];
}

// Document actions
export interface DocumentActions {
  onView?: (document: Document) => void;
  onEdit?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onDuplicate?: (document: Document) => void;
  onDownload?: (document: Document) => void;
}

import { useState, useMemo } from "react";

export interface UseDocumentPaginationProps {
  documents: any[];
  initialPageSize?: number;
  initialPage?: number;
}

export const useDocumentPagination = ({
  documents,
  initialPageSize = 10,
  initialPage = 1,
}: UseDocumentPaginationProps) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  // Calculate pagination
  const totalCount = documents.length;
  const totalPages = useMemo(
    () => Math.ceil(totalCount / pageSize),
    [totalCount, pageSize]
  );

  // Calculate pagination info
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;

  // Get paginated documents
  const paginatedDocuments = useMemo(() => {
    return documents.slice(
      (currentPage - 1) * pageSize,
      currentPage * pageSize
    );
  }, [documents, currentPage, pageSize]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  // Get the current slice of data based on pagination settings
  const getPaginatedData = <T>(data: T[]): T[] => {
    return data.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  };

  return {
    currentPage,
    pageSize,
    totalPages,
    paginatedDocuments,
    startIndex,
    endIndex,
    hasNextPage,
    hasPreviousPage,
    handlePageChange,
    handlePageSizeChange,
    goToPage,
    setPageSize: handlePageSizeChange,
    getPaginatedData,
  };
};

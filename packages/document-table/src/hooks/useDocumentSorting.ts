import { useState, useMemo } from "react";
import { Document, SortField, SortDirection } from "../types";

interface UseDocumentSortingProps {
  documents: Document[];
  initialSortField?: SortField;
  initialSortDirection?: SortDirection;
}

interface UseDocumentSortingReturn {
  sortedDocuments: Document[];
  sortField: SortField;
  sortDirection: SortDirection;
  handleSort: (field: SortField) => void;
  setSorting: (field: SortField, direction: SortDirection) => void;
}

export const useDocumentSorting = ({
  documents,
  initialSortField = 'title',
  initialSortDirection = 'asc',
}: UseDocumentSortingProps): UseDocumentSortingReturn => {
  const [sortField, setSortField] = useState<SortField>(initialSortField);
  const [sortDirection, setSortDirection] = useState<SortDirection>(initialSortDirection);

  const sortedDocuments = useMemo(() => {
    const sorted = [...documents].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case 'title':
          aValue = a.title?.toLowerCase() || '';
          bValue = b.title?.toLowerCase() || '';
          break;
        case 'status':
          aValue = a.status?.toLowerCase() || '';
          bValue = b.status?.toLowerCase() || '';
          break;
        case 'department':
          aValue = a.department?.toLowerCase() || '';
          bValue = b.department?.toLowerCase() || '';
          break;
        case 'category':
          aValue = a.category?.toLowerCase() || '';
          bValue = b.category?.toLowerCase() || '';
          break;
        case 'assignee':
          aValue = a.assignee?.toLowerCase() || '';
          bValue = b.assignee?.toLowerCase() || '';
          break;
        case 'publishedDate':
          aValue = a.publishedDate ? new Date(a.publishedDate).getTime() : 0;
          bValue = b.publishedDate ? new Date(b.publishedDate).getTime() : 0;
          break;
        case 'reviewDate':
          aValue = a.reviewDate ? new Date(a.reviewDate).getTime() : 0;
          bValue = b.reviewDate ? new Date(b.reviewDate).getTime() : 0;
          break;
        default:
          aValue = '';
          bValue = '';
      }

      // Handle comparison
      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    return sorted;
  }, [documents, sortField, sortDirection]);

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with ascending direction
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const setSorting = (field: SortField, direction: SortDirection) => {
    setSortField(field);
    setSortDirection(direction);
  };

  return {
    sortedDocuments,
    sortField,
    sortDirection,
    handleSort,
    setSorting,
  };
};

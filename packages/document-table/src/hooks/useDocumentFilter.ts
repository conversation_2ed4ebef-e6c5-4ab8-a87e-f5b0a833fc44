import { useState, useMemo, useEffect } from "react";
import { Document, FilterState } from "../types";

export interface UseDocumentFilterProps {
  documents: Document[];
  appliedFilters: FilterState;
  initialSearchTerm?: string;
}

export const useDocumentFilter = ({
  documents,
  appliedFilters,
  initialSearchTerm = "",
}: UseDocumentFilterProps) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);

  // Update searchTerm when initialSearchTerm changes
  useEffect(() => {
    setSearchTerm(initialSearchTerm);
  }, [initialSearchTerm]);

  // Filter documents based on applied filters and search term
  const filteredDocuments = useMemo(() => {
    return documents.filter((doc) => {
      // Filter by search term if provided (at least 3 characters)
      const searchMatch =
        searchTerm.length < 3 ||
        Object.values(doc).some(
          (value) =>
            value &&
            typeof value === "string" &&
            value.toLowerCase().includes(searchTerm.toLowerCase())
        );

      // Check each filter type
      const statusMatch =
        appliedFilters.status.length === 0 ||
        appliedFilters.status.some(
          (s) => doc.status?.toLowerCase() === s.toLowerCase()
        );

      const categoryMatch =
        appliedFilters.categories.length === 0 ||
        appliedFilters.categories.some(
          (c) => doc.category?.toLowerCase() === c.toLowerCase()
        );

      const departmentMatch =
        appliedFilters.departments.length === 0 ||
        appliedFilters.departments.some(
          (d) => doc.department?.toLowerCase() === d.toLowerCase()
        );

      const assigneeMatch =
        appliedFilters.assignee.length === 0 ||
        appliedFilters.assignee.some(
          (a) => doc.assignee?.toLowerCase() === a.toLowerCase()
        );

      // Check processes filter
      const processesMatch =
        !appliedFilters.processes ||
        appliedFilters.processes.length === 0 ||
        (doc.processes &&
          appliedFilters.processes.some((p) => {
            // Split comma-separated process values and check if any match
            const docProcesses = doc.processes
              ?.split(",")
              .map((item) => item.trim().toLowerCase());
            return docProcesses?.includes(p.toLowerCase());
          }));

      return (
        searchMatch &&
        statusMatch &&
        categoryMatch &&
        departmentMatch &&
        assigneeMatch &&
        processesMatch
      );
    });
  }, [documents, appliedFilters, searchTerm]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  return {
    filteredDocuments,
    searchTerm,
    handleSearch,
  };
};

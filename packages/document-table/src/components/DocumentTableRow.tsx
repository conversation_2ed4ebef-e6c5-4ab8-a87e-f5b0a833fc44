import React, { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { TableCell, TableRow } from "./ui/Table";
import { DocumentStatusBadge } from "./DocumentStatusBadge";
import { DocumentActions } from "./DocumentActions";
import { ReviewDateCell } from "./ReviewDateCell";
import { Document } from "../types";
import { cn } from "../utils/cn";

interface DocumentTableRowProps {
  document: Document;
  isMobile: boolean;
  showExpandColumn?: boolean;
  onEdit?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onView?: (document: Document) => void;
  onDuplicate?: (document: Document) => void;
}

export const DocumentTableRow: React.FC<DocumentTableRowProps> = ({
  document,
  isMobile,
  showExpandColumn = false,
  onEdit,
  onDelete,
  onView,
  onDuplicate
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return "-";
    }
  };

  const formatProcesses = (processes: string[]) => {
    if (!processes || processes.length === 0) return "-";
    if (processes.length === 1) return processes[0];
    return `${processes[0]} +${processes.length - 1}`;
  };

  if (isMobile) {
    return (
      <>
        <TableRow className="border-b border-gray-200 hover:bg-gray-50">
          <TableCell className="w-10 p-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-gray-100 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronRight className="h-4 w-4 text-gray-500" />
              )}
            </button>
          </TableCell>
          
          <TableCell className="p-2">
            <div className="font-medium text-gray-900 text-sm leading-tight">
              {document.title}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {document.department} • {document.category}
            </div>
          </TableCell>
          
          <TableCell className="p-2">
            <DocumentStatusBadge status={document.status} />
          </TableCell>
          
          <TableCell className="w-10 p-2">
            <DocumentActions
              document={document}
              onEdit={onEdit}
              onDelete={onDelete}
              onView={onView}
              onDuplicate={onDuplicate}
              isMobile={true}
            />
          </TableCell>
        </TableRow>
        
        {isExpanded && (
          <TableRow className="bg-gray-50 border-b border-gray-200">
            <TableCell colSpan={4} className="p-4">
              <div className="space-y-3 text-sm">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <span className="font-medium text-gray-700">Department:</span>
                    <div className="text-gray-600">{document.department}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Category:</span>
                    <div className="text-gray-600">{document.category}</div>
                  </div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Processes:</span>
                  <div className="text-gray-600">
                    {document.processes?.length > 0 ? document.processes.join(", ") : "-"}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <span className="font-medium text-gray-700">Assignee:</span>
                    <div className="text-gray-600">{document.assignee || "-"}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Approver:</span>
                    <div className="text-gray-600">{document.approver || "-"}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <span className="font-medium text-gray-700">Published:</span>
                    <div className="text-gray-600">{formatDate(document.publishedDate)}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Review Date:</span>
                    <div className="text-gray-600">
                      <ReviewDateCell reviewDate={document.reviewDate} />
                    </div>
                  </div>
                </div>
              </div>
            </TableCell>
          </TableRow>
        )}
      </>
    );
  }

  return (
    <TableRow className="border-b border-gray-200 hover:bg-gray-50">
      {showExpandColumn && (
        <TableCell className="w-12 p-3">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-gray-100 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </button>
        </TableCell>
      )}
      
      <TableCell className="px-4 py-3 w-1/4 min-w-[200px]">
        <div className="font-medium text-gray-900 text-sm leading-tight">
          {document.title}
        </div>
      </TableCell>
      
      <TableCell className="px-4 py-3 w-24">
        <DocumentStatusBadge status={document.status} />
      </TableCell>
      
      {!showExpandColumn && (
        <>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-28">
            {document.department}
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-24">
            {document.category}
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-28">
            {formatProcesses(document.processes || [])}
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-24">
            {document.assignee || "-"}
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-28">
            {formatDate(document.publishedDate)}
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-28">
            <ReviewDateCell reviewDate={document.reviewDate} />
          </TableCell>
          <TableCell className="px-4 py-3 text-sm text-gray-600 w-20">
            {document.approver || "-"}
          </TableCell>
        </>
      )}
      
      <TableCell className="px-4 py-3 w-20 text-right">
        <DocumentActions
          document={document}
          onEdit={onEdit}
          onDelete={onDelete}
          onView={onView}
          onDuplicate={onDuplicate}
          isMobile={false}
        />
      </TableCell>
    </TableRow>
  );
};

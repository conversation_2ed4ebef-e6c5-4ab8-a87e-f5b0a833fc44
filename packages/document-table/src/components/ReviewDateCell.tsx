import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { cn } from "../utils/cn";

interface ReviewDateCellProps {
  reviewDate: string | null;
  className?: string;
}

export const ReviewDateCell: React.FC<ReviewDateCellProps> = ({
  reviewDate,
  className
}) => {
  if (!reviewDate) {
    return <span className={cn("text-gray-500", className)}>-</span>;
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getDateStatus = (dateString: string) => {
    try {
      const reviewDate = new Date(dateString);
      const today = new Date();
      const diffTime = reviewDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays < 0) {
        return {
          status: "overdue",
          icon: AlertTriangle,
          className: "text-red-600",
          bgClassName: "bg-red-50",
          message: `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`
        };
      } else if (diffDays <= 7) {
        return {
          status: "due-soon",
          icon: Clock,
          className: "text-amber-600",
          bgClassName: "bg-amber-50",
          message: diffDays === 0 ? "Due today" : `Due in ${diffDays} day${diffDays !== 1 ? 's' : ''}`
        };
      } else if (diffDays <= 30) {
        return {
          status: "upcoming",
          icon: Clock,
          className: "text-blue-600",
          bgClassName: "bg-blue-50",
          message: `Due in ${diffDays} day${diffDays !== 1 ? 's' : ''}`
        };
      } else {
        return {
          status: "future",
          icon: null,
          className: "text-gray-600",
          bgClassName: "",
          message: ""
        };
      }
    } catch {
      return {
        status: "invalid",
        icon: null,
        className: "text-gray-600",
        bgClassName: "",
        message: ""
      };
    }
  };

  const dateStatus = getDateStatus(reviewDate);
  const formattedDate = formatDate(reviewDate);
  const Icon = dateStatus.icon;

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {Icon && (
        <Icon className={cn("h-3 w-3", dateStatus.className)} />
      )}
      <span 
        className={cn(
          "text-sm",
          dateStatus.className,
          dateStatus.bgClassName && "px-2 py-1 rounded-md"
        )}
        title={dateStatus.message}
      >
        {formattedDate}
      </span>
    </div>
  );
};

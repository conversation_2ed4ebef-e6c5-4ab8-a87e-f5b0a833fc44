import React, { useState, useRef, useEffect } from "react";
import { MoreH<PERSON><PERSON>tal, Eye, Edit, Download, Copy, Trash2 } from "lucide-react";
import { Button } from "./ui/Button";
import { Document } from "../types";
import { cn } from "../utils/cn";

interface DocumentActionsProps {
  document: Document;
  onEdit?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  onView?: (document: Document) => void;
  onDuplicate?: (document: Document) => void;
  onDownload?: (document: Document) => void;
  isMobile?: boolean;
  className?: string;
}

export const DocumentActions: React.FC<DocumentActionsProps> = ({
  document,
  onEdit,
  onDelete,
  onView,
  onDuplicate,
  onDownload,
  isMobile = false,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleAction = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  const actions = [
    {
      icon: Eye,
      label: "View",
      onClick: onView,
      className: "text-gray-700 hover:text-gray-900"
    },
    {
      icon: Edit,
      label: "Edit",
      onClick: onEdit,
      className: "text-gray-700 hover:text-gray-900"
    },
    {
      icon: Download,
      label: "Download",
      onClick: onDownload,
      className: "text-gray-700 hover:text-gray-900"
    },
    {
      icon: Copy,
      label: "Duplicate",
      onClick: onDuplicate,
      className: "text-gray-700 hover:text-gray-900"
    },
    {
      icon: Trash2,
      label: "Delete",
      onClick: onDelete,
      className: "text-red-600 hover:text-red-700"
    }
  ].filter(action => action.onClick); // Only show actions that have handlers

  if (actions.length === 0) {
    return null;
  }

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-8 w-8 p-0 hover:bg-gray-100"
      >
        <MoreHorizontal className="h-4 w-4" />
        <span className="sr-only">Open menu</span>
      </Button>

      {isOpen && (
        <div className={cn(
          "absolute z-50 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-[140px]",
          isMobile ? "right-0 top-full mt-1" : "right-0 top-full mt-1"
        )}>
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={() => handleAction(() => action.onClick!(document))}
                className={cn(
                  "w-full flex items-center gap-2 px-3 py-2 text-sm transition-colors hover:bg-gray-50",
                  action.className
                )}
              >
                <Icon className="h-4 w-4" />
                {action.label}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

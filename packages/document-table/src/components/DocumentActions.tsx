import React, { useState, useRef, useEffect } from "react";
import {
  MoreH<PERSON>zontal,
  Eye,
  Edit,
  Download,
  Copy,
  Trash2,
} from "lucide-react";
import { Button } from "./ui/Button";
import { Document, DocumentActions as DocumentActionsType } from "../types";
import { cn } from "../utils/cn";

interface DocumentActionsProps {
  document: Document;
  actions?: DocumentActionsType;
  className?: string;
}

export const DocumentActions: React.FC<DocumentActionsProps> = ({
  document,
  actions,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    window.document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleAction = (action: () => void) => {
    action();
    setIsOpen(false);
  };

  const actionItems = [
    {
      icon: Eye,
      label: "View",
      onClick: actions?.onView ? () => actions.onView!(document) : undefined,
      className: "text-gray-700 hover:text-gray-900",
    },
    {
      icon: Edit,
      label: "Edit",
      onClick: actions?.onEdit ? () => actions.onEdit!(document) : undefined,
      className: "text-gray-700 hover:text-gray-900",
    },
    {
      icon: Download,
      label: "Download",
      onClick: actions?.onDownload
        ? () => actions.onDownload!(document)
        : undefined,
      className: "text-gray-700 hover:text-gray-900",
    },
    {
      icon: Copy,
      label: "Duplicate",
      onClick: actions?.onDuplicate
        ? () => actions.onDuplicate!(document)
        : undefined,
      className: "text-gray-700 hover:text-gray-900",
    },
    {
      icon: Trash2,
      label: "Delete",
      onClick: actions?.onDelete
        ? () => actions.onDelete!(document)
        : undefined,
      className: "text-red-600 hover:text-red-700",
    },
  ].filter((action) => action.onClick); // Only show actions that have handlers

  if (actionItems.length === 0) {
    return null;
  }

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-8 w-8 p-0 hover:bg-gray-100"
      >
        <MoreHorizontal className="h-4 w-4" />
        <span className="sr-only">Open menu</span>
      </Button>

      {isOpen && (
        <div
          className={cn(
            "absolute z-50 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-[140px]",
            "right-0 top-full mt-1"
          )}
        >
          {actionItems.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={() => handleAction(action.onClick!)}
                className={cn(
                  "w-full flex items-center gap-2 px-3 py-2 text-sm transition-colors hover:bg-gray-50",
                  action.className
                )}
              >
                <Icon className="h-4 w-4" />
                {action.label}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

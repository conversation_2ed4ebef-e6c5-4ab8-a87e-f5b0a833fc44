import React, { useState, useMemo } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "./ui/Table";
import { Button } from "./ui/Button";
import { Input } from "./ui/Input";
import { Select } from "./ui/Select";
import { Pagination } from "./Pagination";
import { DocumentStatusBadge } from "./DocumentStatusBadge";
import { useDocumentFilter } from "../hooks/useDocumentFilter";
import { useDocumentPagination } from "../hooks/useDocumentPagination";
import { useIsMobile } from "../hooks/useIsMobile";
import { Document, StandaloneDocumentTableProps, SortField, SortDirection } from "../types";
import { mockDocuments } from "../data/sampleData";
import { Search, Filter, Eye, Edit, Download, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "../utils/cn";

export const DocumentTable: React.FC<StandaloneDocumentTableProps> = ({
  title = "Document Table",
  subtitle = "Manage and view documents",
  showSearch = true,
  showFilters = true,
  initialDocuments = mockDocuments,
  className
}) => {
  const [appliedFilters, setAppliedFilters] = useState({
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: []
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  
  const isMobile = useIsMobile();

  const { filteredDocuments } = useDocumentFilter({
    documents: initialDocuments,
    appliedFilters,
    initialSearchTerm: searchTerm
  });

  const {
    currentPage,
    pageSize,
    totalPages,
    handlePageChange,
    handlePageSizeChange,
    getPaginatedData
  } = useDocumentPagination({
    totalCount: filteredDocuments.length,
    initialPageSize: 10,
    initialPage: 1
  });

  // Sort documents
  const sortedDocuments = useMemo(() => {
    if (!sortField || !sortDirection) return filteredDocuments;
    
    return [...filteredDocuments].sort((a, b) => {
      const aValue = a[sortField] || '';
      const bValue = b[sortField] || '';
      
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });
  }, [filteredDocuments, sortField, sortDirection]);

  const paginatedDocuments = getPaginatedData(sortedDocuments);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc');
      if (sortDirection === 'desc') {
        setSortField(null);
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const toggleRowExpansion = (documentId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(documentId)) {
      newExpanded.delete(documentId);
    } else {
      newExpanded.add(documentId);
    }
    setExpandedRows(newExpanded);
  };

  const handleAction = (action: string, document: Document) => {
    console.log(`${action} action for document:`, document);
    // Implement your action handlers here
  };

  return (
    <div className={cn("h-full w-full space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center gap-3">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          <p className="text-gray-500 text-sm">{subtitle}</p>
        </div>
      </div>

      {/* Search and Filter */}
      {(showSearch || showFilters) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {showSearch && (
            <div className="relative w-full sm:w-96">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          )}
          
          {showFilters && (
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Document Table */}
      <div className="bg-white rounded-md shadow-sm border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              {isMobile && <TableHead className="w-10"></TableHead>}
              <TableHead 
                sortable 
                onSort={() => handleSort('title')}
                sortDirection={sortField === 'title' ? sortDirection : null}
              >
                Title
              </TableHead>
              {!isMobile && (
                <>
                  <TableHead 
                    sortable 
                    onSort={() => handleSort('department')}
                    sortDirection={sortField === 'department' ? sortDirection : null}
                  >
                    Department
                  </TableHead>
                  <TableHead 
                    sortable 
                    onSort={() => handleSort('category')}
                    sortDirection={sortField === 'category' ? sortDirection : null}
                  >
                    Category
                  </TableHead>
                </>
              )}
              <TableHead 
                sortable 
                onSort={() => handleSort('status')}
                sortDirection={sortField === 'status' ? sortDirection : null}
              >
                Status
              </TableHead>
              {!isMobile && (
                <>
                  <TableHead 
                    sortable 
                    onSort={() => handleSort('assignee')}
                    sortDirection={sortField === 'assignee' ? sortDirection : null}
                  >
                    Assignee
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedDocuments.length > 0 ? (
              paginatedDocuments.map((document, index) => (
                <React.Fragment key={document.id}>
                  <TableRow className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                    {isMobile && (
                      <TableCell className="w-10">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRowExpansion(document.id)}
                          className="p-1 h-6 w-6"
                        >
                          {expandedRows.has(document.id) ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                    )}
                    <TableCell>
                      <div>
                        <div className="font-medium text-gray-900">{document.title}</div>
                        {document.version && (
                          <div className="text-sm text-gray-500">{document.version}</div>
                        )}
                      </div>
                    </TableCell>
                    {!isMobile && (
                      <>
                        <TableCell>{document.department}</TableCell>
                        <TableCell>{document.category}</TableCell>
                      </>
                    )}
                    <TableCell>
                      <DocumentStatusBadge status={document.status} />
                    </TableCell>
                    {!isMobile && (
                      <>
                        <TableCell>{document.assignee}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("View", document)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("Edit", document)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("Download", document)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </>
                    )}
                  </TableRow>
                  
                  {/* Mobile expanded row */}
                  {isMobile && expandedRows.has(document.id) && (
                    <TableRow className="bg-gray-50">
                      <TableCell colSpan={3} className="py-4">
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Department:</span>
                              <div className="text-gray-900">{document.department}</div>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Category:</span>
                              <div className="text-gray-900">{document.category}</div>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Assignee:</span>
                              <div className="text-gray-900">{document.assignee}</div>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Approver:</span>
                              <div className="text-gray-900">{document.approver}</div>
                            </div>
                          </div>
                          
                          {document.description && (
                            <div>
                              <span className="font-medium text-gray-700 text-sm">Description:</span>
                              <div className="text-gray-900 text-sm mt-1">{document.description}</div>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2 pt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("View", document)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("Edit", document)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAction("Download", document)}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={isMobile ? 3 : 6} className="text-center py-8 text-gray-500">
                  No documents found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          total={filteredDocuments.length}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
};

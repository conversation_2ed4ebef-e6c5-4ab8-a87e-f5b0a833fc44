import React, { useState, useMemo } from "react";
import { ResizableTable } from "./ui/Table";
import { Button } from "./ui/Button";
import { Pagination } from "./Pagination";
import { SearchAndFilter } from "./SearchAndFilter";
import { DocumentTableHeader } from "./DocumentTableHeader";
import { DocumentTableRow } from "./DocumentTableRow";
import { ViewToggle } from "./ViewToggle";
import { DocumentActions } from "./DocumentActions";
import { useDocumentFilter } from "../hooks/useDocumentFilter";
import { useDocumentPagination } from "../hooks/useDocumentPagination";
import { useDocumentSorting } from "../hooks/useDocumentSorting";
import { useIsMobile } from "../hooks/useIsMobile";
import {
  Document,
  DocumentTableProps,
  FilterState,
  SortField,
  SortDirection,
  ViewMode,
  DocumentActions as DocumentActionsType,
} from "../types";
import { mockDocuments, filterOptions } from "../data/sampleData";
import { Plus } from "lucide-react";
import { cn } from "../utils/cn";

export const DocumentTable: React.FC<DocumentTableProps> = ({
  documents = mockDocuments,
  loading = false,
  error = null,
  onRefresh,
  actions,
  initialFilters = {
    status: [],
    categories: [],
    departments: [],
    assignee: [],
    processes: [],
  },
  initialSortField = "title",
  initialSortDirection = "asc",
  initialPageSize = 25,
  showSearch = true,
  showFilters = true,
  showPagination = true,
  showViewToggle = true,
  showCreateButton = true,
  onCreateClick,
  filterOptions: providedFilterOptions = filterOptions,
  className,
}) => {
  // State management
  const [appliedFilters, setAppliedFilters] =
    useState<FilterState>(initialFilters);
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterSidePanelOpen, setIsFilterSidePanelOpen] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>("table");

  const isMobile = useIsMobile();

  // Use hooks for filtering, sorting, and pagination
  const { filteredDocuments } = useDocumentFilter({
    documents,
    appliedFilters,
    initialSearchTerm: searchTerm,
  });

  const { sortedDocuments, sortField, sortDirection, handleSort } =
    useDocumentSorting({
      documents: filteredDocuments,
      initialSortField,
      initialSortDirection,
    });

  const {
    currentPage,
    pageSize,
    totalPages,
    paginatedDocuments,
    handlePageChange,
    handlePageSizeChange,
  } = useDocumentPagination({
    documents: sortedDocuments,
    initialPageSize,
    initialPage: 1,
  });

  // Event handlers
  const handleSearch = (searchTerm: string) => {
    setSearchTerm(searchTerm);
  };

  const handleApplyFilters = (filters: FilterState) => {
    setAppliedFilters(filters);
  };

  const toggleFilterSidePanel = () => {
    setIsFilterSidePanelOpen(!isFilterSidePanelOpen);
  };

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
  };

  // Loading and error states
  if (loading) {
    return (
      <div
        className={cn(
          "h-full w-full flex items-center justify-center",
          className
        )}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading documents...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={cn(
          "h-full w-full flex items-center justify-center",
          className
        )}
      >
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          {onRefresh && (
            <Button onClick={onRefresh} variant="outline">
              Try Again
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full w-full space-y-4", className)}>
      {/* Header with Create Button and View Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold text-gray-900">Documents</h1>
          {showCreateButton && (
            <Button onClick={onCreateClick} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Document
            </Button>
          )}
        </div>

        {showViewToggle && (
          <ViewToggle
            currentView={viewMode}
            onViewChange={handleViewModeChange}
          />
        )}
      </div>

      {/* Search and Filter */}
      <SearchAndFilter
        isFilterSidePanelOpen={isFilterSidePanelOpen}
        toggleFilterSidePanel={toggleFilterSidePanel}
        appliedFilters={appliedFilters}
        onApplyFilters={handleApplyFilters}
        onSearch={handleSearch}
        searchTerm={searchTerm}
        showSearch={showSearch}
        showFilters={showFilters}
        showCreateButton={false} // Already shown in header
        filterOptions={providedFilterOptions}
      />

      {/* Document Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <ResizableTable>
          <DocumentTableHeader
            sortField={sortField}
            sortDirection={sortDirection}
            onSort={handleSort}
            isMobile={isMobile}
          />
          <tbody>
            {paginatedDocuments.length > 0 ? (
              paginatedDocuments.map((document) => (
                <DocumentTableRow
                  key={document.id}
                  document={document}
                  isMobile={isMobile}
                  actions={actions}
                />
              ))
            ) : (
              <tr>
                <td
                  colSpan={isMobile ? 3 : 7}
                  className="text-center py-12 text-gray-500"
                >
                  <div className="flex flex-col items-center gap-2">
                    <p className="text-lg font-medium">No documents found</p>
                    <p className="text-sm">
                      Try adjusting your search or filters
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </ResizableTable>
      </div>

      {/* Pagination */}
      {showPagination && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          total={sortedDocuments.length}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      )}
    </div>
  );
};

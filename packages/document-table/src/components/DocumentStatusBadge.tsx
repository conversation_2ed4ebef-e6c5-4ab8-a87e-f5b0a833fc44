import React from "react";
import { Badge } from "./ui/Badge";
import { cn } from "../utils/cn";

export type DocumentStatus =
  | "published"
  | "draft"
  | "needs-approval"
  | "rejected"
  | "expired"
  | "not-uploaded"
  | "review"
  | "under review"
  | "approved"
  | "archived";

interface DocumentStatusBadgeProps {
  status: string;
  className?: string;
}

export const DocumentStatusBadge: React.FC<DocumentStatusBadgeProps> = ({
  status,
  className,
}) => {
  const lowercase = status.toLowerCase();

  const getStatusConfig = () => {
    switch (lowercase) {
      case "published":
        return {
          label: "Published",
          className: "bg-green-100 text-green-800 border-green-200",
        };
      case "draft":
        return {
          label: "Draft",
          className: "bg-gray-100 text-gray-800 border-gray-200",
        };
      case "needs-approval":
      case "needs approval":
        return {
          label: "Needs Approval",
          className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        };
      case "review":
      case "under review":
        return {
          label: "Under Review",
          className: "bg-amber-100 text-amber-800 border-amber-200",
        };
      case "approved":
        return {
          label: "Approved",
          className: "bg-green-100 text-green-800 border-green-200",
        };
      case "rejected":
        return {
          label: "Rejected",
          className: "bg-red-100 text-red-800 border-red-200",
        };
      case "expired":
        return {
          label: "Expired",
          className: "bg-orange-100 text-orange-800 border-orange-200",
        };
      case "not-uploaded":
      case "not uploaded":
        return {
          label: "Not Uploaded",
          className: "bg-gray-100 text-gray-600 border-gray-200",
        };
      case "archived":
        return {
          label: "Archived",
          className: "bg-gray-100 text-gray-700 border-gray-300",
        };
      default:
        return {
          label: status,
          className: "bg-gray-100 text-gray-800 border-gray-200",
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Badge
      variant="outline"
      className={cn(
        "text-xs font-medium px-2 py-1 rounded-full border",
        config.className,
        className
      )}
    >
      {config.label}
    </Badge>
  );
};

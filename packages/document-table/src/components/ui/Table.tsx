import React from "react";
import { cn } from "../../utils/cn";
import { ArrowDown, ArrowU<PERSON>, ArrowUpDown } from "lucide-react";

export const Table: React.FC<React.HTMLAttributes<HTMLTableElement>> = ({
  className,
  ...props
}) => (
  <div className="relative w-full overflow-auto">
    <table
      className={cn(
        "w-full caption-bottom text-sm border-separate border-spacing-0",
        className
      )}
      {...props}
    />
  </div>
);

export const TableHeader: React.FC<
  React.HTMLAttributes<HTMLTableSectionElement>
> = ({ className, ...props }) => (
  <thead className={cn("z-20", className)} {...props} />
);

export const TableBody: React.FC<
  React.HTMLAttributes<HTMLTableSectionElement>
> = ({ className, ...props }) => (
  <tbody className={cn("[&_tr:last-child]:border-0", className)} {...props} />
);

export const TableFooter: React.FC<
  React.HTMLAttributes<HTMLTableSectionElement>
> = ({ className, ...props }) => (
  <tfoot
    className={cn(
      "border-t bg-gray-100 font-medium [&>tr]:last:border-b-0",
      className
    )}
    {...props}
  />
);

export const TableRow: React.FC<React.HTMLAttributes<HTMLTableRowElement>> = ({
  className,
  ...props
}) => (
  <tr
    className={cn(
      "transition-colors hover:bg-gray-100 data-[state=selected]:bg-gray-100",
      className
    )}
    {...props}
  />
);

export interface TableHeadProps
  extends React.ThHTMLAttributes<HTMLTableCellElement> {
  sortable?: boolean;
  onSort?: () => void;
  sortDirection?: "asc" | "desc" | null;
}

export const TableHead: React.FC<TableHeadProps> = ({
  className,
  children,
  sortable,
  onSort,
  sortDirection,
  ...props
}) => (
  <th
    className={cn(
      "h-12 px-4 py-3 text-left align-middle font-bold text-gray-700 [&:has([role=checkbox])]:pr-0 text-xs uppercase tracking-wide",
      sortable && "cursor-pointer select-none hover:bg-gray-100",
      className
    )}
    onClick={sortable ? onSort : undefined}
    {...props}
  >
    {sortable ? (
      <div className="flex items-center gap-1">
        {children}
        {sortDirection === null && <ArrowUpDown className="ml-1 h-4 w-4" />}
        {sortDirection === "asc" && <ArrowUp className="ml-1 h-4 w-4" />}
        {sortDirection === "desc" && <ArrowDown className="ml-1 h-4 w-4" />}
      </div>
    ) : (
      children
    )}
  </th>
);

export const TableCell: React.FC<
  React.TdHTMLAttributes<HTMLTableCellElement>
> = ({ className, ...props }) => (
  <td
    className={cn(
      "p-3 px-4 align-middle text-sm [&:has([role=checkbox])]:pr-0",
      className
    )}
    {...props}
  />
);

export const TableCaption: React.FC<
  React.HTMLAttributes<HTMLTableCaptionElement>
> = ({ className, ...props }) => (
  <caption className={cn("mt-4 text-sm text-gray-600", className)} {...props} />
);

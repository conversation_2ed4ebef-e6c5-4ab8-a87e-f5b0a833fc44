import React from "react";
import { cn } from "../../utils/cn";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

export const Button: React.FC<ButtonProps> = ({
  className,
  variant = "default",
  size = "default",
  ...props
}) => {
  const baseClasses =
    "inline-flex items-center justify-center gap-2.5 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-250 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";

  const variantClasses = {
    default: "bg-teal-600 text-white shadow hover:bg-teal-700",
    destructive: "bg-red-600 text-white shadow hover:bg-red-700",
    outline:
      "border border-gray-200 bg-white text-gray-900 shadow hover:bg-gray-50",
    secondary: "bg-gray-100 text-gray-900 shadow hover:bg-gray-200",
    ghost: "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
    link: "text-teal-600 underline-offset-4 hover:underline hover:text-teal-700",
  };

  const sizeClasses = {
    default: "h-11 px-6 py-2.5",
    sm: "h-9 rounded-lg px-4 text-sm",
    lg: "h-13 rounded-xl px-8 text-base font-semibold",
    icon: "h-11 w-11",
  };

  return (
    <button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    />
  );
};

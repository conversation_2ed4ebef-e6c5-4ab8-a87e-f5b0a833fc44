import React, { useEffect, useState } from "react";
import { X, Filter } from "lucide-react";
import { useIsMobile } from "../hooks/useIsMobile";
import { Button } from "./ui/Button";
import { Checkbox } from "./ui/Checkbox";
import { cn } from "../utils/cn";

export interface FilterState {
  status: string[];
  categories: string[];
  departments: string[];
  assignee: string[];
  processes: string[];
}

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

interface FilterSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  filterOptions?: {
    status?: FilterOption[];
    categories?: FilterOption[];
    departments?: FilterOption[];
    assignee?: FilterOption[];
    processes?: FilterOption[];
  };
}

export const FilterSidePanel: React.FC<FilterSidePanelProps> = ({
  isOpen,
  onClose,
  filters,
  onApplyFilters,
  filterOptions = {}
}) => {
  const isMobile = useIsMobile();
  const [localFilters, setLocalFilters] = useState<FilterState>(filters);

  // Default filter options
  const defaultFilterOptions = {
    status: [
      { id: "published", label: "Published", count: 0 },
      { id: "draft", label: "Draft", count: 0 },
      { id: "needs-approval", label: "Needs Approval", count: 0 },
      { id: "rejected", label: "Rejected", count: 0 },
      { id: "expired", label: "Expired", count: 0 },
      { id: "not-uploaded", label: "Not Uploaded", count: 0 }
    ],
    categories: [
      { id: "sop", label: "SOP", count: 0 },
      { id: "policy", label: "Policy", count: 0 },
      { id: "procedure", label: "Procedure", count: 0 },
      { id: "guideline", label: "Guideline", count: 0 },
      { id: "manual", label: "Manual", count: 0 }
    ],
    departments: [
      { id: "maintenance", label: "Maintenance", count: 0 },
      { id: "project-management", label: "Project Management", count: 0 },
      { id: "production", label: "Production", count: 0 },
      { id: "quality-assurance", label: "Quality Assurance", count: 0 },
      { id: "human-resources", label: "Human Resources", count: 0 }
    ],
    assignee: [
      { id: "harsh-jha", label: "Harsh Jha", count: 0 },
      { id: "milanjeet-singh", label: "Milanjeet Singh", count: 0 },
      { id: "vishnu-tripathi", label: "Vishnu Tripathi", count: 0 },
      { id: "maitreyi-sharma", label: "Maitreyi Sharma", count: 0 },
      { id: "rehan-qureshi", label: "Rehan Qureshi", count: 0 }
    ],
    processes: [
      { id: "qa-process", label: "QA Process", count: 0 },
      { id: "inspection", label: "Inspection", count: 0 },
      { id: "procurement", label: "Procurement", count: 0 },
      { id: "manufacturing", label: "Manufacturing", count: 0 },
      { id: "certification", label: "Certification", count: 0 }
    ]
  };

  const options = { ...defaultFilterOptions, ...filterOptions };

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (filterType: keyof FilterState, selectedIds: string[]) => {
    setLocalFilters(prev => ({
      ...prev,
      [filterType]: selectedIds
    }));
  };

  const handleCheckboxChange = (filterType: keyof FilterState, optionId: string, checked: boolean) => {
    const currentValues = localFilters[filterType];
    const newValues = checked
      ? [...currentValues, optionId]
      : currentValues.filter(id => id !== optionId);
    
    handleFilterChange(filterType, newValues);
  };

  const handleReset = () => {
    setLocalFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: []
    });
  };

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  const countActiveFilters = (): number => {
    return Object.values(localFilters).reduce((count, filterArray) => count + filterArray.length, 0);
  };

  if (!isOpen) return null;

  const renderFilterSection = (title: string, filterType: keyof FilterState, options: FilterOption[]) => (
    <div className="space-y-3">
      <h3 className="font-medium text-gray-900">{title}</h3>
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {options.map((option) => (
          <label key={option.id} className="flex items-center space-x-2 cursor-pointer">
            <Checkbox
              checked={localFilters[filterType].includes(option.id)}
              onChange={(e) => handleCheckboxChange(filterType, option.id, e.target.checked)}
            />
            <span className="text-sm text-gray-700 flex-1">{option.label}</span>
            {option.count !== undefined && (
              <span className="text-xs text-gray-500">({option.count})</span>
            )}
          </label>
        ))}
      </div>
    </div>
  );

  return (
    <>
      {isMobile && <div className="fixed inset-0 bg-black/50 z-30" onClick={onClose} />}
      <div 
        className={cn(
          "fixed z-40 bg-white border-l border-gray-200 shadow-xl transform transition-transform duration-300 ease-in-out",
          isMobile 
            ? "inset-y-0 inset-x-0 w-full h-[90vh] bottom-0 top-auto rounded-t-xl" 
            : "inset-y-0 right-0 w-80"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-600" />
              <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
              {countActiveFilters() > 0 && (
                <span className="bg-teal-100 text-teal-800 text-xs font-medium px-2 py-1 rounded-full">
                  {countActiveFilters()}
                </span>
              )}
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {renderFilterSection("Status", "status", options.status || [])}
            {renderFilterSection("Categories", "categories", options.categories || [])}
            {renderFilterSection("Departments", "departments", options.departments || [])}
            {renderFilterSection("Assignee", "assignee", options.assignee || [])}
            {renderFilterSection("Processes", "processes", options.processes || [])}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 space-y-2">
            <Button onClick={handleApply} className="w-full">
              Apply Filters
            </Button>
            <Button variant="outline" onClick={handleReset} className="w-full">
              Reset All
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

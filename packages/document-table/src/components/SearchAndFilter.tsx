import React, { useState } from "react";
import { Filter, Plus } from "lucide-react";
import { FilterSidePanel, FilterState } from "./FilterSidePanel";
import { SearchBar } from "./SearchBar";
import { StatusFilter } from "./StatusFilter";
import { Button } from "./ui/Button";
import { useIsMobile } from "../hooks/useIsMobile";

interface FilterOption {
  id: string;
  label: string;
  count?: number;
}

interface SearchAndFilterProps {
  isFilterSidePanelOpen: boolean;
  toggleFilterSidePanel: () => void;
  appliedFilters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onSearch: (searchTerm: string) => void;
  searchTerm?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  showCreateButton?: boolean;
  onCreateClick?: () => void;
  filterOptions?: {
    status?: FilterOption[];
    categories?: FilterOption[];
    departments?: FilterOption[];
    assignee?: FilterOption[];
    processes?: FilterOption[];
  };
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  isFilterSidePanelOpen,
  toggleFilterSidePanel,
  appliedFilters,
  onApplyFilters,
  onSearch,
  searchTerm = "",
  showSearch = true,
  showFilters = true,
  showCreateButton = false,
  onCreateClick,
  filterOptions
}) => {
  const isMobile = useIsMobile();
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);

  // Default status options
  const defaultStatusOptions = [
    { id: "published", label: "Published", count: 0 },
    { id: "draft", label: "Draft", count: 0 },
    { id: "needs-approval", label: "Needs Approval", count: 0 },
    { id: "rejected", label: "Rejected", count: 0 },
    { id: "expired", label: "Expired", count: 0 },
    { id: "not-uploaded", label: "Not Uploaded", count: 0 }
  ];

  const statusOptions = filterOptions?.status || defaultStatusOptions;

  const countActiveFilters = (): number => {
    return Object.entries(appliedFilters)
      .filter(([key]) => key !== "status")
      .reduce((count, [_, filterArray]) => count + filterArray.length, 0);
  };

  const handleStatusChange = (selectedStatuses: string[]) => {
    const updatedFilters = {
      ...appliedFilters,
      status: selectedStatuses,
    };
    onApplyFilters(updatedFilters);
  };

  const resetAllFilters = () => {
    onApplyFilters({
      status: [],
      categories: [],
      departments: [],
      assignee: [],
      processes: [],
    });
  };

  const toggleSearch = () => {
    setIsSearchExpanded(!isSearchExpanded);
  };

  const handleSearch = (term: string) => {
    onSearch(term);
  };

  return (
    <div
      className={`bg-white px-4 ${
        isMobile ? "py-2" : "py-4"
      } border-b border-gray-200`}
    >
      <div className="max-w-full flex items-center gap-4 justify-between">
        {showSearch && (
          <div className={`${isMobile ? "flex-grow" : "w-96 max-w-md"}`}>
            <SearchBar
              onSearch={handleSearch}
              initialSearchTerm={searchTerm}
              expanded={isSearchExpanded}
              onToggleExpand={toggleSearch}
            />
          </div>
        )}

        {showFilters && (!isMobile || !isSearchExpanded) && (
          <div className="flex items-center gap-3">
            <StatusFilter
              appliedFilters={appliedFilters}
              handleStatusChange={handleStatusChange}
              statusOptions={statusOptions}
            />

            <Button
              variant="outline"
              size="sm"
              onClick={toggleFilterSidePanel}
              className={countActiveFilters() > 0 ? "bg-teal-50 border-teal-200 text-teal-700" : ""}
            >
              <Filter className="h-4 w-4 mr-2" />
              {isMobile ? "Filters" : "More Filters"}
              {countActiveFilters() > 0 && (
                <span className="ml-1 bg-teal-600 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
                  {countActiveFilters()}
                </span>
              )}
            </Button>

            {showCreateButton && onCreateClick && (
              <Button
                size="sm"
                onClick={onCreateClick}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                {isMobile ? "Create" : "Create Document"}
              </Button>
            )}

            {countActiveFilters() > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetAllFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                Clear All
              </Button>
            )}
          </div>
        )}

        {showFilters && (
          <FilterSidePanel
            isOpen={isFilterSidePanelOpen}
            onClose={toggleFilterSidePanel}
            filters={appliedFilters}
            onApplyFilters={onApplyFilters}
            filterOptions={filterOptions}
          />
        )}
      </div>
    </div>
  );
};

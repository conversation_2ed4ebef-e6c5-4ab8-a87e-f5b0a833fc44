import React from "react";
import { List, Grid } from "lucide-react";
import { Button } from "./ui/Button";
import { cn } from "../utils/cn";

export type ViewMode = "table" | "grid";

interface ViewToggleProps {
  currentView: ViewMode;
  onViewChange: (view: ViewMode) => void;
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onViewChange,
  className
}) => {
  return (
    <div className={cn("flex items-center bg-gray-100 rounded-lg p-1", className)}>
      <Button
        variant={currentView === "table" ? "default" : "ghost"}
        size="sm"
        onClick={() => onViewChange("table")}
        className={cn(
          "h-8 px-3 rounded-md transition-all",
          currentView === "table"
            ? "bg-white shadow-sm text-gray-900"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
        )}
      >
        <List className="h-4 w-4 mr-1" />
        Table
      </Button>
      
      <Button
        variant={currentView === "grid" ? "default" : "ghost"}
        size="sm"
        onClick={() => onViewChange("grid")}
        className={cn(
          "h-8 px-3 rounded-md transition-all",
          currentView === "grid"
            ? "bg-white shadow-sm text-gray-900"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
        )}
      >
        <Grid className="h-4 w-4 mr-1" />
        Grid
      </Button>
    </div>
  );
};

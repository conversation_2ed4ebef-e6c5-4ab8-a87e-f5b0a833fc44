import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Check } from "lucide-react";
import { Button } from "./ui/Button";
import { cn } from "../utils/cn";
import { FilterState } from "./FilterSidePanel";

interface StatusOption {
  id: string;
  label: string;
  count?: number;
}

interface StatusFilterProps {
  appliedFilters: FilterState;
  handleStatusChange: (selectedStatuses: string[]) => void;
  statusOptions: StatusOption[];
  className?: string;
}

export const StatusFilter: React.FC<StatusFilterProps> = ({
  appliedFilters,
  handleStatusChange,
  statusOptions,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleStatusToggle = (statusId: string) => {
    const currentStatuses = appliedFilters.status || [];
    const newStatuses = currentStatuses.includes(statusId)
      ? currentStatuses.filter(id => id !== statusId)
      : [...currentStatuses, statusId];
    
    handleStatusChange(newStatuses);
  };

  const getSelectedCount = () => {
    return appliedFilters.status?.length || 0;
  };

  const getButtonText = () => {
    const selectedCount = getSelectedCount();
    if (selectedCount === 0) return "Status";
    if (selectedCount === 1) {
      const selectedStatus = statusOptions.find(option => 
        appliedFilters.status.includes(option.id)
      );
      return selectedStatus?.label || "Status";
    }
    return `Status (${selectedCount})`;
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center gap-2 min-w-[100px]",
          getSelectedCount() > 0 && "bg-teal-50 border-teal-200 text-teal-700"
        )}
      >
        {getButtonText()}
        <ChevronDown className={cn(
          "h-4 w-4 transition-transform",
          isOpen && "transform rotate-180"
        )} />
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide px-2 py-1">
              Filter by Status
            </div>
            <div className="space-y-1 max-h-64 overflow-y-auto">
              {statusOptions.map((option) => {
                const isSelected = appliedFilters.status?.includes(option.id) || false;
                return (
                  <button
                    key={option.id}
                    onClick={() => handleStatusToggle(option.id)}
                    className={cn(
                      "w-full flex items-center justify-between px-2 py-2 text-sm rounded hover:bg-gray-50 transition-colors",
                      isSelected && "bg-teal-50 text-teal-700"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-4 h-4 border border-gray-300 rounded flex items-center justify-center",
                        isSelected && "bg-teal-600 border-teal-600"
                      )}>
                        {isSelected && <Check className="h-3 w-3 text-white" />}
                      </div>
                      <span>{option.label}</span>
                    </div>
                    {option.count !== undefined && (
                      <span className="text-xs text-gray-500">({option.count})</span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

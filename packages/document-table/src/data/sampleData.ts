import { Document, FilterOptions } from "../types";

export const mockDocuments: Document[] = [
  {
    id: "1",
    title: "Quality Management System Manual",
    version: "v2.1",
    department: "Quality Assurance",
    category: "Policy",
    processes: ["Quality Control", "Audit Management"],
    status: "published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-01-15",
    publishedDate: "2024-01-15",
    reviewDate: "2024-07-15",
    description:
      "Comprehensive manual outlining quality management procedures and standards.",
    parentFolderId: null,
  },
  {
    id: "2",
    title: "Employee Safety Guidelines",
    version: "v1.3",
    department: "Human Resources",
    category: "Guidelines",
    processes: ["Safety Training", "Incident Reporting"],
    status: "under review",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-01",
    publishedDate: "2024-01-20",
    reviewDate: "2024-08-01",
    description: "Safety protocols and guidelines for all employees.",
    parentFolderId: null,
  },
  {
    id: "3",
    title: "Financial Reporting Standards",
    version: "v3.0",
    department: "Finance",
    category: "Standards",
    processes: ["Financial Reporting", "Compliance"],
    status: "draft",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-10",
    publishedDate: undefined,
    reviewDate: "2024-08-10",
    description:
      "Standards for financial reporting and compliance requirements.",
    parentFolderId: null,
  },
  {
    id: "4",
    title: "IT Security Policy",
    version: "v2.5",
    department: "Information Technology",
    category: "Policy",
    processes: ["Security Management", "Access Control"],
    status: "published",
    assignee: "Alex Rodriguez",
    approver: "Emily Davis",
    date: "2024-01-25",
    publishedDate: "2024-01-25",
    reviewDate: "2024-07-25",
    description: "Comprehensive IT security policies and procedures.",
    parentFolderId: null,
  },
  {
    id: "5",
    title: "Customer Service Manual",
    version: "v1.8",
    department: "Customer Service",
    category: "Manual",
    processes: ["Customer Support", "Issue Resolution"],
    status: "published",
    assignee: "Maria Garcia",
    approver: "James Wilson",
    date: "2024-02-05",
    publishedDate: "2024-02-05",
    reviewDate: "2024-08-05",
    description: "Guidelines for customer service representatives.",
    parentFolderId: null,
  },
  {
    id: "6",
    title: "Environmental Compliance Guide",
    version: "v1.2",
    department: "Environmental",
    category: "Guidelines",
    processes: ["Environmental Monitoring", "Compliance Reporting"],
    status: "archived",
    assignee: "Thomas Lee",
    approver: "Susan Miller",
    date: "2023-12-15",
    publishedDate: "2023-12-15",
    reviewDate: "2024-06-15",
    description: "Environmental compliance requirements and procedures.",
    parentFolderId: null,
  },
  {
    id: "7",
    title: "Product Development Process",
    version: "v2.0",
    department: "Research & Development",
    category: "Process",
    processes: ["Product Design", "Testing", "Launch"],
    status: "under review",
    assignee: "Kevin Zhang",
    approver: "Rachel Green",
    date: "2024-02-12",
    publishedDate: "2024-01-30",
    reviewDate: "2024-08-12",
    description: "Step-by-step product development methodology.",
    parentFolderId: null,
  },
  {
    id: "8",
    title: "Vendor Management Policy",
    version: "v1.5",
    department: "Procurement",
    category: "Policy",
    processes: ["Vendor Selection", "Contract Management"],
    status: "published",
    assignee: "Amanda White",
    approver: "Daniel Kim",
    date: "2024-01-30",
    publishedDate: "2024-01-30",
    reviewDate: "2024-07-30",
    description: "Policies for vendor selection and management.",
    parentFolderId: null,
  },
  {
    id: "9",
    title: "Training and Development Plan",
    version: "v1.1",
    department: "Human Resources",
    category: "Plan",
    processes: ["Training Programs", "Skill Development"],
    status: "draft",
    assignee: "Michelle Thompson",
    approver: "Christopher Lee",
    date: "2024-02-08",
    publishedDate: undefined,
    reviewDate: "2024-08-08",
    description: "Annual training and development plan for employees.",
    parentFolderId: null,
  },
  {
    id: "10",
    title: "Data Privacy Policy",
    version: "v2.2",
    department: "Legal",
    category: "Policy",
    processes: ["Data Protection", "Privacy Compliance"],
    status: "published",
    assignee: "Brian Johnson",
    approver: "Nicole Brown",
    date: "2024-01-20",
    publishedDate: "2024-01-20",
    reviewDate: "2024-07-20",
    description: "Data privacy and protection policies.",
    parentFolderId: null,
  },
];

// Available document statuses
export const documentStatuses = [
  "draft",
  "under review",
  "published",
  "archived",
  "expired",
  "needs-approval",
  "rejected",
  "not-uploaded",
] as const;

// Available departments
export const documentDepartments = [
  "Quality Assurance",
  "Human Resources",
  "Finance",
  "Information Technology",
  "Customer Service",
  "Environmental",
  "Research & Development",
  "Procurement",
  "Legal",
] as const;

// Available categories
export const documentCategories = [
  "Policy",
  "Guidelines",
  "Standards",
  "Manual",
  "Process",
  "Plan",
] as const;

// Filter options for the DocumentTable
export const filterOptions: FilterOptions = {
  status: [
    { id: "published", label: "Published", count: 4 },
    { id: "draft", label: "Draft", count: 2 },
    { id: "under review", label: "Under Review", count: 2 },
    { id: "archived", label: "Archived", count: 1 },
    { id: "expired", label: "Expired", count: 1 },
  ],
  categories: [
    { id: "Policy", label: "Policy", count: 4 },
    { id: "Guidelines", label: "Guidelines", count: 2 },
    { id: "Manual", label: "Manual", count: 1 },
    { id: "Standards", label: "Standards", count: 1 },
    { id: "Process", label: "Process", count: 1 },
    { id: "Plan", label: "Plan", count: 1 },
  ],
  departments: [
    { id: "Human Resources", label: "Human Resources", count: 2 },
    { id: "Quality Assurance", label: "Quality Assurance", count: 1 },
    { id: "Finance", label: "Finance", count: 1 },
    { id: "Information Technology", label: "Information Technology", count: 1 },
    { id: "Customer Service", label: "Customer Service", count: 1 },
    { id: "Environmental", label: "Environmental", count: 1 },
    { id: "Research & Development", label: "Research & Development", count: 1 },
    { id: "Procurement", label: "Procurement", count: 1 },
    { id: "Legal", label: "Legal", count: 1 },
  ],
  assignee: [
    { id: "Sarah Johnson", label: "Sarah Johnson", count: 1 },
    { id: "David Wilson", label: "David Wilson", count: 1 },
    { id: "Jennifer Brown", label: "Jennifer Brown", count: 1 },
    { id: "Alex Rodriguez", label: "Alex Rodriguez", count: 1 },
    { id: "Maria Garcia", label: "Maria Garcia", count: 1 },
    { id: "Thomas Lee", label: "Thomas Lee", count: 1 },
    { id: "Kevin Zhang", label: "Kevin Zhang", count: 1 },
    { id: "Amanda White", label: "Amanda White", count: 1 },
    { id: "Michelle Thompson", label: "Michelle Thompson", count: 1 },
    { id: "Brian Johnson", label: "Brian Johnson", count: 1 },
  ],
  processes: [
    { id: "Quality Control", label: "Quality Control", count: 1 },
    { id: "Audit Management", label: "Audit Management", count: 1 },
    { id: "Safety Training", label: "Safety Training", count: 1 },
    { id: "Incident Reporting", label: "Incident Reporting", count: 1 },
    { id: "Financial Reporting", label: "Financial Reporting", count: 1 },
    { id: "Compliance", label: "Compliance", count: 1 },
    { id: "Security Management", label: "Security Management", count: 1 },
    { id: "Access Control", label: "Access Control", count: 1 },
    { id: "Customer Support", label: "Customer Support", count: 1 },
    { id: "Issue Resolution", label: "Issue Resolution", count: 1 },
  ],
};

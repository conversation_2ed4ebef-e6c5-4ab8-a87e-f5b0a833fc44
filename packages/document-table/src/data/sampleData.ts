import { Document } from "../hooks/useDocumentFilter";

export const mockDocuments: Document[] = [
  {
    id: "1",
    title: "Quality Management System Manual",
    version: "v2.1",
    department: "Quality Assurance",
    category: "Policy",
    processes: "Quality Control, Audit Management",
    status: "Published",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-01-15",
    publishedDate: "2024-01-15",
    reviewDate: "2024-07-15",
    description:
      "Comprehensive manual outlining quality management procedures and standards.",
    parentFolderId: null,
  },
  {
    id: "2",
    title: "Employee Safety Guidelines",
    version: "v1.3",
    department: "Human Resources",
    category: "Guidelines",
    processes: "Safety Training, Incident Reporting",
    status: "Under Review",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-01",
    publishedDate: "2024-01-20",
    reviewDate: "2024-08-01",
    description: "Safety protocols and guidelines for all employees.",
    parentFolderId: null,
  },
  {
    id: "3",
    title: "Financial Reporting Standards",
    version: "v3.0",
    department: "Finance",
    category: "Standards",
    processes: "Financial Reporting, Compliance",
    status: "Draft",
    assignee: "<PERSON>",
    approver: "<PERSON>",
    date: "2024-02-10",
    publishedDate: undefined,
    reviewDate: "2024-08-10",
    description:
      "Standards for financial reporting and compliance requirements.",
    parentFolderId: null,
  },
  {
    id: "4",
    title: "IT Security Policy",
    version: "v2.5",
    department: "Information Technology",
    category: "Policy",
    processes: "Security Management, Access Control",
    status: "Published",
    assignee: "Alex Rodriguez",
    approver: "Emily Davis",
    date: "2024-01-25",
    publishedDate: "2024-01-25",
    reviewDate: "2024-07-25",
    description: "Comprehensive IT security policies and procedures.",
    parentFolderId: null,
  },
  {
    id: "5",
    title: "Customer Service Manual",
    version: "v1.8",
    department: "Customer Service",
    category: "Manual",
    processes: "Customer Support, Issue Resolution",
    status: "Published",
    assignee: "Maria Garcia",
    approver: "James Wilson",
    date: "2024-02-05",
    publishedDate: "2024-02-05",
    reviewDate: "2024-08-05",
    description: "Guidelines for customer service representatives.",
    parentFolderId: null,
  },
  {
    id: "6",
    title: "Environmental Compliance Guide",
    version: "v1.2",
    department: "Environmental",
    category: "Guidelines",
    processes: "Environmental Monitoring, Compliance Reporting",
    status: "Archived",
    assignee: "Thomas Lee",
    approver: "Susan Miller",
    date: "2023-12-15",
    publishedDate: "2023-12-15",
    reviewDate: "2024-06-15",
    description: "Environmental compliance requirements and procedures.",
    parentFolderId: null,
  },
  {
    id: "7",
    title: "Product Development Process",
    version: "v2.0",
    department: "Research & Development",
    category: "Process",
    processes: "Product Design, Testing, Launch",
    status: "Under Review",
    assignee: "Kevin Zhang",
    approver: "Rachel Green",
    date: "2024-02-12",
    publishedDate: "2024-01-30",
    reviewDate: "2024-08-12",
    description: "Step-by-step product development methodology.",
    parentFolderId: null,
  },
  {
    id: "8",
    title: "Vendor Management Policy",
    version: "v1.5",
    department: "Procurement",
    category: "Policy",
    processes: "Vendor Selection, Contract Management",
    status: "Published",
    assignee: "Amanda White",
    approver: "Daniel Kim",
    date: "2024-01-30",
    publishedDate: "2024-01-30",
    reviewDate: "2024-07-30",
    description: "Policies for vendor selection and management.",
    parentFolderId: null,
  },
  {
    id: "9",
    title: "Training and Development Plan",
    version: "v1.1",
    department: "Human Resources",
    category: "Plan",
    processes: "Training Programs, Skill Development",
    status: "Draft",
    assignee: "Michelle Thompson",
    approver: "Christopher Lee",
    date: "2024-02-08",
    publishedDate: undefined,
    reviewDate: "2024-08-08",
    description: "Annual training and development plan for employees.",
    parentFolderId: null,
  },
  {
    id: "10",
    title: "Data Privacy Policy",
    version: "v2.2",
    department: "Legal",
    category: "Policy",
    processes: "Data Protection, Privacy Compliance",
    status: "Published",
    assignee: "Brian Johnson",
    approver: "Nicole Brown",
    date: "2024-01-20",
    publishedDate: "2024-01-20",
    reviewDate: "2024-07-20",
    description: "Data privacy and protection policies.",
    parentFolderId: null,
  },
];

// Available document statuses
export const documentStatuses = [
  "Draft",
  "Under Review",
  "Published",
  "Archived",
  "Expired",
] as const;

// Available departments
export const documentDepartments = [
  "Quality Assurance",
  "Human Resources",
  "Finance",
  "Information Technology",
  "Customer Service",
  "Environmental",
  "Research & Development",
  "Procurement",
  "Legal",
] as const;

// Available categories
export const documentCategories = [
  "Policy",
  "Guidelines",
  "Standards",
  "Manual",
  "Process",
  "Plan",
] as const;

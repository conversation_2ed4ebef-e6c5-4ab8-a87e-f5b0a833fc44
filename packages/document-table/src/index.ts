// Main component
export { DocumentTable } from "./components/DocumentTable";

// UI Components
export { Button } from "./components/ui/Button";
export { Badge } from "./components/ui/Badge";
export { Input } from "./components/ui/Input";
export { Select } from "./components/ui/Select";
export {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
  ResizableTable,
} from "./components/ui/Table";

// Document Components
export { DocumentStatusBadge } from "./components/DocumentStatusBadge";
export { DocumentActions } from "./components/DocumentActions";
export { DocumentTableHeader } from "./components/DocumentTableHeader";
export { DocumentTableRow } from "./components/DocumentTableRow";
export { ReviewDateCell } from "./components/ReviewDateCell";
export { SearchAndFilter } from "./components/SearchAndFilter";
export { FilterSidePanel } from "./components/FilterSidePanel";
export { ViewToggle } from "./components/ViewToggle";
export { Pagination } from "./components/Pagination";

// Hooks
export { useDocumentFilter } from "./hooks/useDocumentFilter";
export { useDocumentPagination } from "./hooks/useDocumentPagination";
export { useDocumentSorting } from "./hooks/useDocumentSorting";
export { useIsMobile } from "./hooks/useIsMobile";

// Types
export type {
  Document,
  DocumentStatus,
  FilterState,
  FilterOption,
  FilterOptions,
  DocumentTableProps,
  StandaloneDocumentTableProps,
  DocumentActions as DocumentActionsType,
  SortField,
  SortDirection,
  ViewMode,
} from "./types";

// Sample data
export {
  mockDocuments,
  documentStatuses,
  documentDepartments,
  documentCategories,
  filterOptions,
} from "./data/sampleData";

// Utilities
export { cn } from "./utils/cn";

// Main component
export { DocumentTable } from "./components/DocumentTable";

// UI Components
export { Button } from "./components/ui/Button";
export { Badge } from "./components/ui/Badge";
export { Input } from "./components/ui/Input";
export { Select } from "./components/ui/Select";
export {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "./components/ui/Table";

// Other components
export { DocumentStatusBadge } from "./components/DocumentStatusBadge";
export { Pagination } from "./components/Pagination";

// Hooks
export { useDocumentFilter } from "./hooks/useDocumentFilter";
export { useDocumentPagination } from "./hooks/useDocumentPagination";
export { useIsMobile } from "./hooks/useIsMobile";

// Types
export type {
  Document,
  FilterState,
  DocumentTableProps,
  StandaloneDocumentTableProps,
  SortField,
  SortDirection,
} from "./types";

// Sample data
export {
  mockDocuments,
  documentStatuses,
  documentDepartments,
  documentCategories,
} from "./data/sampleData";

// Utilities
export { cn } from "./utils/cn";

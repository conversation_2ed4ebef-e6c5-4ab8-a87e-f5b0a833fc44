import React from 'https://esm.sh/react@18';
import ReactDOM from 'https://esm.sh/react-dom@18/client';
import { DocumentTable } from '../dist/index.esm.js';

const App = () => {
  return React.createElement('div', {
    className: 'p-6 min-h-screen bg-gray-50'
  }, [
    React.createElement('h1', {
      key: 'title',
      className: 'text-3xl font-bold mb-6 text-gray-900'
    }, 'Document Table Package Example'),
    
    React.createElement(DocumentTable, {
      key: 'table',
      title: 'Sample Documents',
      subtitle: 'Testing the standalone package',
      showSearch: true,
      showFilters: true,
      className: 'bg-white'
    })
  ]);
};

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(React.createElement(App));

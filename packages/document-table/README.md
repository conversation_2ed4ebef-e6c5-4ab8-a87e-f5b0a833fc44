# @bprhub/document-table

A comprehensive, standalone React document table component with search, filtering, sorting, and pagination capabilities.

## Features

- 📱 **Responsive Design**: Automatically adapts to mobile and desktop layouts
- 🔍 **Search**: Built-in search functionality across all document fields
- 🎯 **Filtering**: Advanced filtering by status, department, category, and more
- 📊 **Sorting**: Click-to-sort on any column with visual indicators
- 📄 **Pagination**: Configurable page sizes with navigation controls
- 🎨 **Customizable**: Flexible styling and configuration options
- 📦 **TypeScript**: Full TypeScript support with comprehensive type definitions
- 🚀 **Zero Dependencies**: Minimal external dependencies for easy integration

## Installation

```bash
npm install @bprhub/document-table
```

## Quick Start

```tsx
import React from 'react';
import { DocumentTable } from '@bprhub/document-table';

function App() {
  return (
    <div className="p-6">
      <DocumentTable
        title="My Documents"
        subtitle="Manage your document library"
        showSearch={true}
        showFilters={true}
      />
    </div>
  );
}

export default App;
```

## Props

### DocumentTable

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | `"Document Table"` | Table title |
| `subtitle` | `string` | `"Manage and view documents"` | Table subtitle |
| `showSearch` | `boolean` | `true` | Show/hide search functionality |
| `showFilters` | `boolean` | `true` | Show/hide filter controls |
| `initialDocuments` | `Document[]` | `mockDocuments` | Initial document data |
| `className` | `string` | `undefined` | Additional CSS classes |

## Document Interface

```typescript
interface Document {
  id: string;
  title: string;
  version?: string;
  department: string;
  category: string;
  processes?: string;
  status: string;
  assignee: string;
  approver: string;
  date?: string;
  publishedDate?: string;
  reviewDate?: string;
  description?: string;
  parentFolderId?: string | null;
}
```

## Advanced Usage

### Custom Document Data

```tsx
import { DocumentTable, Document } from '@bprhub/document-table';

const customDocuments: Document[] = [
  {
    id: '1',
    title: 'Custom Document',
    department: 'Engineering',
    category: 'Technical',
    status: 'Published',
    assignee: 'John Doe',
    approver: 'Jane Smith',
    // ... other fields
  },
  // ... more documents
];

function CustomDocumentTable() {
  return (
    <DocumentTable
      title="Engineering Documents"
      initialDocuments={customDocuments}
      showSearch={true}
      showFilters={false}
    />
  );
}
```

### Using Individual Components

```tsx
import { 
  Button, 
  Badge, 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow,
  DocumentStatusBadge 
} from '@bprhub/document-table';

function CustomTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Title</TableHead>
          <TableHead>Status</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell>Sample Document</TableCell>
          <TableCell>
            <DocumentStatusBadge status="Published" />
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
}
```

### Using Hooks

```tsx
import { useDocumentFilter, useDocumentPagination } from '@bprhub/document-table';

function CustomImplementation() {
  const { filteredDocuments } = useDocumentFilter({
    documents: myDocuments,
    appliedFilters: { status: ['Published'] },
    initialSearchTerm: ''
  });

  const { currentPage, pageSize, handlePageChange } = useDocumentPagination({
    totalCount: filteredDocuments.length,
    initialPageSize: 25,
    initialPage: 1
  });

  // Your custom implementation
}
```

## Styling

The component uses Tailwind CSS classes for styling. Make sure your project has Tailwind CSS configured, or provide custom CSS for the classes used.

### Required CSS Classes

The component relies on standard Tailwind CSS utility classes. If you're not using Tailwind, you'll need to provide equivalent styles for classes like:

- Layout: `flex`, `grid`, `w-full`, `h-full`, etc.
- Spacing: `p-4`, `m-2`, `gap-4`, etc.
- Colors: `bg-white`, `text-gray-700`, `border-gray-200`, etc.
- Typography: `text-sm`, `font-medium`, `text-center`, etc.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT © BPR Hub
